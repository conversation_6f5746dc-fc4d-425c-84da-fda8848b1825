package com.imile.attendance.permission.mapstruct;


import com.imile.attendance.enums.DeptOrgTypeEnum;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.permission.dto.AttendanceDeptTreeDTO;
import com.imile.attendance.permission.query.AttendanceDeptQuery;
import com.imile.attendance.permission.query.DeptQuery;
import com.imile.attendance.permission.vo.AttendanceDeptTreeVO;
import com.imile.attendance.permission.vo.AttendanceDeptVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface PermissionMapstruct {

    PermissionMapstruct INSTANCE = Mappers.getMapper(PermissionMapstruct.class);

    AttendanceDeptVO mapToDept(AttendanceDept dept);

    List<AttendanceDeptVO> mapToDept(List<AttendanceDept> deptList);

    List<AttendanceDeptTreeVO> mapToDeptTree(List<AttendanceDeptTreeDTO> deptList);

    default List<Integer> getOrgTypeList(Integer isDriver) {
        return DeptOrgTypeEnum.getDeptOrgTypeList(isDriver);
    }

    @Mapping(target = "deptOrgTypeList", expression = "java(getOrgTypeList(query.getIsDriver()))")
    AttendanceDeptQuery mapToAttendanceDeptQuery(DeptQuery query);
}
