package com.imile.attendance.permission.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.permission.dto.AttendanceDeptTreeDTO;
import com.imile.attendance.permission.mapstruct.PermissionMapstruct;
import com.imile.attendance.permission.query.DeptQuery;
import com.imile.attendance.permission.vo.AttendanceDeptVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.mapstruct.Mapping;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考勤公共权限查询
 *
 * <AUTHOR>
 * @date 2025/4/18
 */
@Service
public class AttendancePermissionServiceImpl implements AttendancePermissionService {
    @Resource
    private CountryService countryService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private UserResourceService userResourceService;

    @Override
    public List<String> getUserCountryPermission() {
        // 通过国家+部门权限取并集
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (Objects.isNull(userContext)) {
            return Collections.emptyList();
        }
        List<String> countryCode = Lists.newArrayList();
        // 常驻国权限
        if (CollectionUtils.isNotEmpty(userContext.getCountryList())) {
            countryCode = userContext.getCountryList();
        }
        // 部门权限
        if (CollectionUtils.isNotEmpty(userContext.getOrganizationIds())) {
            List<AttendanceDept> deptList = deptService.listByDeptIds(userContext.getOrganizationIds());
            countryCode.addAll(Optional.ofNullable(deptList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(item -> item.getCountry())
                    .distinct()
                    .collect(Collectors.toList()));
        }
        return countryCode.stream()
                .filter(item -> Objects.nonNull(item))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getUserLocationCountryPermission() {
        // 查询常驻国权限
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (Objects.isNull(userContext)) {
            return Collections.emptyList();
        }
        List<String> locationCountryList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userContext.getCountryList())) {
            locationCountryList = userContext.getCountryList();
        }
        return locationCountryList;
    }

    @Override
    public List<Long> getUserDeptPermission() {
        // 查询部门权限
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (Objects.isNull(userContext)) {
            return Collections.emptyList();
        }
        List<Long> deptIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(userContext.getOrganizationIds())) {
            deptIds = userContext.getOrganizationIds();
        }
        return deptIds;
    }

    @Override
    public List<String> getUserCountryPermissionByParam(List<String> countryListParam) {
        List<String> countryList = Lists.newArrayList();
        List<String> userCountryPermission = this.getUserCountryPermission();
        if (CollectionUtils.isEmpty(userCountryPermission)) {
            return countryList;
        }
        if (CollectionUtils.isEmpty(countryListParam)) {
            return userCountryPermission;
        }
        // 传递国家集合不为空
        if (CollectionUtils.isNotEmpty(countryListParam)) {
            for (String country : countryListParam) {
                if (userCountryPermission.contains(country)) {
                    countryList.add(country);
                }
            }
        }
        return countryList.stream()
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<AttendanceDeptVO> getUserDeptPermissionByCountry(String country) {
//        // 通过国家获取地理国对应的部门与当前拥有的权限部门取交集
//        UserContext userContext = RequestInfoHolder.getLoginInfo();
//        if (Objects.isNull(userContext)) {
//            return Collections.emptyList();
//        }
//        List<Long> organizationIds = userContext.getOrganizationIds();
//        if (CollectionUtils.isEmpty(organizationIds)) {
//            return Collections.emptyList();
//        }
//        List<AttendanceDept> attendanceDeptList = deptService.listByDeptIds(organizationIds);
//        List<AttendanceDeptVO> attendanceDeptVOList = PermissionMapstruct.INSTANCE.mapToDept(attendanceDeptList);
//        if (CollectionUtils.isEmpty(attendanceDeptVOList)) {
//            return Collections.emptyList();
//        }
//        // 部门权限
//        if (CollectionUtils.isNotEmpty(organizationIds)) {
//            return attendanceDeptVOList.stream()
//                    .filter(item -> StringUtils.equalsIgnoreCase(country, item.getCountry()))
//                    .collect(Collectors.toList());
//        }
//        return Collections.emptyList();
        List<AttendanceDept> attendanceDeptList = deptService.listByCountry(country);
        return PermissionMapstruct.INSTANCE.mapToDept(attendanceDeptList);
    }

    @Override
    public List<AttendanceDeptTreeDTO> getDeptTree(Long userId, DeptQuery deptQuery) {
//        AttendanceDeptQuery attendanceDeptQuery = PermissionMapstruct.INSTANCE.mapToAttendanceDeptQuery(deptQuery);
//        PermissionDeptVO permissionDept = getPermissionDeptVO(userId, attendanceDeptQuery);
//        if (!permissionDept.getHasDeptPermission()) {
//            return new ArrayList<>();
//        }
//        return getDeptTreeByPermission(deptQuery, permissionDept);
        return null;
    }

    @Override
    public PermissionCountryDeptVO getPermissionCountryDeptVO(List<Long> deptIds, List<String> countryList) {
        PermissionCountryDeptVO permissionDept = this.getPermissionCountryDeptVO();

        // 部门
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<Long> deptIdList = permissionDept.getDeptIdList();
            Collection<Long> intersectionDept = CollectionUtils.intersection(deptIds, deptIdList);
            if (CollectionUtils.isNotEmpty(intersectionDept)) {
                permissionDept.setDeptIdList(new ArrayList<>(intersectionDept));
            } else {
                permissionDept.setHasDeptPermission(Boolean.FALSE);
                permissionDept.setDeptIdList(new ArrayList<>());
            }
        }

        // 国家
        if (CollectionUtils.isNotEmpty(countryList)) {
            Collection<String> intersectionCountry = CollectionUtils.intersection(countryList, permissionDept.getCountryList());
            if (CollectionUtils.isNotEmpty(intersectionCountry)) {
                permissionDept.setCountryList(new ArrayList<>(intersectionCountry));
            } else {
                permissionDept.setHasCountryPermission(Boolean.FALSE);
                permissionDept.setCountryList(new ArrayList<>());
            }
        }

        // 部门国家权限
        if (CollectionUtils.isNotEmpty(permissionDept.getDeptIdList()) &&
                CollectionUtils.isNotEmpty(permissionDept.getCountryList())) {
            permissionDept
                    .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                    .setHasOrDeptAndCountryPermission(Boolean.TRUE);
        } else {
            if (CollectionUtils.isNotEmpty(permissionDept.getDeptIdList()) ||
                    CollectionUtils.isNotEmpty(permissionDept.getCountryList())) {
                permissionDept
                        .setHasOrDeptAndCountryPermission(Boolean.TRUE);
            } else {
                permissionDept
                        .setHasOrDeptAndCountryPermission(Boolean.FALSE);
            }
            permissionDept
                    .setHasAndDeptAndCountryPermission(Boolean.FALSE);
        }

        return permissionDept;
    }

    @Override
    public List<String> filterUserCountryAuth(String country,
                                              List<String> countryList) {
        // 获取当前用户常驻国权限
        List<String> countryAuthList = this.getUserLocationCountryPermission();
        if (StringUtils.isBlank(country)
                && CollectionUtils.isEmpty(countryList)) {
            return countryAuthList;
        }
        // 两者同传、暂时没有这种情况
        if (StringUtils.isNotBlank(country)
                && CollectionUtils.isNotEmpty(countryList)) {
            countryList.add(country);
            return countryList
                    .stream()
                    .filter(item -> countryAuthList.contains(item))
                    .distinct()
                    .collect(Collectors.toList());
        }
        // 过滤参数需在权限范围内
        if (StringUtils.isNotBlank(country)
                && !countryAuthList.contains(country)) {
            return Collections.emptyList();
        }
        // 只传单个国家
        if (StringUtils.isNotBlank(country)) {
            return Arrays.asList(country);
        }
        // 只传集合
        if (CollectionUtils.isNotEmpty(countryList)) {
            return countryList
                    .stream()
                    .filter(item -> countryAuthList.contains(item))
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    public PermissionCountryDeptVO getPermissionCountryDeptVO() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        PermissionCountryDeptVO permissionCountryDeptVO = new PermissionCountryDeptVO();
        Set<String> allCountryList = new HashSet<>();

        // 部门
        List<Long> organizationIds = userContext.getOrganizationIds();
        if (CollectionUtils.isNotEmpty(organizationIds)) {
            permissionCountryDeptVO
                    .setHasDeptPermission(Boolean.TRUE)
                    .setDeptIdList(organizationIds);
            //部门地理国
            Set<String> countrySet = deptService.listByDeptIds(organizationIds)
                    .stream().map(AttendanceDept::getCountry).collect(Collectors.toSet());
            allCountryList.addAll(countrySet);
        } else {
            permissionCountryDeptVO
                    .setHasDeptPermission(Boolean.FALSE)
                    .setDeptIdList(new ArrayList<>());
        }

        // 国家 用户常驻国+部门地理国
        List<String> countryList = userContext.getCountryList();
        if (CollectionUtils.isNotEmpty(countryList)) {
            allCountryList.addAll(countryList);
            permissionCountryDeptVO
                    .setHasCountryPermission(Boolean.TRUE)
                    .setCountryList(new ArrayList<>(allCountryList));
        } else {
            if (CollectionUtils.isNotEmpty(allCountryList)) {
                permissionCountryDeptVO
                        .setHasCountryPermission(Boolean.TRUE)
                        .setCountryList(new ArrayList<>(allCountryList));
            } else {
                permissionCountryDeptVO
                        .setHasCountryPermission(Boolean.FALSE)
                        .setCountryList(new ArrayList<>());
            }
        }

        // 部门国家权限（拷贝HRMS原有恶心的权限逻辑）代码不做更改
        if (CollectionUtils.isNotEmpty(permissionCountryDeptVO.getDeptIdList()) &&
                CollectionUtils.isNotEmpty(permissionCountryDeptVO.getCountryList())) {
            permissionCountryDeptVO
                    .setHasAndDeptAndCountryPermission(Boolean.TRUE)
                    .setHasOrDeptAndCountryPermission(Boolean.TRUE);
        } else {
            if (CollectionUtils.isNotEmpty(permissionCountryDeptVO.getDeptIdList()) ||
                    CollectionUtils.isNotEmpty(permissionCountryDeptVO.getCountryList())) {
                permissionCountryDeptVO
                        .setHasOrDeptAndCountryPermission(Boolean.TRUE);
            } else {
                permissionCountryDeptVO
                        .setHasOrDeptAndCountryPermission(Boolean.FALSE);
            }
            permissionCountryDeptVO
                    .setHasAndDeptAndCountryPermission(Boolean.FALSE);
        }

        return permissionCountryDeptVO;
    }

}
