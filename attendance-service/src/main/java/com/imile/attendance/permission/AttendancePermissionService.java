package com.imile.attendance.permission;

import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.permission.dto.AttendanceDeptTreeDTO;
import com.imile.attendance.permission.query.DeptQuery;
import com.imile.attendance.permission.vo.AttendanceDeptVO;

import java.util.List;
import java.util.function.Function;

/**
 * 考勤公共权限查询
 *
 * <AUTHOR>
 * @date 2025/4/21
 */
public interface AttendancePermissionService {


    /**
     * 获取登录用户有权限国家范围（含部门地理国）
     *
     * @return
     */
    List<String> getUserCountryPermission();


    /**
     * 获取登录用户有权限国家范围（仅有常驻国）
     *
     * @return
     */
    List<String> getUserLocationCountryPermission();

    /**
     * 获取登录用户有权限部门范围
     *
     * @return
     */
    List<Long> getUserDeptPermission();

    /**
     * 获取登录用户有权限国家范围（同国家取交集）
     *
     * @return
     */
    List<String> getUserCountryPermissionByParam(List<String> countryListParam);

    /**
     * 通过地理国获取登录用户有权限部门范围
     *
     * @param country
     * @return
     */
    List<AttendanceDeptVO> getUserDeptPermissionByCountry(String country);

    /**
     * 得到组织树
     *
     * @param userId    用户id
     * @param deptQuery 查询参数
     * @return 组织树
     */
    List<AttendanceDeptTreeDTO> getDeptTree(Long userId, DeptQuery deptQuery);

    /**
     * 查询规则权限
     * 1、获取当前账号部门权限以及常驻国权限
     * 2、拥有的部门权限和拥有的常驻国权限取并集 +  选择的查询条件（dept=选择的部门 + country=选择的国家）取交集
     * @param deptIds
     * @param countryList
     * @return
     */
     PermissionCountryDeptVO getPermissionCountryDeptVO(List<Long> deptIds, List<String> countryList);


    /**
     * 过滤列表常驻国权限
     * @param country
     * @param countryList
     */
     List<String> filterUserCountryAuth(String country, List<String> countryList);

}
