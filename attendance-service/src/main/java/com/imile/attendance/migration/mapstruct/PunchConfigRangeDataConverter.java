package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigRangeMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 打卡配置范围DO转换器
 * 实现PunchConfigRangeDO和PunchConfigRangeMigrateDO之间的相互转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchConfigRangeDataConverter implements DataConverter<PunchConfigRangeMigrateDO, PunchConfigRangeDO> {

    @Override
    public PunchConfigRangeDO convertFromMigrate(PunchConfigRangeMigrateDO migrateDO) {
        if (migrateDO == null) {
            return null;
        }
        
        log.debug("转换打卡配置范围: migrate -> real, rangeId: {}", migrateDO.getId());
        return PunchConfigRangeDOMapstruct.INSTANCE.mapToReal(migrateDO);
    }

    @Override
    public PunchConfigRangeMigrateDO convertFromReal(PunchConfigRangeDO realDO) {
        if (realDO == null) {
            return null;
        }
        
        log.debug("转换打卡配置范围: real -> migrate, rangeId: {}", realDO.getId());
        return PunchConfigRangeDOMapstruct.INSTANCE.mapToMigrate(realDO);
    }

    @Override
    public Class<PunchConfigRangeMigrateDO> getMigrateType() {
        return PunchConfigRangeMigrateDO.class;
    }

    @Override
    public Class<PunchConfigRangeDO> getRealType() {
        return PunchConfigRangeDO.class;
    }
}
