package com.imile.attendance.migration.adapter;

import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchClassConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchClassConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 班次配置适配器
 * 支持在原始表和migrate表之间透明切换的班次配置DAO操作
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchClassConfigAdapter extends AbstractPairAdapter<PunchClassConfigMigrateDO, PunchClassConfigDO> {

    @Resource
    private PunchClassConfigMigrateDao punchClassConfigMigrateDao;
    @Resource
    private PunchClassConfigMigrateMapper punchClassConfigMigrateMapper;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private PunchClassConfigMapper punchClassConfigMapper;

    public PunchClassConfigAdapter(List<DataConverter<PunchClassConfigMigrateDO, PunchClassConfigDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个班次配置
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param punchClassConfigDO 班次配置DO对象
     */
    public void saveOne(PunchClassConfigDO punchClassConfigDO) {
        log.info("保存班次配置, configId: {}, className: {}, 当前使用{}表", 
                punchClassConfigDO.getId(), punchClassConfigDO.getClassName(),
                isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateOneWrapper(
                punchClassConfigDO,
                migrateDO -> {
                    // 保存到migrate表
                    punchClassConfigMigrateDao.save(migrateDO);
                    log.debug("班次配置已保存到migrate表, configId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    punchClassConfigDao.save(realDO);
                    log.debug("班次配置已保存到原始表, configId: {}", realDO.getId());
                }
        );
    }

    /**
     * 批量保存班次配置
     * 
     * @param punchClassConfigDOList 班次配置DO列表
     */
    public void saveBatch(List<PunchClassConfigDO> punchClassConfigDOList) {
        if (punchClassConfigDOList == null || punchClassConfigDOList.isEmpty()) {
            log.warn("批量保存班次配置: 输入列表为空");
            return;
        }
        
        log.info("批量保存班次配置, 数量: {}, 当前使用{}表", 
                punchClassConfigDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateBatchWrapper(
                punchClassConfigDOList,
                migrateDOList -> {
                    // 批量保存到migrate表
                    punchClassConfigMigrateMapper.insertBatchSomeColumn(migrateDOList);
                    log.debug("批量保存班次配置到migrate表完成, 数量: {}", migrateDOList.size());
                },
                realDOList -> {
                    // 批量保存到原始表
                    punchClassConfigMapper.insertBatchSomeColumn(realDOList);
                    log.debug("批量保存班次配置到原始表完成, 数量: {}", realDOList.size());
                }
        );
    }

    /**
     * 根据ID查询班次配置
     *
     * @param id 配置ID
     * @return 班次配置（转换为原始DO类型）
     */
    public PunchClassConfigDO getById(Long id) {
        log.debug("根据ID查询班次配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");
        return super.readWrapper(
                () -> punchClassConfigMigrateDao.getById(id),
                () -> punchClassConfigDao.getById(id)
        );
    }

    /**
     * 根据ID更新班次配置
     *
     * @param punchClassConfigDO 班次配置DO对象
     * @return 更新是否成功
     */
    public boolean updateById(PunchClassConfigDO punchClassConfigDO) {
        log.info("根据ID更新班次配置, configId: {}, 当前使用{}表",
                punchClassConfigDO.getId(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并更新migrate表
                    PunchClassConfigMigrateDO migrateDO = getConverter().convertFromReal(punchClassConfigDO);
                    boolean result = punchClassConfigMigrateDao.updateById(migrateDO);
                    log.debug("班次配置更新到migrate表结果: {}, configId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 更新原始表
                    boolean result = punchClassConfigDao.updateById(punchClassConfigDO);
                    log.debug("班次配置更新到原始表结果: {}, configId: {}", result, punchClassConfigDO.getId());
                    return result;
                }
        );
    }

    /**
     * 根据ID物理删除班次配置
     *
     * @param id 配置ID
     * @return 删除是否成功
     */
    public boolean removeById(Long id) {
        log.info("物理删除班次配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 物理删除migrate表记录
                    boolean result = punchClassConfigMigrateDao.removeById(id);
                    log.info("班次配置从migrate表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                },
                () -> {
                    // 物理删除原始表记录
                    boolean result = punchClassConfigDao.removeById(id);
                    log.info("班次配置从原始表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                }
        );
    }
}
