package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 新考勤规则范围创建服务接口
 */
public interface RuleRangeCreationService {

    /**
     * 创建打卡规则范围
     *
     * @param ruleConfigId 规则配置ID
     * @param ruleConfigNo 规则配置编码
     * @param hrRanges HR范围列表
     * @return 打卡规则范围列表
     */
    List<PunchConfigRangeDO> createPunchConfigRanges(Long ruleConfigId, String ruleConfigNo,
                                                     HrAttendanceGroupDTO group,
                                                     List<HrAttendanceGroupRangeDTO> hrRanges);

    /**
     * 创建班次配置规则范围
     *
     * @param ruleConfigId 规则配置ID
     * @param ruleConfigNo 规则配置编码
     * @param group 考勤组信息
     * @param hrRanges HR范围列表
     * @return 班次配置规则范围列表
     */
    List<PunchClassConfigRangeDO> createPunchClassConfigRanges(Long ruleConfigId, String ruleConfigNo,
                                                               HrAttendanceGroupDTO group,
                                                               List<HrAttendanceGroupRangeDTO> hrRanges);

    /**
     * 创建单个打卡规则范围
     *
     * @param ruleConfigId 规则配置ID
     * @param ruleConfigNo 规则配置编码
     * @param hrRange HR范围
     * @return 打卡规则范围
     */
    PunchConfigRangeDO buildSinglePunchConfigRange(Long ruleConfigId,
                                                   String ruleConfigNo,
                                                   HrAttendanceGroupDTO group,
                                                   HrAttendanceGroupRangeDTO hrRange);

    /**
     * 创建补卡规则范围（虚拟迁移）
     *
     * @param reissueCardConfig 规则配置
     * @param country 国家代码
     * @param timeZone 时区
     * @return 补卡规则范围列表
     */
    List<ReissueCardConfigRangeDO> createReissueCardConfigRanges(ReissueCardConfigDO reissueCardConfig,
                                                                 String country, String timeZone);

    /**
     * 创建考勤组级别的补卡规则范围（考勤组迁移）
     *
     * @param reissueCardConfig 补卡规则配置
     * @param attendanceGroup 考勤组信息
     * @param hrRanges HR范围列表
     * @return 补卡规则范围列表
     */
    List<ReissueCardConfigRangeDO> createAttendanceGroupReissueCardConfigRanges(ReissueCardConfigDO reissueCardConfig,
                                                                                HrAttendanceGroupDTO attendanceGroup,
                                                                                List<HrAttendanceGroupRangeDTO> hrRanges);
}
