package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则范围映射服务接口
 */
public interface MappingRuleConfigRangeService {

    /**
     * 保存规则范围映射
     *
     * @param mappingRuleConfigRange 规则范围映射
     * @return 是否成功
     */
    boolean save(MappingRuleConfigRangeDO mappingRuleConfigRange);

    /**
     * 批量保存规则范围映射
     *
     * @param mappingRuleConfigRanges 规则范围映射列表
     * @return 是否成功
     */
    boolean batchSave(List<MappingRuleConfigRangeDO> mappingRuleConfigRanges);

    /**
     * 创建HR考勤组范围到新考勤规则范围的映射记录
     *
     * @param groupDTO HR考勤组DTO
     * @param ruleConfigRangeId 规则范围id
     * @param ruleConfigId 规则id
     * @param ruleBizId 规则范围人员id
     * @return 映射配置
     */
    MappingRuleConfigRangeDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                          HrAttendanceGroupRangeDTO rangeDTO,
                                          Long ruleConfigRangeId,
                                          Long ruleConfigId,
                                          Long ruleBizId);
}
