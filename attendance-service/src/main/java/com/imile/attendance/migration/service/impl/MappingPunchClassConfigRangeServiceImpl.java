package com.imile.attendance.migration.service.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingPunchClassConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigRangeDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import com.imile.attendance.migration.service.MappingPunchClassConfigRangeService;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 考勤班次范围映射服务实现类
 */
@Slf4j
@Service
public class MappingPunchClassConfigRangeServiceImpl implements MappingPunchClassConfigRangeService {

    @Resource
    private MappingPunchClassConfigRangeDao mappingPunchClassConfigRangeDao;
    @Resource
    private MappingPunchClassConfigRangeMapper mappingPunchClassConfigRangeMapper;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Override
    public boolean save(MappingPunchClassConfigRangeDO mappingPunchClassConfigRange) {
        return mappingPunchClassConfigRangeDao.save(mappingPunchClassConfigRange);
    }

    @Override
    public boolean batchSave(List<MappingPunchClassConfigRangeDO> mappingPunchClassConfigRanges) {
        List<List<MappingPunchClassConfigRangeDO>> partitions = Lists.partition(mappingPunchClassConfigRanges, BusinessConstant.MAX_BATCH_SIZE);
        for (List<MappingPunchClassConfigRangeDO> partition : partitions) {
            mappingPunchClassConfigRangeMapper.insertBatchSomeColumn(partition);
        }
        return true;
    }

    @Override
    public MappingPunchClassConfigRangeDO getByHrPunchConfigRangeId(Long hrPunchConfigRangeId) {
        return mappingPunchClassConfigRangeDao.getByHrPunchConfigRangeId(hrPunchConfigRangeId);
    }

    @Override
    public List<MappingPunchClassConfigRangeDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        return mappingPunchClassConfigRangeDao.listByHrPunchConfigId(hrPunchConfigId);
    }

    @Override
    public MappingPunchClassConfigRangeDO getByPunchClassConfigRangeId(Long punchClassConfigRangeId) {
        return mappingPunchClassConfigRangeDao.getByPunchClassConfigRangeId(punchClassConfigRangeId);
    }

    @Override
    public MappingPunchClassConfigRangeDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                                       HrAttendanceGroupRangeDTO rangeDTO,
                                                       Long punchClassConfigRangeId,
                                                       Long punchClassConfigId,
                                                       Long ruleBizId) {
        log.debug("构建班次范围映射, hrGroupId: {}, hrRangeId: {}, punchClassConfigRangeId: {}",
                groupDTO.getId(), rangeDTO.getId(), punchClassConfigRangeId);

        MappingPunchClassConfigRangeDO mappingDO = new MappingPunchClassConfigRangeDO();
        mappingDO.setId(defaultIdWorker.nextId());
        
        // 设置HR相关信息
        mappingDO.setHrPunchConfigRangeId(rangeDTO.getId());
        mappingDO.setHrPunchConfigId(groupDTO.getId());
        mappingDO.setHrRangeType(rangeDTO.getRangeType());
        mappingDO.setHrIsNeedPunch(rangeDTO.getIsNeedPunch());
        mappingDO.setHrEffectTime(rangeDTO.getEffectTime());
        mappingDO.setHrExpireTime(rangeDTO.getExpireTime());
        mappingDO.setHrIsLatest(rangeDTO.getIsLatest());
        mappingDO.setHrRangeStatus(groupDTO.getStatus());
        
        // 设置新规则相关信息
        mappingDO.setPunchClassConfigRangeId(punchClassConfigRangeId);
        mappingDO.setPunchClassConfigId(punchClassConfigId);
        mappingDO.setRuleBizId(ruleBizId);

        // 设置基础字段
        BaseDOUtil.fillDOInsertByUsrOrSystem(mappingDO);
        
        return mappingDO;
    }

    @Override
    public boolean existsMapping(Long hrPunchConfigRangeId) {
        if (hrPunchConfigRangeId == null) {
            return false;
        }
        MappingPunchClassConfigRangeDO mapping = getByHrPunchConfigRangeId(hrPunchConfigRangeId);
        return mapping != null;
    }
}
