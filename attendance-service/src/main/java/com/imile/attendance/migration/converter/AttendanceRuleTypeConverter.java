package com.imile.attendance.migration.converter;

import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.migration.constants.HrPunchConfigTypeEnum;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则类型转换器
 */
@Component
public class AttendanceRuleTypeConverter {

    /**
     * HR考勤规则类型转换为新考勤规则类型
     *
     * HR系统类型：
     * - 自由上下班 -> 灵活打卡一次/两次
     * - 固定上下班 -> 固定班次打卡
     * - 按班次上班 -> 固定班次打卡/多班次打卡
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 新考勤规则类型
     */
    public String convertPunchConfigType(String hrPunchConfigType) {
        if (hrPunchConfigType == null) {
            return PunchConfigTypeEnum.FIXED_WORK.getCode();
        }
        HrPunchConfigTypeEnum hrPunchConfigTypeEnum = HrPunchConfigTypeEnum.getInstance(hrPunchConfigType);
        switch (hrPunchConfigTypeEnum) {
            case FIXED_WORK:
            case CLASS_WORK:
                return PunchConfigTypeEnum.FIXED_WORK.getCode();
            case FIXED_WORK_ONCE:
                return PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode();
            case FREE_WORK:
                return PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode();
            default:
                break;
        }
        return null;
    }

    /**
     * 判断是否为免打卡规则
     *
     * @param isNeedPunch 是否需要打卡
     * @return 是否为免打卡
     */
    public boolean isNoPunchRequired(Integer isNeedPunch) {
        return isNeedPunch != null && isNeedPunch.equals(0);
    }

    /**
     * 获取免打卡规则类型
     *
     * @return 免打卡规则类型
     */
    public String getNoPunchConfigType() {
        return PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode();
    }

    /**
     * 判断HR规则类型是否为灵活类型
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 是否为灵活类型
     */
    public boolean isFlexibleType(String hrPunchConfigType) {
        return isFlexibleWorkOnce(hrPunchConfigType) || isFlexibleWorkTwice(hrPunchConfigType);
    }

    /**
     * 判断HR规则类型是否为灵活打卡一次
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 是否为灵活打卡一次
     */
    public boolean isFlexibleWorkOnce(String hrPunchConfigType) {
        return HrPunchConfigTypeEnum.FIXED_WORK_ONCE.getCode().equals(hrPunchConfigType);
    }

    /**
     * 判断HR规则类型是否为灵活打卡二次
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 是否为灵活打卡二次
     */
    public boolean isFlexibleWorkTwice(String hrPunchConfigType) {
        return HrPunchConfigTypeEnum.FREE_WORK.getCode().equals(hrPunchConfigType);
    }

    /**
     * 判断HR规则类型是否为固定类型
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 是否为固定类型
     */
    public boolean isFixedType(String hrPunchConfigType) {
        return HrPunchConfigTypeEnum.FIXED_WORK.getCode().equals(hrPunchConfigType);
    }

    /**
     * 判断HR规则类型是否为班次类型
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 是否为班次类型
     */
    public boolean isClassType(String hrPunchConfigType) {
        return HrPunchConfigTypeEnum.CLASS_WORK.getCode().equals(hrPunchConfigType);
    }

//    /**
//     * 判断是否需要创建班次规则
//     *
//     * @param hrPunchConfigType HR考勤规则类型
//     * @return 是否需要创建班次规则
//     */
//    public boolean needCreateClassConfig(String hrPunchConfigType) {
//        // 固定上下班、按班次上班、自由上下班（灵活一次）需要创建班次
//        // 自由上下班（灵活两次）不需要创建班次
//        return isFixedType(hrPunchConfigType) || isClassType(hrPunchConfigType) || isFlexibleWorkOnce(hrPunchConfigType);
//    }

    /**
     * 将HR考勤规则类型转换为班次性质
     *
     * 转换规则：
     * - 固定上下班 -> 固定班次 (FIXED_CLASS)
     * - 自由上下班（灵活一次） -> 固定班次 (FIXED_CLASS)
     * - 自由上下班（灵活两次） -> 固定班次 (FIXED_CLASS)
     * - 按班次上班 -> 多班次 (MULTIPLE_CLASS)
     *
     * @param hrPunchConfigType HR考勤规则类型
     * @return 班次性质
     */
    public String convertToClassNature(String hrPunchConfigType) {
        if (hrPunchConfigType == null) {
            return ClassNatureEnum.FIXED_CLASS.getCode();
        }

        HrPunchConfigTypeEnum hrPunchConfigTypeEnum = HrPunchConfigTypeEnum.getInstance(hrPunchConfigType);
        if (hrPunchConfigTypeEnum == null) {
            return ClassNatureEnum.FIXED_CLASS.getCode();
        }

        if (hrPunchConfigTypeEnum == HrPunchConfigTypeEnum.CLASS_WORK) {// 按班次上班对应多班次
            return ClassNatureEnum.MULTIPLE_CLASS.getCode();
        }
        return ClassNatureEnum.FIXED_CLASS.getCode();
    }

    /**
     * 将HR范围类型转换为新规则范围类型
     *
     * @param hrRangeType HR范围类型
     * @return 新规则范围类型
     */
    public static String convertHrRangeTypeToRuleRangeType(String hrRangeType) {
        if (hrRangeType == null) {
            return null;
        }
        return RangeTypeEnum.isDefault(hrRangeType) ?
                RuleRangeTypeEnum.USER.getCode() : hrRangeType;
    }
}
