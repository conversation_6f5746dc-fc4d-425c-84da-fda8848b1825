package com.imile.attendance.migration.adapter.base;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
public interface DataConverter<MIGRATE, REAL> {

    /**
     * 将新系统对象转换为旧系统对象
     */
    REAL convertFromMigrate(MIGRATE migrateObj);

    /**
     * 将旧系统对象转换为新系统对象
     */
    MIGRATE convertFromReal(REAL realObj);

    /**
     * 获取新系统对象的类型
     */
    Class<MIGRATE> getMigrateType();

    /**
     * 获取旧系统对象的类型
     */
    Class<REAL> getRealType();
}
