package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 班次配置DO转换器
 * 实现PunchClassConfigDO和PunchClassConfigMigrateDO之间的相互转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchClassConfigDataConverter implements DataConverter<PunchClassConfigMigrateDO, PunchClassConfigDO> {

    @Override
    public PunchClassConfigDO convertFromMigrate(PunchClassConfigMigrateDO migrateDO) {
        if (migrateDO == null) {
            return null;
        }
        
        log.debug("转换班次配置: migrate -> real, configId: {}", migrateDO.getId());
        return PunchClassConfigDOMapstruct.INSTANCE.mapToReal(migrateDO);
    }

    @Override
    public PunchClassConfigMigrateDO convertFromReal(PunchClassConfigDO realDO) {
        if (realDO == null) {
            return null;
        }
        
        log.debug("转换班次配置: real -> migrate, configId: {}", realDO.getId());
        return PunchClassConfigDOMapstruct.INSTANCE.mapToMigrate(realDO);
    }

    @Override
    public Class<PunchClassConfigMigrateDO> getMigrateType() {
        return PunchClassConfigMigrateDO.class;
    }

    @Override
    public Class<PunchClassConfigDO> getRealType() {
        return PunchClassConfigDO.class;
    }
}
