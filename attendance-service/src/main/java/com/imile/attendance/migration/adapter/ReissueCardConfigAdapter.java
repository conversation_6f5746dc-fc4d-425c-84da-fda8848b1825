package com.imile.attendance.migration.adapter;

import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.ReissueCardConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.ReissueCardConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.ReissueCardConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 补卡配置适配器
 * 支持在原始表和migrate表之间透明切换的补卡配置DAO操作
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class ReissueCardConfigAdapter extends AbstractPairAdapter<ReissueCardConfigMigrateDO, ReissueCardConfigDO> {

    @Resource
    private ReissueCardConfigMigrateDao reissueCardConfigMigrateDao;
    @Resource
    private ReissueCardConfigMigrateMapper reissueCardConfigMigrateMapper;
    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigMapper reissueCardConfigMapper;

    public ReissueCardConfigAdapter(List<DataConverter<ReissueCardConfigMigrateDO, ReissueCardConfigDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个补卡配置
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param reissueCardConfigDO 补卡配置DO对象
     */
    public void saveOne(ReissueCardConfigDO reissueCardConfigDO) {
        log.info("保存补卡配置, configId: {}, configName: {}, 当前使用{}表", 
                reissueCardConfigDO.getId(), reissueCardConfigDO.getConfigName(),
                isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateOneWrapper(
                reissueCardConfigDO,
                migrateDO -> {
                    // 保存到migrate表
                    reissueCardConfigMigrateDao.save(migrateDO);
                    log.debug("补卡配置已保存到migrate表, configId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    reissueCardConfigDao.save(realDO);
                    log.debug("补卡配置已保存到原始表, configId: {}", realDO.getId());
                }
        );
    }

    /**
     * 保存单个补卡配置并返回结果
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param reissueCardConfigDO 补卡配置DO对象
     * @return 保存结果
     */
    public boolean saveOneWithResult(ReissueCardConfigDO reissueCardConfigDO) {
        log.info("保存补卡配置(带返回结果), configId: {}, configName: {}, 当前使用{}表",
                reissueCardConfigDO.getId(), reissueCardConfigDO.getConfigName(),
                isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并保存到migrate表
                    ReissueCardConfigMigrateDO migrateDO = getConverter().convertFromReal(reissueCardConfigDO);
                    boolean result = reissueCardConfigMigrateDao.save(migrateDO);
                    log.debug("补卡配置保存到migrate表结果: {}, configId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 保存到原始表
                    boolean result = reissueCardConfigDao.save(reissueCardConfigDO);
                    log.debug("补卡配置保存到原始表结果: {}, configId: {}", result, reissueCardConfigDO.getId());
                    return result;
                }
        );
    }

    /**
     * 批量保存补卡配置
     * 
     * @param reissueCardConfigDOList 补卡配置DO列表
     */
    public void saveBatch(List<ReissueCardConfigDO> reissueCardConfigDOList) {
        if (reissueCardConfigDOList == null || reissueCardConfigDOList.isEmpty()) {
            log.warn("批量保存补卡配置: 输入列表为空");
            return;
        }
        
        log.info("批量保存补卡配置, 数量: {}, 当前使用{}表", 
                reissueCardConfigDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateBatchWrapper(
                reissueCardConfigDOList,
                migrateDOList -> {
                    // 批量保存到migrate表
                    reissueCardConfigMigrateMapper.insertBatchSomeColumn(migrateDOList);
                    log.debug("批量保存补卡配置到migrate表完成, 数量: {}", migrateDOList.size());
                },
                realDOList -> {
                    // 批量保存到原始表
                    reissueCardConfigMapper.insertBatchSomeColumn(realDOList);
                    log.debug("批量保存补卡配置到原始表完成, 数量: {}", realDOList.size());
                }
        );
    }

    /**
     * 批量更新补卡配置
     * 
     * @param reissueCardConfigDOList 补卡配置DO列表
     */
    public void updateBatch(List<ReissueCardConfigDO> reissueCardConfigDOList) {
        if (reissueCardConfigDOList == null || reissueCardConfigDOList.isEmpty()) {
            log.warn("批量更新补卡配置: 输入列表为空");
            return;
        }
        
        log.info("批量更新补卡配置, 数量: {}, 当前使用{}表", 
                reissueCardConfigDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateBatchWrapper(
                reissueCardConfigDOList,
                migrateDOList -> {
                    // 批量更新migrate表
                    reissueCardConfigMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("批量更新补卡配置到migrate表完成, 数量: {}", migrateDOList.size());
                },
                realDOList -> {
                    // 批量更新原始表
                    reissueCardConfigMapper.replaceIntoBatchSomeColumn(realDOList);
                    log.debug("批量更新补卡配置到原始表完成, 数量: {}", realDOList.size());
                }
        );
    }

    /**
     * 根据名称获取补卡配置
     * 
     * @param name 配置名称
     * @return 补卡配置
     */
    public ReissueCardConfigDO getByName(String name) {
        log.debug("根据名称查询补卡配置, name: {}, 当前使用{}表", 
                name, isShouldUseMigrate() ? "migrate" : "原始");
        
        return super.readWrapper(
                () -> reissueCardConfigMigrateDao.getByName(name),
                () -> reissueCardConfigDao.getByName(name)
        );
    }

    /**
     * 根据国家获取补卡配置
     * 
     * @param country 国家代码
     * @return 补卡配置列表
     */
    public List<ReissueCardConfigDO> getByCountry(String country) {
        log.debug("根据国家查询补卡配置, country: {}, 当前使用{}表", 
                country, isShouldUseMigrate() ? "migrate" : "原始");
        
        return super.readBatchWrapper(
                () -> reissueCardConfigMigrateDao.getByCountry(country),
                () -> reissueCardConfigDao.getByCountry(country)
        );
    }

    /**
     * 根据配置编码获取最新补卡配置
     * 
     * @param configNo 配置编码
     * @return 补卡配置
     */
    public ReissueCardConfigDO getLatestByConfigNo(String configNo) {
        log.debug("根据配置编码查询最新补卡配置, configNo: {}, 当前使用{}表", 
                configNo, isShouldUseMigrate() ? "migrate" : "原始");
        
        return super.readWrapper(
                () -> reissueCardConfigMigrateDao.getLatestByConfigNo(configNo),
                () -> reissueCardConfigDao.getLatestByConfigNo(configNo)
        );
    }

    /**
     * 根据配置ID列表查询配置
     *
     * @param configIdList 配置ID列表
     * @return 补卡配置列表
     */
    public List<ReissueCardConfigDO> listByConfigIds(List<Long> configIdList) {
        log.debug("根据配置ID列表查询补卡配置, configIds数量: {}, 当前使用{}表",
                configIdList != null ? configIdList.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> reissueCardConfigMigrateDao.listByConfigIds(configIdList),
                () -> reissueCardConfigDao.listByConfigIds(configIdList)
        );
    }

    /**
     * 根据ID获取补卡配置
     *
     * @param id 配置ID
     * @return 补卡配置
     */
    public ReissueCardConfigDO getById(Long id) {
        log.debug("根据ID查询补卡配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readWrapper(
                () -> reissueCardConfigMigrateDao.getById(id),
                () -> reissueCardConfigDao.getById(id)
        );
    }

    /**
     * 更新补卡配置
     *
     * @param reissueCardConfigDO 补卡配置DO对象
     * @return 更新结果
     */
    public boolean updateById(ReissueCardConfigDO reissueCardConfigDO) {
        log.info("更新补卡配置, configId: {}, configName: {}, 当前使用{}表",
                reissueCardConfigDO.getId(), reissueCardConfigDO.getConfigName(),
                isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并更新migrate表
                    ReissueCardConfigMigrateDO migrateDO = getConverter().convertFromReal(reissueCardConfigDO);
                    boolean result = reissueCardConfigMigrateDao.updateById(migrateDO);
                    log.debug("补卡配置更新到migrate表结果: {}, configId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 更新原始表
                    boolean result = reissueCardConfigDao.updateById(reissueCardConfigDO);
                    log.debug("补卡配置更新到原始表结果: {}, configId: {}", result, reissueCardConfigDO.getId());
                    return result;
                }
        );
    }

    /**
     * 根据ID物理删除补卡配置
     *
     * @param id 补卡配置ID
     * @return 删除是否成功
     */
    public boolean removeById(Long id) {
        log.info("物理删除补卡配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 物理删除migrate表记录
                    boolean result = reissueCardConfigMigrateDao.removeById(id);
                    log.info("补卡配置从migrate表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                },
                () -> {
                    // 物理删除原始表记录
                    boolean result = reissueCardConfigDao.removeById(id);
                    log.info("补卡配置从原始表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                }
        );
    }
}
