package com.imile.attendance.migration.processor;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.migration.converter.AttendanceRuleTypeConverter;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import com.imile.attendance.migration.service.RuleCreationService;
import com.imile.attendance.migration.service.RuleRangeCreationService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤组迁移处理器
 */
@Slf4j
@Component
public class HrPunchConfigMigrationProcessor {

    @Resource
    private AttendanceRuleTypeConverter attendanceRuleTypeConverter;
    @Resource
    private RuleCreationService ruleCreationService;
    @Resource
    private RuleRangeCreationService ruleRangeCreationService;

    /**
     * 处理默认考勤组
     *
     * @param defaultGroup 默认考勤组
     */
    public void processDefaultAttendanceGroup(HrAttendanceGroupDTO defaultGroup) {
        log.info("开始处理默认考勤组, groupId: {}, groupName: {}", defaultGroup.getId(), defaultGroup.getPunchConfigName());
        XxlJobLogger.log("开始处理默认考勤组, groupId: {}, groupName: {}", defaultGroup.getId(), defaultGroup.getPunchConfigName());

        try {
            // 1. 获取关联的适用范围
            List<HrAttendanceGroupRangeDTO> rangeList = defaultGroup.getRangeList();
            if (CollectionUtils.isEmpty(rangeList)) {
                log.warn("默认考勤组没有适用范围, groupId: {}, 不处理", defaultGroup.getId());
                XxlJobLogger.log("默认考勤组没有适用范围, groupId: {}, 不处理", defaultGroup.getId());
                return;
            }

            // 2. 分离免打卡和需要打卡的范围
            List<HrAttendanceGroupRangeDTO> noPunchRanges = rangeList.stream()
                    .filter(HrAttendanceGroupRangeDTO::isNoPunchRequired)
                    .collect(Collectors.toList());

            List<HrAttendanceGroupRangeDTO> punchRanges = rangeList.stream()
                    .filter(HrAttendanceGroupRangeDTO::needPunch)
                    .collect(Collectors.toList());

            log.info("默认考勤组范围统计, groupId: {}, 免打卡范围: {}, 需打卡范围: {}",
                    defaultGroup.getId(), noPunchRanges.size(), punchRanges.size());
            XxlJobLogger.log("默认考勤组范围统计, groupId: {}, 免打卡范围: {}, 需打卡范围: {}",
                    defaultGroup.getId(), noPunchRanges.size(), punchRanges.size());


            // 3. 处理免打卡范围
            if (CollectionUtils.isNotEmpty(noPunchRanges)) {
                processNoPunchRanges(defaultGroup, noPunchRanges);
            }

            // 4. 处理需要打卡的范围
            if (CollectionUtils.isNotEmpty(punchRanges)) {
                processPunchRanges(defaultGroup, punchRanges, true);
            }

            // 5. 创建班次规则，使用默认考勤组下所有的人
            createPunchClassConfigs(defaultGroup, rangeList);

            log.info("默认考勤组处理完成, groupId: {}", defaultGroup.getId());
            XxlJobLogger.log("默认考勤组处理完成, groupId: {}", defaultGroup.getId());

        } catch (Exception e) {
            log.error("处理默认考勤组失败, groupId: {}", defaultGroup.getId(), e);
            XxlJobLogger.log("处理默认考勤组失败, groupId: {}", defaultGroup.getId(), e);
            throw e;
        }
    }

    /**
     * 处理自定义考勤组
     *
     * @param customGroup 自定义考勤组
     */
    public void processCustomAttendanceGroup(HrAttendanceGroupDTO customGroup) {
        log.info("开始处理自定义考勤组, groupId: {}, groupName: {}", customGroup.getId(), customGroup.getPunchConfigName());
        XxlJobLogger.log("开始处理自定义考勤组, groupId: {}, groupName: {}", customGroup.getId(), customGroup.getPunchConfigName());

        try {
            // 1. 获取关联的适用范围
            List<HrAttendanceGroupRangeDTO> rangeList = customGroup.getRangeList();
            if (CollectionUtils.isEmpty(rangeList)) {
                log.warn("自定义考勤组没有适用范围, groupId: {}", customGroup.getId());
                XxlJobLogger.log("自定义考勤组没有适用范围, groupId: {}", customGroup.getId());
                return;
            }

            // 2. 自定义考勤组只处理需要打卡的范围
            List<HrAttendanceGroupRangeDTO> punchRanges = rangeList.stream()
                    .filter(HrAttendanceGroupRangeDTO::needPunch)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(punchRanges)) {
                log.warn("自定义考勤组没有需要打卡的范围, groupId: {}", customGroup.getId());
                XxlJobLogger.log("自定义考勤组没有需要打卡的范围, groupId: {}", customGroup.getId());
                return;
            }

            log.info("自定义考勤组需打卡范围数量: {}, groupName: {}", punchRanges.size(), customGroup.getPunchConfigName());
            XxlJobLogger.log("自定义考勤组需打卡范围数量: {}, groupName: {}", punchRanges.size(), customGroup.getPunchConfigName());

            // 3. 创建打卡规则
            processPunchRanges(customGroup, punchRanges, false);

            // 4. 创建班次
            createPunchClassConfigs(customGroup, punchRanges);

            log.info("自定义考勤组处理完成, groupId: {}", customGroup.getId());
            XxlJobLogger.log("自定义考勤组处理完成, groupId: {}", customGroup.getId());

        } catch (Exception e) {
            log.error("处理自定义考勤组失败, groupId: {}", customGroup.getId(), e);
            XxlJobLogger.log("处理自定义考勤组失败, groupId: {}", customGroup.getId(), e);
            throw e;
        }
    }

    /**
     * 处理免打卡范围, 创建免打卡的规则
     *
     * @param defaultGroup 默认考勤组
     * @param noPunchRanges 免打卡范围列表
     */
    private void processNoPunchRanges(HrAttendanceGroupDTO defaultGroup, List<HrAttendanceGroupRangeDTO> noPunchRanges) {
        log.info("处理免打卡范围, groupName: {}, rangeCount: {}", defaultGroup.getPunchConfigName(), noPunchRanges.size());
        XxlJobLogger.log("处理免打卡范围, groupName: {}, rangeCount: {}", defaultGroup.getPunchConfigName(), noPunchRanges.size());
        try {
            //创建免打卡的规则
            PunchConfigDO noPunchConfig = ruleCreationService.createNoPunchConfig(defaultGroup);
            if (noPunchConfig == null) {
                log.error("创建免打卡规则失败, groupId: {}", defaultGroup.getId());
                XxlJobLogger.log("创建免打卡规则失败, groupId: {}", defaultGroup.getId());
                return;
            }
            // 创建免打卡规则适用范围
            ruleRangeCreationService.createPunchConfigRanges(
                    noPunchConfig.getId(),
                    noPunchConfig.getConfigNo(),
                    defaultGroup,
                    noPunchRanges
            );
        } catch (Exception e) {
            log.error("处理免打卡范围失败, groupId: {}", defaultGroup.getId(), e);
            XxlJobLogger.log("处理免打卡范围失败, groupId: {},{}", defaultGroup.getId(), e.getMessage());
            throw e;
        }
    }

    /**
     * 处理需要打卡的范围
     *
     * @param group 考勤组
     * @param punchRanges 需要打卡的范围列表
     * @param isDefault 是否为默认考勤组
     */
    private void processPunchRanges(HrAttendanceGroupDTO group, List<HrAttendanceGroupRangeDTO> punchRanges, boolean isDefault) {
        log.info("处理考勤组需要创建的打卡规则, groupName: {}, rangeCount: {}, isDefault: {}",
                group.getPunchConfigName(), punchRanges.size(), isDefault);
        XxlJobLogger.log("处理考勤组需要创建的打卡规则, groupName: {}, rangeCount: {}, isDefault: {}",
                group.getPunchConfigName(), punchRanges.size(), isDefault);

        try {
            //创建打卡规则
            createPunchConfig(group, punchRanges);
            log.info("考勤组:{}需要打卡的规则处理完成", group.getPunchConfigName());
            XxlJobLogger.log("考勤组:{}需要打卡的规则处理完成", group.getPunchConfigName());

        } catch (Exception e) {
            log.error("考勤组:{}处理需要打卡的规则失败, ", group.getPunchConfigName(), e);
            XxlJobLogger.log("考勤组:{}处理需要打卡的规则失败,{} ", group.getPunchConfigName(), e.getMessage());
            throw e;
        }
    }

    /**
     * 创建班次规则配置
     *
     * @param hrGroup 考勤组信息
     * @param punchRanges 需要打卡的适用范围列表
     */
    private void createPunchClassConfigs(HrAttendanceGroupDTO hrGroup, List<HrAttendanceGroupRangeDTO> punchRanges) {
        log.info("创建班次规则, groupName: {}, isDefault: {}", hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup());
        XxlJobLogger.log("创建班次规则, groupName: {}, isDefault: {}", hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup());
        // 判断是走固定班次还是多班次
        if (attendanceRuleTypeConverter.isFixedType(hrGroup.getPunchConfigType()) ||
                attendanceRuleTypeConverter.isFlexibleWorkOnce(hrGroup.getPunchConfigType()) ||
                attendanceRuleTypeConverter.isFlexibleWorkTwice(hrGroup.getPunchConfigType())) {
            createFixedClassConfigs(hrGroup, punchRanges);
        } else if (attendanceRuleTypeConverter.isClassType(hrGroup.getPunchConfigType())) {
            createMultipleClassConfigs(hrGroup, punchRanges);
        }
    }

    /**
     * 创建固定班次规则配置
     * 处理固定班次和灵活一次打卡类型的班次规则创建，包括班次配置和适用范围的创建
     *
     * @param hrGroup 考勤组信息
     * @param punchRanges 需要打卡的适用范围列表
     */
    private void createFixedClassConfigs(HrAttendanceGroupDTO hrGroup, List<HrAttendanceGroupRangeDTO> punchRanges) {
        log.info("创建固定班次规则, groupName: {}, isDefault: {}, rangeCount: {}",
                hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup(), punchRanges.size());
        XxlJobLogger.log("创建固定班次规则, groupName: {}, isDefault: {}, rangeCount: {}",
                hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup(), punchRanges.size());

        try {
            // 获取班次配置列表，固定班次和灵活一次打卡类型只有一个班次配置
            List<HrAttendanceClassConfigDTO> classList = hrGroup.getClassList();
            if (CollectionUtils.isEmpty(classList)) {
                log.warn("考勤组班次配置列表为空, groupId: {}, groupName: {}",
                        hrGroup.getId(), hrGroup.getPunchConfigName());
                XxlJobLogger.log("考勤组班次配置列表为空, groupId: {}, groupName: {}",
                        hrGroup.getId(), hrGroup.getPunchConfigName());
                return;
            }

            // 获取第一个班次配置（固定班次只有一个）
            HrAttendanceClassConfigDTO classConfig = classList.get(0);
            log.info("处理固定班次配置, classId: {}, className: {}",
                    classConfig.getId(), classConfig.getClassName());
            XxlJobLogger.log("处理固定班次配置, classId: {}, className: {}",
                    classConfig.getId(), classConfig.getClassName());

            // 创建班次规则配置
            PunchClassConfigDO punchClassConfig = ruleCreationService.createSinglePunchClassConfig(hrGroup, classConfig);
            if (punchClassConfig == null) {
                log.error("创建固定班次规则失败, groupId: {}, classId: {}",
                        hrGroup.getId(), classConfig.getId());
                XxlJobLogger.log("创建固定班次规则失败, groupId: {}, classId: {}",
                        hrGroup.getId(), classConfig.getId());
                return;
            }

            log.info("固定班次规则创建成功, configId: {}, configNo: {}, className: {}",
                    punchClassConfig.getId(), punchClassConfig.getConfigNo(), punchClassConfig.getClassName());
            XxlJobLogger.log("固定班次规则创建成功, configId: {}, configNo: {}, className: {}",
                    punchClassConfig.getId(), punchClassConfig.getConfigNo(), punchClassConfig.getClassName());

            // 创建班次规则适用范围
            ruleRangeCreationService.createPunchClassConfigRanges(
                    punchClassConfig.getId(),
                    punchClassConfig.getConfigNo(),
                    hrGroup,
                    punchRanges
            );

            log.info("固定班次规则适用范围创建完成, configId: {}, rangeCount: {}",
                    punchClassConfig.getId(), punchRanges.size());
            XxlJobLogger.log("固定班次规则适用范围创建完成, configId: {}, rangeCount: {}",
                    punchClassConfig.getId(), punchRanges.size());

        } catch (Exception e) {
            log.error("创建固定班次规则失败, groupId: {}, groupName: {}",
                    hrGroup.getId(), hrGroup.getPunchConfigName(), e);
            XxlJobLogger.log("创建固定班次规则失败, groupId: {}, groupName: {}",
                    hrGroup.getId(), hrGroup.getPunchConfigName(), e);
            throw e;
        }
    }

    /**
     * 创建多班次规则配置
     * 处理多班次类型的班次规则创建，为每个HR班次配置创建对应的班次规则和适用范围
     *
     * @param hrGroup 考勤组信息
     * @param punchRanges 需要打卡的适用范围列表
     */
    private void createMultipleClassConfigs(HrAttendanceGroupDTO hrGroup, List<HrAttendanceGroupRangeDTO> punchRanges) {
        log.info("创建多班次规则, groupName: {}, isDefault: {}, rangeCount: {}",
                hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup(), punchRanges.size());
        XxlJobLogger.log("创建多班次规则, groupName: {}, isDefault: {}, rangeCount: {}",
                hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup(), punchRanges.size());

        try {
            // 获取班次配置列表，多班次类型有多个班次配置
            List<HrAttendanceClassConfigDTO> classList = hrGroup.getClassList();
            if (CollectionUtils.isEmpty(classList)) {
                log.warn("考勤组班次配置列表为空, groupId: {}, groupName: {}",
                        hrGroup.getId(), hrGroup.getPunchConfigName());
                XxlJobLogger.log("考勤组班次配置列表为空, groupId: {}, groupName: {}",
                        hrGroup.getId(), hrGroup.getPunchConfigName());
                return;
            }

            log.info("开始创建多班次规则, groupId: {}, 班次数量: {}", hrGroup.getId(), classList.size());
            XxlJobLogger.log("开始创建多班次规则, groupId: {}, 班次数量: {}", hrGroup.getId(), classList.size());

            // 遍历每个班次配置，为每个班次创建对应的班次规则
            for (HrAttendanceClassConfigDTO classConfig : classList) {
                createSingleClassConfigForMultiple(hrGroup, classConfig, punchRanges);
            }

            log.info("多班次规则创建完成, groupId: {}, 成功创建班次数量: {}",
                    hrGroup.getId(), classList.size());
            XxlJobLogger.log("多班次规则创建完成, groupId: {}, 成功创建班次数量: {}",
                    hrGroup.getId(), classList.size());

        } catch (Exception e) {
            log.error("创建多班次规则失败, groupId: {}, groupName: {}",
                    hrGroup.getId(), hrGroup.getPunchConfigName(), e);
            XxlJobLogger.log("创建多班次规则失败, groupId: {}, groupName: {}",
                    hrGroup.getId(), hrGroup.getPunchConfigName(), e.getMessage());
            throw e;
        }
    }

    /**
     * 为多班次类型创建单个班次配置
     * 每个HR班次配置都会创建一个独立的班次规则和适用范围
     *
     * @param hrGroup 考勤组信息
     * @param classConfig HR班次配置
     * @param punchRanges 需要打卡的适用范围列表
     */
    private void createSingleClassConfigForMultiple(HrAttendanceGroupDTO hrGroup,
                                                    HrAttendanceClassConfigDTO classConfig,
                                                    List<HrAttendanceGroupRangeDTO> punchRanges) {
        log.info("创建多班次中的单个班次配置, groupId: {}, classId: {}, className: {}",
                hrGroup.getId(), classConfig.getId(), classConfig.getClassName());
        XxlJobLogger.log("创建多班次中的单个班次配置, groupId: {}, classId: {}, className: {}",
                hrGroup.getId(), classConfig.getId(), classConfig.getClassName());

        try {
            // 创建班次规则配置
            PunchClassConfigDO punchClassConfig = ruleCreationService.createSinglePunchClassConfig(hrGroup, classConfig);
            if (punchClassConfig == null) {
                log.error("创建多班次中的班次规则失败, groupId: {}, classId: {}",
                        hrGroup.getId(), classConfig.getId());
                XxlJobLogger.log("创建多班次中的班次规则失败, groupId: {}, classId: {}",
                        hrGroup.getId(), classConfig.getId());
                return;
            }

            log.info("多班次中的班次规则创建成功, configId: {}, configNo: {}, className: {}",
                    punchClassConfig.getId(), punchClassConfig.getConfigNo(), punchClassConfig.getClassName());
            XxlJobLogger.log("多班次中的班次规则创建成功, configId: {}, configNo: {}, className: {}",
                    punchClassConfig.getId(), punchClassConfig.getConfigNo(), punchClassConfig.getClassName());

            // 为每个班次创建独立的适用范围
            ruleRangeCreationService.createPunchClassConfigRanges(
                    punchClassConfig.getId(),
                    punchClassConfig.getConfigNo(),
                    hrGroup,
                    punchRanges
            );

            log.info("多班次中的班次规则适用范围创建完成, configId: {}, className: {}, rangeCount: {}",
                    punchClassConfig.getId(), punchClassConfig.getClassName(), punchRanges.size());
            XxlJobLogger.log("多班次中的班次规则适用范围创建完成, configId: {}, className: {}, rangeCount: {}",
                    punchClassConfig.getId(), punchClassConfig.getClassName(), punchRanges.size());

        } catch (Exception e) {
            log.error("创建多班次中的单个班次配置失败, groupId: {}, classId: {}, className: {}",
                    hrGroup.getId(), classConfig.getId(), classConfig.getClassName(), e);
            XxlJobLogger.log("创建多班次中的单个班次配置失败, groupId: {}, classId: {}, className: {}",
                    hrGroup.getId(), classConfig.getId(), classConfig.getClassName(), e.getMessage());
            throw e;
        }
    }

    /**
     * 创建或合并打卡规则
     */
    private void createPunchConfig(HrAttendanceGroupDTO hrGroup,
                                   List<HrAttendanceGroupRangeDTO> punchRanges) {
        log.info("创建打卡规则, groupName: {}, isDefault: {}", hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup());
        XxlJobLogger.log("创建打卡规则, groupName: {}, isDefault: {}", hrGroup.getPunchConfigName(), hrGroup.isDefaultGroup());

        //创建规则
        PunchConfigDO newPunchConfig = ruleCreationService.createPunchConfig(hrGroup, hrGroup.isDefaultGroup());

        //创建范围
        ruleRangeCreationService.createPunchConfigRanges(
                newPunchConfig.getId(),
                newPunchConfig.getConfigNo(),
                hrGroup,
                punchRanges
        );
    }
}
