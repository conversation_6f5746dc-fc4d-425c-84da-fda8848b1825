package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassItemConfigMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 班次时间配置DO映射器
 * 负责PunchClassItemConfigDO和PunchClassItemConfigMigrateDO之间的字段映射
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Mapper
public interface PunchClassItemConfigDOMapstruct extends BaseMapstruct {

    PunchClassItemConfigDOMapstruct INSTANCE = Mappers.getMapper(PunchClassItemConfigDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     */
    default Long setModelId(PunchClassItemConfigMigrateDO module) {
        return setModelId(module,
                PunchClassItemConfigMigrateDO::getId,
                PunchClassItemConfigMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     */
    default Long setModelId(PunchClassItemConfigDO module) {
        return setModelId(module,
                PunchClassItemConfigDO::getId,
                PunchClassItemConfigDO::setId);
    }

    /**
     * 将migrate表DO转换为原始表DO
     * 
     * @param punchClassItemConfigMigrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchClassItemConfigMigrateDO))")
    PunchClassItemConfigDO mapToReal(PunchClassItemConfigMigrateDO punchClassItemConfigMigrateDO);

    /**
     * 批量将migrate表DO转换为原始表DO
     * 
     * @param punchClassItemConfigMigrateDOList migrate表DO列表
     * @return 原始表DO列表
     */
    List<PunchClassItemConfigDO> mapToReal(List<PunchClassItemConfigMigrateDO> punchClassItemConfigMigrateDOList);

    /**
     * 将原始表DO转换为migrate表DO
     * 
     * @param punchClassItemConfigDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchClassItemConfigDO))")
    PunchClassItemConfigMigrateDO mapToMigrate(PunchClassItemConfigDO punchClassItemConfigDO);

    /**
     * 批量将原始表DO转换为migrate表DO
     * 
     * @param punchClassItemConfigDOList 原始表DO列表
     * @return migrate表DO列表
     */
    List<PunchClassItemConfigMigrateDO> mapToMigrate(List<PunchClassItemConfigDO> punchClassItemConfigDOList);
}
