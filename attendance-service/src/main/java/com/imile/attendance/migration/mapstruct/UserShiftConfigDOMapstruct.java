package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.migrate.UserShiftConfigMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 员工排班配置DO映射器
 * 负责UserShiftConfigDO和UserShiftConfigMigrateDO之间的字段映射转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Mapper
public interface UserShiftConfigDOMapstruct extends BaseMapstruct {

    UserShiftConfigDOMapstruct INSTANCE = Mappers.getMapper(UserShiftConfigDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     * 为migrate表的DO对象生成新的ID
     * 
     * @param module migrate表DO对象
     * @return 生成的ID
     */
    default Long setModelId(UserShiftConfigMigrateDO module) {
        return setModelId(module,
                UserShiftConfigMigrateDO::getId,
                UserShiftConfigMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     * 为原始表的DO对象生成新的ID
     * 
     * @param module 原始表DO对象
     * @return 生成的ID
     */
    default Long setModelId(UserShiftConfigDO module) {
        return setModelId(module,
                UserShiftConfigDO::getId,
                UserShiftConfigDO::setId);
    }

    /**
     * 将原始表DO转换为migrate表DO
     * 用于数据从原始表迁移到migrate表的场景
     * 
     * @param realDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(realDO))")
    UserShiftConfigMigrateDO mapToMigrate(UserShiftConfigDO realDO);

    /**
     * 将migrate表DO转换为原始表DO
     * 用于数据从migrate表迁移回原始表的场景
     * 
     * @param migrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(migrateDO))")
    UserShiftConfigDO mapToReal(UserShiftConfigMigrateDO migrateDO);
}
