package com.imile.attendance.migration.service.impl;

import com.imile.attendance.enums.rule.RuleConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigItemDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigRangeDao;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.migration.adapter.PunchClassConfigAdapter;
import com.imile.attendance.migration.adapter.PunchClassConfigRangeAdapter;
import com.imile.attendance.migration.adapter.PunchClassItemConfigAdapter;
import com.imile.attendance.migration.adapter.PunchConfigAdapter;
import com.imile.attendance.migration.adapter.PunchConfigRangeAdapter;
import com.imile.attendance.migration.adapter.ReissueCardConfigAdapter;
import com.imile.attendance.migration.adapter.ReissueCardConfigRangeAdapter;
import com.imile.attendance.migration.service.RollbackService;
import com.imile.common.enums.IsDeleteEnum;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 考勤规则回滚服务实现类
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Slf4j
@Service
public class RollbackServiceImpl implements RollbackService {

    // 规则表适配器
    @Resource
    private PunchConfigAdapter punchConfigAdapter;
    @Resource
    private PunchConfigRangeAdapter punchConfigRangeAdapter;
    @Resource
    private ReissueCardConfigAdapter reissueCardConfigAdapter;
    @Resource
    private ReissueCardConfigRangeAdapter reissueCardConfigRangeAdapter;
    @Resource
    private PunchClassConfigAdapter punchClassConfigAdapter;
    @Resource
    private PunchClassConfigRangeAdapter punchClassConfigRangeAdapter;
    @Resource
    private PunchClassItemConfigAdapter punchClassItemConfigAdapter;

    // 保留原始DAO用于兼容性（后续可以移除）
    @Resource
    private MappingRuleConfigDao mappingRuleConfigDao;
    @Resource
    private MappingRuleConfigRangeDao mappingRuleConfigRangeDao;
    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private OverTimeConfigDao overTimeConfigDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private MappingPunchClassConfigDao mappingPunchClassConfigDao;
    @Resource
    private MappingPunchClassConfigItemDao mappingPunchClassConfigItemDao;
    @Resource
    private MappingPunchClassConfigRangeDao mappingPunchClassConfigRangeDao;

    @Override
    public Boolean rollbackAttendanceRulesByCountry(String country) {
        log.info("开始根据国家回滚考勤规则, country: {}", country);
        XxlJobLogger.log("开始根据国家回滚考勤规则, country: {}", country);

        try {
            // 参数校验
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                return false;
            }

            // 查询该国家的所有映射记录
            List<MappingRuleConfigDO> mappingConfigs = mappingRuleConfigDao.listByCountry(country);
            if (CollectionUtils.isEmpty(mappingConfigs)) {
                log.warn("未找到需要回滚的映射记录, country: {}", country);
                XxlJobLogger.log("未找到需要回滚的映射记录, country: {}", country);
                return true;
            }

            log.info("查询到映射记录数量: {}, country: {}", mappingConfigs.size(), country);
            XxlJobLogger.log("查询到映射记录数量: {}, country: {}", mappingConfigs.size(), country);

            // 步骤1：执行规则回滚
            XxlJobLogger.log("步骤2.1: 开始回滚考勤规则");
            Boolean ruleRollbackResult = rollbackAttendanceRulesByMappings(mappingConfigs);

            // 步骤2：执行班次映射记录回滚
            XxlJobLogger.log("步骤2.2: 开始回滚班次映射记录");
            Boolean classRollbackResult = rollbackPunchClassMappingRecordsByCountry(country);

            // 综合判断回滚结果
            boolean finalResult = Boolean.TRUE.equals(ruleRollbackResult) && Boolean.TRUE.equals(classRollbackResult);
            if (finalResult) {
                log.info("所有回滚操作成功, country: {}", country);
                XxlJobLogger.log("所有回滚操作成功, country: {}", country);
            } else {
                log.warn("部分回滚操作失败, country: {}, 规则回滚: {}, 班次映射回滚: {}",
                        country, ruleRollbackResult, classRollbackResult);
                XxlJobLogger.log("部分回滚操作失败, country: {}, 规则回滚: {}, 班次映射回滚: {}",
                        country, ruleRollbackResult, classRollbackResult);
            }

            return finalResult;

        } catch (Exception e) {
            log.error("根据国家回滚考勤规则失败, country: {}", country, e);
            XxlJobLogger.log("根据国家回滚考勤规则失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean rollbackAttendanceRulesByMappings(List<MappingRuleConfigDO> mappingConfigs) {
        log.info("开始根据映射记录回滚考勤规则, 映射记录数量: {}", CollectionUtils.size(mappingConfigs));
        XxlJobLogger.log("开始根据映射记录回滚考勤规则, 映射记录数量: {}", CollectionUtils.size(mappingConfigs));

        if (CollectionUtils.isEmpty(mappingConfigs)) {
            log.warn("映射记录列表为空，无需回滚");
            XxlJobLogger.log("映射记录列表为空，无需回滚");
            return true;
        }

        try {
            int successCount = 0;
            int failCount = 0;

            // 按规则类型分组处理
            for (MappingRuleConfigDO mappingConfig : mappingConfigs) {
                try {
                    boolean rollbackResult = rollbackSingleRule(mappingConfig);
                    if (rollbackResult) {
                        successCount++;
                        // 删除映射记录
                        deleteMappingRecord(mappingConfig.getId());
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("回滚单个规则失败, mappingId: {}, ruleType: {}, ruleId: {}",
                            mappingConfig.getId(), mappingConfig.getRuleType(), mappingConfig.getRuleId(), e);
                    XxlJobLogger.log("回滚单个规则失败, mappingId: {}, ruleType: {}, ruleId: {}, error: {}",
                            mappingConfig.getId(), mappingConfig.getRuleType(), mappingConfig.getRuleId(), e.getMessage());
                    failCount++;
                }
            }

            log.info("回滚完成, 成功: {}, 失败: {}", successCount, failCount);
            XxlJobLogger.log("回滚完成, 成功: {}, 失败: {}", successCount, failCount);

            return failCount == 0;

        } catch (Exception e) {
            log.error("根据映射记录回滚考勤规则失败", e);
            XxlJobLogger.log("根据映射记录回滚考勤规则失败, error: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * 回滚单个规则
     */
    private boolean rollbackSingleRule(MappingRuleConfigDO mappingConfig) {
        String ruleType = mappingConfig.getRuleType();
        Long ruleId = mappingConfig.getRuleId();

        log.info("开始回滚单个规则, ruleType: {}, ruleId: {}", ruleType, ruleId);
        XxlJobLogger.log("开始回滚单个规则, ruleType: {}, ruleId: {}", ruleType, ruleId);

        RuleConfigTypeEnum ruleConfigTypeEnum = RuleConfigTypeEnum.getInstance(ruleType);

        // 处理其他规则类型（枚举匹配）
        if (ruleConfigTypeEnum != null) {
            switch (ruleConfigTypeEnum) {
                case PUNCH_CONFIG:
                    return rollbackPunchConfig(ruleId);
                case REISSUE_CARD_CONFIG:
                    return rollbackReissueCardConfig(ruleId);
                case OVERTIME_CONFIG:
                    log.warn("加班不处理: {}", ruleType);
                    XxlJobLogger.log("加班不处理: {}", ruleType);
                    break;
                default:
                    log.warn("未知的规则类型: {}", ruleType);
                    XxlJobLogger.log("未知的规则类型: {}", ruleType);
                    return false;
            }
        }

        log.warn("未知的规则类型: {}", ruleType);
        XxlJobLogger.log("未知的规则类型: {}", ruleType);
        return false;
    }

    @Override
    public Boolean rollbackPunchConfig(Long ruleId) {
        log.info("开始物理删除打卡规则, ruleId: {}", ruleId);
        XxlJobLogger.log("开始物理删除打卡规则, ruleId: {}", ruleId);

        try {
            // 查询打卡规则是否存在
            PunchConfigDO punchConfig = punchConfigAdapter.getById(ruleId);
            if (punchConfig == null) {
                log.warn("打卡规则不存在, ruleId: {}", ruleId);
                XxlJobLogger.log("打卡规则不存在, ruleId: {}", ruleId);
                return true; // 规则不存在视为回滚成功
            }

            // 先删除相关的范围配置
            rollbackPunchConfigRanges(ruleId);

            // 物理删除打卡规则
            boolean configDeleted = punchConfigAdapter.removeById(ruleId);

            if (configDeleted) {
                log.info("打卡规则物理删除成功, ruleId: {}", ruleId);
                XxlJobLogger.log("打卡规则物理删除成功, ruleId: {}", ruleId);
                return true;
            } else {
                log.error("打卡规则物理删除失败, ruleId: {}", ruleId);
                XxlJobLogger.log("打卡规则物理删除失败, ruleId: {}", ruleId);
                return false;
            }

        } catch (Exception e) {
            log.error("物理删除打卡规则失败, ruleId: {}", ruleId, e);
            XxlJobLogger.log("物理删除打卡规则失败, ruleId: {}, error: {}", ruleId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除打卡规则范围配置
     */
    private void rollbackPunchConfigRanges(Long configId) {
        log.info("开始物理删除打卡规则范围配置, configId: {}", configId);
        XxlJobLogger.log("开始物理删除打卡规则范围配置, configId: {}", configId);

        try {
            List<PunchConfigRangeDO> ranges = punchConfigRangeAdapter.listByConfigId(configId);
            if (CollectionUtils.isEmpty(ranges)) {
                log.info("未找到需要删除的打卡规则范围配置, configId: {}", configId);
                XxlJobLogger.log("未找到需要删除的打卡规则范围配置, configId: {}", configId);
                return;
            }

            // 提取ID列表进行批量物理删除
            List<Long> rangeIds = ranges.stream()
                    .map(PunchConfigRangeDO::getId)
                    .collect(Collectors.toList());

            boolean rangesDeleted = punchConfigRangeAdapter.removeBatchByIds(rangeIds);
            if (rangesDeleted) {
                log.info("打卡规则范围配置物理删除成功, configId: {}, 删除数量: {}", configId, ranges.size());
                XxlJobLogger.log("打卡规则范围配置物理删除成功, configId: {}, 删除数量: {}", configId, ranges.size());
            } else {
                log.error("打卡规则范围配置物理删除失败, configId: {}", configId);
                XxlJobLogger.log("打卡规则范围配置物理删除失败, configId: {}", configId);
            }

        } catch (Exception e) {
            log.error("物理删除打卡规则范围配置失败, configId: {}", configId, e);
            XxlJobLogger.log("物理删除打卡规则范围配置失败, configId: {}, error: {}", configId, e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean rollbackReissueCardConfig(Long ruleId) {
        log.info("开始物理删除补卡规则, ruleId: {}", ruleId);
        XxlJobLogger.log("开始物理删除补卡规则, ruleId: {}", ruleId);

        try {
            // 查询补卡规则是否存在
            ReissueCardConfigDO reissueConfig = reissueCardConfigAdapter.getById(ruleId);
            if (reissueConfig == null) {
                log.warn("补卡规则不存在, ruleId: {}", ruleId);
                XxlJobLogger.log("补卡规则不存在, ruleId: {}", ruleId);
                return true;
            }

            // 先删除相关的范围配置
            rollbackReissueCardConfigRanges(ruleId);

            // 物理删除补卡规则
            boolean configDeleted = reissueCardConfigAdapter.removeById(ruleId);

            if (configDeleted) {
                log.info("补卡规则物理删除成功, ruleId: {}", ruleId);
                XxlJobLogger.log("补卡规则物理删除成功, ruleId: {}", ruleId);
                return true;
            } else {
                log.error("补卡规则物理删除失败, ruleId: {}", ruleId);
                XxlJobLogger.log("补卡规则物理删除失败, ruleId: {}", ruleId);
                return false;
            }

        } catch (Exception e) {
            log.error("物理删除补卡规则失败, ruleId: {}", ruleId, e);
            XxlJobLogger.log("物理删除补卡规则失败, ruleId: {}, error: {}", ruleId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除补卡规则范围配置
     */
    private void rollbackReissueCardConfigRanges(Long configId) {
        log.info("开始物理删除补卡规则范围配置, configId: {}", configId);
        XxlJobLogger.log("开始物理删除补卡规则范围配置, configId: {}", configId);

        try {
            List<ReissueCardConfigRangeDO> ranges = reissueCardConfigRangeAdapter.listByRuleConfigId(configId);
            if (CollectionUtils.isEmpty(ranges)) {
                log.info("未找到需要删除的补卡规则范围配置, configId: {}", configId);
                XxlJobLogger.log("未找到需要删除的补卡规则范围配置, configId: {}", configId);
                return;
            }

            // 提取ID列表进行批量物理删除
            List<Long> rangeIds = ranges.stream()
                    .map(ReissueCardConfigRangeDO::getId)
                    .collect(Collectors.toList());

            boolean rangesDeleted = reissueCardConfigRangeAdapter.removeBatchByIds(rangeIds);
            if (rangesDeleted) {
                log.info("补卡规则范围配置物理删除成功, configId: {}, 删除数量: {}", configId, ranges.size());
                XxlJobLogger.log("补卡规则范围配置物理删除成功, configId: {}, 删除数量: {}", configId, ranges.size());
            } else {
                log.error("补卡规则范围配置物理删除失败, configId: {}", configId);
                XxlJobLogger.log("补卡规则范围配置物理删除失败, configId: {}", configId);
            }

        } catch (Exception e) {
            log.error("物理删除补卡规则范围配置失败, configId: {}", configId, e);
            XxlJobLogger.log("物理删除补卡规则范围配置失败, configId: {}, error: {}", configId, e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean rollbackPunchClassConfig(Long ruleId) {
        log.info("开始物理删除班次规则, ruleId: {}", ruleId);
        XxlJobLogger.log("开始物理删除班次规则, ruleId: {}", ruleId);

        try {
            // 查询班次规则是否存在
            PunchClassConfigDO classConfig = punchClassConfigAdapter.getById(ruleId);
            if (classConfig == null) {
                log.warn("班次规则不存在, ruleId: {}", ruleId);
                XxlJobLogger.log("班次规则不存在, ruleId: {}", ruleId);
                return true;
            }

            // 先删除相关的范围配置
            rollbackPunchClassConfigRanges(ruleId);

            // 删除相关的班次时间配置
            try {
                rollbackPunchClassItemConfigs(ruleId);
            } catch (Exception e) {
                log.warn("物理删除班次时间配置失败, ruleId: {}, 但不影响整体回滚结果", ruleId, e);
                XxlJobLogger.log("物理删除班次时间配置失败, ruleId: {}, error: {}, 但不影响整体回滚结果", ruleId, e.getMessage());
            }

            // 删除相关的班次映射记录
            try {
                rollbackPunchClassMappingRecords(ruleId);
            } catch (Exception e) {
                log.warn("物理删除班次映射记录失败, ruleId: {}, 但不影响整体回滚结果", ruleId, e);
                XxlJobLogger.log("物理删除班次映射记录失败, ruleId: {}, error: {}, 但不影响整体回滚结果", ruleId, e.getMessage());
            }

            // 物理删除班次规则
            boolean configDeleted = punchClassConfigAdapter.removeById(ruleId);

            if (configDeleted) {
                log.info("班次规则物理删除成功, ruleId: {}", ruleId);
                XxlJobLogger.log("班次规则物理删除成功, ruleId: {}", ruleId);
                return true;
            } else {
                log.error("班次规则物理删除失败, ruleId: {}", ruleId);
                XxlJobLogger.log("班次规则物理删除失败, ruleId: {}", ruleId);
                return false;
            }

        } catch (Exception e) {
            log.error("物理删除班次规则失败, ruleId: {}", ruleId, e);
            XxlJobLogger.log("物理删除班次规则失败, ruleId: {}, error: {}", ruleId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除班次规则范围配置
     */
    private void rollbackPunchClassConfigRanges(Long configId) {
        log.info("开始物理删除班次规则范围配置, configId: {}", configId);
        XxlJobLogger.log("开始物理删除班次规则范围配置, configId: {}", configId);

        try {
            // 查询关联的范围配置
            List<PunchClassConfigRangeDO> ranges =
                    punchClassConfigRangeAdapter.selectByRuleConfigIds(Collections.singletonList(configId));

            if (CollectionUtils.isEmpty(ranges)) {
                log.info("未找到需要删除的班次规则范围配置, configId: {}", configId);
                XxlJobLogger.log("未找到需要删除的班次规则范围配置, configId: {}", configId);
                return;
            }

            // 提取ID列表进行批量物理删除
            List<Long> rangeIds = ranges.stream()
                    .map(PunchClassConfigRangeDO::getId)
                    .collect(Collectors.toList());

            boolean rangesDeleted = punchClassConfigRangeAdapter.removeBatchByIds(rangeIds);
            if (rangesDeleted) {
                log.info("班次规则范围配置物理删除成功, configId: {}, 删除数量: {}", configId, ranges.size());
                XxlJobLogger.log("班次规则范围配置物理删除成功, configId: {}, 删除数量: {}", configId, ranges.size());
            } else {
                log.error("班次规则范围配置物理删除失败, configId: {}", configId);
                XxlJobLogger.log("班次规则范围配置物理删除失败, configId: {}", configId);
            }

        } catch (Exception e) {
            log.error("物理删除班次规则范围配置失败, configId: {}", configId, e);
            XxlJobLogger.log("物理删除班次规则范围配置失败, configId: {}, error: {}", configId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除班次时间配置
     * 删除指定班次规则的所有时间配置记录
     */
    private void rollbackPunchClassItemConfigs(Long punchClassId) {
        log.info("开始物理删除班次时间配置, punchClassId: {}", punchClassId);
        XxlJobLogger.log("开始物理删除班次时间配置, punchClassId: {}", punchClassId);

        try {
            // 查询该班次的所有时间配置
            List<PunchClassItemConfigDO> itemConfigs =
                    punchClassItemConfigAdapter.selectByClassIds(Collections.singletonList(punchClassId));

            if (CollectionUtils.isEmpty(itemConfigs)) {
                log.info("未找到需要删除的班次时间配置, punchClassId: {}", punchClassId);
                XxlJobLogger.log("未找到需要删除的班次时间配置, punchClassId: {}", punchClassId);
                return;
            }

            // 提取ID列表进行批量物理删除
            List<Long> itemIds = itemConfigs.stream()
                    .map(PunchClassItemConfigDO::getId)
                    .collect(Collectors.toList());

            boolean itemsDeleted = punchClassItemConfigAdapter.removeBatchByIds(itemIds);
            if (itemsDeleted) {
                log.info("班次时间配置物理删除成功, punchClassId: {}, 删除数量: {}", punchClassId, itemConfigs.size());
                XxlJobLogger.log("班次时间配置物理删除成功, punchClassId: {}, 删除数量: {}", punchClassId, itemConfigs.size());
            } else {
                log.error("班次时间配置物理删除失败, punchClassId: {}", punchClassId);
                XxlJobLogger.log("班次时间配置物理删除失败, punchClassId: {}", punchClassId);
            }

        } catch (Exception e) {
            log.error("物理删除班次时间配置失败, punchClassId: {}", punchClassId, e);
            XxlJobLogger.log("物理删除班次时间配置失败, punchClassId: {}, error: {}", punchClassId, e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean deleteMappingRecord(Long mappingId) {
        log.info("开始物理删除映射记录, mappingId: {}", mappingId);
        XxlJobLogger.log("开始物理删除映射记录, mappingId: {}", mappingId);

        try {
            // 查询映射记录
            MappingRuleConfigDO mappingConfig = mappingRuleConfigDao.getById(mappingId);
            if (mappingConfig == null) {
                log.warn("映射记录不存在, mappingId: {}", mappingId);
                XxlJobLogger.log("映射记录不存在, mappingId: {}", mappingId);
                return true;
            }

            // 先删除相关的范围映射记录
            deleteMappingRangeRecords(mappingConfig.getHrPunchConfigId());

            // 物理删除映射记录
            boolean deleted = mappingRuleConfigDao.removeById(mappingId);

            if (deleted) {
                log.info("映射记录物理删除成功, mappingId: {}", mappingId);
                XxlJobLogger.log("映射记录物理删除成功, mappingId: {}", mappingId);
                return true;
            } else {
                log.error("映射记录物理删除失败, mappingId: {}", mappingId);
                XxlJobLogger.log("映射记录物理删除失败, mappingId: {}", mappingId);
                return false;
            }

        } catch (Exception e) {
            log.error("物理删除映射记录失败, mappingId: {}", mappingId, e);
            XxlJobLogger.log("物理删除映射记录失败, mappingId: {}, error: {}", mappingId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除映射范围记录
     */
    private void deleteMappingRangeRecords(Long hrPunchConfigId) {
        log.info("开始物理删除映射范围记录, hrPunchConfigId: {}", hrPunchConfigId);
        XxlJobLogger.log("开始物理删除映射范围记录, hrPunchConfigId: {}", hrPunchConfigId);

        try {
            List<MappingRuleConfigRangeDO> rangeRecords = mappingRuleConfigRangeDao.listByHrPunchConfigId(hrPunchConfigId);
            if (CollectionUtils.isEmpty(rangeRecords)) {
                log.info("未找到需要删除的映射范围记录, hrPunchConfigId: {}", hrPunchConfigId);
                XxlJobLogger.log("未找到需要删除的映射范围记录, hrPunchConfigId: {}", hrPunchConfigId);
                return;
            }

            // 提取ID列表进行批量物理删除
            List<Long> rangeIds = rangeRecords.stream()
                    .map(MappingRuleConfigRangeDO::getId)
                    .collect(Collectors.toList());

            boolean rangesDeleted = mappingRuleConfigRangeDao.removeByIds(rangeIds);
            if (rangesDeleted) {
                log.info("映射范围记录物理删除成功, hrPunchConfigId: {}, 删除数量: {}", hrPunchConfigId, rangeRecords.size());
                XxlJobLogger.log("映射范围记录物理删除成功, hrPunchConfigId: {}, 删除数量: {}", hrPunchConfigId, rangeRecords.size());
            } else {
                log.error("映射范围记录物理删除失败, hrPunchConfigId: {}", hrPunchConfigId);
                XxlJobLogger.log("映射范围记录物理删除失败, hrPunchConfigId: {}", hrPunchConfigId);
            }

        } catch (Exception e) {
            log.error("物理删除映射范围记录失败, hrPunchConfigId: {}", hrPunchConfigId, e);
            XxlJobLogger.log("物理删除映射范围记录失败, hrPunchConfigId: {}, error: {}", hrPunchConfigId, e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean batchDeleteMappingRecords(List<Long> mappingIds) {
        log.info("开始批量删除映射记录, 数量: {}", CollectionUtils.size(mappingIds));
        XxlJobLogger.log("开始批量删除映射记录, 数量: {}", CollectionUtils.size(mappingIds));

        if (CollectionUtils.isEmpty(mappingIds)) {
            log.warn("映射记录ID列表为空，无需删除");
            XxlJobLogger.log("映射记录ID列表为空，无需删除");
            return true;
        }

        try {
            int successCount = 0;
            int failCount = 0;

            for (Long mappingId : mappingIds) {
                try {
                    boolean deleted = deleteMappingRecord(mappingId);
                    if (deleted) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("删除单个映射记录失败, mappingId: {}", mappingId, e);
                    XxlJobLogger.log("删除单个映射记录失败, mappingId: {}, error: {}", mappingId, e.getMessage());
                    failCount++;
                }
            }

            log.info("批量删除映射记录完成, 成功: {}, 失败: {}", successCount, failCount);
            XxlJobLogger.log("批量删除映射记录完成, 成功: {}, 失败: {}", successCount, failCount);

            return failCount == 0;

        } catch (Exception e) {
            log.error("批量删除映射记录失败", e);
            XxlJobLogger.log("批量删除映射记录失败, error: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean rollbackPunchClassMappingRecords(Long punchClassConfigId) {
        log.info("开始回滚班次映射记录, punchClassConfigId: {}", punchClassConfigId);
        XxlJobLogger.log("开始回滚班次映射记录, punchClassConfigId: {}", punchClassConfigId);

        if (Objects.isNull(punchClassConfigId)) {
            log.warn("班次规则ID为空，无需回滚映射记录");
            XxlJobLogger.log("班次规则ID为空，无需回滚映射记录");
            return true;
        }

        try {
            boolean allSuccess = true;

            // 步骤1：删除班次映射记录 (MappingPunchClassConfigDO)
            log.info("步骤1: 开始删除班次映射记录, punchClassConfigId: {}", punchClassConfigId);
            XxlJobLogger.log("步骤1: 开始删除班次映射记录, punchClassConfigId: {}", punchClassConfigId);
            boolean mappingDeleted = rollbackPunchClassMappingConfig(punchClassConfigId);
            if (!mappingDeleted) {
                allSuccess = false;
            }

            // 步骤2：删除班次时间配置映射记录 (MappingPunchClassConfigItemDO)
            log.info("步骤2: 开始删除班次时间配置映射记录, punchClassConfigId: {}", punchClassConfigId);
            XxlJobLogger.log("步骤2: 开始删除班次时间配置映射记录, punchClassConfigId: {}", punchClassConfigId);
            boolean itemMappingDeleted = rollbackPunchClassMappingConfigItems(punchClassConfigId);
            if (!itemMappingDeleted) {
                allSuccess = false;
            }

            // 步骤3：删除班次范围映射记录 (MappingPunchClassConfigRangeDO)
            log.info("步骤3: 开始删除班次范围映射记录, punchClassConfigId: {}", punchClassConfigId);
            XxlJobLogger.log("步骤3: 开始删除班次范围映射记录, punchClassConfigId: {}", punchClassConfigId);
            boolean rangeMappingDeleted = rollbackPunchClassMappingConfigRanges(punchClassConfigId);
            if (!rangeMappingDeleted) {
                allSuccess = false;
            }

            if (allSuccess) {
                log.info("班次映射记录回滚成功, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("班次映射记录回滚成功, punchClassConfigId: {}", punchClassConfigId);
            } else {
                log.warn("班次映射记录回滚部分失败, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("班次映射记录回滚部分失败, punchClassConfigId: {}", punchClassConfigId);
            }

            return allSuccess;

        } catch (Exception e) {
            log.error("回滚班次映射记录失败, punchClassConfigId: {}", punchClassConfigId, e);
            XxlJobLogger.log("回滚班次映射记录失败, punchClassConfigId: {}, error: {}", punchClassConfigId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除班次映射记录
     * 根据新考勤班次ID删除对应的班次映射记录
     */
    private boolean rollbackPunchClassMappingConfig(Long punchClassConfigId) {
        try {
            // 使用适配器物理删除班次映射记录
            boolean deleted = mappingPunchClassConfigDao.removeByPunchClassConfigId(punchClassConfigId);

            if (deleted) {
                log.info("班次映射记录物理删除成功, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("班次映射记录物理删除成功, punchClassConfigId: {}", punchClassConfigId);
                return true;
            } else {
                log.info("未找到需要删除的班次映射记录或删除失败, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("未找到需要删除的班次映射记录或删除失败, punchClassConfigId: {}", punchClassConfigId);
                return true; // 记录不存在也视为删除成功
            }

        } catch (Exception e) {
            log.error("物理删除班次映射记录异常, punchClassConfigId: {}", punchClassConfigId, e);
            XxlJobLogger.log("物理删除班次映射记录异常, punchClassConfigId: {}, error: {}", punchClassConfigId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除班次时间配置映射记录
     * 根据新考勤班次ID删除对应的班次时间配置映射记录
     */
    private boolean rollbackPunchClassMappingConfigItems(Long punchClassConfigId) {
        try {
            // 使用适配器物理删除班次时间配置映射记录
            boolean deleted = mappingPunchClassConfigItemDao.removeByPunchClassConfigId(punchClassConfigId);

            if (deleted) {
                log.info("班次时间配置映射记录物理删除成功, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("班次时间配置映射记录物理删除成功, punchClassConfigId: {}", punchClassConfigId);
                return true;
            } else {
                log.info("未找到需要删除的班次时间配置映射记录或删除失败, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("未找到需要删除的班次时间配置映射记录或删除失败, punchClassConfigId: {}", punchClassConfigId);
                return true; // 记录不存在也视为删除成功
            }

        } catch (Exception e) {
            log.error("物理删除班次时间配置映射记录异常, punchClassConfigId: {}", punchClassConfigId, e);
            XxlJobLogger.log("物理删除班次时间配置映射记录异常, punchClassConfigId: {}, error: {}", punchClassConfigId, e.getMessage());
            throw e;
        }
    }

    /**
     * 物理删除班次范围映射记录
     * 根据新考勤班次ID删除对应的班次范围映射记录
     */
    private boolean rollbackPunchClassMappingConfigRanges(Long punchClassConfigId) {
        try {
            // 使用适配器物理删除班次范围映射记录
            boolean deleted = mappingPunchClassConfigRangeDao.removeByPunchClassConfigId(punchClassConfigId);

            if (deleted) {
                log.info("班次范围映射记录物理删除成功, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("班次范围映射记录物理删除成功, punchClassConfigId: {}", punchClassConfigId);
                return true;
            } else {
                log.info("未找到需要删除的班次范围映射记录或删除失败, punchClassConfigId: {}", punchClassConfigId);
                XxlJobLogger.log("未找到需要删除的班次范围映射记录或删除失败, punchClassConfigId: {}", punchClassConfigId);
                return true; // 记录不存在也视为删除成功
            }

        } catch (Exception e) {
            log.error("物理删除班次范围映射记录异常, punchClassConfigId: {}", punchClassConfigId, e);
            XxlJobLogger.log("物理删除班次范围映射记录异常, punchClassConfigId: {}, error: {}", punchClassConfigId, e.getMessage());
            throw e;
        }
    }

    @Override
    public Boolean rollbackPunchClassMappingRecordsByCountry(String country) {
        log.info("开始根据国家回滚班次规则及映射记录, country: {}", country);
        XxlJobLogger.log("开始根据国家回滚班次规则及映射记录, country: {}", country);

        if (StringUtils.isBlank(country)) {
            log.warn("国家参数为空，无需回滚班次规则及映射记录");
            XxlJobLogger.log("国家参数为空，无需回滚班次规则及映射记录");
            return true;
        }

        try {
            // 查询该国家的所有班次映射记录
            List<MappingPunchClassConfigDO> mappingConfigs = mappingPunchClassConfigDao.listByCountry(country);
            if (CollectionUtils.isEmpty(mappingConfigs)) {
                log.info("未找到需要回滚的班次规则, country: {}", country);
                XxlJobLogger.log("未找到需要回滚的班次规则, country: {}", country);
                return true;
            }

            log.info("查询到班次规则数量: {}, country: {}", mappingConfigs.size(), country);
            XxlJobLogger.log("查询到班次规则数量: {}, country: {}", mappingConfigs.size(), country);

            int successCount = 0;
            int failCount = 0;

            // 逐个处理班次规则及其映射记录
            for (MappingPunchClassConfigDO mappingConfig : mappingConfigs) {
                try {
                    Long punchClassConfigId = mappingConfig.getPunchClassConfigId();
                    if (punchClassConfigId != null) {
                        // 调用完整的班次规则回滚，包括：
                        // 1. 删除班次规则本身（PunchClassConfigDO）
                        // 2. 删除班次范围配置（PunchClassConfigRangeDO）
                        // 3. 删除班次映射记录（MappingPunchClassConfigDO + Item + Range）
                        boolean rollbackResult = rollbackPunchClassConfig(punchClassConfigId);
                        if (rollbackResult) {
                            successCount++;
                        } else {
                            failCount++;
                        }
                    } else {
                        log.warn("班次映射记录的punchClassConfigId为空, mappingId: {}", mappingConfig.getId());
                        XxlJobLogger.log("班次映射记录的punchClassConfigId为空, mappingId: {}", mappingConfig.getId());
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("回滚单个班次规则失败, mappingId: {}, punchClassConfigId: {}",
                            mappingConfig.getId(), mappingConfig.getPunchClassConfigId(), e);
                    XxlJobLogger.log("回滚单个班次规则失败, mappingId: {}, punchClassConfigId: {}, error: {}",
                            mappingConfig.getId(), mappingConfig.getPunchClassConfigId(), e.getMessage());
                    failCount++;
                }
            }

            log.info("班次规则及映射记录回滚完成, country: {}, 成功: {}, 失败: {}", country, successCount, failCount);
            XxlJobLogger.log("班次规则及映射记录回滚完成, country: {}, 成功: {}, 失败: {}", country, successCount, failCount);

            return failCount == 0;

        } catch (Exception e) {
            log.error("根据国家回滚班次规则及映射记录失败, country: {}", country, e);
            XxlJobLogger.log("根据国家回滚班次规则及映射记录失败, country: {}, error: {}", country, e.getMessage());
            throw e;
        }
    }
}
