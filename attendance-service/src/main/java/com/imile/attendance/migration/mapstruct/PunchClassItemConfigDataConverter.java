package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassItemConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 班次时间配置DO转换器
 * 实现PunchClassItemConfigDO和PunchClassItemConfigMigrateDO之间的相互转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchClassItemConfigDataConverter implements DataConverter<PunchClassItemConfigMigrateDO, PunchClassItemConfigDO> {

    @Override
    public PunchClassItemConfigDO convertFromMigrate(PunchClassItemConfigMigrateDO migrateDO) {
        if (migrateDO == null) {
            return null;
        }
        
        log.debug("转换班次时间配置: migrate -> real, itemId: {}", migrateDO.getId());
        return PunchClassItemConfigDOMapstruct.INSTANCE.mapToReal(migrateDO);
    }

    @Override
    public PunchClassItemConfigMigrateDO convertFromReal(PunchClassItemConfigDO realDO) {
        if (realDO == null) {
            return null;
        }
        
        log.debug("转换班次时间配置: real -> migrate, itemId: {}", realDO.getId());
        return PunchClassItemConfigDOMapstruct.INSTANCE.mapToMigrate(realDO);
    }

    @Override
    public Class<PunchClassItemConfigMigrateDO> getMigrateType() {
        return PunchClassItemConfigMigrateDO.class;
    }

    @Override
    public Class<PunchClassItemConfigDO> getRealType() {
        return PunchClassItemConfigDO.class;
    }
}
