package com.imile.attendance.migration.service.impl;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.migration.adapter.PunchClassConfigRangeAdapter;
import com.imile.attendance.migration.adapter.PunchConfigRangeAdapter;
import com.imile.attendance.migration.adapter.ReissueCardConfigRangeAdapter;
import com.imile.attendance.migration.converter.AttendanceRuleTypeConverter;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import com.imile.attendance.migration.service.MappingPunchClassConfigRangeService;
import com.imile.attendance.migration.service.MappingRuleConfigRangeService;
import com.imile.attendance.migration.service.RuleRangeCreationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 新考勤规则范围创建服务实现类
 */
@Slf4j
@Service
public class RuleRangeCreationServiceImpl implements RuleRangeCreationService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private PunchClassConfigRangeDao punchClassConfigRangeDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private PunchConfigRangeAdapter punchConfigRangeAdapter;
    @Resource
    private PunchClassConfigRangeAdapter punchClassConfigRangeAdapter;
    @Resource
    private ReissueCardConfigRangeAdapter reissueCardConfigRangeAdapter;
    @Resource
    private MappingRuleConfigRangeService mappingRuleConfigRangeService;
    @Resource
    private MappingPunchClassConfigRangeService mappingPunchClassConfigRangeService;

    @Override
    public List<PunchConfigRangeDO> createPunchConfigRanges(Long ruleConfigId,
                                                            String ruleConfigNo,
                                                            HrAttendanceGroupDTO hrGroup,
                                                            List<HrAttendanceGroupRangeDTO> hrRanges) {
        log.info("创建打卡规则范围, ruleConfigId: {}, ruleConfigNo: {}, rangeCount: {}",
                ruleConfigId, ruleConfigNo, CollectionUtils.size(hrRanges));

        List<PunchConfigRangeDO> result = new ArrayList<>();
        List<MappingRuleConfigRangeDO> mappingResult = new ArrayList<>();

        if (CollectionUtils.isEmpty(hrRanges)) {
            log.warn("HR考勤组应用范围列表为空, ruleConfigId: {}", ruleConfigId);
            return result;
        }

        for (HrAttendanceGroupRangeDTO hrRange : hrRanges) {
            PunchConfigRangeDO rangeDO = buildSinglePunchConfigRange(ruleConfigId, ruleConfigNo, hrGroup, hrRange);
            MappingRuleConfigRangeDO mappingRuleConfigRangeDO = mappingRuleConfigRangeService.buildMapping(hrGroup, hrRange, rangeDO.getId(), ruleConfigId, rangeDO.getBizId());
            result.add(rangeDO);
            mappingResult.add(mappingRuleConfigRangeDO);
        }

        // 批量保存
        if (CollectionUtils.isNotEmpty(result)) {
//            boolean saved = punchConfigRangeDao.saveBatch(result);
            boolean saved = punchConfigRangeAdapter.saveBatch(result);
            if (saved) {
                log.info("批量保存打卡规则范围成功, ruleConfigId: {}, count: {}", ruleConfigId, result.size());
            } else {
                log.error("批量保存打卡规则范围失败, ruleConfigId: {}", ruleConfigId);
                result.clear();
            }
        }
        if (CollectionUtils.isNotEmpty(mappingResult)) {
            boolean saved = mappingRuleConfigRangeService.batchSave(mappingResult);
            if (saved) {
                log.info("批量保存打卡规则范围映射成功, ruleConfigId: {}, count: {}", ruleConfigId, mappingResult.size());
            } else {
                log.error("批量保存打卡规则范围映射失败, ruleConfigId: {}", ruleConfigId);
            }
        }
        return result;
    }

    @Override
    public PunchConfigRangeDO buildSinglePunchConfigRange(Long ruleConfigId,
                                                          String ruleConfigNo,
                                                          HrAttendanceGroupDTO hrGroup,
                                                          HrAttendanceGroupRangeDTO hrRange) {
        PunchConfigRangeDO rangeDO = new PunchConfigRangeDO();
        BaseDOUtil.fillDOInsertByUsrOrSystem(rangeDO);
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setRuleConfigId(ruleConfigId);
        rangeDO.setRuleConfigNo(ruleConfigNo);
        rangeDO.setBizId(hrRange.getBizId());
        rangeDO.setRangeType(AttendanceRuleTypeConverter.convertHrRangeTypeToRuleRangeType(hrRange.getRangeType()));
        rangeDO.setEffectTime(hrRange.getEffectTime());
        rangeDO.setExpireTime(hrRange.getExpireTime());
        // 设置时间戳
        String timeZone = hrGroup.getTimeZone();
        rangeDO.setEffectTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, hrRange.getEffectTime()));
        rangeDO.setExpireTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, hrRange.getExpireTime()));
        rangeDO.setRemark(hrRange.getRemark());
        rangeDO.setIsFromHrHistoryConfig(BusinessConstant.Y);
        //考勤组创建的规则默认为停用
        rangeDO.setStatus(StatusEnum.DISABLED.getCode());
        //非最新处理为非最新
        if (Objects.equals(hrRange.getIsLatest(), BusinessConstant.N)) {
            rangeDO.setIsLatest(BusinessConstant.N);
        } else {
            rangeDO.setIsLatest(BusinessConstant.Y);
        }
        return rangeDO;
    }

    @Override
    public List<PunchClassConfigRangeDO> createPunchClassConfigRanges(Long ruleConfigId, String ruleConfigNo,
                                                                      HrAttendanceGroupDTO group,
                                                                      List<HrAttendanceGroupRangeDTO> hrRanges) {
        log.info("创建班次配置规则范围, ruleConfigId: {}, ruleConfigNo: {}, rangeCount: {}",
                ruleConfigId, ruleConfigNo, CollectionUtils.size(hrRanges));
        XxlJobLogger.log("创建班次配置规则范围, ruleConfigId: {}, ruleConfigNo: {}, rangeCount: {}",
                ruleConfigId, ruleConfigNo, CollectionUtils.size(hrRanges));

        List<PunchClassConfigRangeDO> result = new ArrayList<>();
        List<MappingPunchClassConfigRangeDO> mappingResult = new ArrayList<>();

        if (CollectionUtils.isEmpty(hrRanges)) {
            log.warn("HR考勤组应用范围列表为空, ruleConfigId: {}", ruleConfigId);
            XxlJobLogger.log("HR考勤组应用范围列表为空, ruleConfigId: {}", ruleConfigId);
            return result;
        }

        try {
            // 遍历HR范围列表，创建班次配置范围
            for (HrAttendanceGroupRangeDTO hrRange : hrRanges) {
                PunchClassConfigRangeDO rangeDO = createSinglePunchClassConfigRange(ruleConfigId, ruleConfigNo, group, hrRange);
                if (rangeDO != null) {
                    result.add(rangeDO);

                    // 创建映射关系
                    MappingPunchClassConfigRangeDO mappingDO = mappingPunchClassConfigRangeService.buildMapping(
                            group, hrRange, rangeDO.getId(), rangeDO.getRuleConfigId(), rangeDO.getBizId());
                    if (mappingDO != null) {
                        mappingResult.add(mappingDO);
                    }
                }
            }

            // 批量保存班次配置范围
            if (CollectionUtils.isNotEmpty(result)) {
//                punchClassConfigRangeDao.saveBatch(result);
                punchClassConfigRangeAdapter.saveBatch(result);
                log.info("班次配置规则范围保存成功, ruleConfigId: {}, 保存数量: {}", ruleConfigId, result.size());
                XxlJobLogger.log("班次配置规则范围保存成功, ruleConfigId: {}, 保存数量: {}", ruleConfigId, result.size());
            }

            // 批量保存映射关系
            if (CollectionUtils.isNotEmpty(mappingResult)) {
                mappingPunchClassConfigRangeService.batchSave(mappingResult);
                log.info("班次配置规则范围映射关系保存成功, ruleConfigId: {}, 映射数量: {}", ruleConfigId, mappingResult.size());
                XxlJobLogger.log("班次配置规则范围映射关系保存成功, ruleConfigId: {}, 映射数量: {}", ruleConfigId, mappingResult.size());
            }

        } catch (Exception e) {
            log.error("创建班次配置规则范围失败, ruleConfigId: {}", ruleConfigId, e);
            XxlJobLogger.log("创建班次配置规则范围失败, ruleConfigId: {}", ruleConfigId);
            throw e;
        }

        return result;
    }

    /**
     * 创建单个班次配置规则范围
     *
     * @param ruleConfigId 规则配置ID
     * @param ruleConfigNo 规则配置编码
     * @param group 考勤组信息
     * @param hrRange HR范围
     * @return 班次配置规则范围
     */
    private PunchClassConfigRangeDO createSinglePunchClassConfigRange(Long ruleConfigId, String ruleConfigNo,
                                                                      HrAttendanceGroupDTO group,
                                                                      HrAttendanceGroupRangeDTO hrRange) {
        PunchClassConfigRangeDO rangeDO = new PunchClassConfigRangeDO();
        BaseDOUtil.fillDOInsertByUsrOrSystem(rangeDO);
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setRuleConfigId(ruleConfigId);
        rangeDO.setRuleConfigNo(ruleConfigNo);
        rangeDO.setBizId(hrRange.getBizId());
        rangeDO.setRangeType(AttendanceRuleTypeConverter.convertHrRangeTypeToRuleRangeType(hrRange.getRangeType()));
        rangeDO.setEffectTime(hrRange.getEffectTime());
        rangeDO.setExpireTime(hrRange.getExpireTime());

        // 设置时间戳
        String timeZone = group.getTimeZone();
        rangeDO.setEffectTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, hrRange.getEffectTime()));
        rangeDO.setExpireTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, hrRange.getExpireTime()));

        rangeDO.setRemark(hrRange.getRemark());
        rangeDO.setIsFromHrHistoryConfig(BusinessConstant.Y);

        // 考勤组映射规则为停用
        rangeDO.setStatus(StatusEnum.DISABLED.getCode());
        //非最新处理为非最新
        if (Objects.equals(hrRange.getIsLatest(), BusinessConstant.N)) {
            rangeDO.setIsLatest(BusinessConstant.N);
        } else {
            rangeDO.setIsLatest(BusinessConstant.Y);
        }
        return rangeDO;
    }

    @Override
    public List<ReissueCardConfigRangeDO> createReissueCardConfigRanges(ReissueCardConfigDO reissueCardConfig,
                                                                        String country, String timeZone) {
        Long ruleConfigId = reissueCardConfig.getId();
        log.info("创建补卡规则范围（迁移用）, ruleConfigId: {}, country: {}", ruleConfigId, country);

        try {
            // 查询该国家所有在职非司机用户
            List<UserInfoDO> userList = reissueCardConfigRangeAdapter.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder()
                            .country(country)
                            .build()
            );

            if (CollectionUtils.isEmpty(userList)) {
                log.warn("未找到符合条件的用户, country: {}", country);
                return Collections.emptyList();
            }

            List<ReissueCardConfigRangeDO> result = new ArrayList<>();
            List<MappingRuleConfigRangeDO> mappingResult = new ArrayList<>();

            // 为每个用户创建补卡规则范围
            for (UserInfoDO user : userList) {
                ReissueCardConfigRangeDO rangeDO = new ReissueCardConfigRangeDO();
                rangeDO.setId(defaultIdWorker.nextId());
                rangeDO.setRuleConfigId(ruleConfigId);
                rangeDO.setRuleConfigNo(reissueCardConfig.getConfigNo());
                rangeDO.setBizId(user.getId());
                rangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
                rangeDO.setEffectTime(reissueCardConfig.getEffectTime());
                rangeDO.setExpireTime(reissueCardConfig.getExpireTime());
                rangeDO.setEffectTimestamp(reissueCardConfig.getEffectTimestamp());
                rangeDO.setExpireTimestamp(reissueCardConfig.getExpireTimestamp());
                rangeDO.setStatus(reissueCardConfig.getStatus());
                rangeDO.setIsLatest(reissueCardConfig.getIsLatest());
                rangeDO.setRemark("迁移生成的国家级补卡规则范围");
                rangeDO.setIsFromHrHistoryConfig(BusinessConstant.N);
                BaseDOUtil.fillDOInsertByUsrOrSystem(rangeDO);
                result.add(rangeDO);

                // 创建映射关系（使用虚拟的HR考勤组信息）
                MappingRuleConfigRangeDO mappingDO = new MappingRuleConfigRangeDO();
                mappingDO.setId(defaultIdWorker.nextId());
                mappingDO.setHrPunchConfigRangeId(-1L);
                mappingDO.setHrPunchConfigId(-1L);
                mappingDO.setRuleConfigRangeId(rangeDO.getId());
                mappingDO.setRuleConfigId(rangeDO.getRuleConfigId());
                mappingDO.setRuleBizId(rangeDO.getBizId());
                BaseDOUtil.fillDOInsertByUsrOrSystem(mappingDO);
                mappingResult.add(mappingDO);
            }

            // 批量保存补卡规则范围（使用适配器）
            if (CollectionUtils.isNotEmpty(result)) {
                reissueCardConfigRangeAdapter.saveBatch(result);
                log.info("补卡规则范围保存成功, ruleConfigId: {}, 保存数量: {}", ruleConfigId, result.size());
            }

            // 批量保存映射关系
            if (CollectionUtils.isNotEmpty(mappingResult)) {
                mappingRuleConfigRangeService.batchSave(mappingResult);
                log.info("补卡规则范围映射关系保存成功, ruleConfigId: {}, 映射数量: {}", ruleConfigId, mappingResult.size());
            }

            return result;

        } catch (Exception e) {
            log.error("创建补卡规则范围异常, ruleConfigId: {}, country: {}", ruleConfigId, country, e);
            throw e;
        }
    }

    @Override
    public List<ReissueCardConfigRangeDO> createAttendanceGroupReissueCardConfigRanges(ReissueCardConfigDO reissueCardConfig,
                                                                                       HrAttendanceGroupDTO attendanceGroup,
                                                                                       List<HrAttendanceGroupRangeDTO> hrRanges) {
        Long ruleConfigId = reissueCardConfig.getId();
        log.info("创建考勤组级别的补卡规则范围, ruleConfigId: {}, groupId: {}, rangeCount: {}",
                ruleConfigId, attendanceGroup.getId(), CollectionUtils.size(hrRanges));

        try {
            if (CollectionUtils.isEmpty(hrRanges)) {
                log.warn("HR范围列表为空, ruleConfigId: {}, groupId: {}", ruleConfigId, attendanceGroup.getId());
                return Collections.emptyList();
            }

            List<ReissueCardConfigRangeDO> result = new ArrayList<>();
            List<MappingRuleConfigRangeDO> mappingResult = new ArrayList<>();

            for (HrAttendanceGroupRangeDTO hrRange : hrRanges) {
                try {
                    // 创建补卡规则范围
                    ReissueCardConfigRangeDO rangeDO = buildAttendanceGroupReissueCardConfigRange(
                            reissueCardConfig, attendanceGroup, hrRange);

                    if (rangeDO != null) {
                        result.add(rangeDO);

                        // 创建映射关系
                        MappingRuleConfigRangeDO mappingDO = mappingRuleConfigRangeService.buildMapping(
                                attendanceGroup, hrRange, rangeDO.getId(), ruleConfigId, rangeDO.getBizId());
                        if (mappingDO != null) {
                            mappingResult.add(mappingDO);
                        }
                    }

                } catch (Exception e) {
                    log.error("创建单个补卡规则范围失败, ruleConfigId: {}, hrRangeId: {}", ruleConfigId, hrRange.getId(), e);
                    // 继续处理其他范围，不中断整个流程
                }
            }

            // 批量保存补卡规则范围（使用适配器）
            if (CollectionUtils.isNotEmpty(result)) {
                reissueCardConfigRangeAdapter.saveBatch(result);
                log.info("考勤组级别的补卡规则范围保存成功, ruleConfigId: {}, groupId: {}, 保存数量: {}",
                        ruleConfigId, attendanceGroup.getId(), result.size());
            }

            // 批量保存映射关系
            if (CollectionUtils.isNotEmpty(mappingResult)) {
                mappingRuleConfigRangeService.batchSave(mappingResult);
                log.info("考勤组级别的补卡规则范围映射关系保存成功, ruleConfigId: {}, groupId: {}, 映射数量: {}",
                        ruleConfigId, attendanceGroup.getId(), mappingResult.size());
            }

            return result;

        } catch (Exception e) {
            log.error("创建考勤组级别的补卡规则范围异常, ruleConfigId: {}, groupId: {}",
                    ruleConfigId, attendanceGroup.getId(), e);
            throw e;
        }
    }

    /**
     * 构建单个考勤组级别的补卡规则范围
     */
    private ReissueCardConfigRangeDO buildAttendanceGroupReissueCardConfigRange(ReissueCardConfigDO reissueCardConfig,
                                                                               HrAttendanceGroupDTO attendanceGroup,
                                                                               HrAttendanceGroupRangeDTO hrRange) {
        try {
            ReissueCardConfigRangeDO rangeDO = new ReissueCardConfigRangeDO();
            BaseDOUtil.fillDOInsertByUsrOrSystem(rangeDO);
            rangeDO.setId(defaultIdWorker.nextId());
            rangeDO.setRuleConfigId(reissueCardConfig.getId());
            rangeDO.setRuleConfigNo(reissueCardConfig.getConfigNo());
            rangeDO.setBizId(hrRange.getBizId());
            rangeDO.setRangeType(AttendanceRuleTypeConverter.convertHrRangeTypeToRuleRangeType(hrRange.getRangeType()));
            // 设置来自考勤组
            rangeDO.setIsFromHrHistoryConfig(BusinessConstant.Y);

            //考勤组创建的规则默认为停用
            rangeDO.setStatus(StatusEnum.DISABLED.getCode());
            //非最新处理为非最新
            if (Objects.equals(hrRange.getIsLatest(), BusinessConstant.N)) {
                rangeDO.setIsLatest(BusinessConstant.N);
            } else {
                rangeDO.setIsLatest(BusinessConstant.Y);
            }

            // 生效失效时间处理
            rangeDO.setEffectTime(hrRange.getEffectTime());
            rangeDO.setExpireTime(hrRange.getExpireTime());

            // 设置时间戳
            String timeZone = attendanceGroup.getTimeZone();
            rangeDO.setEffectTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, hrRange.getEffectTime()));
            rangeDO.setExpireTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, hrRange.getExpireTime()));

            log.debug("构建考勤组级别的补卡规则范围成功, rangeId: {}, bizId: {}, rangeType: {}",
                    rangeDO.getId(), rangeDO.getBizId(), rangeDO.getRangeType());

            return rangeDO;

        } catch (Exception e) {
            log.error("构建考勤组级别的补卡规则范围失败, ruleConfigId: {}, hrRangeId: {}",
                    reissueCardConfig.getId(), hrRange.getId(), e);
            return null;
        }
    }
}
