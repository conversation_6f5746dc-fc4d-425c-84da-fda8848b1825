package com.imile.attendance.migration.dto;

import lombok.Data;

/**
 * 优化迁移统计信息DTO
 * 用于记录和分析优化迁移服务的性能指标
 *
 * <AUTHOR> chen
 * @since 2025/7/25
 */
@Data
public class OptimizedMigrationStatistics {

    /**
     * 国家代码
     */
    private String country;

    /**
     * 开始时间戳
     */
    private Long startTime;

    /**
     * 结束时间戳
     */
    private Long endTime;

    /**
     * 总耗时（毫秒）
     */
    private Long durationMs;

    /**
     * 迁移是否成功
     */
    private boolean success;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 考勤组数量
     */
    private Integer punchConfigCount;

    /**
     * 总记录数（统计得出）
     */
    private Long totalRecordCount;

    /**
     * 实际读取记录数
     */
    private Long actualReadCount;

    /**
     * HR读取批次大小
     */
    private Integer hrReadBatchSize;

    /**
     * HR读取批次数
     */
    private Integer hrReadBatchCount = 0;

    /**
     * 本地写入批次大小
     */
    private Integer localWriteBatchSize;

    /**
     * 本地写入批次数
     */
    private Integer localWriteBatchCount = 0;

    /**
     * 总写入批次数（预计）
     */
    private Integer totalWriteBatchCount;

    /**
     * 成功处理的记录数
     */
    private Long successCount = 0L;

    /**
     * 失败处理的记录数
     */
    private Long failedCount = 0L;

    /**
     * 计算总耗时
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            this.durationMs = endTime - startTime;
        }
    }

    /**
     * 增加HR读取批次计数
     */
    public void incrementHrReadBatchCount() {
        this.hrReadBatchCount++;
    }

    /**
     * 增加本地写入批次计数
     */
    public void incrementLocalWriteBatchCount() {
        this.localWriteBatchCount++;
    }

    /**
     * 增加成功记录数
     */
    public void addSuccessCount(long count) {
        this.successCount += count;
    }

    /**
     * 增加失败记录数
     */
    public void addFailedCount(long count) {
        this.failedCount += count;
    }

    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        long total = successCount + failedCount;
        if (total == 0) {
            return 0.0;
        }
        return (double) successCount / total * 100;
    }

    /**
     * 计算平均处理速度（记录/秒）
     */
    public double getProcessingSpeed() {
        if (durationMs == null || durationMs == 0) {
            return 0.0;
        }
        return (double) successCount / (durationMs / 1000.0);
    }

    /**
     * 获取数据源切换次数（优化后固定为2次）
     */
    public int getDataSourceSwitchCount() {
        return 2; // 切换到HRMS + 切换回默认数据源
    }

    /**
     * 获取原方法的数据源切换次数（用于对比）
     */
    public int getOriginalDataSourceSwitchCount() {
        // 原方法每个分页都会切换数据源
        return hrReadBatchCount + localWriteBatchCount;
    }

    /**
     * 计算数据源切换优化比例
     */
    public double getDataSourceSwitchOptimizationRatio() {
        int originalSwitchCount = getOriginalDataSourceSwitchCount();
        if (originalSwitchCount == 0) {
            return 0.0;
        }
        return (double) (originalSwitchCount - getDataSourceSwitchCount()) / originalSwitchCount * 100;
    }

    /**
     * 获取详细的统计报告
     */
    public String getDetailedReport() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 优化迁移详细统计报告 ===\n");
        sb.append(String.format("国家: %s\n", country));
        sb.append(String.format("迁移结果: %s\n", success ? "成功" : "失败"));
        
        if (errorMessage != null) {
            sb.append(String.format("错误信息: %s\n", errorMessage));
        }
        
        sb.append(String.format("总耗时: %d ms (%.2f 秒)\n", durationMs, durationMs / 1000.0));
        sb.append(String.format("考勤组数量: %d\n", punchConfigCount));
        sb.append(String.format("总记录数: %d\n", totalRecordCount));
        sb.append(String.format("实际读取: %d\n", actualReadCount));
        sb.append(String.format("成功处理: %d\n", successCount));
        sb.append(String.format("失败处理: %d\n", failedCount));
        sb.append(String.format("成功率: %.2f%%\n", getSuccessRate()));
        sb.append(String.format("处理速度: %.2f 条/秒\n", getProcessingSpeed()));
        
        sb.append("\n=== 批次处理统计 ===\n");
        sb.append(String.format("HR读取批次大小: %d 条\n", hrReadBatchSize));
        sb.append(String.format("HR读取批次数: %d\n", hrReadBatchCount));
        sb.append(String.format("本地写入批次大小: %d 条\n", localWriteBatchSize));
        sb.append(String.format("本地写入批次数: %d\n", localWriteBatchCount));
        
        sb.append("\n=== 性能优化效果 ===\n");
        sb.append(String.format("数据源切换次数: %d (原方法: %d)\n", 
                getDataSourceSwitchCount(), getOriginalDataSourceSwitchCount()));
        sb.append(String.format("数据源切换优化: %.2f%%\n", getDataSourceSwitchOptimizationRatio()));
        sb.append(String.format("批次大小优化: HR读取提升 %d 倍\n", hrReadBatchSize / 1000));
        
        sb.append("=== 报告结束 ===");
        
        return sb.toString();
    }

    /**
     * 获取简要统计信息（用于日志输出）
     */
    public String getSummary() {
        return String.format("国家: %s, 耗时: %.2fs, 成功: %d, 失败: %d, 成功率: %.2f%%, 速度: %.2f条/s", 
                country, durationMs / 1000.0, successCount, failedCount, getSuccessRate(), getProcessingSpeed());
    }

    /**
     * 检查是否有性能警告
     */
    public boolean hasPerformanceWarning() {
        // 如果处理速度低于100条/秒，或成功率低于95%，则发出警告
        return getProcessingSpeed() < 100.0 || getSuccessRate() < 95.0;
    }

    /**
     * 获取性能警告信息
     */
    public String getPerformanceWarning() {
        if (!hasPerformanceWarning()) {
            return null;
        }
        
        StringBuilder warnings = new StringBuilder();
        if (getProcessingSpeed() < 100.0) {
            warnings.append(String.format("处理速度较慢: %.2f条/s (建议>100条/s); ", getProcessingSpeed()));
        }
        if (getSuccessRate() < 95.0) {
            warnings.append(String.format("成功率较低: %.2f%% (建议>95%%); ", getSuccessRate()));
        }
        
        return warnings.toString();
    }
}
