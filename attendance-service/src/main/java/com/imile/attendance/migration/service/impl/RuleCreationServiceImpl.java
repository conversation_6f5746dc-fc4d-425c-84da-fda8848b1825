package com.imile.attendance.migration.service.impl;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.enums.rule.RuleConfigTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.migration.adapter.PunchClassConfigAdapter;
import com.imile.attendance.migration.adapter.PunchClassItemConfigAdapter;
import com.imile.attendance.migration.adapter.PunchConfigAdapter;
import com.imile.attendance.migration.adapter.ReissueCardConfigAdapter;
import com.imile.attendance.migration.converter.AttendanceRuleTypeConverter;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import com.imile.attendance.migration.service.MappingPunchClassConfigItemService;
import com.imile.attendance.migration.service.MappingPunchClassConfigService;
import com.imile.attendance.migration.service.MappingRuleConfigService;
import com.imile.attendance.migration.service.RuleCreationService;
import com.imile.attendance.migration.util.ClassTimeUtil;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 新考勤规则创建服务实现类
 */
@Slf4j
@Service
public class RuleCreationServiceImpl implements RuleCreationService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private PunchConfigAdapter punchConfigAdapter;
    @Resource
    private PunchClassConfigAdapter punchClassConfigAdapter;
    @Resource
    private PunchClassItemConfigAdapter punchClassItemConfigAdapter;
    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigAdapter reissueCardConfigAdapter;
    @Resource
    private AttendanceRuleTypeConverter attendanceRuleTypeConverter;
    @Resource
    private MappingRuleConfigService mappingRuleConfigService;
    @Resource
    private MappingPunchClassConfigService mappingPunchClassConfigService;
    @Resource
    private MappingPunchClassConfigItemService mappingPunchClassConfigItemService;


    @Override
    public PunchConfigDO createPunchConfig(HrAttendanceGroupDTO hrGroup, boolean isDefault) {
        log.info("创建打卡规则, hrGroupId: {}, hrGroupName: {}, isDefault: {}",
                hrGroup.getId(), hrGroup.getPunchConfigName(), isDefault);

        //创建新的打卡规则
        PunchConfigDO punchConfig = new PunchConfigDO();
        BaseDOUtil.fillDOInsertByUsrOrSystem(punchConfig);
        punchConfig.setId(defaultIdWorker.nextId());
        punchConfig.setCountry(hrGroup.getCountry());
        punchConfig.setConfigNo(defaultIdWorker.nextPunchConfigNo());
        punchConfig.setConfigName(hrGroup.getPunchConfigName() + ":" + "打卡规则");
        punchConfig.setConfigType(attendanceRuleTypeConverter.convertPunchConfigType(hrGroup.getPunchConfigType()));
        punchConfig.setPunchTimeInterval(hrGroup.getFreeWorkTwicePunchTimeInterval());
        punchConfig.setIsCountryLevel(BusinessConstant.N);
        // 如果为默认考勤组的非免打卡人员，其创建的规则没有部门（这些人是其他规则删除人员或部门添加到默认规则的，是人员级别，无部门映射）
        punchConfig.setDeptIds(hrGroup.isDefaultGroup() ? null : hrGroup.getDeptIds());
        punchConfig.setEffectTime(hrGroup.getEffectTime());
        punchConfig.setExpireTime(hrGroup.getExpireTime());

        // 设置时间戳
        String timeZone = hrGroup.getTimeZone();
        punchConfig.setEffectTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, punchConfig.getEffectTime()));
        punchConfig.setExpireTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, punchConfig.getExpireTime()));

        punchConfig.setIsFromHrHistoryConfig(BusinessConstant.Y);
        //考勤组创建的规则默认为停用
        punchConfig.setStatus(StatusEnum.DISABLED.getCode());
        //非最新处理为非最新
        if (Objects.equals(hrGroup.getIsLatest(), BusinessConstant.N)) {
            punchConfig.setIsLatest(BusinessConstant.N);
        } else {
            punchConfig.setIsLatest(BusinessConstant.Y);
        }

//        boolean saved = punchConfigDao.save(punchConfig);
        boolean saved = punchConfigAdapter.saveOneWithResult(punchConfig);

        if (saved) {
            log.info("创建打卡规则成功, configId: {}, configNo: {}, hrGroupId: {}",
                    punchConfig.getId(), punchConfig.getConfigNo(), hrGroup.getId());
            // 规则的映射记录
            mappingRuleConfigService.createMapping(
                    hrGroup,
                    RuleConfigTypeEnum.PUNCH_CONFIG.name(),
                    punchConfig.getId()
            );
            return punchConfig;
        } else {
            log.error("创建打卡规则失败, hrGroupId: {}", hrGroup.getId());
            return null;
        }
    }

    @Override
    public PunchClassConfigDO createSinglePunchClassConfig(HrAttendanceGroupDTO hrGroup, HrAttendanceClassConfigDTO classConfig) {
        log.info("创建单个班次规则配置, hrGroupId: {}, hrGroupName: {}, classId: {}, className: {}",
                hrGroup.getId(), hrGroup.getPunchConfigName(), classConfig.getId(), classConfig.getClassName());

        try {
            // 创建班次配置DO对象
            PunchClassConfigDO punchClassConfigDO = new PunchClassConfigDO();
            BaseDOUtil.fillDOInsertByUsrOrSystem(punchClassConfigDO);

            // 设置基础信息
            punchClassConfigDO.setId(defaultIdWorker.nextId());
            punchClassConfigDO.setConfigNo(defaultIdWorker.nextPunchClassConfigNo());
            punchClassConfigDO.setClassName(hrGroup.getPunchConfigName() + ":" + classConfig.getClassNameForAttendance());
            punchClassConfigDO.setClassType(classConfig.getClassType());
            punchClassConfigDO.setCountry(hrGroup.getCountry());

            // 设置班次性质（固定班次）
            punchClassConfigDO.setClassNature(attendanceRuleTypeConverter.convertToClassNature(hrGroup.getPunchConfigType()));

            // 设置工作时长信息
            punchClassConfigDO.setLegalWorkingHours(classConfig.getLegalWorkingHours());
            punchClassConfigDO.setAttendanceHours(classConfig.getAttendanceHours());

            // 设置时段数（根据班次时间配置列表确定）
            List<HrAttendanceClassItemConfigDTO> itemList = classConfig.getItemList();
            punchClassConfigDO.setItemNum(CollectionUtils.isNotEmpty(itemList) ? itemList.size() : 1);

            // 设置部门
            punchClassConfigDO.setDeptIds(hrGroup.getDeptIds());

            punchClassConfigDO.setIsCountryLevel(BusinessConstant.N);
            punchClassConfigDO.setIsFromHrHistoryConfig(BusinessConstant.Y);

            //考勤组创建的规则默认为停用
            punchClassConfigDO.setStatus(StatusEnum.DISABLED.getCode());
            //非最新处理为非最新
            if (Objects.equals(hrGroup.getIsLatest(), BusinessConstant.N)) {
                punchClassConfigDO.setIsLatest(BusinessConstant.N);
            } else {
                punchClassConfigDO.setIsLatest(BusinessConstant.Y);
            }

            // 保存班次配置
            punchClassConfigAdapter.saveOne(punchClassConfigDO);

            log.info("单个班次规则配置创建成功, configId: {}, configNo: {}, className: {}",
                    punchClassConfigDO.getId(), punchClassConfigDO.getConfigNo(), punchClassConfigDO.getClassName());

            // 创建班次时间规则
            createPunchClassItemConfigs(punchClassConfigDO, hrGroup, classConfig, itemList);

            // 创建班次配置映射关系
            createPunchClassConfigMapping(hrGroup, classConfig, punchClassConfigDO);

            return punchClassConfigDO;

        } catch (Exception e) {
            log.error("创建单个班次规则配置失败, hrGroupId: {}, classId: {}",
                    hrGroup.getId(), classConfig.getId(), e);
            throw e;
        }
    }

    /**
     * 创建班次时间规则配置
     * 将HR班次时间配置转换为新的班次时间规则，并创建映射关系
     *
     * @param punchClassConfig 班次配置
     * @param hrGroup HR考勤组
     * @param hrClassConfig HR班次配置
     * @param hrItemList HR班次时间配置列表
     */
    private void createPunchClassItemConfigs(PunchClassConfigDO punchClassConfig,
                                             HrAttendanceGroupDTO hrGroup,
                                             HrAttendanceClassConfigDTO hrClassConfig,
                                             List<HrAttendanceClassItemConfigDTO> hrItemList) {
        log.info("创建班次时间规则, classConfigId: {}, itemCount: {}",
                punchClassConfig.getId(), CollectionUtils.size(hrItemList));

        if (CollectionUtils.isEmpty(hrItemList)) {
            log.warn("HR班次时间配置列表为空, classConfigId: {}", punchClassConfig.getId());
            return;
        }

        try {
            List<PunchClassItemConfigDO> itemConfigList = new ArrayList<>();
            List<MappingPunchClassConfigItemDO> mappingList = new ArrayList<>();

            // 遍历HR班次时间配置，转换为新的班次时间规则
            for (HrAttendanceClassItemConfigDTO hrItem : hrItemList) {
                PunchClassItemConfigDO itemConfigDO = convertToPunchClassItemConfig(punchClassConfig, hrItem);
                if (itemConfigDO != null) {
                    itemConfigList.add(itemConfigDO);

                    // 创建班次时间配置映射关系
                    MappingPunchClassConfigItemDO mappingDO = mappingPunchClassConfigItemService.buildMapping(
                            hrGroup, hrClassConfig.getId(), hrItem, punchClassConfig.getId(), itemConfigDO.getId());
                    if (mappingDO != null) {
                        mappingList.add(mappingDO);
                    }
                }
            }

            // 批量保存班次时间规则
            if (CollectionUtils.isNotEmpty(itemConfigList)) {
                punchClassItemConfigAdapter.saveBatch(itemConfigList);
                log.info("班次时间规则创建成功, classConfigId: {}, 保存数量: {}",
                        punchClassConfig.getId(), itemConfigList.size());
            }

            // 批量保存班次时间配置映射关系
            if (CollectionUtils.isNotEmpty(mappingList)) {
                mappingPunchClassConfigItemService.batchSave(mappingList);
                log.info("班次时间配置映射关系创建成功, classConfigId: {}, 映射数量: {}",
                        punchClassConfig.getId(), mappingList.size());
            }

        } catch (Exception e) {
            log.error("创建班次时间规则失败, classConfigId: {}", punchClassConfig.getId(), e);
            throw e;
        }
    }

    /**
     * 将HR班次时间配置转换为新的班次时间规则
     *
     * @param punchClassConfig 班次配置
     * @param hrItem HR班次时间配置
     * @return 班次时间规则配置
     */
    private PunchClassItemConfigDO convertToPunchClassItemConfig(PunchClassConfigDO punchClassConfig,
                                                                 HrAttendanceClassItemConfigDTO hrItem) {
        PunchClassItemConfigDO itemConfigDO = new PunchClassItemConfigDO();
        BaseDOUtil.fillDOInsertByUsrOrSystem(itemConfigDO);

        // 设置基础信息
        itemConfigDO.setId(defaultIdWorker.nextId());
        itemConfigDO.setPunchClassId(punchClassConfig.getId());
        itemConfigDO.setSortNo(hrItem.getSortNo());

        // 设置打卡时间信息
        itemConfigDO.setPunchInTime(hrItem.getPunchInTime());
        itemConfigDO.setPunchOutTime(hrItem.getPunchOutTime());
        itemConfigDO.setEarliestPunchInTime(hrItem.getEarliestPunchInTime());
        itemConfigDO.setLatestPunchInTime(hrItem.getLatestPunchInTime());
        itemConfigDO.setLatestPunchOutTime(hrItem.getLatestPunchOutTime());

        // 设置跨天信息
        itemConfigDO.setIsAcross(hrItem.getIsAcross());

        // 设置弹性时间和工作时长
        itemConfigDO.setElasticTime(hrItem.getElasticTime());

        // 计算出勤时长和法定工作时长
        Pair<BigDecimal, BigDecimal> workingHours = ClassTimeUtil.calculateWorkingHours(hrItem);
        // 上下班时间为空，代表为灵活打卡两次的班次，直接使用班次的出勤和工作时长作为唯一的时间配置的时长
        if (workingHours.getLeft().compareTo(BigDecimal.ZERO) == 0 &&
                workingHours.getRight().compareTo(BigDecimal.ZERO) == 0) {
            itemConfigDO.setAttendanceHours(punchClassConfig.getAttendanceHours());
            itemConfigDO.setLegalWorkingHours(punchClassConfig.getLegalWorkingHours());
        } else {
            itemConfigDO.setAttendanceHours(workingHours.getLeft());      // 出勤时长
            itemConfigDO.setLegalWorkingHours(workingHours.getRight());   // 法定工作时长
        }

        // 设置休息时间
        itemConfigDO.setRestStartTime(hrItem.getRestStartTime());
        itemConfigDO.setRestEndTime(hrItem.getRestEndTime());

        // 考勤组创建的规则默认为停用
        itemConfigDO.setStatus(StatusEnum.DISABLED.getCode());
        //非最新处理为非最新
        if (Objects.equals(hrItem.getIsLatest(), BusinessConstant.N)) {
            itemConfigDO.setIsLatest(BusinessConstant.N);
        } else {
            itemConfigDO.setIsLatest(BusinessConstant.Y);
        }

        return itemConfigDO;
    }

    /**
     * 创建班次配置映射关系
     * 将HR班次配置映射到新的班次配置
     *
     * @param hrGroup HR考勤组
     * @param hrClassConfig HR班次配置
     * @param punchClassConfigDO 新班次配置
     */
    private void createPunchClassConfigMapping(HrAttendanceGroupDTO hrGroup,
                                               HrAttendanceClassConfigDTO hrClassConfig,
                                               PunchClassConfigDO punchClassConfigDO) {
        log.info("创建班次配置映射关系, hrGroupId: {}, hrClassId: {}, punchClassConfigId: {}",
                hrGroup.getId(), hrClassConfig.getId(), punchClassConfigDO.getId());

        try {
            // 创建班次配置映射关系
            MappingPunchClassConfigDO mappingDO = mappingPunchClassConfigService.buildMapping(
                    hrGroup, hrClassConfig, punchClassConfigDO);

            if (mappingDO != null) {
                mappingPunchClassConfigService.save(mappingDO);
                log.info("班次配置映射关系创建成功, hrClassId: {}, punchClassConfigId: {}",
                        hrClassConfig.getId(), punchClassConfigDO);
            }

        } catch (Exception e) {
            log.error("创建班次配置映射关系失败, hrClassId: {}, punchClassConfigId: {}",
                    hrClassConfig.getId(), punchClassConfigDO, e);
            throw e;
        }
    }

    @Override
    public PunchConfigDO createNoPunchConfig(HrAttendanceGroupDTO defaultGroup) {
        log.info("创建免打卡规则, hrGroupName: {}", defaultGroup.getPunchConfigName());

        //创建新的免打卡规则
        PunchConfigDO noPunchConfig = new PunchConfigDO();
        BaseDOUtil.fillDOInsertByUsrOrSystem(noPunchConfig);
        noPunchConfig.setId(defaultIdWorker.nextId());
        noPunchConfig.setCountry(defaultGroup.getCountry());
        noPunchConfig.setConfigNo(defaultIdWorker.nextPunchConfigNo());
        noPunchConfig.setConfigName(defaultGroup.getPunchConfigName() + ":免打卡规则");
        noPunchConfig.setConfigType(attendanceRuleTypeConverter.getNoPunchConfigType());
        noPunchConfig.setPunchTimeInterval(null);
        noPunchConfig.setIsCountryLevel(BusinessConstant.N);
        noPunchConfig.setIsFromHrHistoryConfig(BusinessConstant.Y);
        //考勤组创建的规则默认为停用
        noPunchConfig.setStatus(StatusEnum.DISABLED.getCode());
        //非最新处理为非最新
        if (Objects.equals(defaultGroup.getIsLatest(), BusinessConstant.N)) {
            noPunchConfig.setIsLatest(BusinessConstant.N);
        } else {
            noPunchConfig.setIsLatest(BusinessConstant.Y);
        }
        noPunchConfig.setEffectTime(defaultGroup.getEffectTime());
        noPunchConfig.setExpireTime(defaultGroup.getExpireTime());
        //设置部门
        noPunchConfig.setDeptIds(defaultGroup.getDeptIds());

        // 设置生效失效的时间戳
        String timeZone = defaultGroup.getTimeZone();
        Long effectTimestamp = CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, noPunchConfig.getEffectTime());
        Long expireTimestamp = CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, noPunchConfig.getExpireTime());
        noPunchConfig.setEffectTimestamp(effectTimestamp);
        noPunchConfig.setExpireTimestamp(expireTimestamp);

//        boolean saved = punchConfigDao.save(noPunchConfig);
        boolean saved = punchConfigAdapter.saveOneWithResult(noPunchConfig);

        if (saved) {
            log.info("创建免打卡规则成功, configId: {}, configNo: {}, hrGroupId: {}",
                    noPunchConfig.getId(), noPunchConfig.getConfigNo(), defaultGroup.getId());
            // 规则的映射记录
            mappingRuleConfigService.createMapping(
                    defaultGroup,
                    RuleConfigTypeEnum.PUNCH_CONFIG.name(),
                    noPunchConfig.getId()
            );
            return noPunchConfig;
        } else {
            log.error("创建免打卡规则失败, hrGroupId: {}", defaultGroup.getId());
            return null;
        }
    }

    @Override
    public ReissueCardConfigDO createReissueCardConfig(String country, Integer maxRepunchNumber) {
        log.info("创建补卡规则（迁移用）, country: {}, maxRepunchNumber: {}", country, maxRepunchNumber);

        try {
            // 创建新的补卡规则
            ReissueCardConfigDO reissueCardConfig = new ReissueCardConfigDO();
            reissueCardConfig.setId(defaultIdWorker.nextId());
            reissueCardConfig.setCountry(country);
            reissueCardConfig.setConfigNo(defaultIdWorker.nextReissueCardConfigNo());
            reissueCardConfig.setConfigName(country + "国家级补卡规则（迁移生成）");
            reissueCardConfig.setMaxRepunchNumber(maxRepunchNumber);
            reissueCardConfig.setIsCountryLevel(BusinessConstant.Y);
            reissueCardConfig.setDeptIds(null); // 国家级规则不设置部门
            //补卡规则处理为启用和最新的，不同于打卡和班次
            reissueCardConfig.setStatus(StatusEnum.ACTIVE.getCode());
            reissueCardConfig.setIsLatest(BusinessConstant.Y);

            // 设置生效时间：从2023-01-01 00:00:00开始
            Date effectTime = DateUtil.parse("2023-01-01 00:00:00", DateConvertUtils.FORMAT_DATE_TIME);
            reissueCardConfig.setEffectTime(effectTime);
            reissueCardConfig.setExpireTime(BusinessConstant.DEFAULT_END_TIME);

            // 设置时间戳（使用默认时区）
            reissueCardConfig.setEffectTimestamp(effectTime.getTime());
            reissueCardConfig.setExpireTimestamp(BusinessConstant.DEFAULT_END_TIMESTAMP);

            // 设置创建信息
            BaseDOUtil.fillDOInsertByUsrOrSystem(reissueCardConfig);

            // 保存补卡规则（使用适配器）
            boolean saved = reissueCardConfigAdapter.saveOneWithResult(reissueCardConfig);

            if (saved) {
                log.info("创建补卡规则成功, configId: {}, configNo: {}, country: {}",
                        reissueCardConfig.getId(), reissueCardConfig.getConfigNo(), country);
                return reissueCardConfig;
            } else {
                log.error("创建补卡规则失败, country: {}", country);
                return null;
            }
        } catch (Exception e) {
            log.error("创建补卡规则异常, country: {}, maxRepunchNumber: {}", country, maxRepunchNumber, e);
            return null;
        }
    }

    @Override
    public ReissueCardConfigDO createAttendanceGroupReissueCardConfig(HrAttendanceGroupDTO attendanceGroup) {
        log.info("创建考勤组级别的补卡规则, groupId: {}, groupName: {}, maxRepunchNumber: {}",
                attendanceGroup.getId(), attendanceGroup.getPunchConfigName(), attendanceGroup.getMaxRepunchNumber());

        try {
            // 参数验证
            if (attendanceGroup.getMaxRepunchNumber() == null || attendanceGroup.getMaxRepunchNumber() <= 0) {
                log.warn("考勤组最大补卡次数无效，跳过创建补卡规则, groupId: {}, maxRepunchNumber: {}",
                        attendanceGroup.getId(), attendanceGroup.getMaxRepunchNumber());
                return null;
            }

            // 创建新的补卡规则
            ReissueCardConfigDO reissueCardConfig = new ReissueCardConfigDO();
            BaseDOUtil.fillDOInsertByUsrOrSystem(reissueCardConfig);
            reissueCardConfig.setId(defaultIdWorker.nextId());
            reissueCardConfig.setCountry(attendanceGroup.getCountry());
            reissueCardConfig.setConfigNo(defaultIdWorker.nextReissueCardConfigNo());
            reissueCardConfig.setConfigName(attendanceGroup.getPunchConfigName() + ":补卡规则");
            reissueCardConfig.setMaxRepunchNumber(attendanceGroup.getMaxRepunchNumber().intValue());
            reissueCardConfig.setIsCountryLevel(BusinessConstant.N); // 考勤组级别，非国家级
            reissueCardConfig.setDeptIds(attendanceGroup.getDeptIds());
            // 补卡规则来自考勤组
            reissueCardConfig.setIsFromHrHistoryConfig(BusinessConstant.Y);

            // 考勤组创建的规则默认为停用
            reissueCardConfig.setStatus(StatusEnum.DISABLED.getCode());
            // 非最新处理为非最新
            if (Objects.equals(attendanceGroup.getIsLatest(), BusinessConstant.N)) {
                reissueCardConfig.setIsLatest(BusinessConstant.N);
            } else {
                reissueCardConfig.setIsLatest(BusinessConstant.Y);
            }

            // 设置生效时间，使用考勤组的时间
            reissueCardConfig.setEffectTime(attendanceGroup.getEffectTime());
            reissueCardConfig.setExpireTime(attendanceGroup.getExpireTime());

            // 设置时间戳
            String timeZone = attendanceGroup.getTimeZone();
            reissueCardConfig.setEffectTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, attendanceGroup.getEffectTime()));
            reissueCardConfig.setExpireTimestamp(CommonUtil.restoreOriginalDateAndGetTimeStamp(timeZone, attendanceGroup.getExpireTime()));

            // 保存补卡规则（使用适配器）
            boolean saved = reissueCardConfigAdapter.saveOneWithResult(reissueCardConfig);

            if (saved) {
                log.info("创建考勤组级别的补卡规则成功, configId: {}, configNo: {}, groupId: {}",
                        reissueCardConfig.getId(), reissueCardConfig.getConfigNo(), attendanceGroup.getId());

                // 创建映射记录
                mappingRuleConfigService.createMapping(
                        attendanceGroup,
                        RuleConfigTypeEnum.REISSUE_CARD_CONFIG.name(),
                        reissueCardConfig.getId()
                );

                return reissueCardConfig;
            } else {
                log.error("保存考勤组级别的补卡规则失败, groupId: {}", attendanceGroup.getId());
                return null;
            }

        } catch (Exception e) {
            log.error("创建考勤组级别的补卡规则失败, groupId: {}, groupName: {}",
                    attendanceGroup.getId(), attendanceGroup.getPunchConfigName(), e);
            return null;
        }
    }

}
