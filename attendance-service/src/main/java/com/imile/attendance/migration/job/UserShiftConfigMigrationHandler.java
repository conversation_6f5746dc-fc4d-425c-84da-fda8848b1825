package com.imile.attendance.migration.job;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.migration.dto.UserShiftConfigMigrationParam;
import com.imile.attendance.migration.impl.UserShiftConfigMigrationServiceImpl;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 排班数据迁移XXL-Job任务处理器
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Component
public class UserShiftConfigMigrationHandler {
    
    @Resource
    private UserShiftConfigMigrationServiceImpl migrationService;
    
    /**
     * 排班数据迁移任务
     * 
     * @param param 任务参数JSON字符串，包含以下参数：
     *              <ul>
     *                <li>country: 国家代码（必填）</li>
     *                <li>migrationType: 迁移类型（可选：FULL-全量迁移，HISTORY-仅历史数据，CURRENT-仅当前数据）</li>
     *                <li>startDayId: 开始日期ID（可选，格式：yyyyMMdd）</li>
     *                <li>endDayId: 结束日期ID（可选，格式：yyyyMMdd）</li>
     *                <li>forceExecute: 是否强制执行（可选，默认false）</li>
     *                <li>skipValidation: 是否跳过数据验证（可选，默认false）</li>
     *              </ul>
     * @return ReturnT<String> XXL-Job执行结果
     */
    @XxlJob(BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER)
    public ReturnT<String> userShiftConfigMigrationHandler(String param) {
        XxlJobLogger.log("XXL-JOB, {} Start. The Param: {}", 
                BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, param);
        
        try {
            // 解析参数
            if (StringUtils.isBlank(param)) {
                XxlJobLogger.log("XXL-JOB, {} 参数为空", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER);
                return ReturnT.FAIL;
            }
            
            UserShiftConfigMigrationParam migrationParam;
            try {
                migrationParam = JSON.parseObject(param, UserShiftConfigMigrationParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("XXL-JOB, {} 参数解析失败: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, e.getMessage());
                return ReturnT.FAIL;
            }
            
            // 验证参数
            if (!migrationParam.isValid()) {
                XxlJobLogger.log("XXL-JOB, {} 参数验证失败: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, 
                        migrationParam.toString());
                return ReturnT.FAIL;
            }
            
            XxlJobLogger.log("XXL-JOB, {} 开始执行迁移任务, 参数: {}", 
                    BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, 
                    migrationParam.toString());
            
            // 根据迁移类型执行不同的迁移逻辑
            Boolean result = false;
            switch (migrationParam.getMigrationTypeEnum()) {
                case FULL:
                    result = migrationService.executeFullMigration(migrationParam.getCountry());
                    break;
                case HISTORY:
                    result = migrationService.migrateHistoryDataOnly(
                            migrationParam.getCountry(), null, BusinessConstant.ShiftMigration.HISTORY_END_DAY_ID);
                    break;
                case CURRENT:
                    result = migrationService.migrateCurrentDataOnly(
                            migrationParam.getCountry(), BusinessConstant.ShiftMigration.SPLIT_DAY_ID, null);
                    break;
                default:
                    XxlJobLogger.log("XXL-JOB, {} 不支持的迁移类型: {}", 
                            BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, 
                            migrationParam.getMigrationType());
                    return ReturnT.FAIL;
            }
            
            if (Boolean.TRUE.equals(result)) {
                XxlJobLogger.log("XXL-JOB, {} 迁移任务执行成功, country: {}, type: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, 
                        migrationParam.getCountry(), migrationParam.getMigrationType());
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("XXL-JOB, {} 迁移任务执行失败, country: {}, type: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, 
                        migrationParam.getCountry(), migrationParam.getMigrationType());
                return ReturnT.FAIL;
            }
            
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, {} 迁移任务执行异常: {}", 
                    BusinessConstant.JobHandler.USER_SHIFT_CONFIG_MIGRATION_HANDLER, 
                    e.getMessage());
            log.error("排班数据迁移任务执行异常", e);
            return ReturnT.FAIL;
        }
    }
    
    /**
     * 非灰度用户排班数据迁移任务
     * 
     * @param param 任务参数JSON字符串，包含以下参数：
     *              <ul>
     *                <li>country: 国家代码（必填）</li>
     *                <li>shiftStartDayId: 排班开始日期ID（必填，格式：yyyyMMdd）</li>
     *              </ul>
     * @return ReturnT<String> XXL-Job执行结果
     */
    @XxlJob(BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER)
    public ReturnT<String> userShiftConfigNoGrayscaleMigrationHandler(String param) {
        XxlJobLogger.log("XXL-JOB, {} Start. The Param: {}", 
                BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, param);
        
        try {
            // 解析参数
            if (StringUtils.isBlank(param)) {
                XxlJobLogger.log("XXL-JOB, {} 参数为空", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER);
                return ReturnT.FAIL;
            }
            
            NoGrayscaleMigrationParam migrationParam;
            try {
                migrationParam = JSON.parseObject(param, NoGrayscaleMigrationParam.class);
            } catch (Exception e) {
                XxlJobLogger.log("XXL-JOB, {} 参数解析失败: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, e.getMessage());
                return ReturnT.FAIL;
            }
            
            // 验证参数
            if (!migrationParam.isValid()) {
                XxlJobLogger.log("XXL-JOB, {} 参数验证失败: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, 
                        migrationParam.toString());
                return ReturnT.FAIL;
            }
            
            XxlJobLogger.log("XXL-JOB, {} 开始执行非灰度用户迁移任务, 参数: {}", 
                    BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, 
                    migrationParam.toString());
            
            // 执行非灰度用户迁移
            Boolean result = migrationService.migrateByCountryNoGrayscaleUser(
                    migrationParam.getCountry(), 
                    migrationParam.getShiftStartDayId());
            
            if (Boolean.TRUE.equals(result)) {
                XxlJobLogger.log("XXL-JOB, {} 非灰度用户迁移任务执行成功, country: {}, shiftStartDayId: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, 
                        migrationParam.getCountry(), migrationParam.getShiftStartDayId());
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("XXL-JOB, {} 非灰度用户迁移任务执行失败, country: {}, shiftStartDayId: {}", 
                        BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, 
                        migrationParam.getCountry(), migrationParam.getShiftStartDayId());
                return ReturnT.FAIL;
            }
            
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, {} 非灰度用户迁移任务执行异常: {}", 
                    BusinessConstant.JobHandler.USER_SHIFT_CONFIG_NO_GRAYSCALE_MIGRATION_HANDLER, 
                    e.getMessage());
            log.error("非灰度用户排班数据迁移任务执行异常", e);
            return ReturnT.FAIL;
        }
    }
    
    /**
     * 排班数据迁移回滚任务
     * 
     * @param param 任务参数JSON字符串，包含以下参数：
     *              <ul>
     *                <li>country: 国家代码（必填）</li>
     *                <li>rollbackType: 回滚类型（可选：ALL-全部回滚，HISTORY-仅历史数据，CURRENT-仅当前数据）</li>
     *                <li>startDayId: 开始日期ID（可选）</li>
     *                <li>endDayId: 结束日期ID（可选）</li>
     *              </ul>
     * @return ReturnT<String> XXL-Job执行结果
     */
    @XxlJob(BusinessConstant.JobHandler.USER_SHIFT_CONFIG_ROLLBACK_HANDLER)
    public ReturnT<String> userShiftConfigRollbackHandler(String param) {
        XxlJobLogger.log("XXL-JOB, {} Start. The Param: {}", 
                BusinessConstant.JobHandler.USER_SHIFT_CONFIG_ROLLBACK_HANDLER, param);
        
        try {
            // TODO: 实现回滚逻辑
            // 这里可以实现数据回滚的逻辑，比如删除迁移的数据等
            
            XxlJobLogger.log("XXL-JOB, {} 回滚功能暂未实现", 
                    BusinessConstant.JobHandler.USER_SHIFT_CONFIG_ROLLBACK_HANDLER);
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, {} 回滚任务执行异常: {}", 
                    BusinessConstant.JobHandler.USER_SHIFT_CONFIG_ROLLBACK_HANDLER, 
                    e.getMessage());
            log.error("排班数据回滚任务执行异常", e);
            return ReturnT.FAIL;
        }
    }
    
    /**
     * 非灰度用户迁移任务参数
     */
    @Data
    public static class NoGrayscaleMigrationParam {
        
        /**
         * 国家代码（必填）
         */
        private String country;
        
        /**
         * 排班开始日期ID（必填）
         * 格式：yyyyMMdd
         */
        private Long shiftStartDayId;
        
        /**
         * 验证参数有效性
         */
        public boolean isValid() {
            // 国家代码必填
            if (country == null || country.trim().isEmpty()) {
                return false;
            }
            
            // 排班开始日期ID必填且格式正确
            if (shiftStartDayId == null || shiftStartDayId <= 0) {
                return false;
            }
            
            // 验证日期ID格式（应为8位数字，格式yyyyMMdd）
            String dateStr = String.valueOf(shiftStartDayId);
            if (dateStr.length() != 8) {
                return false;
            }
            
            return true;
        }
        
        @Override
        public String toString() {
            return "NoGrayscaleMigrationParam{" +
                    "country='" + country + '\'' +
                    ", shiftStartDayId=" + shiftStartDayId +
                    '}';
        }
    }
}
