package com.imile.attendance.migration.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description HR考勤组适用范围DTO
 */
@Data
public class HrAttendanceGroupRangeDTO {

    /**
     * 范围ID
     */
    private Long id;

    /**
     * 业务ID 部门id、用户ID
     */
    private Long bizId;

    /**
     * 打卡规则方案ID
     */
    private Long punchConfigId;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 范围类型 DEPT,USER
     */
    private String rangeType;

    /**
     * 是否需要打卡 0:false,1:true
     */
    private Integer isNeedPunch;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 是否为最新
     */
    private Integer isLatest;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 是否需要打卡
     */
    public Boolean needPunch() {
        return isNeedPunch != null && isNeedPunch.equals(1);
    }

    /**
     * 是否免打卡
     */
    public Boolean isNoPunchRequired() {
        return !needPunch();
    }

    /**
     * 是否为部门范围
     */
    public Boolean isDeptRange() {
        return "DEPT".equals(rangeType);
    }

    /**
     * 是否为用户范围
     */
    public Boolean isUserRange() {
        return "USER".equals(rangeType);
    }
}
