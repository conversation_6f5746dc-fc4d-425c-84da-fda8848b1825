package com.imile.attendance.migration.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.context.UserContext;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hrms.dto.UserDynamicInfoRpcDTO;
import com.imile.attendance.hrms.support.RpcUserClientSupport;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/27
 * @Description
 */
@Slf4j
@Service
public class MigrationServiceImpl implements MigrationService {

    @Value("${newAttendance.enable.countries:CHN}")
    private String enableNewAttendanceCountries;
    @Value("${newAttendance.backup.operator.userCodes:}")
    private String backupOperatorUserCodes;

    @Resource
    private AttendanceProperties attendanceProperties;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private RpcUserClientSupport rpcUserClientSupport;


    /**
     * 获取启用新考勤系统的国家列表
     */
    @Override
    public List<String> getEnableNewAttendanceCountry() {
        if (StringUtils.isEmpty(enableNewAttendanceCountries)) {
            log.error("启用新考勤系统的国家配置为空");
            return Collections.emptyList();
        }
        return Arrays.stream(enableNewAttendanceCountries.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllGrayScaleSwitchedCountry() {
        List<String> enabledCountries = getEnableNewAttendanceCountry();
        if (CollectionUtils.isEmpty(enabledCountries)) {
            return Collections.emptyList();
        }
        Set<String> grayscaleCountries = Arrays.stream(attendanceProperties.getAttendance().getGrayscaleCountry().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(grayscaleCountries)) {
            return enabledCountries;
        }
        return enabledCountries.stream()
                .filter(Objects::nonNull)
                .filter(grayscaleCountries::contains)
                .collect(Collectors.toList());
    }

    @Override
    public Set<String> getBackupOperatorUserCodes() {
        if (StringUtils.isBlank(backupOperatorUserCodes)) {
            return Collections.emptySet();
        }
        return Arrays.stream(backupOperatorUserCodes.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    @Override
    public Boolean verifyUserIsEnableAttendanceForCountry(Long userId) {
        if (null == userId) {
            return false;
        }
        // 查询用户信息
        UserDynamicInfoRpcDTO userDynamicInfoRpcDTO = rpcUserClientSupport.getUserDynamicInfoById(userId);
        if (null == userDynamicInfoRpcDTO) {
            log.warn("用户信息不存在, userId: {}", userId);
            return false;
        }
        String locationCountry = userDynamicInfoRpcDTO.getLocationCountry();
        log.info("verifyUserIsEnableAttendanceForCountry | locationCountry: {},userId: {}", locationCountry, userId);
        return StringUtils.isNotBlank(locationCountry) &&
                getEnableNewAttendanceCountry().contains(locationCountry);
    }

    /**
     * 获取当前用户编码
     */
    @Override
    public String getCurrentUserCode() {
        UserContext userContext = RequestInfoHolder.getLoginInfo();
        if (null == userContext) {
            log.debug("获取当前用户上下文为空");
            return null;
        }
        String userCode = userContext.getUserCode();
        log.debug("获取当前用户编码: {}", userCode);
        return userCode;
    }

    /**
     * 验证用户是否启用新考勤系统
     */
    @Override
    public Boolean verifyUserIsEnableNewAttendance() {
        String currentUserCode = getCurrentUserCode();
        if (StringUtils.isBlank(currentUserCode)) {
            log.info("verifyUserIsEnableNewAttendance | 当前用户编码为空");
            return false;
        }

        // 查询用户信息（使用缓存）
        UserDynamicInfoRpcDTO userDynamicInfoRpcDTO = rpcUserClientSupport.getUserDynamicInfoByCode(currentUserCode);
        if (null == userDynamicInfoRpcDTO) {
            log.info("verifyUserIsEnableNewAttendance | 用户信息不存在, userCode: {}", currentUserCode);
            return false;
        }

        return verifyUserEnableNewAttendanceByUserInfo(userDynamicInfoRpcDTO);
    }

    /**
     * 验证指定用户是否启用新考勤系统
     *
     * @param userId 用户ID
     * @return 是否启用新考勤系统
     */
    @Override
    public Boolean verifyUserIsEnableNewAttendance(Long userId) {
        if (null == userId) {
            log.debug("用户ID为空");
            return false;
        }

        try {
            // 查询用户信息
            UserDynamicInfoRpcDTO userDynamicInfoRpcDTO = rpcUserClientSupport.getUserDynamicInfoById(userId);

            if (null == userDynamicInfoRpcDTO) {
                log.warn("用户信息不存在, userId: {}", userId);
                return false;
            }

            return verifyUserEnableNewAttendanceByUserInfo(userDynamicInfoRpcDTO);
        } catch (Exception e) {
            log.error("验证用户是否启用新考勤系统异常, userId: {}", userId, e);
            return false;
        }
    }

    /**
     * 批量验证用户是否启用新考勤系统
     *
     * @param userIds 用户ID列表
     * @return Map结构，key为用户ID，value为是否启用新考勤系统
     */
    @Override
    public Map<Long, Boolean> verifyUsersIsEnableNewAttendance(List<Long> userIds) {
        Map<Long, Boolean> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(userIds)) {
            log.debug("用户ID列表为空");
            return resultMap;
        }

        try {
            // 批量查询用户信息
            List<UserDynamicInfoRpcDTO> userDynamicInfoRpcDTOList = rpcUserClientSupport.listUserDynamicInfoById(userIds);
            if (CollectionUtils.isEmpty(userDynamicInfoRpcDTOList)) {
                log.warn("批量查询用户信息为空, userIds: {}", userIds);
                // 对于查询不到的用户，设置为false
                userIds.forEach(userId -> resultMap.put(userId, false));
                return resultMap;
            }

            // 将用户信息转换为Map，便于后续处理
            Map<Long, UserDynamicInfoRpcDTO> userMap = userDynamicInfoRpcDTOList.stream()
                    .collect(Collectors.toMap(UserDynamicInfoRpcDTO::getId, Function.identity()));

            // 逐个验证用户是否启用新考勤系统
            for (Long userId : userIds) {
                UserDynamicInfoRpcDTO userDynamicInfoRpcDTO = userMap.get(userId);
                if (null == userDynamicInfoRpcDTO) {
                    log.warn("用户信息不存在, userId: {}", userId);
                    resultMap.put(userId, false);
                } else {
                    Boolean isEnabled = verifyUserEnableNewAttendanceByUserInfo(userDynamicInfoRpcDTO);
                    resultMap.put(userId, isEnabled);
                }
            }

            log.debug("批量验证用户新考勤系统启用状态完成, 总数: {}, 启用数: {}",
                    userIds.size(),
                    resultMap.values().stream().mapToInt(enabled -> enabled ? 1 : 0).sum());

        } catch (Exception e) {
            log.error("批量验证用户是否启用新考勤系统异常, userIds: {}", userIds, e);
            // 异常情况下，所有用户都设置为false
            userIds.forEach(userId -> resultMap.put(userId, false));
        }

        return resultMap;
    }

    @Override
    public List<String> getWareHouseCountry() {
        AttendanceProperties.Attendance attendance = attendanceProperties.getAttendance();
        if (StringUtils.isEmpty(attendance.getWareHouseCountry())) {
            log.error("新考勤仓内推广国家配置为空");
            return Collections.emptyList();
        }
        return Arrays.stream(attendance.getWareHouseCountry().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getAttendanceOSCountry() {
        AttendanceProperties.Attendance attendance = attendanceProperties.getAttendance();
        if (StringUtils.isEmpty(attendance.getOsCountry())) {
            log.error("新考勤劳务派遣管理国家配置为空");
            return Collections.emptyList();
        }
        return Arrays.stream(attendance.getOsCountry().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    /**
     * 根据用户信息验证是否启用新考勤系统的核心逻辑
     * 通过灰度配置判断：用户编码白名单 > 部门白名单 > 国家白名单
     *
     * @param userDynamicInfoRpcDTO 用户信息
     * @return 是否启用新考勤系统
     */
    private Boolean verifyUserEnableNewAttendanceByUserInfo(UserDynamicInfoRpcDTO userDynamicInfoRpcDTO) {
        if (null == userDynamicInfoRpcDTO) {
            return false;
        }

        AttendanceProperties.Attendance attendance = attendanceProperties.getAttendance();

        // 优先级1：检查用户编码白名单
        if (StringUtils.isNotBlank(attendance.getGrayscaleUserCode())) {
            Set<String> userCodeSet = Arrays.stream(attendance.getGrayscaleUserCode().split(BusinessConstant.DEFAULT_DELIMITER))
                    .collect(Collectors.toSet());
            if (userCodeSet.contains(userDynamicInfoRpcDTO.getUserCode())) {
                log.info("verifyUserEnableNewAttendanceByUserInfo | 用户在用户编码白名单中, userId: {}, userCode: {}",
                        userDynamicInfoRpcDTO.getId(), userDynamicInfoRpcDTO.getUserCode());
                return true;
            }
        }

        // 优先级2：检查部门白名单
        if (StringUtils.isNotBlank(attendance.getGrayscaleDept()) && null != userDynamicInfoRpcDTO.getDeptCode()) {
            Set<String> deptSet = Arrays.stream(attendance.getGrayscaleDept().split(BusinessConstant.DEFAULT_DELIMITER))
                    .collect(Collectors.toSet());
            if (deptSet.contains(userDynamicInfoRpcDTO.getDeptCode())) {
                log.info("verifyUserEnableNewAttendanceByUserInfo | 用户在部门白名单中, userId: {}, deptId: {}",
                        userDynamicInfoRpcDTO.getId(), userDynamicInfoRpcDTO.getDeptCode());
                return true;
            }
        }

        // 优先级3：检查国家白名单
        if (StringUtils.isNotBlank(attendance.getGrayscaleCountry()) && StringUtils.isNotBlank(userDynamicInfoRpcDTO.getLocationCountry())) {
            Set<String> countrySet = Arrays.stream(attendance.getGrayscaleCountry().split(BusinessConstant.DEFAULT_DELIMITER))
                    .collect(Collectors.toSet());
            if (countrySet.contains(userDynamicInfoRpcDTO.getLocationCountry())) {
                log.info("verifyUserEnableNewAttendanceByUserInfo | 用户在国家白名单中, userId: {}, locationCountry: {}",
                        userDynamicInfoRpcDTO.getId(), userDynamicInfoRpcDTO.getLocationCountry());
                return true;
            }
        }

        log.info("verifyUserEnableNewAttendanceByUserInfo | 用户未在任何灰度白名单中, userId: {}, userCode: {}",
                userDynamicInfoRpcDTO.getId(), userDynamicInfoRpcDTO.getUserCode());
        return false;
    }

    /**
     * 根据国家和入职确认时间查找用户
     *
     * @param locationCountry 国家代码，如'CHN'
     * @param confirmDate     入职确认时间的起始日期
     * @return 符合条件的用户列表
     */
    @Override
    public List<UserEntryInfoDTO> findUsersByCountryAndConfirmDate(String locationCountry, Date confirmDate) {
        // 参数验证
        BusinessLogicException.check(StringUtils.isNotBlank(locationCountry),
                ErrorCodeEnum.PARAM_NOT_NULL.getCode(), "国家代码不能为空");
        BusinessLogicException.check(confirmDate != null,
                ErrorCodeEnum.PARAM_NOT_NULL.getCode(), "入职确认时间不能为空");

        log.info("开始查询用户信息, locationCountry: {}, confirmDate: {}", locationCountry, confirmDate);

        try {
            List<UserEntryInfoDTO> result = userEntryRecordDao.findUsersByCountryAndConfirmDate(locationCountry, confirmDate);
            log.info("查询用户信息完成, locationCountry: {}, confirmDate: {}, 结果数量: {}",
                    locationCountry, confirmDate, result.size());
            return result;
        } catch (Exception e) {
            log.error("查询用户信息异常, locationCountry: {}, confirmDate: {}", locationCountry, confirmDate, e);
            throw new RuntimeException("查询用户信息失败", e);
        }
    }

}
