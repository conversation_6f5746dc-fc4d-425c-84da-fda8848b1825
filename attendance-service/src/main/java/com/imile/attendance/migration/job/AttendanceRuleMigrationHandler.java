package com.imile.attendance.migration.job;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.migration.RuleMigrationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * HR考勤组到新考勤规则迁移定时任务处理器
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Slf4j
@Component
public class AttendanceRuleMigrationHandler {

    @Resource
    private RuleMigrationService ruleMigrationService;

    /**
     * HR考勤组到新考勤规则迁移任务
     *
     * @param param 任务参数JSON字符串，包含以下参数：
     *              <ul>
     *                <li>country: 国家代码（必填）</li>
     *              </ul>
     * @return ReturnT<String> XXL-Job执行结果，SUCCESS表示执行成功
     */
    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER)
    public ReturnT<String> attendanceRuleMigrationHandler(String param) {
        XxlJobLogger.log("XXL-JOB, {} Start. The Param: {}", 
                BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER, param);

        try {
            // 解析任务参数
            MigrationParam migrationParam = StringUtils.isNotBlank(param) ?
                    JSON.parseObject(param, MigrationParam.class) :
                    new MigrationParam();

            // 参数校验
            if (StringUtils.isBlank(migrationParam.getCountry())) {
                XxlJobLogger.log("XXL-JOB, {} 参数校验失败: 国家参数不能为空", 
                        BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER);
                return ReturnT.FAIL;
            }

            XxlJobLogger.log("XXL-JOB, {} 开始执行迁移任务, country: {}", 
                    BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER, 
                    migrationParam.getCountry());

            // 执行迁移
            Boolean result = ruleMigrationService.migrateAttendanceRules(migrationParam.getCountry());

            if (Boolean.TRUE.equals(result)) {
                XxlJobLogger.log("XXL-JOB, {} 迁移任务执行成功, country: {}", 
                        BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER, 
                        migrationParam.getCountry());
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("XXL-JOB, {} 迁移任务执行失败, country: {}", 
                        BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER, 
                        migrationParam.getCountry());
                return ReturnT.FAIL;
            }

        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, {} 迁移任务执行异常: {}", 
                    BusinessConstant.JobHandler.ATTENDANCE_RULE_MIGRATION_HANDLER, 
                    e.getMessage());
            log.error("HR考勤组迁移任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 考勤规则回滚任务
     *
     * @param param 任务参数JSON字符串，包含以下参数：
     *              <ul>
     *                <li>country: 国家代码（必填）</li>
     *              </ul>
     * @return ReturnT<String> XXL-Job执行结果，SUCCESS表示执行成功
     */
    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER)
    public ReturnT<String> attendanceRuleRollbackHandler(String param) {
        XxlJobLogger.log("XXL-JOB, {} Start. The Param: {}", 
                BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER, param);

        try {
            // 解析任务参数
            RollbackParam rollbackParam = StringUtils.isNotBlank(param) ?
                    JSON.parseObject(param, RollbackParam.class) :
                    new RollbackParam();

            // 参数校验
            if (StringUtils.isBlank(rollbackParam.getCountry())) {
                XxlJobLogger.log("XXL-JOB, {} 参数校验失败: 国家参数不能为空", 
                        BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER);
                return ReturnT.FAIL;
            }

            XxlJobLogger.log("XXL-JOB, {} 开始执行回滚任务, country: {}", 
                    BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER, 
                    rollbackParam.getCountry());

            // 执行回滚
            Boolean result = ruleMigrationService.rollbackAttendanceRules(rollbackParam.getCountry());

            if (Boolean.TRUE.equals(result)) {
                XxlJobLogger.log("XXL-JOB, {} 回滚任务执行成功, country: {}", 
                        BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER, 
                        rollbackParam.getCountry());
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("XXL-JOB, {} 回滚任务执行失败, country: {}", 
                        BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER, 
                        rollbackParam.getCountry());
                return ReturnT.FAIL;
            }

        } catch (Exception e) {
            XxlJobLogger.log("XXL-JOB, {} 回滚任务执行异常: {}", 
                    BusinessConstant.JobHandler.ATTENDANCE_RULE_ROLLBACK_HANDLER, 
                    e.getMessage());
            log.error("考勤规则回滚任务执行异常", e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 迁移任务参数
     */
    @Data
    public static class MigrationParam {
        /**
         * 国家编码
         */
        private String country;
    }

    /**
     * 回滚任务参数
     */
    @Data
    public static class RollbackParam {
        /**
         * 国家编码
         */
        private String country;
    }
}
