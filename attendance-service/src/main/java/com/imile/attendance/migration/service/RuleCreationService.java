package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 新考勤规则创建服务接口
 */
public interface RuleCreationService {

    /**
     * 创建打卡规则
     * 
     * @param hrGroup HR考勤组
     * @param isDefault 是否为默认考勤组
     * @return 打卡规则
     */
    PunchConfigDO createPunchConfig(HrAttendanceGroupDTO hrGroup, boolean isDefault);


    /**
     * 创建免打卡规则 (一定是默认考勤组)
     *
     * @param defaultGroup HR考勤组
     * @return 免打卡规则
     */
    PunchConfigDO createNoPunchConfig(HrAttendanceGroupDTO defaultGroup);

    /**
     * 创建单个班次规则配置
     *
     * @param hrGroup HR考勤组
     * @param classConfig HR班次配置
     * @return 班次规则配置
     */
    PunchClassConfigDO createSinglePunchClassConfig(HrAttendanceGroupDTO hrGroup, HrAttendanceClassConfigDTO classConfig);

    /**
     * 创建补卡规则（虚拟迁移）
     *
     * @param country 国家代码
     * @param maxRepunchNumber 每月最大补卡次数
     * @return 补卡规则
     */
    ReissueCardConfigDO createReissueCardConfig(String country, Integer maxRepunchNumber);

    /**
     * 创建考勤组级别的补卡规则（考勤组迁移）
     *
     * @param attendanceGroup HR考勤组
     * @return 补卡规则
     */
    ReissueCardConfigDO createAttendanceGroupReissueCardConfig(HrAttendanceGroupDTO attendanceGroup);
}
