package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 补卡配置DO转换器
 * 实现ReissueCardConfigDO和ReissueCardConfigMigrateDO之间的相互转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class ReissueCardConfigDataConverter implements DataConverter<ReissueCardConfigMigrateDO, ReissueCardConfigDO> {

    @Override
    public ReissueCardConfigDO convertFromMigrate(ReissueCardConfigMigrateDO migrateDO) {
        if (migrateDO == null) {
            return null;
        }
        
        log.debug("转换补卡配置: migrate -> real, configId: {}", migrateDO.getId());
        return ReissueCardConfigDOMapstruct.INSTANCE.mapToReal(migrateDO);
    }

    @Override
    public ReissueCardConfigMigrateDO convertFromReal(ReissueCardConfigDO realDO) {
        if (realDO == null) {
            return null;
        }
        
        log.debug("转换补卡配置: real -> migrate, configId: {}", realDO.getId());
        return ReissueCardConfigDOMapstruct.INSTANCE.mapToMigrate(realDO);
    }

    @Override
    public Class<ReissueCardConfigMigrateDO> getMigrateType() {
        return ReissueCardConfigMigrateDO.class;
    }

    @Override
    public Class<ReissueCardConfigDO> getRealType() {
        return ReissueCardConfigDO.class;
    }
}
