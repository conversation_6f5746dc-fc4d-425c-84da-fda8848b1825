package com.imile.attendance.migration;

import com.imile.attendance.infrastructure.repository.employee.dto.UserEntryInfoDTO;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/5/27
 * @Description
 */
public interface MigrationService {

    /**
     * 获取启用新考勤系统的国家列表(用于判断当前灰度的国家范围，不用来判断新老系统之间的切换)
     */
    List<String> getEnableNewAttendanceCountry();

    /**
     * 获取所有已完全切换灰度的国家列表
     */
    List<String> getAllGrayScaleSwitchedCountry();

    /**
     * 获取备份操作员的用户编码列表
     */
    Set<String> getBackupOperatorUserCodes();

    /**
     * 用于判断指定人是否在当前灰度的国家范围，不用来判断新老系统之间的切换
     */
    Boolean verifyUserIsEnableAttendanceForCountry(Long userId);

    /**
     * 验证用户是否启用新考勤系统
     */
    String getCurrentUserCode();

    /**
     * 验证用户是否启用新考勤系统
     */
    Boolean verifyUserIsEnableNewAttendance();

    /**
     * 验证指定用户是否启用新考勤系统
     * @param userId 用户ID
     * @return 是否启用新考勤系统
     */
    Boolean verifyUserIsEnableNewAttendance(Long userId);

    /**
     * 批量验证用户是否启用新考勤系统
     * @param userIds 用户ID列表
     * @return Map结构，key为用户ID，value为是否启用新考勤系统
     */
    Map<Long, Boolean> verifyUsersIsEnableNewAttendance(List<Long> userIds);

    /**
     * 根据国家和入职确认时间查找用户
     *
     * @param locationCountry 国家代码，如'CHN'
     * @param confirmDate 入职确认时间的起始日期
     * @return 符合条件的用户列表
     */
    List<UserEntryInfoDTO> findUsersByCountryAndConfirmDate(String locationCountry, Date confirmDate);


    /**
     * 获取推广仓内的国家列表
     */
    List<String> getWareHouseCountry();

    /**
     * 获取考勤管理范围内管理劳务派遣的国家列表
     */
    List<String> getAttendanceOSCountry();

}
