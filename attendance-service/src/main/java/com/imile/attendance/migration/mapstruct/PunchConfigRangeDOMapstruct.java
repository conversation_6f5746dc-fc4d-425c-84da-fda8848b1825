package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigRangeMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 打卡配置范围DO映射器
 * 负责PunchConfigRangeDO和PunchConfigRangeMigrateDO之间的字段映射
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Mapper
public interface PunchConfigRangeDOMapstruct extends BaseMapstruct {

    PunchConfigRangeDOMapstruct INSTANCE = Mappers.getMapper(PunchConfigRangeDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     */
    default Long setModelId(PunchConfigRangeMigrateDO module) {
        return setModelId(module,
                PunchConfigRangeMigrateDO::getId,
                PunchConfigRangeMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     */
    default Long setModelId(PunchConfigRangeDO module) {
        return setModelId(module,
                PunchConfigRangeDO::getId,
                PunchConfigRangeDO::setId);
    }

    /**
     * 将migrate表DO转换为原始表DO
     * 
     * @param punchConfigRangeMigrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchConfigRangeMigrateDO))")
    PunchConfigRangeDO mapToReal(PunchConfigRangeMigrateDO punchConfigRangeMigrateDO);

    /**
     * 批量将migrate表DO转换为原始表DO
     * 
     * @param punchConfigRangeMigrateDOList migrate表DO列表
     * @return 原始表DO列表
     */
    List<PunchConfigRangeDO> mapToReal(List<PunchConfigRangeMigrateDO> punchConfigRangeMigrateDOList);

    /**
     * 将原始表DO转换为migrate表DO
     * 
     * @param punchConfigRangeDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchConfigRangeDO))")
    PunchConfigRangeMigrateDO mapToMigrate(PunchConfigRangeDO punchConfigRangeDO);

    /**
     * 批量将原始表DO转换为migrate表DO
     * 
     * @param punchConfigRangeDOList 原始表DO列表
     * @return migrate表DO列表
     */
    List<PunchConfigRangeMigrateDO> mapToMigrate(List<PunchConfigRangeDO> punchConfigRangeDOList);
}
