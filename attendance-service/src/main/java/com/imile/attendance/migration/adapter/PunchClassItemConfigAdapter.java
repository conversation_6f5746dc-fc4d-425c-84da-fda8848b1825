package com.imile.attendance.migration.adapter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchClassItemConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassItemConfigMapper;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchClassItemConfigMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassItemConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import com.imile.common.enums.IsDeleteEnum;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 班次时间配置适配器
 * 支持在原始表和migrate表之间透明切换的班次时间配置DAO操作
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchClassItemConfigAdapter extends AbstractPairAdapter<PunchClassItemConfigMigrateDO, PunchClassItemConfigDO> {

    @Resource
    private PunchClassItemConfigMigrateDao punchClassItemConfigMigrateDao;
    @Resource
    private PunchClassItemConfigMigrateMapper punchClassItemConfigMigrateMapper;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private PunchClassItemConfigMapper punchClassItemConfigMapper;

    public PunchClassItemConfigAdapter(List<DataConverter<PunchClassItemConfigMigrateDO, PunchClassItemConfigDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个班次时间配置
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param punchClassItemConfigDO 班次时间配置DO对象
     */
    public void saveOne(PunchClassItemConfigDO punchClassItemConfigDO) {
        log.info("保存班次时间配置, itemId: {}, classId: {}, 当前使用{}表", 
                punchClassItemConfigDO.getId(), punchClassItemConfigDO.getPunchClassId(),
                isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateOneWrapper(
                punchClassItemConfigDO,
                migrateDO -> {
                    // 保存到migrate表
                    punchClassItemConfigMigrateDao.save(migrateDO);
                    log.debug("班次时间配置已保存到migrate表, itemId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    punchClassItemConfigDao.save(realDO);
                    log.debug("班次时间配置已保存到原始表, itemId: {}", realDO.getId());
                }
        );
    }

    /**
     * 批量保存班次时间配置
     * 
     * @param punchClassItemConfigDOList 班次时间配置DO列表
     */
    public void saveBatch(List<PunchClassItemConfigDO> punchClassItemConfigDOList) {
        if (punchClassItemConfigDOList == null || punchClassItemConfigDOList.isEmpty()) {
            log.warn("批量保存班次时间配置: 输入列表为空");
            return;
        }
        
        log.info("批量保存班次时间配置, 数量: {}, 当前使用{}表", 
                punchClassItemConfigDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateBatchWrapper(
                punchClassItemConfigDOList,
                migrateDOList -> {
                    // 批量保存到migrate表
                    punchClassItemConfigMigrateMapper.insertBatchSomeColumn(migrateDOList);
                    log.debug("批量保存班次时间配置到migrate表完成, 数量: {}", migrateDOList.size());
                },
                realDOList -> {
                    // 批量保存到原始表
                    punchClassItemConfigMapper.insertBatchSomeColumn(realDOList);
                    log.debug("批量保存班次时间配置到原始表完成, 数量: {}", realDOList.size());
                }
        );
    }

    /**
     * 根据班次ID列表查询时间配置
     * 
     * @param classIdList 班次ID列表
     * @return 时间配置列表（转换为原始DO类型）
     */
    public List<PunchClassItemConfigDO> selectByClassIds(List<Long> classIdList) {
        log.debug("根据班次ID列表查询时间配置, classIds数量: {}, 当前使用{}表", 
                classIdList != null ? classIdList.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return readBatchWrapper(
                () -> {
                    LambdaQueryWrapper<PunchClassItemConfigMigrateDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(PunchClassItemConfigMigrateDO::getPunchClassId, classIdList);
                    return punchClassItemConfigMigrateDao.list(queryWrapper);
                },
                () -> {
                    LambdaQueryWrapper<PunchClassItemConfigDO> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.in(PunchClassItemConfigDO::getPunchClassId, classIdList);
                    return punchClassItemConfigDao.list(queryWrapper);
                }
        );
    }

    /**
     * 根据班次ID列表查询最新时间配置
     * 
     * @param classIdList 班次ID列表
     * @return 最新时间配置列表（转换为原始DO类型）
     */
    public List<PunchClassItemConfigDO> selectLatestByClassIds(List<Long> classIdList) {
        log.debug("根据班次ID列表查询最新时间配置, classIds数量: {}, 当前使用{}表", 
                classIdList != null ? classIdList.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return readBatchWrapper(
                () -> punchClassItemConfigMigrateDao.selectLatestByClassIds(classIdList),
                () -> punchClassItemConfigDao.selectLatestByClassIds(classIdList)
        );
    }

    /**
     * 根据ID查询班次时间配置
     * 
     * @param id 时间配置ID
     * @return 时间配置（转换为原始DO类型）
     */
    public PunchClassItemConfigDO selectById(Long id) {
        log.debug("根据ID查询班次时间配置, id: {}, 当前使用{}表", 
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return readWrapper(
                () -> punchClassItemConfigMigrateDao.selectById(id),
                () -> punchClassItemConfigDao.selectById(id)
        );
    }

    /**
     * 批量根据ID更新班次时间配置
     *
     * @param classItemConfigDOList 班次时间配置
     * @return 更新是否成功
     */
    public Integer updateBatchById(List<PunchClassItemConfigDO> classItemConfigDOList) {
        if (classItemConfigDOList == null || classItemConfigDOList.isEmpty()) {
            log.warn("批量更新班次配置范围: 输入列表为空");
            return 0;
        }

        log.info("批量根据ID更新班次配置范围, 数量: {}, 当前使用{}表",
                classItemConfigDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并批量更新migrate表
                    List<PunchClassItemConfigMigrateDO> migrateDOList = classItemConfigDOList.stream()
                            .map(realDO -> getConverter().convertFromReal(realDO))
                            .collect(Collectors.toList());
                    Integer result = punchClassItemConfigMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("批量更新班次配置范围到migrate表结果: {}, 数量: {}", result, migrateDOList.size());
                    return result;
                },
                () -> {
                    // 批量更新原始表
                    Integer result = punchClassItemConfigMapper.replaceIntoBatchSomeColumn(classItemConfigDOList);
                    log.debug("批量更新班次配置范围到原始表结果: {}, 数量: {}", result, classItemConfigDOList.size());
                    return result;
                }
        );
    }

    /**
     * 批量物理删除班次时间配置
     *
     * @param ids 班次时间配置ID列表
     * @return 删除是否成功
     */
    public boolean removeBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量物理删除班次时间配置: ID列表为空");
            return true;
        }

        log.info("批量物理删除班次时间配置, 数量: {}, 当前使用{}表",
                ids.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 批量物理删除migrate表记录
                    boolean result = punchClassItemConfigMigrateDao.removeByIds(ids);
                    log.info("班次时间配置从migrate表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                },
                () -> {
                    // 批量物理删除原始表记录
                    boolean result = punchClassItemConfigDao.removeByIds(ids);
                    log.info("班次时间配置从原始表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                }
        );
    }
}
