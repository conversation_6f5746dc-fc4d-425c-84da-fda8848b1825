package com.imile.attendance.migration.api;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.migration.AttendanceMigrationApi;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.migration.dto.BatchUserMigrationVerifyResult;
import com.imile.attendance.migration.dto.UserMigrationVerifyResult;
import com.imile.attendance.migration.param.BatchUserMigrationVerifyParam;
import com.imile.attendance.migration.param.UserMigrationVerifyParam;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR> chen
 * @Date 2025/6/26
 * @Description 考勤迁移验证API实现
 */
@Service(version = "1.0.0")
@Slf4j
public class AttendanceMigrationApiImpl implements AttendanceMigrationApi {

    @Resource
    private MigrationService migrationService;

    @Override
    public RpcResult<UserMigrationVerifyResult> verifyUserIsEnableNewAttendance(UserMigrationVerifyParam param) {
        log.info("verifyUserIsEnableNewAttendance param: {}", param);
        
        // 参数校验
        checkSingleUserParam(param);

        try {
            // 调用业务服务验证用户
            Boolean isEnabled = migrationService.verifyUserIsEnableNewAttendance(param.getUserId());
            
            // 构建返回结果
            UserMigrationVerifyResult result = UserMigrationVerifyResult.builder()
                    .userId(param.getUserId())
                    .isEnabled(isEnabled)
                    .build();

            log.info("verifyUserIsEnableNewAttendance result: userId={}, isEnabled={}", 
                    param.getUserId(), isEnabled);
            
            return RpcResult.ok(result);
        } catch (Exception e) {
            log.error("验证用户是否启用新考勤系统异常, userId: {}", param.getUserId(), e);
            throw BusinessLogicException.getException(ErrorCodeEnum.SERVICE_HANDLE_EXCEPTION);
        }
    }

    @Override
    public RpcResult<BatchUserMigrationVerifyResult> verifyUsersIsEnableNewAttendance(BatchUserMigrationVerifyParam param) {
        log.info("verifyUsersIsEnableNewAttendance param: userIds size={}", 
                CollectionUtils.isEmpty(param.getUserIds()) ? 0 : param.getUserIds().size());
        
        // 参数校验
        checkBatchUserParam(param);

        try {
            // 调用业务服务批量验证用户
            Map<Long, Boolean> verifyResultMap = migrationService.verifyUsersIsEnableNewAttendance(param.getUserIds());
            
            // 构建用户验证结果列表
            List<UserMigrationVerifyResult> userResults = new ArrayList<>();
            int enabledCount = 0;
            int disabledCount = 0;
            
            for (Long userId : param.getUserIds()) {
                Boolean isEnabled = verifyResultMap.getOrDefault(userId, false);
                if (isEnabled) {
                    enabledCount++;
                } else {
                    disabledCount++;
                }
                
                UserMigrationVerifyResult userResult = UserMigrationVerifyResult.builder()
                        .userId(userId)
                        .isEnabled(isEnabled)
                        .build();
                userResults.add(userResult);
            }
            
            // 构建批量验证结果
            BatchUserMigrationVerifyResult result = BatchUserMigrationVerifyResult.builder()
                    .userResults(userResults)
                    .totalCount(param.getUserIds().size())
                    .enabledCount(enabledCount)
                    .disabledCount(disabledCount)
                    .build();

            log.info("verifyUsersIsEnableNewAttendance result: totalCount={}, enabledCount={}, disabledCount={}", 
                    result.getTotalCount(), result.getEnabledCount(), result.getDisabledCount());
            
            return RpcResult.ok(result);
        } catch (Exception e) {
            log.error("批量验证用户是否启用新考勤系统异常, userIds size: {}", param.getUserIds().size(), e);
            throw BusinessLogicException.getException(ErrorCodeEnum.SERVICE_HANDLE_EXCEPTION);
        }
    }

    @Override
    public RpcResult<List<String>> getEnableNewAttendanceCountry() {
        return RpcResult.ok(migrationService.getEnableNewAttendanceCountry());
    }

    @Override
    public RpcResult<Set<String>> getBackupOperatorUserCodes() {
        return RpcResult.ok(migrationService.getBackupOperatorUserCodes());
    }

    /**
     * 校验单用户参数
     * @param param 单用户验证参数
     */
    private void checkSingleUserParam(UserMigrationVerifyParam param) {
        if (Objects.isNull(param)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALIDATE_FAIL);
        }
        
        if (Objects.isNull(param.getUserId())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.MIGRATION_USER_ID_NOT_EMPTY);
        }
    }

    /**
     * 校验批量用户参数
     * @param param 批量用户验证参数
     */
    private void checkBatchUserParam(BatchUserMigrationVerifyParam param) {
        if (Objects.isNull(param)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALIDATE_FAIL);
        }
        
        if (CollectionUtils.isEmpty(param.getUserIds())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.MIGRATION_USER_IDS_NOT_EMPTY);
        }
    }

}
