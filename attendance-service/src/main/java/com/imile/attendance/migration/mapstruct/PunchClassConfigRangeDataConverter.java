package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigRangeMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 班次配置范围DO转换器
 * 实现PunchClassConfigRangeDO和PunchClassConfigRangeMigrateDO之间的相互转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchClassConfigRangeDataConverter implements DataConverter<PunchClassConfigRangeMigrateDO, PunchClassConfigRangeDO> {

    @Override
    public PunchClassConfigRangeDO convertFromMigrate(PunchClassConfigRangeMigrateDO migrateDO) {
        if (migrateDO == null) {
            return null;
        }
        
        log.debug("转换班次配置范围: migrate -> real, rangeId: {}", migrateDO.getId());
        return PunchClassConfigRangeDOMapstruct.INSTANCE.mapToReal(migrateDO);
    }

    @Override
    public PunchClassConfigRangeMigrateDO convertFromReal(PunchClassConfigRangeDO realDO) {
        if (realDO == null) {
            return null;
        }
        
        log.debug("转换班次配置范围: real -> migrate, rangeId: {}", realDO.getId());
        return PunchClassConfigRangeDOMapstruct.INSTANCE.mapToMigrate(realDO);
    }

    @Override
    public Class<PunchClassConfigRangeMigrateDO> getMigrateType() {
        return PunchClassConfigRangeMigrateDO.class;
    }

    @Override
    public Class<PunchClassConfigRangeDO> getRealType() {
        return PunchClassConfigRangeDO.class;
    }
}
