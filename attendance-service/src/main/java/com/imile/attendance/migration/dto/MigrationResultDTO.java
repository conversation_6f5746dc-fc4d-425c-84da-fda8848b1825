package com.imile.attendance.migration.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 迁移结果DTO
 */
@Data
public class MigrationResultDTO {

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 国家
     */
    private String country;

    /**
     * 迁移的HR考勤组数量
     */
    private Integer hrGroupCount;

    /**
     * 创建的打卡规则数量
     */
    private Integer punchConfigCount;

    /**
     * 创建的补卡规则数量
     */
    private Integer reissueConfigCount;

    /**
     * 创建的加班规则数量
     */
    private Integer overtimeConfigCount;

    /**
     * 创建的班次规则数量
     */
    private Integer classConfigCount;

    /**
     * 错误信息列表
     */
    private List<String> errorMessages;

    /**
     * 成功信息列表
     */
    private List<String> successMessages;

    public MigrationResultDTO() {
        this.success = true;
        this.hrGroupCount = 0;
        this.punchConfigCount = 0;
        this.reissueConfigCount = 0;
        this.overtimeConfigCount = 0;
        this.classConfigCount = 0;
        this.errorMessages = new ArrayList<>();
        this.successMessages = new ArrayList<>();
    }

    /**
     * 添加错误信息
     */
    public void addError(String message) {
        this.success = false;
        this.errorMessages.add(message);
    }

    /**
     * 添加成功信息
     */
    public void addSuccess(String message) {
        this.successMessages.add(message);
    }

    /**
     * 增加HR考勤组数量
     */
    public void incrementHrGroupCount() {
        this.hrGroupCount++;
    }

    /**
     * 增加打卡规则数量
     */
    public void incrementPunchConfigCount() {
        this.punchConfigCount++;
    }

    /**
     * 增加补卡规则数量
     */
    public void incrementReissueConfigCount() {
        this.reissueConfigCount++;
    }

    /**
     * 增加加班规则数量
     */
    public void incrementOvertimeConfigCount() {
        this.overtimeConfigCount++;
    }

    /**
     * 增加班次规则数量
     */
    public void incrementClassConfigCount() {
        this.classConfigCount++;
    }
}
