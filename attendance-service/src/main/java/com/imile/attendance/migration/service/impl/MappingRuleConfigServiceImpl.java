package com.imile.attendance.migration.service.impl;

import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingRuleConfigMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.service.MappingRuleConfigService;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则映射服务实现类
 */
@Slf4j
@Service
public class MappingRuleConfigServiceImpl implements MappingRuleConfigService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private MappingRuleConfigDao mappingRuleConfigDao;
    @Resource
    private MappingRuleConfigMapper mappingRuleConfigMapper;

    @Override
    public boolean save(MappingRuleConfigDO mappingRuleConfig) {
        if (mappingRuleConfig == null) {
            log.warn("映射规则配置为空，无法保存");
            return false;
        }

        // 设置基础字段
        BaseDOUtil.fillDOInsertByUsrOrSystem(mappingRuleConfig);

        return mappingRuleConfigDao.save(mappingRuleConfig);
    }

    @Override
    public boolean batchSave(List<MappingRuleConfigDO> mappingRuleConfigs) {
        if (mappingRuleConfigs == null || mappingRuleConfigs.isEmpty()) {
            log.warn("映射规则配置列表为空，无法批量保存");
            return false;
        }

        // 设置基础字段
        for (MappingRuleConfigDO mappingRuleConfig : mappingRuleConfigs) {
            BaseDOUtil.fillDOInsertByUsrOrSystem(mappingRuleConfig);
        }

        mappingRuleConfigMapper.insertBatchSomeColumn(mappingRuleConfigs);
        return true;
    }

    @Override
    public List<MappingRuleConfigDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        return mappingRuleConfigDao.listByHrPunchConfigId(hrPunchConfigId);
    }

    @Override
    public MappingRuleConfigDO getByHrPunchConfigIdAndRuleType(Long hrPunchConfigId, String ruleType) {
        return mappingRuleConfigDao.getByHrPunchConfigIdAndRuleType(hrPunchConfigId, ruleType);
    }

    @Override
    public MappingRuleConfigDO getByRuleId(Long ruleId) {
        return mappingRuleConfigDao.getByRuleId(ruleId);
    }

    @Override
    public List<MappingRuleConfigDO> listByCountry(String country) {
        return mappingRuleConfigDao.listByCountry(country);
    }

    @Override
    public List<MappingRuleConfigDO> listByRuleType(String ruleType) {
        return mappingRuleConfigDao.listByRuleType(ruleType);
    }

    @Override
    public MappingRuleConfigDO createMapping(HrAttendanceGroupDTO group, String ruleType, Long ruleId) {
        log.info("创建HR考勤组到新考勤规则的映射记录, hrPunchConfigId: {}, country: {}, ruleType: {}, ruleId: {}",
                group.getId(), group.getCountry(), ruleType, ruleId);

        // 创建新的映射记录
        MappingRuleConfigDO mappingRuleConfig = new MappingRuleConfigDO();
        mappingRuleConfig.setId(defaultIdWorker.nextId());
        mappingRuleConfig.setCountry(group.getCountry());
        mappingRuleConfig.setHrPunchConfigId(group.getId());
        mappingRuleConfig.setHrEffectTime(group.getEffectTime());
        mappingRuleConfig.setHrExpireTime(group.getExpireTime());
        mappingRuleConfig.setHrIsLatest(group.getIsLatest());
        mappingRuleConfig.setStatus(group.getStatus());
        mappingRuleConfig.setRuleType(ruleType);
        mappingRuleConfig.setRuleId(ruleId);

        // 保存映射记录
        boolean saved = save(mappingRuleConfig);
        if (!saved) {
            log.error("保存映射记录失败, hrPunchConfigId: {}, ruleType: {}, ruleId: {}",
                    group.getId(), ruleType, ruleId);
            return null;
        }

        log.info("创建映射记录成功, mappingId: {}, hrPunchConfigId: {}, ruleType: {}, ruleId: {}",
                mappingRuleConfig.getId(), group.getId(), ruleType, ruleId);
        return mappingRuleConfig;
    }

    @Override
    public boolean existsMapping(Long hrPunchConfigId, String ruleType) {
        MappingRuleConfigDO mapping = getByHrPunchConfigIdAndRuleType(hrPunchConfigId, ruleType);
        return mapping != null;
    }
}
