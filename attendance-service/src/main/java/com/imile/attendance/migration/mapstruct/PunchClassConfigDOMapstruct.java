package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 班次配置DO映射器
 * 负责PunchClassConfigDO和PunchClassConfigMigrateDO之间的字段映射
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Mapper
public interface PunchClassConfigDOMapstruct extends BaseMapstruct {

    PunchClassConfigDOMapstruct INSTANCE = Mappers.getMapper(PunchClassConfigDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     */
    default Long setModelId(PunchClassConfigMigrateDO module) {
        return setModelId(module,
                PunchClassConfigMigrateDO::getId,
                PunchClassConfigMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     */
    default Long setModelId(PunchClassConfigDO module) {
        return setModelId(module,
                PunchClassConfigDO::getId,
                PunchClassConfigDO::setId);
    }

    /**
     * 将migrate表DO转换为原始表DO
     * 
     * @param punchClassConfigMigrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchClassConfigMigrateDO))")
    PunchClassConfigDO mapToReal(PunchClassConfigMigrateDO punchClassConfigMigrateDO);

    /**
     * 批量将migrate表DO转换为原始表DO
     * 
     * @param punchClassConfigMigrateDOList migrate表DO列表
     * @return 原始表DO列表
     */
    List<PunchClassConfigDO> mapToReal(List<PunchClassConfigMigrateDO> punchClassConfigMigrateDOList);

    /**
     * 将原始表DO转换为migrate表DO
     * 
     * @param punchClassConfigDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchClassConfigDO))")
    PunchClassConfigMigrateDO mapToMigrate(PunchClassConfigDO punchClassConfigDO);

    /**
     * 批量将原始表DO转换为migrate表DO
     * 
     * @param punchClassConfigDOList 原始表DO列表
     * @return migrate表DO列表
     */
    List<PunchClassConfigMigrateDO> mapToMigrate(List<PunchClassConfigDO> punchClassConfigDOList);
}
