package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
@Mapper
public interface PunchConfigDOMapstruct extends BaseMapstruct {

    PunchConfigDOMapstruct INSTANCE = Mappers.getMapper(PunchConfigDOMapstruct.class);

    default Long setModelId(PunchConfigDO module) {
        return setModelId(module,
                PunchConfigDO::getId,
                PunchConfigDO::setId);
    }

    default Long setModelId(PunchConfigMigrateDO module) {
        return setModelId(module,
                PunchConfigMigrateDO::getId,
                PunchConfigMigrateDO::setId);
    }

    @Mapping(target = "id", expression = "java(setModelId(punchConfigMigrateDO))")
    PunchConfigDO mapToReal(PunchConfigMigrateDO punchConfigMigrateDO);

    List<PunchConfigDO> mapToReal(List<PunchConfigMigrateDO> punchConfigMigrateDOList);



    @Mapping(target = "id", expression = "java(setModelId(punchConfigDO))")
    PunchConfigMigrateDO mapToMigrate(PunchConfigDO punchConfigDO);

    List<PunchConfigMigrateDO> mapToMigrate(List<PunchConfigDO> punchConfigDOList);




}
