package com.imile.attendance.migration.converter;

import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.migrate.UserShiftConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import com.imile.attendance.migration.mapstruct.UserShiftConfigDOMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 员工排班配置适配器数据转换器
 * 专门用于适配器模式中UserShiftConfigDO和UserShiftConfigMigrateDO之间的双向转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Slf4j
@Component
public class UserShiftConfigAdapterDataConverter implements DataConverter<UserShiftConfigMigrateDO, UserShiftConfigDO> {

    @Override
    public UserShiftConfigDO convertFromMigrate(UserShiftConfigMigrateDO migrateDO) {
        if (migrateDO == null) {
            log.debug("migrate表DO对象为空，跳过转换");
            return null;
        }

        try {
            UserShiftConfigDO realDO = UserShiftConfigDOMapstruct.INSTANCE.mapToReal(migrateDO);
            log.debug("migrate表DO转换为原始表DO成功, migrateId: {} -> realId: {}", 
                    migrateDO.getId(), realDO.getId());
            return realDO;
        } catch (Exception e) {
            log.error("migrate表DO转换为原始表DO失败, migrateId: {}", migrateDO.getId(), e);
            return null;
        }
    }

    @Override
    public UserShiftConfigMigrateDO convertFromReal(UserShiftConfigDO realDO) {
        if (realDO == null) {
            log.debug("原始表DO对象为空，跳过转换");
            return null;
        }

        try {
            UserShiftConfigMigrateDO migrateDO = UserShiftConfigDOMapstruct.INSTANCE.mapToMigrate(realDO);
            // 为migrate表对象生成新的ID
            UserShiftConfigDOMapstruct.INSTANCE.setModelId(migrateDO);
            
            log.debug("原始表DO转换为migrate表DO成功, realId: {} -> migrateId: {}", 
                    realDO.getId(), migrateDO.getId());
            return migrateDO;
        } catch (Exception e) {
            log.error("原始表DO转换为migrate表DO失败, realId: {}", realDO.getId(), e);
            return null;
        }
    }

    @Override
    public Class<UserShiftConfigMigrateDO> getMigrateType() {
        return UserShiftConfigMigrateDO.class;
    }

    @Override
    public Class<UserShiftConfigDO> getRealType() {
        return UserShiftConfigDO.class;
    }
}
