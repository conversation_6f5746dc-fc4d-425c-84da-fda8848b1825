package com.imile.attendance.migration.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalAttendanceQuery;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceEmployeeDetailSnapshotDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalAttendanceSnapshotDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsEmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsEmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.query.AbnormalMigrationQuery;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigItemDao;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigDao;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.migration.AbnormalMigrationService;
import com.imile.attendance.migration.dto.AbnormalSyncDTO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Service
public class AbnormalMigrationServiceImpl implements AbnormalMigrationService {
    @Resource
    private HrmsEmployeeAbnormalAttendanceDao hrmsEmployeeAbnormalAttendanceDao;
    @Resource
    private HrmsEmployeeAbnormalOperationRecordDao hrmsEmployeeAbnormalOperationRecordDao;
    @Resource
    private HrmsEmployeeAbnormalAttendanceSnapshotDao hrmsEmployeeAbnormalAttendanceSnapshotDao;
    @Resource
    private HrmsAttendanceEmployeeDetailDao hrmsAttendanceEmployeeDetailDao;
    @Resource
    private HrmsAttendanceEmployeeDetailSnapshotDao hrmsAttendanceEmployeeDetailSnapshotDao;
    @Resource
    private HrmsEmployeePunchRecordDao hrmsEmployeePunchRecordDao;

    @Resource
    private HrmsAttendancePunchClassItemConfigDao hrmsAttendancePunchClassItemConfigDao;

    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private EmployeeAbnormalAttendanceSnapshotDao employeeAbnormalAttendanceSnapshotDao;
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private AttendanceEmployeeDetailSnapshotDao attendanceEmployeeDetailSnapshotDao;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private PunchConfigManage punchConfigManage;

    @Resource
    private MappingRuleConfigDao mappingRuleConfigDao;
    @Resource
    private MappingPunchClassConfigDao mappingPunchClassConfigDao;
    @Resource
    private MappingPunchClassConfigItemDao mappingPunchClassConfigItemDao;

    @Resource
    private PunchClassConfigManage punchClassConfigManage;

    @Resource
    private UserShiftConfigManage userShiftConfigManage;

    @Resource
    private UserInfoDao userInfoDao;


    @Override
    public void syncNewSystemAbnormalRecord(AbnormalSyncDTO abnormalSyncDTO) {
        //同步打卡记录
        syncEmployeePunchRecordToNewSystem(abnormalSyncDTO);

        //同步异常表、操作记录表到新系统
        syncAbnormalToNewSystem(abnormalSyncDTO);

        //同步异常快照表到新系统
        syncAbnormalSnapshotToNewSystem(abnormalSyncDTO);

        //同步正常表及其快照表到新系统
        syncAttendanceEmployeeDetailToNewSystem(abnormalSyncDTO);
    }

    @Override
    public void syncOldSystemAbnormalRecord(AbnormalSyncDTO abnormalSyncDTO) {
        //同步异常表、操作记录表、异常快照表到老系统
        syncAbnormalToOldSystem(abnormalSyncDTO);

        //同步异常表、操作记录表、异常快照表到老系统
        syncAttendanceEmployeeDetailToOldSystem(abnormalSyncDTO);

        //同步打卡记录
        syncEmployeePunchRecordToOldSystem(abnormalSyncDTO);
    }

    @Override
    public void removeEmployeePunchRecord(String country, Long id) {
        log.info("打卡记录删除开始");
        employeePunchRecordDao.removeByCountry(country, id);
        log.info("打卡记录删除完毕");
    }

    @Override
    public void removeHrEmployeePunchRecord(Long punchRecordId) {
        hrmsEmployeePunchRecordDao.removeById(punchRecordId);
    }


   /* @DSTransactional
    public void syncAbnormalToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步异常表->attendance start");
        int currentPage = 1;
        Long lastId = 0L;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalMigrationQuery.setUserIdList(userIdList);
        }
        while (true) {
            Long finalLastId = lastId;
            // 总记录数
            List<HrmsEmployeeAbnormalAttendanceDO> pageUserInfoList = hrmsEmployeeAbnormalAttendanceDao.selectAbnormal(abnormalMigrationQuery, finalLastId);
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }
            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            List<Long> punchConfigIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getPunchConfigId).distinct().collect(Collectors.toList());
            List<Long> punchClassIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getPunchClassConfigId).distinct().collect(Collectors.toList());
            List<Long> punchClassItemIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getPunchClassItemConfigId).distinct().collect(Collectors.toList());

            Map<Long, Long> punchConfigIdMap = mappingRuleConfigDao.listByHrPunchConfigIds(punchConfigIdList).stream()
                    .collect(Collectors.toMap(MappingRuleConfigDO::getHrPunchConfigId, MappingRuleConfigDO::getRuleId, (v1, v2) -> v1));

            Map<Long, Long> punchClassIdMap = mappingPunchClassConfigDao.listByHrPunchClassIds(punchClassIdList).stream()
                    .collect(Collectors.toMap(MappingPunchClassConfigDO::getHrPunchClassId, MappingPunchClassConfigDO::getPunchClassConfigId, (v1, v2) -> v1));

            Map<Long, Long> punchClassItemIdMap = mappingPunchClassConfigItemDao.listByHrPunchClassItemIds(punchClassItemIdList).stream()
                    .collect(Collectors.toMap(MappingPunchClassConfigItemDO::getHrPunchClassItemId, MappingPunchClassConfigItemDO::getPunchClassConfigItemId, (v1, v2) -> v1));

            List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = pageUserInfoList.stream()
                    .map(abnormal -> BeanUtils.convert(abnormal, EmployeeAbnormalAttendanceDO.class))
                    .collect(Collectors.toList());

            employeeAbnormalAttendanceDOList.forEach(item -> {
                if (Objects.nonNull(item.getPunchClassConfigId()) && item.getPunchClassConfigId() > 0) {
                    item.setPunchClassConfigId(punchConfigIdMap.getOrDefault(item.getPunchClassConfigId(), item.getPunchClassConfigId()));
                    item.setPunchClassItemConfigId(punchClassItemIdMap.getOrDefault(item.getPunchClassItemConfigId(), item.getPunchClassItemConfigId()));
                }
                if (Objects.nonNull(item.getPunchConfigId())) {
                    item.setPunchConfigId(punchClassIdMap.getOrDefault(item.getPunchConfigId(), item.getPunchConfigId()));
                }
            });

            List<Long> abnormalIdList = employeeAbnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
            List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList = syncAbnormalRecordToNewSystem(abnormalIdList);
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOList)) {
                List<List<EmployeeAbnormalOperationRecordDO>> partitionList = Lists.partition(employeeAbnormalOperationRecordDOList, 1000);
                partitionList.forEach(partition -> {
                    employeeAbnormalOperationRecordDao.saveOrUpdateBatch(partition);
                });
            }
            employeeAbnormalAttendanceDao.saveOrUpdateBatch(employeeAbnormalAttendanceDOList);
            currentPage++;
            log.info("同步异常表->attendance currentPage:{}", currentPage);
        }
        log.info("同步异常表->attendance end");
    }*/

    @DSTransactional
    public void syncAbnormalToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步异常表->attendance start");
        int currentPage = 1;
        Long lastId = 0L;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalMigrationQuery.setUserIdList(userIdList);
        }
        while (true) {
            Long finalLastId = lastId;
            // 总记录数
            List<HrmsEmployeeAbnormalAttendanceDO> pageUserInfoList = hrmsEmployeeAbnormalAttendanceDao.selectAbnormal(abnormalMigrationQuery, finalLastId);
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }

            pageUserInfoList = pageUserInfoList.stream().filter(employeeAbnormal -> {
                if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(employeeAbnormal.getLocationCountry())) {
                    return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeAbnormal.getEmployeeType());
                } else {
                    return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeAbnormal.getEmployeeType());
                }
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                currentPage++;
                continue;
            }

            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            //异常处理
            List<EmployeeAbnormalAttendanceDO> totalAbnormalList = abnormalHandler(pageUserInfoList, abnormalMigrationQuery.getStartDayId(), abnormalMigrationQuery.getEndDayId());

            List<Long> abnormalIdList = totalAbnormalList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
            List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList = syncAbnormalRecordToNewSystem(abnormalIdList);
            if (CollectionUtils.isNotEmpty(employeeAbnormalOperationRecordDOList)) {
                List<List<EmployeeAbnormalOperationRecordDO>> partitionList = Lists.partition(employeeAbnormalOperationRecordDOList, 1000);
                partitionList.forEach(partition -> employeeAbnormalOperationRecordDao.saveOrUpdateBatch(partition));
            }

            employeeAbnormalAttendanceDao.saveOrUpdateBatch(totalAbnormalList);
            currentPage++;
            log.info("同步异常表->attendance currentPage:{}", currentPage);
        }
        log.info("同步异常表->attendance end");
    }

    private List<EmployeeAbnormalAttendanceDO> abnormalHandler(List<HrmsEmployeeAbnormalAttendanceDO> pageUserInfoList,
                                                               Long startDayId,
                                                               Long endDayId) {
        List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = pageUserInfoList.stream()
                .map(abnormal -> BeanUtils.convert(abnormal, EmployeeAbnormalAttendanceDO.class))
                .collect(Collectors.toList());

        Map<Long, List<EmployeeAbnormalAttendanceDO>> dayIdGroup = employeeAbnormalAttendanceDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getDayId));
        Map<Long, Map<Long, PunchConfigDO>> dayPunchConfigMap = new HashMap<>();
        Map<Long, Map<Long, List<PunchClassConfigDTO>>> dayPunchClassConfigMap = new HashMap<>();
        for (Long dayId : dayIdGroup.keySet()) {
            List<Long> userIds = dayIdGroup.get(dayId).stream().map(EmployeeAbnormalAttendanceDO::getUserId).distinct().collect(Collectors.toList());
            Date endDate = DateHelper.endOfDay(DateHelper.transferDayIdToDate(dayId));
            Map<Long, PunchConfigDO> userPunchConfigMap = punchConfigManage.mapByUserIds(userIds, endDate);
            if (MapUtils.isNotEmpty(userPunchConfigMap)) {
                dayPunchConfigMap.put(dayId, userPunchConfigMap);
            }
            Map<Long, List<PunchClassConfigDTO>> userPunchClassConfigMap = punchClassConfigManage.mapByUserIds(userIds, endDate);
            if (MapUtils.isNotEmpty(userPunchClassConfigMap)) {
                dayPunchClassConfigMap.put(dayId, userPunchClassConfigMap);
            }
        }
        List<Long> userIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceDO::getUserId).distinct().collect(Collectors.toList());
        //员工排班
        List<UserShiftConfigDO> multipleClassUserShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIdList, startDayId, endDayId);
        Map<Long, List<UserShiftConfigDO>> userShiftGroup = multipleClassUserShiftConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));
        Map<Long, Map<Long, UserShiftConfigDO>> dayUserShiftConfigMap = new HashMap<>();
        for (Long dayId : dayIdGroup.keySet()) {
            List<UserShiftConfigDO> userShiftConfigDOList = userShiftGroup.get(dayId);
            if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
                continue;
            }
            Map<Long, UserShiftConfigDO> userShiftMap = userShiftConfigDOList.stream().collect(Collectors.toMap(UserShiftConfigDO::getUserId, Function.identity(), (v1, v2) -> v1));
            dayUserShiftConfigMap.put(dayId, userShiftMap);
        }

        employeeAbnormalAttendanceDOList.forEach(item -> {
            //打卡规则
            Map<Long, PunchConfigDO> userPunchConfigMap = dayPunchConfigMap.get(item.getDayId());
            if (MapUtils.isNotEmpty(userPunchConfigMap)) {
                PunchConfigDO punchConfigDO = userPunchConfigMap.get(item.getUserId());
                if (Objects.nonNull(punchConfigDO)) {
                    item.setPunchConfigId(punchConfigDO.getId());
                }
            }

            //排班计划中获取班次id
            Map<Long, UserShiftConfigDO> userShiftMap = dayUserShiftConfigMap.get(item.getDayId());
            if (MapUtils.isEmpty(userShiftMap)) {
                return;
            }
            UserShiftConfigDO userShiftConfigDO = userShiftMap.get(item.getUserId());
            if (Objects.isNull(userShiftConfigDO) || Objects.isNull(userShiftConfigDO.getPunchClassConfigId())) {
                return;
            }
            Long punchClassConfigId = userShiftConfigDO.getPunchClassConfigId();

            //班次
            Map<Long, List<PunchClassConfigDTO>> userPunchClassConfigMap = dayPunchClassConfigMap.get(item.getDayId());

            if (MapUtils.isEmpty(userPunchClassConfigMap)) {
                return;
            }
            List<PunchClassConfigDTO> punchClassConfigDTOList = userPunchClassConfigMap.get(item.getUserId());
            if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
                return;
            }
            PunchClassConfigDTO punchClassConfigDTO = punchClassConfigDTOList.stream()
                    .filter(punchClass -> Objects.equals(punchClass.getId(), punchClassConfigId))
                    .findFirst()
                    .orElse(new PunchClassConfigDTO());
            if (Objects.isNull(punchClassConfigDTO.getId())) {
                return;
            }
            item.setPunchClassConfigId(punchClassConfigDTO.getId());
            if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassItemConfigList())) {
                return;
            }
            if (punchClassConfigDTO.getClassItemConfigList().size() == 1) {
                item.setPunchClassItemConfigId(punchClassConfigDTO.getClassItemConfigList().get(0).getId());
            } else {
                //多时段
                HrmsAttendancePunchClassItemConfigDO hrmsAttendancePunchClassItemConfigDO = hrmsAttendancePunchClassItemConfigDao.selectByClassItemId(item.getPunchClassItemConfigId());
                if (Objects.isNull(hrmsAttendancePunchClassItemConfigDO)) {
                    log.info("考勤老系统班次时段查询为空,classItemId: {}", item.getPunchClassItemConfigId());
                    return;
                }

                Long punchInDayId = DateHelper.getDayId(hrmsAttendancePunchClassItemConfigDO.getPunchInTime());
                Long punchOutDayId = DateHelper.getDayId(hrmsAttendancePunchClassItemConfigDO.getPunchOutTime());
                for (PunchClassItemConfigDTO classItemConfigDTO : punchClassConfigDTO.getClassItemConfigList()) {
                    Long punchIn = DateHelper.getDayId(classItemConfigDTO.getPunchInTime());
                    Long punchOut = DateHelper.getDayId(classItemConfigDTO.getPunchOutTime());
                    if ((punchInDayId.compareTo(punchIn) < 1 && punchOutDayId.compareTo(punchOut) > -1)
                            || (punchIn.compareTo(punchInDayId) < 1 && punchOut.compareTo(punchOutDayId) > -1)) {
                        item.setPunchClassConfigId(classItemConfigDTO.getId());
                    }
                }
            }
        });
        return employeeAbnormalAttendanceDOList;
    }

    private List<EmployeeAbnormalAttendanceSnapshotDO> abnormalSnapshotHandler(List<HrmsEmployeeAbnormalAttendanceSnapshotDO> pageUserInfoList,
                                                                               Long startDayId,
                                                                               Long endDayId) {
        List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList = pageUserInfoList.stream()
                .map(abnormal -> BeanUtils.convert(abnormal, EmployeeAbnormalAttendanceSnapshotDO.class))
                .collect(Collectors.toList());

        Map<Long, List<EmployeeAbnormalAttendanceSnapshotDO>> dayIdGroup = employeeAbnormalAttendanceSnapshotDOList.stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceSnapshotDO::getDayId));
        Map<Long, Map<Long, PunchConfigDO>> dayPunchConfigMap = new HashMap<>();
        Map<Long, Map<Long, List<PunchClassConfigDTO>>> dayPunchClassConfigMap = new HashMap<>();
        for (Long dayId : dayIdGroup.keySet()) {
            List<Long> userIds = dayIdGroup.get(dayId).stream().map(EmployeeAbnormalAttendanceSnapshotDO::getUserId).distinct().collect(Collectors.toList());
            Date endDate = DateHelper.endOfDay(DateHelper.transferDayIdToDate(dayId));
            Map<Long, PunchConfigDO> userPunchConfigMap = punchConfigManage.mapByUserIds(userIds, endDate);
            if (MapUtils.isNotEmpty(userPunchConfigMap)) {
                dayPunchConfigMap.put(dayId, userPunchConfigMap);
            }
            Map<Long, List<PunchClassConfigDTO>> userPunchClassConfigMap = punchClassConfigManage.mapByUserIds(userIds, endDate);
            if (MapUtils.isNotEmpty(userPunchClassConfigMap)) {
                dayPunchClassConfigMap.put(dayId, userPunchClassConfigMap);
            }
        }
        List<Long> userIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceSnapshotDO::getUserId).distinct().collect(Collectors.toList());
        //员工排班
        List<UserShiftConfigDO> multipleClassUserShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(userIdList, startDayId, endDayId);
        Map<Long, List<UserShiftConfigDO>> userShiftGroup = multipleClassUserShiftConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));
        Map<Long, Map<Long, UserShiftConfigDO>> dayUserShiftConfigMap = new HashMap<>();
        for (Long dayId : dayIdGroup.keySet()) {
            List<UserShiftConfigDO> userShiftConfigDOList = userShiftGroup.get(dayId);
            if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
                continue;
            }
            Map<Long, UserShiftConfigDO> userShiftMap = userShiftConfigDOList.stream().collect(Collectors.toMap(UserShiftConfigDO::getUserId, Function.identity(), (v1, v2) -> v1));
            dayUserShiftConfigMap.put(dayId, userShiftMap);
        }

        employeeAbnormalAttendanceSnapshotDOList.forEach(item -> {
            //打卡规则
            Map<Long, PunchConfigDO> userPunchConfigMap = dayPunchConfigMap.get(item.getDayId());
            if (MapUtils.isNotEmpty(userPunchConfigMap)) {
                PunchConfigDO punchConfigDO = userPunchConfigMap.get(item.getUserId());
                if (Objects.nonNull(punchConfigDO)) {
                    item.setPunchConfigId(punchConfigDO.getId());
                }
            }

            //排班计划中获取班次id
            Map<Long, UserShiftConfigDO> userShiftMap = dayUserShiftConfigMap.get(item.getDayId());
            if (MapUtils.isEmpty(userShiftMap)) {
                return;
            }
            UserShiftConfigDO userShiftConfigDO = userShiftMap.get(item.getUserId());
            if (Objects.isNull(userShiftConfigDO) || Objects.isNull(userShiftConfigDO.getPunchClassConfigId())) {
                return;
            }
            Long punchClassConfigId = userShiftConfigDO.getPunchClassConfigId();

            //班次
            Map<Long, List<PunchClassConfigDTO>> userPunchClassConfigMap = dayPunchClassConfigMap.get(item.getDayId());

            if (MapUtils.isEmpty(userPunchClassConfigMap)) {
                return;
            }
            List<PunchClassConfigDTO> punchClassConfigDTOList = userPunchClassConfigMap.get(item.getUserId());
            if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
                return;
            }
            PunchClassConfigDTO punchClassConfigDTO = punchClassConfigDTOList.stream()
                    .filter(punchClass -> Objects.equals(punchClass.getId(), punchClassConfigId))
                    .findFirst()
                    .orElse(new PunchClassConfigDTO());
            if (Objects.isNull(punchClassConfigDTO.getId())) {
                return;
            }
            item.setPunchClassConfigId(punchClassConfigDTO.getId());
            if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassItemConfigList())) {
                return;
            }
            if (punchClassConfigDTO.getClassItemConfigList().size() == 1) {
                item.setPunchClassItemConfigId(punchClassConfigDTO.getClassItemConfigList().get(0).getId());
            } else {
                //多时段
                HrmsAttendancePunchClassItemConfigDO hrmsAttendancePunchClassItemConfigDO = hrmsAttendancePunchClassItemConfigDao.selectByClassItemId(item.getPunchClassItemConfigId());
                if (Objects.isNull(hrmsAttendancePunchClassItemConfigDO)) {
                    log.info("考勤老系统班次时段查询为空,classItemId: {}", item.getPunchClassItemConfigId());
                    return;
                }

                Long punchInDayId = DateHelper.getDayId(hrmsAttendancePunchClassItemConfigDO.getPunchInTime());
                Long punchOutDayId = DateHelper.getDayId(hrmsAttendancePunchClassItemConfigDO.getPunchOutTime());
                for (PunchClassItemConfigDTO classItemConfigDTO : punchClassConfigDTO.getClassItemConfigList()) {
                    Long punchIn = DateHelper.getDayId(classItemConfigDTO.getPunchInTime());
                    Long punchOut = DateHelper.getDayId(classItemConfigDTO.getPunchOutTime());
                    if ((punchInDayId.compareTo(punchIn) < 1 && punchOutDayId.compareTo(punchOut) > -1)
                            || (punchIn.compareTo(punchInDayId) < 1 && punchOut.compareTo(punchOutDayId) > -1)) {
                        item.setPunchClassConfigId(classItemConfigDTO.getId());
                    }
                }
            }
        });
        return employeeAbnormalAttendanceSnapshotDOList;
    }

    private List<EmployeeAbnormalOperationRecordDO> syncAbnormalRecordToNewSystem(List<Long> abnormalList) {
        List<HrmsEmployeeAbnormalOperationRecordDO> hrmsEmployeeAbnormalOperationRecordDOList = hrmsEmployeeAbnormalOperationRecordDao.selectByAbnormalList(abnormalList);
        if (CollectionUtils.isEmpty(hrmsEmployeeAbnormalOperationRecordDOList)) {
            return Collections.emptyList();
        }

        List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList = hrmsEmployeeAbnormalOperationRecordDOList.stream()
                .map(abnormalRecord -> BeanUtils.convert(abnormalRecord, EmployeeAbnormalOperationRecordDO.class))
                .collect(Collectors.toList());
        return employeeAbnormalOperationRecordDOList;
    }

    /*@DSTransactional
    public void syncAbnormalSnapshotToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步异常快照表->attendance start");
        int currentPage = 1;
        Long lastId = 0L;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalMigrationQuery.setUserIdList(userIdList);
        }
        while (true) {
            Long finalLastId = lastId;
            // 总记录数
            List<HrmsEmployeeAbnormalAttendanceSnapshotDO> pageUserInfoList = hrmsEmployeeAbnormalAttendanceSnapshotDao.selectAbnormalSnapshot(abnormalMigrationQuery, finalLastId);

            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }
            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            List<Long> punchConfigIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceSnapshotDO::getPunchConfigId).distinct().collect(Collectors.toList());
            List<Long> punchClassIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceSnapshotDO::getPunchClassConfigId).distinct().collect(Collectors.toList());
            List<Long> punchClassItemIdList = pageUserInfoList.stream().map(HrmsEmployeeAbnormalAttendanceSnapshotDO::getPunchClassItemConfigId).distinct().collect(Collectors.toList());

            Map<Long, Long> punchConfigIdMap = mappingRuleConfigDao.listByHrPunchConfigIds(punchConfigIdList).stream()
                    .collect(Collectors.toMap(MappingRuleConfigDO::getHrPunchConfigId, MappingRuleConfigDO::getRuleId, (v1, v2) -> v1));

            Map<Long, Long> punchClassIdMap = mappingPunchClassConfigDao.listByHrPunchClassIds(punchClassIdList).stream()
                    .collect(Collectors.toMap(MappingPunchClassConfigDO::getHrPunchClassId, MappingPunchClassConfigDO::getPunchClassConfigId, (v1, v2) -> v1));

            Map<Long, Long> punchClassItemIdMap = mappingPunchClassConfigItemDao.listByHrPunchClassItemIds(punchClassItemIdList).stream()
                    .collect(Collectors.toMap(MappingPunchClassConfigItemDO::getHrPunchClassItemId, MappingPunchClassConfigItemDO::getPunchClassConfigItemId, (v1, v2) -> v1));

            List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList = pageUserInfoList.stream()
                    .map(abnormal -> BeanUtils.convert(abnormal, EmployeeAbnormalAttendanceSnapshotDO.class))
                    .collect(Collectors.toList());

            employeeAbnormalAttendanceSnapshotDOList.forEach(item -> {
                if (Objects.nonNull(item.getPunchClassConfigId()) && item.getPunchClassConfigId() > 0) {
                    item.setPunchClassConfigId(punchConfigIdMap.getOrDefault(item.getPunchClassConfigId(), item.getPunchClassConfigId()));
                    item.setPunchClassItemConfigId(punchClassItemIdMap.getOrDefault(item.getPunchClassItemConfigId(), item.getPunchClassItemConfigId()));
                }
                if (Objects.nonNull(item.getPunchConfigId())) {
                    item.setPunchConfigId(punchClassIdMap.getOrDefault(item.getPunchConfigId(), item.getPunchConfigId()));
                }
            });
            currentPage++;
            employeeAbnormalAttendanceSnapshotDao.saveOrUpdateBatch(employeeAbnormalAttendanceSnapshotDOList);
            log.info("同步异常快照表->attendance currentPage:{}", currentPage);
        }
        log.info("同步异常快照表->attendance end");
    }*/

    @DSTransactional
    public void syncAbnormalSnapshotToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步异常快照表->attendance start");
        int currentPage = 1;
        Long lastId = 0L;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalMigrationQuery.setUserIdList(userIdList);
        }
        while (true) {
            Long finalLastId = lastId;
            // 总记录数
            List<HrmsEmployeeAbnormalAttendanceSnapshotDO> pageUserInfoList = hrmsEmployeeAbnormalAttendanceSnapshotDao.selectAbnormalSnapshot(abnormalMigrationQuery, finalLastId);

            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }

            pageUserInfoList = pageUserInfoList.stream().filter(employeeAbnormal -> {
                if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(employeeAbnormal.getLocationCountry())) {
                    return EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeAbnormal.getEmployeeType());
                } else {
                    return EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT.contains(employeeAbnormal.getEmployeeType());
                }
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(pageUserInfoList)){
                currentPage++;
                continue;
            }

            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            //异常处理
            List<EmployeeAbnormalAttendanceSnapshotDO> totalAbnormalSnapshotList = abnormalSnapshotHandler(pageUserInfoList, abnormalMigrationQuery.getStartDayId(), abnormalMigrationQuery.getEndDayId());

            employeeAbnormalAttendanceSnapshotDao.saveOrUpdateBatch(totalAbnormalSnapshotList);
            currentPage++;
            log.info("同步异常快照表->attendance currentPage:{}", currentPage);
        }
        log.info("同步异常快照表->attendance end");
    }

    @DSTransactional
    public void syncAttendanceEmployeeDetailToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步正常表->attendance start");
        int currentPage = 1;
        Long lastId = 0L;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalMigrationQuery.setUserIdList(userIdList);
        }
        while (true) {
            Long finalLastId = lastId;
            // 总记录数
            List<HrmsAttendanceEmployeeDetailDO> pageUserInfoList = hrmsAttendanceEmployeeDetailDao.selectByCondition(abnormalMigrationQuery, finalLastId);

            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }
            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();
            Map<String, List<Long>> attendanceEmployeeDetailMap = pageUserInfoList.stream()
                    .filter(employeeDetail -> Objects.isNull(employeeDetail.getFormId()))
                    .collect(Collectors.groupingBy(employeeDetail -> buildGroupKey(employeeDetail.getUserId(), employeeDetail.getDayId()), Collectors.mapping(HrmsAttendanceEmployeeDetailDO::getId, Collectors.toList())));
            Map<String, Long> userPunchConfigMap = new HashMap<>();
            for (String userKey : attendanceEmployeeDetailMap.keySet()) {
                String[] userKeyStr = userKey.split("_");
                PunchConfigDO punchConfigDO = punchConfigManage.getByUserIdAndDate(Long.valueOf(userKeyStr[0]), DateHelper.endOfDay(DateHelper.transferDayIdToDate(userKeyStr[1])));
                if (Objects.isNull(punchConfigDO)) {
                    continue;
                }
                userPunchConfigMap.put(userKey, punchConfigDO.getId());
            }
            Map<Long, Long> punchConfigMap = new HashMap<>();
            List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = pageUserInfoList.stream()
                    .map(employeeDetail -> {
                        AttendanceEmployeeDetailDO newRecord = BeanUtils.convert(employeeDetail, AttendanceEmployeeDetailDO.class);
                        if (Objects.isNull(employeeDetail.getFormId())) {
                            Long punchConfigId = userPunchConfigMap.get(buildGroupKey(employeeDetail.getUserId(), employeeDetail.getDayId()));
                            if (Objects.nonNull(punchConfigId)) {
                                newRecord.setPunchConfigId(punchConfigId);
                                punchConfigMap.put(newRecord.getId(), punchConfigId);
                            }
                        }
                        return newRecord;
                    })
                    .collect(Collectors.toList());
            List<Long> idList = attendanceEmployeeDetailDOList.stream().map(AttendanceEmployeeDetailDO::getId).collect(Collectors.toList());
            List<AttendanceEmployeeDetailSnapshotDO> attendanceEmployeeDetailSnapshotDOList = syncAttendanceEmployeeDetailSnapshotToNewSystem(idList, punchConfigMap);
            if (CollectionUtils.isNotEmpty(attendanceEmployeeDetailSnapshotDOList)) {
                List<List<AttendanceEmployeeDetailSnapshotDO>> partitionList = Lists.partition(attendanceEmployeeDetailSnapshotDOList, 1000);
                partitionList.forEach(partition -> attendanceEmployeeDetailSnapshotDao.saveOrUpdateBatch(partition));
            }
            attendanceEmployeeDetailDao.saveOrUpdateBatch(attendanceEmployeeDetailDOList);
            currentPage++;
            log.info("同步正常表->attendance currentPage:{}", currentPage);
        }
        log.info("同步正常表->attendance end");
    }


    @DSTransactional
    @Override
    public void syncEmployeePunchRecordToNewSystem(AbnormalSyncDTO abnormalSyncDTO) {
        log.info("同步打卡记录表->attendance start");
        Long lastId = 0L;
        int currentPage = 0;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);

        EmployeePunchCardRecordQuery recordQuery = new EmployeePunchCardRecordQuery();
        recordQuery.setCountryList(abnormalMigrationQuery.getCountryList());
        if (Objects.nonNull(abnormalSyncDTO.getStartDayId())) {
            recordQuery.setStartDayId(abnormalSyncDTO.getStartDayId());
        }
        if (Objects.nonNull(abnormalSyncDTO.getEndDayId())) {
            recordQuery.setEndDayId(abnormalSyncDTO.getEndDayId());
        }
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            recordQuery.setUserCodes(abnormalSyncDTO.getUserCodeList());
        }
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getDeptIdList())) {
            recordQuery.setDeptIdList(abnormalSyncDTO.getDeptIdList());
        }
        Set<Long> allHrPunchRecordIdList = new HashSet<>();
        while (true) {
            recordQuery.setLastId(lastId);
            List<HrmsEmployeePunchRecordDO> pageUserInfoList = hrmsEmployeePunchRecordDao.listRecords(recordQuery);
            // 总记录数
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                break;
            }
            lastId = pageUserInfoList.get(pageUserInfoList.size() - 1).getId();

            List<Long> hrPunchRecordIdList = pageUserInfoList.stream().map(HrmsEmployeePunchRecordDO::getId).collect(Collectors.toList());
            List<Long> existHrPunchRecordIdList = employeePunchRecordDao.listHrPunchRecord(hrPunchRecordIdList, abnormalMigrationQuery.getStartDayId());

            List<EmployeePunchRecordDO> employeePunchRecordDOList = pageUserInfoList.stream()
                    .filter(record -> !existHrPunchRecordIdList.contains(record.getId())
                            && !allHrPunchRecordIdList.contains(record.getId()))
                    .map(punchRecord -> {
                        EmployeePunchRecordDO model = BeanUtils.convert(punchRecord, EmployeePunchRecordDO.class);
                        model.setHrPunchRecordId(punchRecord.getId());
                        return model;
                    })
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(employeePunchRecordDOList)) {
                employeePunchRecordDao.saveBatch(employeePunchRecordDOList);
            }
            allHrPunchRecordIdList.addAll(hrPunchRecordIdList);
            currentPage++;
            log.info("同步打卡记录表->attendance currentPage:{},allHrPunchRecordIdList.size:{}", currentPage, allHrPunchRecordIdList.size());
        }
        log.info("同步正常打卡记录表->attendance end");

        log.info("同步补卡打卡记录表->attendance start");
        List<HrmsEmployeePunchRecordDO> hrmsEmployeePunchRecordDOList = hrmsEmployeePunchRecordDao.listReissueCard(abnormalMigrationQuery);
        if (CollectionUtils.isNotEmpty(hrmsEmployeePunchRecordDOList)) {
            List<Long> hrPunchRecordIdList = hrmsEmployeePunchRecordDOList.stream().map(HrmsEmployeePunchRecordDO::getId).collect(Collectors.toList());
            List<Long> existHrPunchRecordIdList = employeePunchRecordDao.listHrPunchRecord(hrPunchRecordIdList, abnormalMigrationQuery.getStartDayId());

            List<EmployeePunchRecordDO> employeePunchRecordDOList = hrmsEmployeePunchRecordDOList.stream()
                    .filter(record -> !existHrPunchRecordIdList.contains(record.getId())
                            && !allHrPunchRecordIdList.contains(record.getId()))
                    .map(punchRecord -> {
                        EmployeePunchRecordDO model = BeanUtils.convert(punchRecord, EmployeePunchRecordDO.class);
                        model.setHrPunchRecordId(punchRecord.getId());
                        return model;
                    })
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(employeePunchRecordDOList)) {
                employeePunchRecordDao.saveBatch(employeePunchRecordDOList);
            }
        }

        log.info("同步打卡记录表->attendance end");
    }

    private List<AttendanceEmployeeDetailSnapshotDO> syncAttendanceEmployeeDetailSnapshotToNewSystem(List<Long> abnormalList, Map<Long, Long> punchConfigMap) {
        List<HrmsAttendanceEmployeeDetailSnapshotDO> hrmsAttendanceEmployeeDetailSnapshotDOList = hrmsAttendanceEmployeeDetailSnapshotDao.selectByIds(abnormalList);
        if (CollectionUtils.isEmpty(hrmsAttendanceEmployeeDetailSnapshotDOList)) {
            return Collections.emptyList();
        }

        List<AttendanceEmployeeDetailSnapshotDO> employeeAbnormalAttendanceSnapshotDOList = hrmsAttendanceEmployeeDetailSnapshotDOList.stream()
                .map(abnormalSnapshot -> {
                    AttendanceEmployeeDetailSnapshotDO newRecord = BeanUtils.convert(abnormalSnapshot, AttendanceEmployeeDetailSnapshotDO.class);
                    newRecord.setPunchConfigId(punchConfigMap.get(abnormalSnapshot.getId()));
                    return newRecord;
                })
                .collect(Collectors.toList());
        return employeeAbnormalAttendanceSnapshotDOList;
    }

    private void syncAbnormalToOldSystem(AbnormalSyncDTO abnormalSyncDTO) {
        int currentPage = 1;
        int pageSize = 5000;
        PageInfo<EmployeeAbnormalAttendanceDO> pageInfo;
        AbnormalAttendanceQuery abnormalAttendanceQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalAttendanceQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalAttendanceQuery.setUserIdList(userIdList);
        }
        do {
            Page<EmployeeAbnormalAttendanceDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> employeeAbnormalAttendanceDao.listAbnormal(abnormalAttendanceQuery));
            // 总记录数
            List<EmployeeAbnormalAttendanceDO> pageUserInfoList = pageInfo.getList();
            currentPage++;
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                continue;
            }
            List<Long> punchConfigIdList = pageUserInfoList.stream().map(EmployeeAbnormalAttendanceDO::getPunchConfigId).distinct().collect(Collectors.toList());
            List<Long> punchClassIdList = pageUserInfoList.stream().map(EmployeeAbnormalAttendanceDO::getPunchClassConfigId).distinct().collect(Collectors.toList());

            Map<Long, Long> hrPunchConfigIdMap = mappingRuleConfigDao.listByRuleIds(punchConfigIdList).stream()
                    .collect(Collectors.toMap(MappingRuleConfigDO::getRuleId, MappingRuleConfigDO::getHrPunchConfigId, (v1, v2) -> v1));

            Map<Long, Long> hrPunchClassIdMap = mappingPunchClassConfigDao.listByPunchClassConfigIds(punchClassIdList).stream()
                    .collect(Collectors.toMap(MappingPunchClassConfigDO::getPunchClassConfigId, MappingPunchClassConfigDO::getHrPunchClassId, (v1, v2) -> v1));

            Map<Long, Long> hrPunchClassItemIdMap = mappingPunchClassConfigItemDao.listByPunchClassConfigIds(punchClassIdList).stream()
                    .collect(Collectors.toMap(MappingPunchClassConfigItemDO::getPunchClassConfigItemId, MappingPunchClassConfigItemDO::getHrPunchClassItemId, (v1, v2) -> v1));

            List<HrmsEmployeeAbnormalAttendanceDO> hrmsEmployeeAbnormalAttendanceDOList = pageUserInfoList.stream()
                    .map(abnormal -> BeanUtils.convert(abnormal, HrmsEmployeeAbnormalAttendanceDO.class))
                    .collect(Collectors.toList());
            List<List<HrmsEmployeeAbnormalAttendanceDO>> partitionList = Lists.partition(hrmsEmployeeAbnormalAttendanceDOList, 1000);
            partitionList.forEach(partition -> {
                partition.forEach(item -> {
                    if (Objects.nonNull(item.getPunchClassConfigId()) && item.getPunchClassConfigId() > 0) {
                        item.setPunchClassConfigId(hrPunchClassIdMap.getOrDefault(item.getPunchClassConfigId(), item.getPunchClassConfigId()));
                        item.setPunchClassItemConfigId(hrPunchClassItemIdMap.getOrDefault(item.getPunchClassItemConfigId(), item.getPunchClassItemConfigId()));
                    }
                    if (Objects.nonNull(item.getPunchConfigId())) {
                        item.setPunchConfigId(hrPunchConfigIdMap.getOrDefault(item.getPunchConfigId(), item.getPunchConfigId()));
                    }
                });
                hrmsEmployeeAbnormalAttendanceDao.saveOrUpdateBatch(partition);
                List<Long> abnormalIdList = partition.stream().map(HrmsEmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
                syncAbnormalRecordToOldSystem(abnormalIdList);
                syncAbnormalSnapshotToOldSystem(abnormalIdList, partition);
            });
        } while (currentPage <= pageInfo.getPages());
        log.info("同步到异常表数据到考勤老系统结束");
    }

    private void syncAbnormalRecordToOldSystem(List<Long> abnormalList) {
        List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList = employeeAbnormalOperationRecordDao.selectByAbnormalList(abnormalList);
        if (CollectionUtils.isEmpty(employeeAbnormalOperationRecordDOList)) {
            return;
        }

        List<HrmsEmployeeAbnormalOperationRecordDO> hrmsEmployeeAbnormalOperationRecordDOList = employeeAbnormalOperationRecordDOList.stream()
                .map(abnormalRecord -> BeanUtils.convert(abnormalRecord, HrmsEmployeeAbnormalOperationRecordDO.class))
                .collect(Collectors.toList());
        List<List<HrmsEmployeeAbnormalOperationRecordDO>> partitionList = Lists.partition(hrmsEmployeeAbnormalOperationRecordDOList, 1000);
        partitionList.forEach(partition -> hrmsEmployeeAbnormalOperationRecordDao.saveOrUpdateBatch(partition));
    }

    private void syncAbnormalSnapshotToOldSystem(List<Long> abnormalList, List<HrmsEmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList) {
        List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList = employeeAbnormalAttendanceSnapshotDao.selectByIds(abnormalList);
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceSnapshotDOList)) {
            return;
        }

        Map<Long, HrmsEmployeeAbnormalAttendanceDO> abnormalMap = employeeAbnormalAttendanceDOList.stream().collect(Collectors.toMap(HrmsEmployeeAbnormalAttendanceDO::getId, Function.identity(), (v1, v2) -> v1));
        List<HrmsEmployeeAbnormalAttendanceSnapshotDO> hrmsEmployeeAbnormalAttendanceSnapshotDOList = employeeAbnormalAttendanceSnapshotDOList.stream()
                .map(abnormalSnapshot -> {
                    HrmsEmployeeAbnormalAttendanceSnapshotDO result = BeanUtils.convert(abnormalSnapshot, HrmsEmployeeAbnormalAttendanceSnapshotDO.class);
                    HrmsEmployeeAbnormalAttendanceDO abnormalAttendanceDO = abnormalMap.get(abnormalSnapshot.getId());
                    if (Objects.nonNull(abnormalAttendanceDO)) {
                        result.setPunchConfigId(abnormalAttendanceDO.getPunchConfigId());
                        result.setPunchClassConfigId(abnormalAttendanceDO.getPunchClassConfigId());
                        result.setPunchClassItemConfigId(abnormalAttendanceDO.getPunchClassItemConfigId());
                    }
                    return result;
                })
                .collect(Collectors.toList());
        List<List<HrmsEmployeeAbnormalAttendanceSnapshotDO>> partitionList = Lists.partition(hrmsEmployeeAbnormalAttendanceSnapshotDOList, 1000);
        partitionList.forEach(partition -> hrmsEmployeeAbnormalAttendanceSnapshotDao.saveOrUpdateBatch(partition));
    }

    private void syncAttendanceEmployeeDetailToOldSystem(AbnormalSyncDTO abnormalSyncDTO) {
        int currentPage = 1;
        int pageSize = 5000;
        PageInfo<AttendanceEmployeeDetailDO> pageInfo;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        AbnormalAttendanceQuery abnormalAttendanceQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalAttendanceQuery.class);
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            List<Long> userIdList = userInfoDao.listByUserCodes(abnormalSyncDTO.getUserCodeList()).stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());
            abnormalAttendanceQuery.setUserIdList(userIdList);
        }
        do {
            Page<AttendanceEmployeeDetailDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> attendanceEmployeeDetailDao.selectByCondition(abnormalMigrationQuery));
            // 总记录数
            List<AttendanceEmployeeDetailDO> pageUserInfoList = pageInfo.getList();
            currentPage++;
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                continue;
            }

            List<HrmsAttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = pageUserInfoList.stream()
                    .map(employeeDetail -> BeanUtils.convert(employeeDetail, HrmsAttendanceEmployeeDetailDO.class)).collect(Collectors.toList());

            List<List<HrmsAttendanceEmployeeDetailDO>> partitionList = Lists.partition(attendanceEmployeeDetailDOList, 1000);
            partitionList.forEach(partition -> {
                hrmsAttendanceEmployeeDetailDao.saveOrUpdateBatch(partition);
                List<Long> idList = partition.stream().map(HrmsAttendanceEmployeeDetailDO::getId).collect(Collectors.toList());
                syncAttendanceEmployeeDetailSnapshotToOldSystem(idList);
            });
        } while (currentPage <= pageInfo.getPages());
        log.info("同步到正常表数据到考勤老系统结束");
    }

    private void syncEmployeePunchRecordToOldSystem(AbnormalSyncDTO abnormalSyncDTO) {
        int currentPage = 1;
        int pageSize = 5000;
        PageInfo<EmployeePunchRecordDO> pageInfo;
        AbnormalMigrationQuery abnormalMigrationQuery = BeanUtils.convert(abnormalSyncDTO, AbnormalMigrationQuery.class);
        EmployeePunchCardRecordQuery recordQuery = new EmployeePunchCardRecordQuery();
        recordQuery.setCountryList(abnormalMigrationQuery.getCountryList());
        if (Objects.nonNull(abnormalSyncDTO.getStartDayId())) {
            recordQuery.setStartDayId(abnormalSyncDTO.getStartDayId());
        }
        if (CollectionUtils.isNotEmpty(abnormalSyncDTO.getUserCodeList())) {
            recordQuery.setUserCodes(abnormalSyncDTO.getUserCodeList());
        }
        do {
            Page<EmployeePunchRecordDO> page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> employeePunchRecordDao.listRecords(recordQuery));
            // 总记录数
            List<EmployeePunchRecordDO> pageUserInfoList = pageInfo.getList();
            currentPage++;
            if (CollectionUtils.isEmpty(pageUserInfoList)) {
                continue;
            }

            List<HrmsEmployeePunchRecordDO> hrmsEmployeePunchRecordDOList = pageUserInfoList.stream()
                    .map(punchRecord -> BeanUtils.convert(punchRecord, HrmsEmployeePunchRecordDO.class))
                    .collect(Collectors.toList());
            List<List<HrmsEmployeePunchRecordDO>> partitionList = Lists.partition(hrmsEmployeePunchRecordDOList, 1000);
            partitionList.forEach(partition -> hrmsEmployeePunchRecordDao.saveOrUpdateBatch(partition));
        } while (currentPage <= pageInfo.getPages());
        log.info("同步到打卡记录表数据到考勤老系统结束");
    }

    private void syncAttendanceEmployeeDetailSnapshotToOldSystem(List<Long> idList) {
        List<AttendanceEmployeeDetailSnapshotDO> attendanceEmployeeDetailSnapshotDOList = attendanceEmployeeDetailSnapshotDao.selectByIds(idList);
        if (CollectionUtils.isEmpty(attendanceEmployeeDetailSnapshotDOList)) {
            return;
        }

        List<HrmsAttendanceEmployeeDetailSnapshotDO> hrmsAttendanceEmployeeDetailSnapshotDOList = attendanceEmployeeDetailSnapshotDOList.stream()
                .map(abnormalSnapshot -> BeanUtils.convert(abnormalSnapshot, HrmsAttendanceEmployeeDetailSnapshotDO.class))
                .collect(Collectors.toList());
        List<List<HrmsAttendanceEmployeeDetailSnapshotDO>> partitionList = Lists.partition(hrmsAttendanceEmployeeDetailSnapshotDOList, 1000);
        partitionList.forEach(partition -> hrmsAttendanceEmployeeDetailSnapshotDao.saveOrUpdateBatch(partition));
    }


    private String buildGroupKey(Long userId, Long dayId) {
        return userId + "_" + dayId;
    }
}

