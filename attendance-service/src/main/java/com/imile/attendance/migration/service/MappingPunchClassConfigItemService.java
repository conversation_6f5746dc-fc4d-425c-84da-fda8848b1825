package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 考勤班次时间配置映射服务接口
 */
public interface MappingPunchClassConfigItemService {

    /**
     * 保存班次时间配置映射
     *
     * @param mappingPunchClassConfigItem 班次时间配置映射
     * @return 是否成功
     */
    boolean save(MappingPunchClassConfigItemDO mappingPunchClassConfigItem);

    /**
     * 批量保存班次时间配置映射
     *
     * @param mappingPunchClassConfigItems 班次时间配置映射列表
     * @return 是否成功
     */
    boolean batchSave(List<MappingPunchClassConfigItemDO> mappingPunchClassConfigItems);

    /**
     * 根据HR考勤组班次时间配置ID查询班次时间配置映射
     *
     * @param hrPunchClassItemId HR考勤组班次时间配置ID
     * @return 班次时间配置映射
     */
    MappingPunchClassConfigItemDO getByHrPunchClassItemId(Long hrPunchClassItemId);

    /**
     * 根据HR考勤组班次ID查询班次时间配置映射列表
     *
     * @param hrPunchClassId HR考勤组班次ID
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByHrPunchClassId(Long hrPunchClassId);

    /**
     * 根据HR考勤组ID查询班次时间配置映射列表
     *
     * @param hrPunchConfigId HR考勤组ID
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据新考勤班次时间配置ID查询班次时间配置映射
     *
     * @param punchClassConfigItemId 新考勤班次时间配置ID
     * @return 班次时间配置映射
     */
    MappingPunchClassConfigItemDO getByPunchClassConfigItemId(Long punchClassConfigItemId);

    /**
     * 根据国家查询班次时间配置映射列表
     *
     * @param country 国家
     * @return 班次时间配置映射列表
     */
    List<MappingPunchClassConfigItemDO> listByCountry(String country);

    /**
     * 创建HR考勤组班次时间配置到新考勤班次时间配置的映射记录
     *
     * @param groupDTO HR考勤组DTO
     * @param hrClassId HR班次配置ID
     * @param itemConfigDTO HR班次时间配置DTO
     * @param punchClassConfigId 新班次配置ID
     * @param punchClassConfigItemId 新班次时间配置ID
     * @return 班次时间配置映射
     */
    MappingPunchClassConfigItemDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                               Long hrClassId,
                                               HrAttendanceClassItemConfigDTO itemConfigDTO,
                                               Long punchClassConfigId,
                                               Long punchClassConfigItemId);

    /**
     * 检查班次时间配置映射是否已存在
     *
     * @param hrPunchClassItemId HR考勤组班次时间配置ID
     * @return 是否存在
     */
    boolean existsMapping(Long hrPunchClassItemId);
}
