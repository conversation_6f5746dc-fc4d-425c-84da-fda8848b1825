package com.imile.attendance.migration.dto;

import lombok.Data;

/**
 * 排班数据迁移参数DTO
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@Data
public class UserShiftConfigMigrationParam {
    
    /**
     * 国家代码（必填）
     */
    private String country;
    
    /**
     * 迁移类型（可选）
     * FULL - 全量迁移（默认）
     * HISTORY - 仅历史数据迁移
     * CURRENT - 仅当前数据迁移
     */
    private String migrationType = "FULL";
    
    /**
     * 开始日期ID（可选）
     * 格式：yyyyMMdd
     */
    private Long startDayId;
    
    /**
     * 结束日期ID（可选）
     * 格式：yyyyMMdd
     */
    private Long endDayId;
    
    /**
     * 是否强制执行（可选）
     * 默认false，如果为true则跳过一些验证
     */
    private Boolean forceExecute = false;
    
    /**
     * 是否跳过数据验证（可选）
     * 默认false，如果为true则跳过迁移后的数据验证
     */
    private Boolean skipValidation = false;
    
    /**
     * 迁移类型枚举
     */
    public enum MigrationType {
        /**
         * 全量迁移
         */
        FULL,
        
        /**
         * 仅历史数据迁移
         */
        HISTORY,
        
        /**
         * 仅当前数据迁移
         */
        CURRENT
    }
    
    /**
     * 获取迁移类型枚举
     */
    public MigrationType getMigrationTypeEnum() {
        try {
            return MigrationType.valueOf(migrationType.toUpperCase());
        } catch (Exception e) {
            return MigrationType.FULL;
        }
    }
    
    /**
     * 验证参数有效性
     */
    public boolean isValid() {
        // 国家代码必填
        if (country == null || country.trim().isEmpty()) {
            return false;
        }
        
        // 验证迁移类型
        try {
            MigrationType.valueOf(migrationType.toUpperCase());
        } catch (Exception e) {
            return false;
        }
        
        // 验证日期范围
        if (startDayId != null && endDayId != null && startDayId > endDayId) {
            return false;
        }
        
        return true;
    }
    
    @Override
    public String toString() {
        return "UserShiftConfigMigrationParam{" +
                "country='" + country + '\'' +
                ", migrationType='" + migrationType + '\'' +
                ", startDayId=" + startDayId +
                ", endDayId=" + endDayId +
                ", forceExecute=" + forceExecute +
                ", skipValidation=" + skipValidation +
                '}';
    }
}
