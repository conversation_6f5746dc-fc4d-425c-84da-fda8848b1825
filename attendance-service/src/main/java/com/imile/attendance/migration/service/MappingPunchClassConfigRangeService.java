package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigRangeDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 考勤班次范围映射服务接口
 */
public interface MappingPunchClassConfigRangeService {

    /**
     * 保存班次范围映射
     *
     * @param mappingPunchClassConfigRange 班次范围映射
     * @return 是否成功
     */
    boolean save(MappingPunchClassConfigRangeDO mappingPunchClassConfigRange);

    /**
     * 批量保存班次范围映射
     *
     * @param mappingPunchClassConfigRanges 班次范围映射列表
     * @return 是否成功
     */
    boolean batchSave(List<MappingPunchClassConfigRangeDO> mappingPunchClassConfigRanges);

    /**
     * 根据HR考勤组范围ID查询班次范围映射配置
     *
     * @param hrPunchConfigRangeId HR考勤组范围ID
     * @return 班次范围映射配置
     */
    MappingPunchClassConfigRangeDO getByHrPunchConfigRangeId(Long hrPunchConfigRangeId);

    /**
     * 根据HR考勤组ID查询班次范围映射配置
     *
     * @param hrPunchConfigId HR考勤组ID
     * @return 班次范围映射配置列表
     */
    List<MappingPunchClassConfigRangeDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据新考勤班次范围ID查询班次范围映射配置
     *
     * @param punchClassConfigRangeId 新考勤班次范围ID
     * @return 班次范围映射配置
     */
    MappingPunchClassConfigRangeDO getByPunchClassConfigRangeId(Long punchClassConfigRangeId);

    /**
     * 创建HR考勤组范围到新考勤班次范围的映射记录
     *
     * @param groupDTO HR考勤组DTO
     * @param rangeDTO HR考勤组范围DTO
     * @param punchClassConfigRangeId 班次范围ID
     * @param punchClassConfigId 班次配置ID
     * @param ruleBizId 规则范围人员ID
     * @return 班次范围映射配置
     */
    MappingPunchClassConfigRangeDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                                HrAttendanceGroupRangeDTO rangeDTO,
                                                Long punchClassConfigRangeId,
                                                Long punchClassConfigId,
                                                Long ruleBizId);

    /**
     * 检查班次范围映射是否已存在
     *
     * @param hrPunchConfigRangeId HR考勤组范围ID
     * @return 是否存在
     */
    boolean existsMapping(Long hrPunchConfigRangeId);
}
