package com.imile.attendance.migration.mapstruct;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
public interface BaseMapstruct {

    default <T> Long setModelId(T module,
                                Function<T, Long> getIdFunction,
                                BiConsumer<T, Long> setIdFunction) {
        if (module == null) {
            return null;
        }
        Long id = getIdFunction.apply(module);
        if (id == null) {
            id = IdWorker.getId();
            setIdFunction.accept(module, id);
        }
        return id;
    }
}
