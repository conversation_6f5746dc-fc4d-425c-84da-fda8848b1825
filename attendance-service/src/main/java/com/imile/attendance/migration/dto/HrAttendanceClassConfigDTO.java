package com.imile.attendance.migration.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description HR考勤班次配置DTO
 */
@Data
public class HrAttendanceClassConfigDTO {

    /**
     * 班次ID
     */
    private Long id;

    /**
     * 打卡规则ID
     */
    private Long punchConfigId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date earliestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private Date latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 上下班打卡时间间隔 单位：小时
     */
    private BigDecimal punchTimeInterval;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 上班和最早打卡时间间隔
     */
    private BigDecimal punchInTimeInterval;

    /**
     * 出勤时长
     */
    private BigDecimal attendanceHours;

    /**
     * 法定工作时长
     */
    private BigDecimal legalWorkingHours;

    /**
     * 班次时间配置列表
     */
    private List<HrAttendanceClassItemConfigDTO> itemList;

    /**
     * 是否跨天
     */
    public Boolean isAcrossDay() {
        return isAcross != null && isAcross.equals(1);
    }

    /**
     * 获取班次名称，如果为空则返回固定班次
     */
    public String getClassNameForAttendance() {
        return StringUtils.defaultIfBlank(className,"固定班次");
    }
}
