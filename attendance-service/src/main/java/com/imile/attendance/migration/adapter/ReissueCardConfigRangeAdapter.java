package com.imile.attendance.migration.adapter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.ReissueCardConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.ReissueCardConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.ReissueCardConfigRangeMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 补卡配置范围适配器
 * 支持在原始表和migrate表之间透明切换的补卡配置范围DAO操作
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class ReissueCardConfigRangeAdapter extends AbstractPairAdapter<ReissueCardConfigRangeMigrateDO, ReissueCardConfigRangeDO> {

    @Resource
    private ReissueCardConfigRangeMigrateDao reissueCardConfigRangeMigrateDao;
    @Resource
    private ReissueCardConfigRangeMigrateMapper reissueCardConfigRangeMigrateMapper;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private ReissueCardConfigRangeMapper reissueCardConfigRangeMapper;

    public ReissueCardConfigRangeAdapter(List<DataConverter<ReissueCardConfigRangeMigrateDO, ReissueCardConfigRangeDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个补卡配置范围
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param reissueCardConfigRangeDO 补卡配置范围DO对象
     */
    public void saveOne(ReissueCardConfigRangeDO reissueCardConfigRangeDO) {
        log.info("保存补卡配置范围, rangeId: {}, ruleConfigId: {}, bizId: {}, 当前使用{}表", 
                reissueCardConfigRangeDO.getId(), reissueCardConfigRangeDO.getRuleConfigId(), 
                reissueCardConfigRangeDO.getBizId(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateOneWrapper(
                reissueCardConfigRangeDO,
                migrateDO -> {
                    // 保存到migrate表
                    reissueCardConfigRangeMigrateDao.save(migrateDO);
                    log.debug("补卡配置范围已保存到migrate表, rangeId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    reissueCardConfigRangeDao.save(realDO);
                    log.debug("补卡配置范围已保存到原始表, rangeId: {}", realDO.getId());
                }
        );
    }

    /**
     * 批量保存补卡配置范围
     * 
     * @param reissueCardConfigRangeDOList 补卡配置范围DO列表
     */
    public void saveBatch(List<ReissueCardConfigRangeDO> reissueCardConfigRangeDOList) {
        if (reissueCardConfigRangeDOList == null || reissueCardConfigRangeDOList.isEmpty()) {
            log.warn("批量保存补卡配置范围: 输入列表为空");
            return;
        }
        
        log.info("批量保存补卡配置范围, 数量: {}, 当前使用{}表", 
                reissueCardConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        List<List<ReissueCardConfigRangeDO>> partitions = Lists.partition(reissueCardConfigRangeDOList, BusinessConstant.MAX_BATCH_SIZE);
        for (List<ReissueCardConfigRangeDO> partition : partitions) {
            saveOrUpdateBatchWrapper(
                    partition,
                    migrateDOList -> {
                        // 批量保存到migrate表
                        reissueCardConfigRangeMigrateMapper.insertBatchSomeColumn(migrateDOList);
                        log.debug("批量保存补卡配置范围到migrate表完成, 数量: {}", migrateDOList.size());
                    },
                    realDOList -> {
                        // 批量保存到原始表
                        reissueCardConfigRangeMapper.insertBatchSomeColumn(realDOList);
                        log.debug("批量保存补卡配置范围到原始表完成, 数量: {}", realDOList.size());
                    }
            );
        }
    }

    /**
     * 批量更新补卡配置范围
     * 
     * @param reissueCardConfigRangeDOList 补卡配置范围DO列表
     */
    public void updateBatch(List<ReissueCardConfigRangeDO> reissueCardConfigRangeDOList) {
        if (reissueCardConfigRangeDOList == null || reissueCardConfigRangeDOList.isEmpty()) {
            log.warn("批量更新补卡配置范围: 输入列表为空");
            return;
        }
        
        log.info("批量更新补卡配置范围, 数量: {}, 当前使用{}表", 
                reissueCardConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateBatchWrapper(
                reissueCardConfigRangeDOList,
                migrateDOList -> {
                    // 批量更新migrate表
                    reissueCardConfigRangeMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("批量更新补卡配置范围到migrate表完成, 数量: {}", migrateDOList.size());
                },
                realDOList -> {
                    // 批量更新原始表
                    reissueCardConfigRangeMapper.replaceIntoBatchSomeColumn(realDOList);
                    log.debug("批量更新补卡配置范围到原始表完成, 数量: {}", realDOList.size());
                }
        );
    }

    /**
     * 根据用户ID列表查询配置范围
     * 
     * @param userIds 用户ID列表
     * @return 配置范围列表
     */
    public List<ReissueCardConfigRangeDO> listConfigRanges(List<Long> userIds) {
        log.debug("根据用户ID列表查询补卡配置范围, userIds数量: {}, 当前使用{}表",
                userIds != null ? userIds.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> reissueCardConfigRangeMigrateDao.listConfigRanges(userIds),
                () -> reissueCardConfigRangeDao.listConfigRanges(userIds)
        );
    }

    /**
     * 根据规则配置ID查询范围
     * 
     * @param ruleConfigId 规则配置ID
     * @return 配置范围列表
     */
    public List<ReissueCardConfigRangeDO> listByRuleConfigId(Long ruleConfigId) {
        log.debug("根据规则配置ID查询补卡配置范围, ruleConfigId: {}, 当前使用{}表",
                ruleConfigId, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> {
                    LambdaQueryWrapper<ReissueCardConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(ReissueCardConfigRangeMigrateDO::getRuleConfigId, ruleConfigId);
                    return reissueCardConfigRangeMigrateDao.list(queryWrapper);
                },
                () -> {
                    LambdaQueryWrapper<ReissueCardConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(ReissueCardConfigRangeDO::getRuleConfigId, ruleConfigId);
                    return reissueCardConfigRangeDao.list(queryWrapper);
                }
        );
    }

    /**
     * 根据规则配置ID列表查询范围
     * 
     * @param ruleConfigIds 规则配置ID列表
     * @return 配置范围列表
     */
    public List<ReissueCardConfigRangeDO> listByRuleConfigIds(List<Long> ruleConfigIds) {
        log.debug("根据规则配置ID列表查询补卡配置范围, ruleConfigIds数量: {}, 当前使用{}表",
                ruleConfigIds != null ? ruleConfigIds.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> reissueCardConfigRangeMigrateDao.listByRuleConfigIds(ruleConfigIds),
                () -> reissueCardConfigRangeDao.listByConfigIds(ruleConfigIds)
        );
    }

    /**
     * 根据ID获取补卡配置范围
     *
     * @param id 范围ID
     * @return 补卡配置范围
     */
    public ReissueCardConfigRangeDO getById(Long id) {
        log.debug("根据ID查询补卡配置范围, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readWrapper(
                () -> reissueCardConfigRangeMigrateDao.getById(id),
                () -> reissueCardConfigRangeDao.getById(id)
        );
    }

    /**
     * 更新补卡配置范围
     *
     * @param reissueCardConfigRangeDO 补卡配置范围DO对象
     * @return 更新结果
     */
    public boolean updateById(ReissueCardConfigRangeDO reissueCardConfigRangeDO) {
        log.info("更新补卡配置范围, rangeId: {}, ruleConfigId: {}, 当前使用{}表",
                reissueCardConfigRangeDO.getId(), reissueCardConfigRangeDO.getRuleConfigId(),
                isShouldUseMigrate() ? "migrate" : "原始");

        return commonQuery(
                () -> {
                    // 转换并更新migrate表
                    ReissueCardConfigRangeMigrateDO migrateDO = getConverter().convertFromReal(reissueCardConfigRangeDO);
                    boolean result = reissueCardConfigRangeMigrateDao.updateById(migrateDO);
                    log.debug("补卡配置范围更新到migrate表结果: {}, rangeId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 更新原始表
                    boolean result = reissueCardConfigRangeDao.updateById(reissueCardConfigRangeDO);
                    log.debug("补卡配置范围更新到原始表结果: {}, rangeId: {}", result, reissueCardConfigRangeDO.getId());
                    return result;
                }
        );
    }

    /**
     * 统计国家下在职非司机且未配置规则的用户列表
     *
     * @param ruleRangeUserQuery 查询条件
     * @return 用户列表
     */
    public List<UserInfoDO> listOnJobNoDriverUsersExcludeConfigured(RuleRangeUserQuery ruleRangeUserQuery){
        return commonQuery(
                () -> reissueCardConfigRangeMigrateDao.listOnJobNoDriverUsersExcludeConfigured(ruleRangeUserQuery),
                () -> reissueCardConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(ruleRangeUserQuery)
        );
    }

    /**
     * 批量物理删除补卡配置范围
     *
     * @param ids 补卡配置范围ID列表
     * @return 删除是否成功
     */
    public boolean removeBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量物理删除补卡配置范围: ID列表为空");
            return true;
        }

        log.info("批量物理删除补卡配置范围, 数量: {}, 当前使用{}表",
                ids.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 批量物理删除migrate表记录
                    boolean result = reissueCardConfigRangeMigrateDao.removeByIds(ids);
                    log.info("补卡配置范围从migrate表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                },
                () -> {
                    // 批量物理删除原始表记录
                    boolean result = reissueCardConfigRangeDao.removeByIds(ids);
                    log.info("补卡配置范围从原始表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                }
        );
    }

}
