package com.imile.attendance.migration.service.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.base.BaseDO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingRuleConfigRangeDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingRuleConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import com.imile.attendance.migration.service.MappingRuleConfigRangeService;
import com.imile.attendance.util.BaseDOUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/12 
 * @Description
 */
@Service
public class MappingRuleConfigRangeServiceImpl implements MappingRuleConfigRangeService {

    @Resource
    private MappingRuleConfigRangeDao mappingRuleConfigRangeDao;
    @Resource
    private MappingRuleConfigRangeMapper mappingRuleConfigRangeMapper;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Override
    public boolean save(MappingRuleConfigRangeDO mappingRuleConfigRange) {
        return mappingRuleConfigRangeDao.save(mappingRuleConfigRange);
    }

    @Override
    public boolean batchSave(List<MappingRuleConfigRangeDO> mappingRuleConfigRanges) {
        List<List<MappingRuleConfigRangeDO>> partitions = Lists.partition(mappingRuleConfigRanges, BusinessConstant.MAX_BATCH_SIZE);
        for (List<MappingRuleConfigRangeDO> partition : partitions) {
            mappingRuleConfigRangeMapper.insertBatchSomeColumn(partition);
        }
        return true;
    }

    @Override
    public MappingRuleConfigRangeDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                                 HrAttendanceGroupRangeDTO rangeDTO,
                                                 Long ruleConfigRangeId,
                                                 Long ruleConfigId, Long ruleBizId) {
        MappingRuleConfigRangeDO mappingRuleConfigRangeDO = new MappingRuleConfigRangeDO();
        mappingRuleConfigRangeDO.setId(defaultIdWorker.nextId());
        mappingRuleConfigRangeDO.setHrPunchConfigRangeId(rangeDTO.getId());
        mappingRuleConfigRangeDO.setHrPunchConfigId(rangeDTO.getPunchConfigId());
        mappingRuleConfigRangeDO.setHrRangeType(rangeDTO.getRangeType());
        mappingRuleConfigRangeDO.setHrIsNeedPunch(rangeDTO.getIsNeedPunch());
        mappingRuleConfigRangeDO.setHrEffectTime(rangeDTO.getEffectTime());
        mappingRuleConfigRangeDO.setHrExpireTime(rangeDTO.getExpireTime());
        mappingRuleConfigRangeDO.setHrIsLatest(rangeDTO.getIsLatest());
        mappingRuleConfigRangeDO.setHrRangeStatus(groupDTO.getStatus());
        mappingRuleConfigRangeDO.setRuleConfigRangeId(ruleConfigRangeId);
        mappingRuleConfigRangeDO.setRuleConfigId(ruleConfigId);
        mappingRuleConfigRangeDO.setRuleBizId(ruleBizId);

        BaseDOUtil.fillDOInsertByUsrOrSystem(mappingRuleConfigRangeDO);
        return mappingRuleConfigRangeDO;
    }
}
