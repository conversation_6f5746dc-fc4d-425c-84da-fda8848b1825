package com.imile.attendance.migration.impl;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.common.CommonUserService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.migration.UserClassNatureService;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/12
 * @Description
 */
@Slf4j
@Service
public class UserClassNatureServiceImpl implements UserClassNatureService {

    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private UserInfoDao userInfoDao;

    @Value("${multi.class.user.codes:2102925601,2101316201,2101078201,210727301,210669101,210660901,2106150,2105597,2104830,2104540,2102429,2102098,2102061,210738,210391,210393,210038,210003,880085}")
    private String multiClassUserCodes;

    @Value("${uae.gray.scale.user.codes:}")
    private String uaeGrayScaleUserCodes;


    @Override
    public List<String> getMultiClassUserCodes() {
        if (StringUtils.isEmpty(multiClassUserCodes)) {
            return Collections.emptyList();
        }
        return Arrays.stream(multiClassUserCodes.split(","))
                .map(String::trim)
                .collect(Collectors.toList());
    }

    @Override
    public void initUserClassNatureByCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            return;
        }
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .locationCountry(country)
                .employeeTypeList(CommonUserService.getCountryEmployeeTypes(country))
                .build();
        List<UserInfoDO> userList = userInfoManage.listUsersByQuery(userDaoQuery);
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }
        List<String> multiClassUserCodes = getMultiClassUserCodes();
        for (UserInfoDO user : userList) {
            if (multiClassUserCodes.contains(user.getUserCode())) {
                user.setClassNature(ClassNatureEnum.MULTIPLE_CLASS.name());
            } else {
                // 如果user没有班次性质，初始化为FIXED_CLASS，不覆盖原有的班次
                if (StringUtils.isEmpty(user.getClassNature())) {
                    user.setClassNature(ClassNatureEnum.FIXED_CLASS.name());
                }
            }
        }
        List<List<UserInfoDO>> partitionUserList = Lists.partition(userList, 500);
        for (List<UserInfoDO> userInfoDOList : partitionUserList) {
            userInfoDao.updateBatchById(userInfoDOList);
        }
    }

    @Override
    public void clearUserClassNatureForAllNoGrayscaleCountry() {
        try {
            // 处理UAE国家（需要过滤灰度用户）
            processCountryUsers(CountryCodeEnum.UAE.getCode(), true);

            // 处理其他国家（除UAE和CHN）
            getOtherCountries().forEach(country ->
                    processCountryUsers(country, false));

        } catch (Exception e) {
            log.error("clearUserClassNatureForAllNoGrayscaleCountry failed", e);
            throw e;
        }
    }

    @Override
    public Boolean refreshUserClassNatureByCountry(String countryCode, ClassNatureEnum targetClassNature, List<String> configUserCodes) {
        log.info("refreshUserClassNatureByCountry | start country:{}, targetClassNature:{}, configUsers:{}",
                countryCode, targetClassNature, CollectionUtils.isEmpty(configUserCodes) ? 0 : configUserCodes.size());

        try {
            if (StringUtils.isBlank(countryCode) || targetClassNature == null) {
                log.warn("refreshUserClassNatureByCountry | invalid parameters, countryCode: {}, targetClassNature: {}",
                        countryCode, targetClassNature);
                return false;
            }

            // 查询指定国家的用户
            List<UserInfoDO> userList = queryActiveUsers(countryCode);
            if (CollectionUtils.isEmpty(userList)) {
                log.info("refreshUserClassNatureByCountry | no users found for country: {}", countryCode);
                return false;
            }

            // 准备哈希集合用于快速查找
            Set<String> configUserCodeSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(configUserCodes)) {
                configUserCodeSet.addAll(configUserCodes.stream()
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList()));
            }

            // 设置用户班次类型
            ClassNatureEnum oppositeClassNature = targetClassNature == ClassNatureEnum.MULTIPLE_CLASS 
                    ? ClassNatureEnum.FIXED_CLASS 
                    : ClassNatureEnum.MULTIPLE_CLASS;

            int configUsersUpdated = 0;
            int otherUsersUpdated = 0;

            for (UserInfoDO user : userList) {
                String userCode = user.getUserCode();
                if (configUserCodeSet.contains(userCode)) {
                    // 配置用户设置为目标班次类型
                    user.setClassNature(targetClassNature.name());
                    configUsersUpdated++;
                } else {
                    // 其他用户设置为相反的班次类型
                    user.setClassNature(oppositeClassNature.name());
                    otherUsersUpdated++;
                }
            }

            // 批量更新用户
            ((UserClassNatureServiceImpl) AopContext.currentProxy()).batchUpdateUserClassNatureForRefresh(userList);

            log.info("refreshUserClassNatureByCountry | end country:{}, configUsersUpdated:{}, otherUsersUpdated:{}, total:{}",
                    countryCode, configUsersUpdated, otherUsersUpdated, userList.size());
            
            return true;

        } catch (Exception e) {
            log.error("refreshUserClassNatureByCountry failed for country:{}, targetClassNature:{}",
                    countryCode, targetClassNature, e);
            throw e;
        }
    }


    /**
     * 处理指定国家的用户班次性质清理
     * @param countryCode 国家代码
     * @param needFilterGrayscale 是否需要过滤灰度用户
     */
    private void processCountryUsers(String countryCode, boolean needFilterGrayscale) {
        log.info("clearUserClassNatureForAllNoGrayscaleCountry | start country:{}", countryCode);

        try {
            // 查询用户列表
            List<UserInfoDO> userList = queryActiveUsers(countryCode);
            if (CollectionUtils.isEmpty(userList)) {
                log.info("clearUserClassNatureForAllNoGrayscaleCountry | no users found for country:{}", countryCode);
                return;
            }

            // 过滤灰度用户（仅UAE需要）
            if (needFilterGrayscale) {
                userList = filterGrayscaleUsers(userList);
            }

            if (CollectionUtils.isEmpty(userList)) {
                log.info("clearUserClassNatureForAllNoGrayscaleCountry | no users to process after filtering for country:{}", countryCode);
                return;
            }

            // 批量更新用户班次性质
            ((UserClassNatureServiceImpl) AopContext.currentProxy()).batchUpdateUserClassNature(userList);

            log.info("clearUserClassNatureForAllNoGrayscaleCountry | end country:{}, processed {} users",
                    countryCode, userList.size());

        } catch (Exception e) {
            log.error("processCountryUsers failed for country:{}", countryCode, e);
            throw e;
        }
    }

    /**
     * 查询活跃用户列表
     */
    private List<UserInfoDO> queryActiveUsers(String countryCode) {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .locationCountry(countryCode)
                .employeeTypeList(CommonUserService.getCountryEmployeeTypes(countryCode))
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .status(StatusEnum.ACTIVE.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        return userInfoDao.userList(userDaoQuery);
    }

    /**
     * 批量更新用户班次性质（用于班次刷新操作）
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateUserClassNatureForRefresh(List<UserInfoDO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            log.warn("batchUpdateUserClassNatureForRefresh: 用户列表为空，跳过处理");
            return;
        }
        
        long startTime = System.currentTimeMillis();
        log.info("开始批量更新用户班次性质，用户总数：{}", userList.size());
        
        // 分批更新，每批500个
        List<List<UserInfoDO>> partitionedList = Lists.partition(userList, BusinessConstant.MAX_BATCH_SIZE);
        int totalBatches = partitionedList.size();
        int processedBatches = 0;

        for (List<UserInfoDO> batch : partitionedList) {
            try {
                userInfoDao.updateBatchById(batch);
                processedBatches++;
                log.info("第{}/{}批更新完成，当前批次大小：{}", 
                        processedBatches, totalBatches, batch.size());
            } catch (Exception e) {
                log.error("第{}/{}批更新失败，批次大小：{}", 
                        processedBatches + 1, totalBatches, batch.size(), e);
                throw e;
            }
        }
        
        long endTime = System.currentTimeMillis();
        log.info("完成批量更新用户班次性质，用户总数：{}，总耗时：{}ms", 
                userList.size(), (endTime - startTime));
    }

    /**
     * 过滤灰度用户
     */
    private List<UserInfoDO> filterGrayscaleUsers(List<UserInfoDO> userList) {
        if (StringUtils.isBlank(uaeGrayScaleUserCodes)) {
            return userList;
        }

        Set<String> grayscaleUserCodeSet = Arrays.stream(uaeGrayScaleUserCodes.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());

        return userList.stream()
                .filter(user -> !grayscaleUserCodeSet.contains(user.getUserCode()))
                .collect(Collectors.toList());
    }

    /**
     * 批量更新用户班次性质
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateUserClassNature(List<UserInfoDO> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            log.warn("batchUpdateUserClassNature: 用户列表为空，跳过处理");
            return;
        }
        
        long startTime = System.currentTimeMillis();
        log.info("开始批量更新用户班次性质，用户总数：{}", userList.size());
        
        // 清空班次性质 - 使用常量代替硬编码空字符串
        userList.forEach(user -> user.setClassNature(BusinessConstant.EMPTY_STR));

        // 分批更新，每批500个
        List<List<UserInfoDO>> partitionedList = Lists.partition(userList, BusinessConstant.MAX_BATCH_SIZE);
        int totalBatches = partitionedList.size();
        int processedBatches = 0;

        for (List<UserInfoDO> batch : partitionedList) {
            try {
                userInfoDao.updateBatchById(batch);
                processedBatches++;
                log.info("第{}/{}批更新完成，当前批次大小：{}", 
                        processedBatches, totalBatches, batch.size());
            } catch (Exception e) {
                log.error("第{}/{}批更新失败，批次大小：{}", 
                        processedBatches + 1, totalBatches, batch.size(), e);
                throw e;
            }
        }
        
        long endTime = System.currentTimeMillis();
        log.info("完成批量更新用户班次性质，用户总数：{}，耗时：{}ms", 
                userList.size(), (endTime - startTime));
    }

    /**
     * 获取除UAE和CHN之外的所有国家代码
     */
    private List<String> getOtherCountries() {
        Set<String> excludeCountries = new HashSet<>();
        excludeCountries.add(CountryCodeEnum.UAE.getCode());
        excludeCountries.add(CountryCodeEnum.CHN.getCode());

        return Arrays.stream(CountryCodeEnum.values())
                .map(CountryCodeEnum::getCode)
                .filter(code -> !excludeCountries.contains(code))
                .collect(Collectors.toList());
    }
}
