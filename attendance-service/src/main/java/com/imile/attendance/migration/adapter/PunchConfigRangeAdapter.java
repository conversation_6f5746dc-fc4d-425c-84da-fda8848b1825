package com.imile.attendance.migration.adapter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchConfigRangeMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigRangeMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 打卡配置范围适配器
 * 支持在原始表和migrate表之间透明切换的打卡配置范围DAO操作
 *
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchConfigRangeAdapter extends AbstractPairAdapter<PunchConfigRangeMigrateDO, PunchConfigRangeDO> {

    @Resource
    private PunchConfigRangeMigrateDao punchConfigRangeMigrateDao;
    @Resource
    private PunchConfigRangeMigrateMapper punchConfigRangeMigrateMapper;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private PunchConfigRangeMapper punchConfigRangeMapper;

    public PunchConfigRangeAdapter(List<DataConverter<PunchConfigRangeMigrateDO, PunchConfigRangeDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个打卡配置范围
     * 根据配置自动选择使用原始表还是migrate表
     *
     * @param punchConfigRangeDO 打卡配置范围DO对象
     */
    public void saveOne(PunchConfigRangeDO punchConfigRangeDO) {
        log.info("保存打卡配置范围, rangeId: {}, ruleConfigId: {}, bizId: {}, 当前使用{}表",
                punchConfigRangeDO.getId(), punchConfigRangeDO.getRuleConfigId(),
                punchConfigRangeDO.getBizId(), isShouldUseMigrate() ? "migrate" : "原始");

        super.saveOrUpdateOneWrapper(
                punchConfigRangeDO,
                migrateDO -> {
                    // 保存到migrate表
                    punchConfigRangeMigrateDao.save(migrateDO);
                    log.debug("打卡配置范围已保存到migrate表, rangeId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    punchConfigRangeDao.save(realDO);
                    log.debug("打卡配置范围已保存到原始表, rangeId: {}", realDO.getId());
                }
        );
    }

    /**
     * 批量保存打卡配置范围
     *
     * @param punchConfigRangeDOList 打卡配置范围DO列表
     */
    public boolean saveBatch(List<PunchConfigRangeDO> punchConfigRangeDOList) {
        if (punchConfigRangeDOList == null || punchConfigRangeDOList.isEmpty()) {
            log.warn("批量保存打卡配置范围: 输入列表为空");
            return false;
        }

        log.info("批量保存打卡配置范围, 数量: {}, 当前使用{}表",
                punchConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        List<List<PunchConfigRangeDO>> partitions = Lists.partition(punchConfigRangeDOList, BusinessConstant.MAX_BATCH_SIZE);

        for (List<PunchConfigRangeDO> partition : partitions) {
            commonWriteWithResult(
                    () -> {
                        // 转换并批量保存到migrate表
                        List<PunchConfigRangeMigrateDO> migrateDOList = partition.stream()
                                .map(realDO -> getConverter().convertFromReal(realDO))
                                .collect(Collectors.toList());
                        Integer result = punchConfigRangeMigrateMapper.insertBatchSomeColumn(migrateDOList);
                        log.debug("批量保存打卡配置范围到migrate表结果: {}, 数量: {}", result, migrateDOList.size());
                        return true;
                    },
                    () -> {
                        // 批量保存到原始表
                        Integer result = punchConfigRangeMapper.insertBatchSomeColumn(partition);
                        log.debug("批量保存打卡配置范围到原始表结果: {}, 数量: {}", result, partition.size());
                        return true;
                    }
            );
        }
        return true;
    }

    /**
     * 批量更新打卡配置范围
     *
     * @param punchConfigRangeDOList 打卡配置范围DO列表
     */
    public void updateBatch(List<PunchConfigRangeDO> punchConfigRangeDOList) {
        if (punchConfigRangeDOList == null || punchConfigRangeDOList.isEmpty()) {
            log.warn("批量更新打卡配置范围: 输入列表为空");
            return;
        }

        log.info("批量更新打卡配置范围, 数量: {}, 当前使用{}表",
                punchConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        super.saveOrUpdateBatchWrapper(
                punchConfigRangeDOList,
                migrateDOList -> {
                    // 批量更新migrate表
                    punchConfigRangeMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("批量更新打卡配置范围到migrate表完成, 数量: {}", migrateDOList.size());
                },
                realDOList -> {
                    // 批量更新原始表
                    punchConfigRangeMapper.replaceIntoBatchSomeColumn(realDOList);
                    log.debug("批量更新打卡配置范围到原始表完成, 数量: {}", realDOList.size());
                }
        );
    }

    /**
     * 根据用户ID列表查询配置范围
     *
     * @param userIds 用户ID列表
     * @return 配置范围列表（转换为原始DO类型）
     */
    public List<PunchConfigRangeDO> listConfigRanges(List<Long> userIds) {
        log.debug("查询打卡配置范围, userIds数量: {}, 当前使用{}表",
                userIds != null ? userIds.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return readBatchWrapper(
                () -> punchConfigRangeMigrateDao.listConfigRanges(userIds),
                () -> punchConfigRangeDao.listConfigRanges(userIds)
        );
    }

    /**
     * 根据配置ID查询范围配置
     *
     * @param configId 配置ID
     * @return 范围配置列表（转换为原始DO类型）
     */
    public List<PunchConfigRangeDO> listByConfigId(Long configId) {
        log.debug("根据配置ID查询未删除的范围配置, configId: {}, 当前使用{}表",
                configId, isShouldUseMigrate() ? "migrate" : "原始");

        return readBatchWrapper(
                () -> {
                    LambdaQueryWrapper<PunchConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(PunchConfigRangeMigrateDO::getRuleConfigId, configId);
                    return punchConfigRangeMigrateDao.list(queryWrapper);
                },
                () -> {
                    LambdaQueryWrapper<PunchConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.eq(PunchConfigRangeDO::getRuleConfigId, configId);
                    return punchConfigRangeDao.list(queryWrapper);
                }
        );
    }

    /**
     * 批量根据ID更新范围配置
     *
     * @param punchConfigRangeDOList 范围配置DO列表
     * @return 更新是否成功
     */
    public boolean updateBatchById(List<PunchConfigRangeDO> punchConfigRangeDOList) {
        if (punchConfigRangeDOList == null || punchConfigRangeDOList.isEmpty()) {
            log.warn("批量更新打卡配置范围: 输入列表为空");
            return true;
        }

        log.info("批量根据ID更新打卡配置范围, 数量: {}, 当前使用{}表",
                punchConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并批量更新migrate表
                    List<PunchConfigRangeMigrateDO> migrateDOList = punchConfigRangeDOList.stream()
                            .map(realDO -> getConverter().convertFromReal(realDO))
                            .collect(Collectors.toList());
                    Integer result = punchConfigRangeMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("批量更新打卡配置范围到migrate表结果: {}, 数量: {}", result, migrateDOList.size());
                    return true;
                },
                () -> {
                    // 批量更新原始表
                    Integer result = punchConfigRangeMapper.replaceIntoBatchSomeColumn(punchConfigRangeDOList);
                    log.debug("批量更新打卡配置范围到原始表结果: {}, 数量: {}", result, punchConfigRangeDOList.size());
                    return true;
                }
        );
    }


    /**
     * 根据ID物理删除打卡配置范围
     *
     * @param ids 范围配置ID
     * @return 删除是否成功
     */
    public boolean removeBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量物理删除打卡配置范围: ID列表为空");
            return true;
        }

        log.info("批量物理删除打卡配置范围, 数量: {}, 当前使用{}表",
                ids.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 批量物理删除migrate表记录
                    boolean result = punchConfigRangeMigrateDao.removeByIds(ids);
                    log.info("打卡配置范围从migrate表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                },
                () -> {
                    // 批量物理删除原始表记录
                    boolean result = punchConfigRangeDao.removeByIds(ids);
                    log.info("打卡配置范围从原始表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                }
        );
    }
}
