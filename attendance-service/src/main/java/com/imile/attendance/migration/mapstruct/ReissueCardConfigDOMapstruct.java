package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 补卡配置DO映射器
 * 负责ReissueCardConfigDO和ReissueCardConfigMigrateDO之间的字段映射
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Mapper
public interface ReissueCardConfigDOMapstruct extends BaseMapstruct {

    ReissueCardConfigDOMapstruct INSTANCE = Mappers.getMapper(ReissueCardConfigDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     */
    default Long setModelId(ReissueCardConfigMigrateDO module) {
        return setModelId(module,
                ReissueCardConfigMigrateDO::getId,
                ReissueCardConfigMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     */
    default Long setModelId(ReissueCardConfigDO module) {
        return setModelId(module,
                ReissueCardConfigDO::getId,
                ReissueCardConfigDO::setId);
    }

    /**
     * 将migrate表DO转换为原始表DO
     * 
     * @param migrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(migrateDO))")
    ReissueCardConfigDO mapToReal(ReissueCardConfigMigrateDO migrateDO);

    /**
     * 将原始表DO转换为migrate表DO
     * 
     * @param realDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(realDO))")
    ReissueCardConfigMigrateDO mapToMigrate(ReissueCardConfigDO realDO);
}
