package com.imile.attendance.migration;


import com.imile.attendance.migration.dto.AbnormalSyncDTO;

/**
 * <AUTHOR> chen
 * @Date 2025/5/27
 * @Description
 */
public interface AbnormalMigrationService {

    /**
     * 同步老系统员工异常、出勤明细、相关快照表到新系统
     */
    void syncNewSystemAbnormalRecord(AbnormalSyncDTO abnormalSyncDTO);

    /**
     * 同步打卡记录到新系统
     */
    void syncEmployeePunchRecordToNewSystem(AbnormalSyncDTO abnormalSyncDTO);

    /**
     * 同步新系统员工异常、出勤明细、相关快照表到老系统
     */
    void syncOldSystemAbnormalRecord(AbnormalSyncDTO abnormalSyncDTO);


    void removeEmployeePunchRecord(String country,Long id);

    void removeHrEmployeePunchRecord(Long punchRecordId);


}
