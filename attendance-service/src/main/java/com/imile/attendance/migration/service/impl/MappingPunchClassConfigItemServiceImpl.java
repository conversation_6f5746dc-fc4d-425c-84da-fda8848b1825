package com.imile.attendance.migration.service.impl;

import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.migration.dao.MappingPunchClassConfigItemDao;
import com.imile.attendance.infrastructure.repository.migration.mapper.MappingPunchClassConfigItemMapper;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigItemDO;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.service.MappingPunchClassConfigItemService;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16
 * @Description 考勤班次时间配置映射服务实现类
 */
@Slf4j
@Service
public class MappingPunchClassConfigItemServiceImpl implements MappingPunchClassConfigItemService {

    @Resource
    private MappingPunchClassConfigItemDao mappingPunchClassConfigItemDao;
    @Resource
    private MappingPunchClassConfigItemMapper mappingPunchClassConfigItemMapper;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    @Override
    public boolean save(MappingPunchClassConfigItemDO mappingPunchClassConfigItem) {
        return mappingPunchClassConfigItemDao.save(mappingPunchClassConfigItem);
    }

    @Override
    public boolean batchSave(List<MappingPunchClassConfigItemDO> mappingPunchClassConfigItems) {
        mappingPunchClassConfigItemMapper.insertBatchSomeColumn(mappingPunchClassConfigItems);
        return true;
    }

    @Override
    public MappingPunchClassConfigItemDO getByHrPunchClassItemId(Long hrPunchClassItemId) {
        return mappingPunchClassConfigItemDao.getByHrPunchClassItemId(hrPunchClassItemId);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByHrPunchClassId(Long hrPunchClassId) {
        return mappingPunchClassConfigItemDao.listByHrPunchClassId(hrPunchClassId);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByHrPunchConfigId(Long hrPunchConfigId) {
        return mappingPunchClassConfigItemDao.listByHrPunchConfigId(hrPunchConfigId);
    }

    @Override
    public MappingPunchClassConfigItemDO getByPunchClassConfigItemId(Long punchClassConfigItemId) {
        return mappingPunchClassConfigItemDao.getByPunchClassConfigItemId(punchClassConfigItemId);
    }

    @Override
    public List<MappingPunchClassConfigItemDO> listByCountry(String country) {
        return mappingPunchClassConfigItemDao.listByCountry(country);
    }

    @Override
    public MappingPunchClassConfigItemDO buildMapping(HrAttendanceGroupDTO groupDTO,
                                                      Long hrClassId,
                                                      HrAttendanceClassItemConfigDTO itemConfigDTO,
                                                      Long punchClassConfigId,
                                                      Long punchClassConfigItemId) {
        log.debug("构建班次时间配置映射, hrGroupId: {}, hrClassId: {}, hrItemId: {}, punchClassConfigItemId: {}",
                groupDTO.getId(), hrClassId, itemConfigDTO.getId(), punchClassConfigItemId);

        MappingPunchClassConfigItemDO mappingDO = new MappingPunchClassConfigItemDO();
        mappingDO.setId(defaultIdWorker.nextId());
        
        // 设置国家信息
        mappingDO.setCountry(groupDTO.getCountry());
        
        // 设置HR相关信息
        mappingDO.setHrPunchConfigId(groupDTO.getId());
        mappingDO.setHrPunchClassId(hrClassId);
        mappingDO.setHrPunchClassItemId(itemConfigDTO.getId());
        
        // 设置新规则相关信息
        mappingDO.setPunchClassConfigId(punchClassConfigId);
        mappingDO.setPunchClassConfigItemId(punchClassConfigItemId);

        // 设置基础字段
        BaseDOUtil.fillDOInsertByUsrOrSystem(mappingDO);
        
        return mappingDO;
    }

    @Override
    public boolean existsMapping(Long hrPunchClassItemId) {
        if (hrPunchClassItemId == null) {
            return false;
        }
        MappingPunchClassConfigItemDO mapping = getByHrPunchClassItemId(hrPunchClassItemId);
        return mapping != null;
    }
}
