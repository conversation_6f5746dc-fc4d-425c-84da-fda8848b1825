package com.imile.attendance.migration.adapter;

import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchConfigMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
@Slf4j
@Component
public class PunchConfigAdapter extends AbstractPairAdapter<PunchConfigMigrateDO, PunchConfigDO> {

    @Resource
    private PunchConfigMigrateDao punchConfigMigrateDao;
    @Resource
    private PunchConfigDao punchConfigDao;

    public PunchConfigAdapter(List<DataConverter<PunchConfigMigrateDO, PunchConfigDO>> dataConverters) {
        super(dataConverters);
    }


    public void saveOne(PunchConfigDO punchConfigDO) {
        super.saveOrUpdateOneWrapper(
                punchConfigDO,
                migrateDO -> {
                    //save new data
                    punchConfigMigrateDao.save(migrateDO);
                },
                // save real data
                realDO -> punchConfigDao.save(realDO)
        );
    }

    public boolean saveOneWithResult(PunchConfigDO punchConfigDO) {
        log.info("保存打卡配置(带返回结果), configId: {}, configName: {}, 当前使用{}表",
                punchConfigDO.getId(), punchConfigDO.getConfigName(),
                isShouldUseMigrate() ? "migrate" : "原始");

        return commonQuery(
                () -> {
                    // 转换并保存到migrate表
                    PunchConfigMigrateDO migrateDO = getConverter().convertFromReal(punchConfigDO);
                    boolean result = punchConfigMigrateDao.save(migrateDO);
                    log.debug("打卡配置保存到migrate表结果: {}, configId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 保存到原始表
                    boolean result = punchConfigDao.save(punchConfigDO);
                    log.debug("打卡配置保存到原始表结果: {}, configId: {}", result, punchConfigDO.getId());
                    return result;
                }
        );
    }

    /**
     * 根据ID查询打卡配置
     *
     * @param id 配置ID
     * @return 打卡配置（转换为原始DO类型）
     */
    public PunchConfigDO getById(Long id) {
        log.debug("根据ID查询打卡配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readWrapper(
                () -> punchConfigMigrateDao.getById(id),
                () -> punchConfigDao.getById(id)
        );
    }

    /**
     * 根据ID更新打卡配置
     *
     * @param punchConfigDO 打卡配置DO对象
     * @return 更新是否成功
     */
    public boolean updateById(PunchConfigDO punchConfigDO) {
        log.info("根据ID更新打卡配置, configId: {}, 当前使用{}表",
                punchConfigDO.getId(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并更新migrate表
                    PunchConfigMigrateDO migrateDO = getConverter().convertFromReal(punchConfigDO);
                    boolean result = punchConfigMigrateDao.updateById(migrateDO);
                    log.debug("打卡配置更新到migrate表结果: {}, configId: {}", result, migrateDO.getId());
                    return result;
                },
                () -> {
                    // 更新原始表
                    boolean result = punchConfigDao.updateById(punchConfigDO);
                    log.debug("打卡配置更新到原始表结果: {}, configId: {}", result, punchConfigDO.getId());
                    return result;
                }
        );
    }

    /**
     * 根据ID物理删除打卡配置
     *
     * @param id 配置ID
     * @return 删除是否成功
     */
    public boolean removeById(Long id) {
        log.info("物理删除打卡配置, id: {}, 当前使用{}表",
                id, isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 物理删除migrate表记录
                    boolean result = punchConfigMigrateDao.removeById(id);
                    log.info("打卡配置从migrate表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                },
                () -> {
                    // 物理删除原始表记录
                    boolean result = punchConfigDao.removeById(id);
                    log.info("打卡配置从原始表物理删除{}, id: {}", result ? "成功" : "失败", id);
                    return result;
                }
        );
    }
}
