package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchConfigMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
@Component(value = "punchConfigDataConverter")
public class PunchConfigDataConverter implements DataConverter<PunchConfigMigrateDO, PunchConfigDO> {

    @Override
    public PunchConfigDO convertFromMigrate(PunchConfigMigrateDO migrateObj) {
        return PunchConfigDOMapstruct.INSTANCE.mapToReal(migrateObj);
    }

    @Override
    public PunchConfigMigrateDO convertFromReal(PunchConfigDO realObj) {
        return PunchConfigDOMapstruct.INSTANCE.mapToMigrate(realObj);
    }

    @Override
    public Class<PunchConfigMigrateDO> getMigrateType() {
        return PunchConfigMigrateDO.class;
    }

    @Override
    public Class<PunchConfigDO> getRealType() {
        return PunchConfigDO.class;
    }
}
