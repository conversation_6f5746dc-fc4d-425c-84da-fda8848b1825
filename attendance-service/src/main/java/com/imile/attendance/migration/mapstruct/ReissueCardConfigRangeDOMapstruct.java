package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 补卡配置范围DO映射器
 * 负责ReissueCardConfigRangeDO和ReissueCardConfigRangeMigrateDO之间的字段映射
 *
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Mapper
public interface ReissueCardConfigRangeDOMapstruct extends BaseMapstruct {

    ReissueCardConfigRangeDOMapstruct INSTANCE = Mappers.getMapper(ReissueCardConfigRangeDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     */
    default Long setModelId(ReissueCardConfigRangeMigrateDO module) {
        return setModelId(module,
                ReissueCardConfigRangeMigrateDO::getId,
                ReissueCardConfigRangeMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     */
    default Long setModelId(ReissueCardConfigRangeDO module) {
        return setModelId(module,
                ReissueCardConfigRangeDO::getId,
                ReissueCardConfigRangeDO::setId);
    }

    /**
     * 将migrate表DO转换为原始表DO
     *
     * @param migrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(migrateDO))")
    ReissueCardConfigRangeDO mapToReal(ReissueCardConfigRangeMigrateDO migrateDO);

    /**
     * 将原始表DO转换为migrate表DO
     *
     * @param realDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(realDO))")
    ReissueCardConfigRangeMigrateDO mapToMigrate(ReissueCardConfigRangeDO realDO);
}
