package com.imile.attendance.migration.dto;

import lombok.Data;

import java.util.Date;

/**
 * 批量更新排班类型结果DTO
 * 
 * <AUTHOR> chen
 * @since 2025/7/2
 */
@Data
public class BatchUpdateShiftTypeResult {
    
    /**
     * 总记录数
     */
    private Integer totalCount;
    
    /**
     * 已处理记录数
     */
    private Integer processedCount;
    
    /**
     * 成功更新记录数
     */
    private Integer successCount;
    
    /**
     * 失败记录数
     */
    private Integer failedCount;
    
    /**
     * 处理状态：RUNNING, COMPLETED, FAILED, CANCELLED
     */
    private String status;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 批次大小
     */
    private Integer batchSize;
    
    /**
     * 总批次数
     */
    private Integer totalBatches;
    
    /**
     * 已完成批次数
     */
    private Integer completedBatches;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 处理进度百分比
     */
    public Double getProgressPercentage() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        return (processedCount.doubleValue() / totalCount.doubleValue()) * 100;
    }
    
    /**
     * 是否处理完成
     */
    public Boolean isCompleted() {
        return "COMPLETED".equals(status) || "FAILED".equals(status) || "CANCELLED".equals(status);
    }
    
    /**
     * 创建初始结果
     */
    public static BatchUpdateShiftTypeResult createInitial(Integer totalCount, Integer batchSize) {
        BatchUpdateShiftTypeResult result = new BatchUpdateShiftTypeResult();
        result.setTotalCount(totalCount);
        result.setProcessedCount(0);
        result.setSuccessCount(0);
        result.setFailedCount(0);
        result.setStatus("RUNNING");
        result.setStartTime(new Date());
        result.setBatchSize(batchSize);
        result.setTotalBatches((int) Math.ceil(totalCount.doubleValue() / batchSize.doubleValue()));
        result.setCompletedBatches(0);
        return result;
    }
    
    /**
     * 更新进度
     */
    public void updateProgress(Integer processed, Integer success, Integer failed, Integer completedBatches) {
        this.processedCount = processed;
        this.successCount = success;
        this.failedCount = failed;
        this.completedBatches = completedBatches;
    }
    
    /**
     * 标记完成
     */
    public void markCompleted() {
        this.status = "COMPLETED";
        this.endTime = new Date();
        if (this.startTime != null) {
            this.duration = this.endTime.getTime() - this.startTime.getTime();
        }
    }
    
    /**
     * 标记失败
     */
    public void markFailed(String errorMessage) {
        this.status = "FAILED";
        this.endTime = new Date();
        this.errorMessage = errorMessage;
        if (this.startTime != null) {
            this.duration = this.endTime.getTime() - this.startTime.getTime();
        }
    }
}
