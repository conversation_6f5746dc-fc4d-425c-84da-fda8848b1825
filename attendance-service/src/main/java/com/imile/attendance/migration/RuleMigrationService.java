package com.imile.attendance.migration;

/**
 * <AUTHOR> chen
 * @Date 2025/6/18 
 * @Description
 */
public interface RuleMigrationService {

    /**
     * HR考勤组到新考勤规则的迁移
     *
     * @param country 国家代码
     * @return 迁移结果
     */
    Boolean migrateAttendanceRules(String country);

    /**
     * 根据映射记录回滚考勤规则
     *
     * @param country 国家代码
     * @return 回滚结果
     */
    Boolean rollbackAttendanceRules(String country);
}
