package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;

import java.util.List;

/**
 * 考勤规则回滚服务接口
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
public interface RollbackService {

    /**
     * 根据映射记录回滚考勤规则
     *
     * @param country 国家代码
     * @return 回滚结果
     */
    Boolean rollbackAttendanceRulesByCountry(String country);

    /**
     * 根据映射记录列表回滚考勤规则
     *
     * @param mappingConfigs 映射记录列表
     * @return 回滚结果
     */
    Boolean rollbackAttendanceRulesByMappings(List<MappingRuleConfigDO> mappingConfigs);

    /**
     * 回滚打卡规则
     *
     * @param ruleId 规则ID
     * @return 回滚结果
     */
    Boolean rollbackPunchConfig(Long ruleId);

    /**
     * 回滚补卡规则
     *
     * @param ruleId 规则ID
     * @return 回滚结果
     */
    Boolean rollbackReissueCardConfig(Long ruleId);

    /**
     * 回滚班次规则
     *
     * @param ruleId 规则ID
     * @return 回滚结果
     */
    Boolean rollbackPunchClassConfig(Long ruleId);

    /**
     * 删除映射记录
     *
     * @param mappingId 映射记录ID
     * @return 删除结果
     */
    Boolean deleteMappingRecord(Long mappingId);

    /**
     * 批量删除映射记录
     *
     * @param mappingIds 映射记录ID列表
     * @return 删除结果
     */
    Boolean batchDeleteMappingRecords(List<Long> mappingIds);

    /**
     * 回滚班次映射记录
     * 删除班次相关的映射记录，包括：
     * 1. MappingPunchClassConfigDO - 班次映射表
     * 2. MappingPunchClassConfigItemDO - 班次时间配置映射表
     * 3. MappingPunchClassConfigRangeDO - 班次范围映射表
     *
     * @param punchClassConfigId 班次规则ID
     * @return 删除结果
     */
    Boolean rollbackPunchClassMappingRecords(Long punchClassConfigId);

    /**
     * 根据国家回滚班次规则及映射记录
     * 删除指定国家的所有班次规则及相关数据，包括：
     * 1. PunchClassConfigDO - 班次规则表
     * 2. PunchClassConfigRangeDO - 班次范围配置表
     * 3. MappingPunchClassConfigDO - 班次映射表
     * 4. MappingPunchClassConfigItemDO - 班次时间配置映射表
     * 5. MappingPunchClassConfigRangeDO - 班次范围映射表
     *
     * @param country 国家代码
     * @return 删除结果
     */
    Boolean rollbackPunchClassMappingRecordsByCountry(String country);
}
