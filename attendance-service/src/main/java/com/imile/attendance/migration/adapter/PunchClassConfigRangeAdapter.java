package com.imile.attendance.migration.adapter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.migrate.PunchClassConfigRangeMigrateDao;
import com.imile.attendance.infrastructure.repository.rule.mapper.PunchClassConfigRangeMapper;
import com.imile.attendance.infrastructure.repository.rule.mapper.migrate.PunchClassConfigRangeMigrateMapper;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigRangeMigrateDO;
import com.imile.attendance.migration.adapter.base.AbstractPairAdapter;
import com.imile.attendance.migration.adapter.base.DataConverter;
import com.imile.common.enums.IsDeleteEnum;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 班次配置范围适配器
 * 支持在原始表和migrate表之间透明切换的班次配置范围DAO操作
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class PunchClassConfigRangeAdapter extends AbstractPairAdapter<PunchClassConfigRangeMigrateDO, PunchClassConfigRangeDO> {

    @Resource
    private PunchClassConfigRangeMigrateDao punchClassConfigRangeMigrateDao;
    @Resource
    private PunchClassConfigRangeMigrateMapper punchClassConfigRangeMigrateMapper;
    @Resource
    private PunchClassConfigRangeDao punchClassConfigRangeDao;
    @Resource
    private PunchClassConfigRangeMapper punchClassConfigRangeMapper;

    public PunchClassConfigRangeAdapter(List<DataConverter<PunchClassConfigRangeMigrateDO, PunchClassConfigRangeDO>> dataConverters) {
        super(dataConverters);
    }

    /**
     * 保存单个班次配置范围
     * 根据配置自动选择使用原始表还是migrate表
     * 
     * @param punchClassConfigRangeDO 班次配置范围DO对象
     */
    public void saveOne(PunchClassConfigRangeDO punchClassConfigRangeDO) {
        log.info("保存班次配置范围, rangeId: {}, ruleConfigId: {}, bizId: {}, 当前使用{}表", 
                punchClassConfigRangeDO.getId(), punchClassConfigRangeDO.getRuleConfigId(), 
                punchClassConfigRangeDO.getBizId(), isShouldUseMigrate() ? "migrate" : "原始");
        
        super.saveOrUpdateOneWrapper(
                punchClassConfigRangeDO,
                migrateDO -> {
                    // 保存到migrate表
                    punchClassConfigRangeMigrateDao.save(migrateDO);
                    log.debug("班次配置范围已保存到migrate表, rangeId: {}", migrateDO.getId());
                },
                // 保存到原始表
                realDO -> {
                    punchClassConfigRangeDao.save(realDO);
                    log.debug("班次配置范围已保存到原始表, rangeId: {}", realDO.getId());
                }
        );
    }

    /**
     * 批量保存班次配置范围
     * 
     * @param punchClassConfigRangeDOList 班次配置范围DO列表
     */
    public void saveBatch(List<PunchClassConfigRangeDO> punchClassConfigRangeDOList) {
        if (punchClassConfigRangeDOList == null || punchClassConfigRangeDOList.isEmpty()) {
            log.warn("批量保存班次配置范围: 输入列表为空");
            return;
        }
        
        log.info("批量保存班次配置范围, 数量: {}, 当前使用{}表", 
                punchClassConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        List<List<PunchClassConfigRangeDO>> partitions = Lists.partition(punchClassConfigRangeDOList, BusinessConstant.MAX_BATCH_SIZE);
        for (List<PunchClassConfigRangeDO> partition : partitions) {
            saveOrUpdateBatchWrapper(
                    partition,
                    migrateDOList -> {
                        // 批量保存到migrate表
                        punchClassConfigRangeMigrateMapper.insertBatchSomeColumn(migrateDOList);
                        log.debug("批量保存班次配置范围到migrate表完成, 数量: {}", migrateDOList.size());
                    },
                    realDOList -> {
                        // 批量保存到原始表
                        punchClassConfigRangeMapper.insertBatchSomeColumn(realDOList);
                        log.debug("批量保存班次配置范围到原始表完成, 数量: {}", realDOList.size());
                    }
            );
        }
    }

    /**
     * 根据规则配置ID列表查询范围
     *
     * @param ruleConfigIds 规则配置ID列表
     * @return 配置范围列表（转换为原始DO类型）
     */
    public List<PunchClassConfigRangeDO> selectByRuleConfigIds(List<Long> ruleConfigIds) {
        log.debug("根据规则配置ID列表查询班次配置范围, ruleConfigIds数量: {}, 当前使用{}表",
                ruleConfigIds != null ? ruleConfigIds.size() : 0, isShouldUseMigrate() ? "migrate" : "原始");

        return super.readBatchWrapper(
                () -> {
                    LambdaQueryWrapper<PunchClassConfigRangeMigrateDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(PunchClassConfigRangeMigrateDO::getRuleConfigId, ruleConfigIds);
                    return punchClassConfigRangeMigrateDao.list(queryWrapper);
                },
                () -> {
                    LambdaQueryWrapper<PunchClassConfigRangeDO> queryWrapper = Wrappers.lambdaQuery();
                    queryWrapper.in(PunchClassConfigRangeDO::getRuleConfigId, ruleConfigIds);
                    return punchClassConfigRangeDao.list(queryWrapper);
                }
        );
    }

    /**
     * 批量根据ID更新范围配置
     *
     * @param punchClassConfigRangeDOList 范围配置DO列表
     */
    public Integer updateBatchById(List<PunchClassConfigRangeDO> punchClassConfigRangeDOList) {
        if (punchClassConfigRangeDOList == null || punchClassConfigRangeDOList.isEmpty()) {
            log.warn("批量更新班次配置范围: 输入列表为空");
            return 0;
        }

        log.info("批量根据ID更新班次配置范围, 数量: {}, 当前使用{}表",
                punchClassConfigRangeDOList.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 转换并批量更新migrate表
                    List<PunchClassConfigRangeMigrateDO> migrateDOList = punchClassConfigRangeDOList.stream()
                            .map(realDO -> getConverter().convertFromReal(realDO))
                            .collect(Collectors.toList());
                    Integer result = punchClassConfigRangeMigrateMapper.replaceIntoBatchSomeColumn(migrateDOList);
                    log.debug("批量更新班次配置范围到migrate表结果: {}, 数量: {}", result, migrateDOList.size());
                    return result;
                },
                () -> {
                    // 批量更新原始表
                    Integer result = punchClassConfigRangeMapper.replaceIntoBatchSomeColumn(punchClassConfigRangeDOList);
                    log.debug("批量更新班次配置范围到原始表结果: {}, 数量: {}", result, punchClassConfigRangeDOList.size());
                    return result;
                }
        );
    }

    //removeBatchByIds
    public boolean removeBatchByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            log.warn("批量物理删除班次配置范围: ID列表为空");
            return true;
        }

        log.info("批量物理删除班次配置范围, 数量: {}, 当前使用{}表",
                ids.size(), isShouldUseMigrate() ? "migrate" : "原始");

        return commonWriteWithResult(
                () -> {
                    // 批量物理删除migrate表记录
                    boolean result = punchClassConfigRangeMigrateDao.removeByIds(ids);
                    log.info("班次配置范围从migrate表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                },
                () -> {
                    // 批量物理删除原始表记录
                    boolean result = punchClassConfigRangeDao.removeByIds(ids);
                    log.info("班次配置范围从原始表批量物理删除{}, 数量: {}", result ? "成功" : "失败", ids.size());
                    return result;
                }
        );
    }
}
