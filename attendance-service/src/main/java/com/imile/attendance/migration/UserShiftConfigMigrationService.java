package com.imile.attendance.migration;

import com.imile.attendance.migration.dto.BatchUpdateShiftTypeResult;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/18
 * @Description
 */
public interface UserShiftConfigMigrationService {

    /**
     * 执行完整的数据迁移
     *
     * @param country 国家代码
     * @return 迁移结果
     */
    Boolean executeFullMigration(String country);

    /**
     * 仅迁移历史数据
     *
     * @param country    国家代码
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果
     */
    Boolean migrateHistoryDataOnly(String country, Long startDayId, Long endDayId);

    /**
     * 仅迁移当前数据
     *
     * @param country    国家代码
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果
     */
    Boolean migrateCurrentDataOnly(String country, Long startDayId, Long endDayId);

    /**
     * 批量更新HRMS迁移排班数据的排班类型
     * <p>
     * 该方法会自动查询需要处理的数据总数，并使用固定的批次大小（1000条记录）
     * 进行分页处理，包含进度监控、错误恢复机制和详细的日志记录。
     *
     * @return 批量更新结果，包含详细的处理统计信息
     */
    BatchUpdateShiftTypeResult batchUpdateShiftTypeForHrmsMigration();

    /**
     * 根据用户ID列表和日期范围迁移数据（先删除这些用户的历史排班数据）
     * 查询指定用户在指定日期范围内的HR排班数据，并迁移转换为新系统的UserShiftConfigDO实体对象
     *
     * @param userIds    用户ID列表
     * @param startDayId 开始日期ID（日期标识）
     * @param endDayId   结束日期ID（日期标识）
     * @return 迁移结果，true表示成功，false表示失败
     */
    Boolean migrateByUserIdsAndDateRange(List<Long> userIds, Long startDayId, Long endDayId);

    /**
     * 根据国家和入职确认时间范围执行排班数据迁移
     * <p>
     * 该方法首先根据国家和入职确认时间查找符合条件的用户，然后为这些用户执行
     * 指定时间范围内的排班数据迁移
     *
     * @param locationCountry 国家
     * @param confirmDayId    入职确认开始时间，用于筛选在此时间之后入职确认的用户
     * @param shiftEndDayId   排班迁移的结束时间（Long类型的dayId格式，如20250101）
     * @return 迁移结果，true表示成功，false表示失败
     */
    Boolean migrateByCountryAndConfirmDate(String locationCountry, Long confirmDayId, Long shiftEndDayId);

    /**
     * 根据国家为非灰度用户执行排班数据迁移
     *
     * @param country         国家
     * @param shiftStartDayId 排班开始日期ID
     * @return 迁移结果
     */
    Boolean migrateByCountryNoGrayscaleUser(String country, Long shiftStartDayId);


    /**
     * 回滚当前数据迁移操作
     * <p>
     * 该方法用于回滚由 migrateCurrentDataOnly 方法创建的所有数据，执行物理删除操作。
     *
     * @param country    国家，用于限定回滚范围
     * @param startDayId 开始日期ID，用于限定回滚的日期范围（可选）
     * @param endDayId   结束日期ID，用于限定回滚的日期范围（可选）
     * @return 回滚结果，true表示成功，false表示失败
     * @throws IllegalArgumentException 当国家为空时抛出
     */
    Boolean rollbackCurrentDataOnly(String country, Long startDayId, Long endDayId);
}
