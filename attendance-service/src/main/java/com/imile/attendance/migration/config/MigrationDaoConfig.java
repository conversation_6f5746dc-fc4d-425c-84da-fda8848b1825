package com.imile.attendance.migration.config;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 迁移DAO配置类
 * 根据配置决定使用原始表还是migrate表的DAO实现
 *
 * <AUTHOR> chen
 * @since 2025/6/16
 */
@Slf4j
@Component
@Getter
public class MigrationDaoConfig {

    @Value("${attendance.migration.use-migrate-tables:false}")
    private Boolean useMigrateTables;

    /**
     * 是否使用migrate表
     */
    public boolean isUseMigrateTables() {
        return Boolean.TRUE.equals(useMigrateTables);
    }

    /**
     * 记录当前使用的表类型
     */
    public void logCurrentTableType() {
        if (isUseMigrateTables()) {
            log.info("当前使用migrate表进行HR考勤组迁移测试");
        } else {
            log.info("当前使用原始表进行HR考勤组迁移");
        }
    }
}


