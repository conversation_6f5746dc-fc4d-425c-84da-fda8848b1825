package com.imile.attendance.migration.param;

import com.imile.attendance.enums.ClassNatureEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 刷新指定国家用户班次性质请求参数
 * 
 * <AUTHOR> chen
 * @Date 2025/7/24
 * @Description 封装刷新用户班次性质的请求参数，包含国家、目标班次性质和配置用户列表
 */
@Data
public class RefreshUserClassNatureByCountryRequest {

    /**
     * 国家
     * 必填参数，指定需要刷新班次性质的国家
     */
    @NotBlank(message = "国家不能为空")
    private String countryCode;

    /**
     * 目标班次性质
     * 必填参数，指定目标班次性质（MULTIPLE_CLASS：多班次，FIXED_CLASS：固定班次）
     */
    @NotNull(message = "目标班次性质不能为空")
    private ClassNatureEnum targetClassNature;

    /**
     * 配置用户编码列表
     * 可选参数，指定需要设置为目标班次性质的特定用户编码列表
     * 如果为空，则对该国家下所有符合条件的用户进行处理
     */
    private List<String> configUserCodes;
}