package com.imile.attendance.migration.adapter.base;

import com.imile.attendance.migration.config.MigrationDaoConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.function.BiFunction;
import java.util.function.Supplier;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
@Slf4j
public abstract class AbstractMigrationAdapter implements BaseMigration {

    @Resource
    private MigrationDaoConfig migrationDaoConfig;

    @Override
    public Boolean isShouldUseMigrate() {
        return migrationDaoConfig.isUseMigrateTables();
    }

    /**
     * 通用查询（如count、sum等）
     * @param migrateSupplier 迁移表查询逻辑
     * @param realSupplier 实体表查询逻辑
     * @return 结果
     */
    protected <R> R commonQuery(Supplier<R> migrateSupplier, Supplier<R> realSupplier) {
        return isShouldUseMigrate() ?
                migrateSupplier.get() :
                realSupplier.get();
    }

    protected void commonQuery(Runnable migrateRunnable, Runnable realRunnable) {
        if (isShouldUseMigrate()) {
            migrateRunnable.run();
        } else {
            realRunnable.run();
        }
    }

    /**
     * 通用写入（支持双写，无返回值）
     *
     * @param newRunnable 新系统写入逻辑
     * @param oldRunnable 旧系统写入逻辑
     */
    protected void commonWrite(Runnable newRunnable, Runnable oldRunnable) {
        commonWriteNoTx(newRunnable, oldRunnable);
    }

    /**
     * 通用写入方法（支持双写）
     * @param migrateWriter 新系统写入逻辑（返回新系统结果）
     * @param realWriter 旧系统写入逻辑（返回旧系统结果）
     * @return 最终返回结果
     */
    protected <R> R commonWriteWithResult(
            Supplier<R> migrateWriter,
            Supplier<R> realWriter
    ) {
        return commonWriteWithResult(
                migrateWriter,
                realWriter,
                (migrateResult, realResult) -> {
                    if (isShouldUseMigrate()) {
                        return migrateResult;
                    }
                    return realResult;
                }
        );
    }


    /**
     * 通用写入方法
     * @param migrateWriter 新系统写入逻辑（返回新系统结果）
     * @param realWriter 旧系统写入逻辑（返回旧系统结果）
     * @param resultStrategy 结果处理策略
     * @return 最终返回结果
     */
    protected <R> R commonWriteWithResult(
            Supplier<R> migrateWriter,
            Supplier<R> realWriter,
            BiFunction<R, R, R> resultStrategy
    ) {
        return commonWriteResultWithTx(
                migrateWriter,
                realWriter,
                resultStrategy
        );
    }

    /**
     * 支持事务的写方法
     */
    protected <R> R commonWriteResultWithTx(
            Supplier<R> migrateWriter,
            Supplier<R> realWriter,
            BiFunction<R, R, R> resultStrategy
    ) {
        R migrateResult = null;
        R realResult = null;

        try {
            if (isShouldUseMigrate()) {
                migrateResult = migrateWriter.get();
            } else {
                realResult = realWriter.get();
            }
            // 根据策略合并结果
            return resultStrategy.apply(migrateResult, realResult);
        } catch (Exception e) {
            log.error("new attendance commonWriteResultWithTx fail:", e);
            throw e;
        }
    }

    protected void commonWriteNoTx(Runnable migrateRunnable, Runnable realRunnable) {
        if (isShouldUseMigrate()) {
            migrateRunnable.run();
        } else {
            realRunnable.run();
        }
    }
}
