package com.imile.attendance.migration.adapter.base;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
public class TypeResolver {

    private final Class<?> targetClass;

    public TypeResolver(Class<?> targetClass) {
        this.targetClass = targetClass;
    }

    @SuppressWarnings("unchecked")
    public <T> Class<T> resolveType(int index) {
        Type genericSuperclass = targetClass.getGenericSuperclass();
        if (!(genericSuperclass instanceof ParameterizedType)) {
            throw new IllegalStateException("Class must be a parameterized type");
        }
        ParameterizedType parameterizedType = (ParameterizedType) genericSuperclass;
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        return (Class<T>) actualTypeArguments[index];
    }
}
