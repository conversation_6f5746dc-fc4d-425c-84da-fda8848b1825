package com.imile.attendance.migration.util;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/6/16 
 * @Description
 */
@Slf4j
public class ClassTimeUtil {


    /**
     * 计算工作时长
     *
     * @param hrItem HR班次时间配置
     * @return Pair对象：Left=出勤时长, Right=法定工作时长
     */
    public static Pair<BigDecimal, BigDecimal> calculateWorkingHours(HrAttendanceClassItemConfigDTO hrItem) {
        Date punchInTime = hrItem.getPunchInTime();
        Date punchOutTime = hrItem.getPunchOutTime();
        Date restStartTime = hrItem.getRestStartTime();
        Date restEndTime = hrItem.getRestEndTime();

        if (punchInTime == null || punchOutTime == null) {
            log.warn("上班时间或下班时间为空，无法计算工作时长");
            return Pair.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }

        // 计算出勤时长 = 下班时间 - 上班时间
        long attendanceMinutes = calculateTimeDifferenceInMinutes(punchInTime, punchOutTime, hrItem.isAcrossDay());
        BigDecimal attendanceHours = BigDecimal.valueOf(attendanceMinutes).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);

        // 计算法定工作时长 = 出勤时长 - 休息时长（如果有休息时间）
        BigDecimal legalWorkingHours = attendanceHours;

        if (restStartTime != null && restEndTime != null) {
            // 验证休息时间的合理性
            RestTimeValidationResult validationResult = validateRestTime(punchInTime, punchOutTime, restStartTime, restEndTime, hrItem.isAcrossDay());

            if (validationResult.isValid()) {
                long restMinutes = calculateRestTimeDifferenceInMinutes(restStartTime, restEndTime, validationResult.isRestAcrossDay());
                BigDecimal restHours = BigDecimal.valueOf(restMinutes).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);

                // 检查休息时间是否超过工作时间
                if (restHours.compareTo(attendanceHours) >= 0) {
                    log.warn("休息时间({} 小时)超过或等于出勤时间({} 小时)，这是异常情况", restHours, attendanceHours);
                    throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, "休息时间超过出勤时间");
                } else {
                    legalWorkingHours = attendanceHours.subtract(restHours);
                }

                log.debug("休息时长: {} 小时", restHours);
            } else {
                log.warn("休息时间设置不合理: {}", validationResult.getErrorMessage());
                // 休息时间不合理时，法定工作时长等于出勤时长
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, validationResult.getErrorMessage());
            }
        }

        // 确保时长不为负数
        if (attendanceHours.compareTo(BigDecimal.ZERO) < 0) {
            attendanceHours = BigDecimal.ZERO;
        }
        if (legalWorkingHours.compareTo(BigDecimal.ZERO) < 0) {
            legalWorkingHours = BigDecimal.ZERO;
        }

        log.debug("计算工作时长完成 - 出勤时长: {}小时, 法定工作时长: {}小时", attendanceHours, legalWorkingHours);
        return Pair.of(attendanceHours, legalWorkingHours);
    }

    /**
     * 验证休息时间的合理性
     *
     * @param punchInTime 上班时间
     * @param punchOutTime 下班时间
     * @param restStartTime 休息开始时间
     * @param restEndTime 休息结束时间
     * @param workAcrossDay 工作是否跨天
     * @return 验证结果
     */
    private static RestTimeValidationResult validateRestTime(Date punchInTime, Date punchOutTime,
                                                      Date restStartTime, Date restEndTime, boolean workAcrossDay) {

        // 转换为标准时间进行比较
//        String punchInTimeStr = DateHelper.formatHHMMSS(punchInTime);
//        String punchOutTimeStr = DateHelper.formatHHMMSS(punchOutTime);
        String restStartTimeStr = DateHelper.formatHHMMSS(restStartTime);
        String restEndTimeStr = DateHelper.formatHHMMSS(restEndTime);

//        Date punchInDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + punchInTimeStr);
//        Date punchOutDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + punchOutTimeStr);
        Date restStartDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + restStartTimeStr);
        Date restEndDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + restEndTimeStr);

//        // 如果工作跨天，调整下班时间
//        if (workAcrossDay || punchOutDateTime.before(punchInDateTime)) {
//            punchOutDateTime = new Date(punchOutDateTime.getTime() + 24 * 60 * 60 * 1000);
//        }

        // 判断休息时间是否跨天
        boolean restAcrossDay = restEndDateTime.before(restStartDateTime);
//        if (restAcrossDay) {
//            restEndDateTime = new Date(restEndDateTime.getTime() + 24 * 60 * 60 * 1000);
//        }

//        // 验证休息开始时间是否在工作时间范围内
//        boolean restStartValid = isTimeInRange(restStartDateTime, punchInDateTime, punchOutDateTime, workAcrossDay);
//
//        // 验证休息结束时间是否在工作时间范围内（考虑跨天情况）
//        Date restEndForValidation = restEndDateTime;
//        if (restAcrossDay && !workAcrossDay) {
//            // 休息跨天但工作不跨天的情况，需要特殊处理
//            // 检查休息结束时间是否在第二天的合理范围内
//            if (restEndForValidation.getTime() - restStartDateTime.getTime() > 12 * 60 * 60 * 1000) {
//                return new RestTimeValidationResult(false, "休息时间跨天且超过12小时，不合理", false);
//            }
//        }
//
//        boolean restEndValid = isTimeInRange(restEndForValidation, punchInDateTime, punchOutDateTime, workAcrossDay || restAcrossDay);
//
//        if (!restStartValid) {
//            return new RestTimeValidationResult(false, "休息开始时间不在工作时间范围内", restAcrossDay);
//        }
//
//        if (!restEndValid && !restAcrossDay) {
//            return new RestTimeValidationResult(false, "休息结束时间不在工作时间范围内", restAcrossDay);
//        }

//        // 验证休息时长是否合理（不超过8小时）
//        long restMinutes = (restEndDateTime.getTime() - restStartDateTime.getTime()) / (60 * 1000);
//        if (restMinutes > 8 * 60) {
//            return new RestTimeValidationResult(false, "休息时间超过8小时，不合理", restAcrossDay);
//        }
//
//        if (restMinutes < 0) {
//            return new RestTimeValidationResult(false, "休息时间为负数，不合理", restAcrossDay);
//        }

        return new RestTimeValidationResult(true, "休息时间设置合理", restAcrossDay);
    }

    /**
     * 判断时间是否在指定范围内
     *
     * @param targetTime 目标时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param acrossDay 是否跨天
     * @return 是否在范围内
     */
    private static boolean isTimeInRange(Date targetTime, Date startTime, Date endTime, boolean acrossDay) {
        if (!acrossDay) {
            return !targetTime.before(startTime) && !targetTime.after(endTime);
        } else {
            // 跨天情况：目标时间应该在开始时间之后，或在第二天的结束时间之前
            return !targetTime.before(startTime) || targetTime.getTime() <= endTime.getTime();
        }
    }

    /**
     * 计算休息时间差（支持跨天）
     *
     * @param restStartTime 休息开始时间
     * @param restEndTime 休息结束时间
     * @param restAcrossDay 休息是否跨天
     * @return 分钟差
     */
    private static long calculateRestTimeDifferenceInMinutes(Date restStartTime, Date restEndTime, boolean restAcrossDay) {
        String restStartTimeStr = DateHelper.formatHHMMSS(restStartTime);
        String restEndTimeStr = DateHelper.formatHHMMSS(restEndTime);

        Date restStartDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + restStartTimeStr);
        Date restEndDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + restEndTimeStr);

        // 如果休息跨天，结束时间加一天
        if (restAcrossDay) {
            restEndDateTime = new Date(restEndDateTime.getTime() + 24 * 60 * 60 * 1000);
        }

        long diffInMillis = restEndDateTime.getTime() - restStartDateTime.getTime();
        return diffInMillis / (60 * 1000);
    }

    /**
     * 计算两个时间之间的分钟差
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param isAcrossDay 是否跨天
     * @return 分钟差
     */
    private static long calculateTimeDifferenceInMinutes(Date startTime, Date endTime, boolean isAcrossDay) {
        // 提取时分秒信息
        String startTimeStr = DateHelper.formatHHMMSS(startTime);
        String endTimeStr = DateHelper.formatHHMMSS(endTime);

        // 使用标准日期进行计算
        Date startDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + startTimeStr);
        Date endDateTime = DateHelper.parseYYYYMMDDHHMMSS("1970-01-01 " + endTimeStr);

        // 如果跨天，结束时间加一天
        if (isAcrossDay || endDateTime.before(startDateTime)) {
            endDateTime = new Date(endDateTime.getTime() + 24 * 60 * 60 * 1000);
        }

        // 计算分钟差
        long diffInMillis = endDateTime.getTime() - startDateTime.getTime();
        return diffInMillis / (60 * 1000);
    }

    /**
     * 休息时间验证结果类
     */
    private static class RestTimeValidationResult {
        private final boolean valid;
        private final String errorMessage;
        private final boolean restAcrossDay;

        public RestTimeValidationResult(boolean valid, String errorMessage, boolean restAcrossDay) {
            this.valid = valid;
            this.errorMessage = errorMessage;
            this.restAcrossDay = restAcrossDay;
        }

        public boolean isValid() {
            return valid;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public boolean isRestAcrossDay() {
            return restAcrossDay;
        }
    }
}
