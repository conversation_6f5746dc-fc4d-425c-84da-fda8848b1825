package com.imile.attendance.migration.dto;

import com.imile.attendance.migration.constants.HrPunchConfigTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description HR考勤组DTO
 */
@Data
public class HrAttendanceGroupDTO {

    /**
     * 考勤组ID
     */
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 自由上下班、固定上下班、按班次上班
     */
    private String punchConfigType;

    /**
     * 是否为默认
     */
    private Integer isDefault;

    /**
     * 加班配置 JSON格式
     */
    private String overtimeConfig;

    /**
     * 最大补卡天数
     */
    private Long maxRepunchDays;

    /**
     * 最大补卡次数
     */
    private Long maxRepunchNumber;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 绑定部门
     */
    private String deptIds;

    /**
     * 适用范围列表
     */
    private List<HrAttendanceGroupRangeDTO> rangeList;

    /**
     * 班次配置列表
     */
    private List<HrAttendanceClassConfigDTO> classList;

    /**
     * 是否为默认考勤组
     */
    public Boolean isDefaultGroup() {
        return isDefault != null && isDefault.equals(1);
    }

    /**
     * 是否为自定义考勤组
     */
    public Boolean isCustomGroup() {
        return !isDefaultGroup();
    }

    /**
     * 获取灵活打卡两次的上下班时间间隔,hr的班次配置只有出勤和上班时间，其他数据都要从班次时间获取
     */
    public BigDecimal getFreeWorkTwicePunchTimeInterval() {
        // 如果不是灵活打卡两次，直接返回null
        if (!HrPunchConfigTypeEnum.FREE_WORK.getCode().equals(punchConfigType)) {
            return null;
        }
        if (CollectionUtils.isEmpty(classList)) {
            return null;
        }
        HrAttendanceClassConfigDTO hrAttendanceClassConfigDTO = classList.get(0);
        if (hrAttendanceClassConfigDTO == null) {
            return null;
        }
        List<HrAttendanceClassItemConfigDTO> itemList = hrAttendanceClassConfigDTO.getItemList();
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        HrAttendanceClassItemConfigDTO hrAttendanceClassItemConfigDTO = itemList.get(0);
        if (hrAttendanceClassItemConfigDTO == null) {
            return null;
        }
        return hrAttendanceClassItemConfigDTO.getPunchTimeInterval();
    }


}
