package com.imile.attendance.migration.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 优化迁移服务配置类
 * 支持通过配置文件动态调整优化参数
 *
 * <AUTHOR> chen
 * @since 2025/7/25
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "attendance.migration.optimized")
public class OptimizedMigrationConfig {

    /**
     * HR读取批次大小配置
     */
    private HrReadBatchConfig hrReadBatch = new HrReadBatchConfig();

    /**
     * 本地写入批次大小
     */
    private Integer localWriteBatchSize = 1000;

    /**
     * 流式处理队列容量
     */
    private Integer streamingQueueCapacity = 5;

    /**
     * HRMS数据源名称
     */
    private String hrmsDataSourceName = "hrms";

    /**
     * 错误处理策略
     */
    private ErrorHandlingConfig errorHandling = new ErrorHandlingConfig();

    /**
     * 性能监控配置
     */
    private PerformanceConfig performance = new PerformanceConfig();

    /**
     * 超时配置
     */
    private TimeoutConfig timeout = new TimeoutConfig();

    /**
     * HR读取批次配置
     */
    @Data
    public static class HrReadBatchConfig {
        /**
         * 默认批次大小
         */
        private Integer defaultSize = 8000;

        /**
         * 最小批次大小
         */
        private Integer minSize = 5000;

        /**
         * 最大批次大小
         */
        private Integer maxSize = 10000;

        /**
         * 小数据集阈值（少于此数量使用最小批次）
         */
        private Long smallDatasetThreshold = 50000L;

        /**
         * 大数据集阈值（超过此数量使用最大批次）
         */
        private Long largeDatasetThreshold = 500000L;

        /**
         * 是否启用动态批次大小调整
         */
        private Boolean enableDynamicSizing = true;
    }

    /**
     * 错误处理配置
     */
    @Data
    public static class ErrorHandlingConfig {
        /**
         * 批次错误时是否继续处理
         */
        private Boolean continueOnBatchError = true;

        /**
         * 最大重试次数
         */
        private Integer maxRetries = 3;

        /**
         * 重试间隔（毫秒）
         */
        private Long retryIntervalMs = 1000L;

        /**
         * 是否启用错误恢复
         */
        private Boolean enableErrorRecovery = true;
    }

    /**
     * 性能监控配置
     */
    @Data
    public static class PerformanceConfig {
        /**
         * 最低处理速度阈值（条/秒）
         */
        private Double minProcessingSpeedThreshold = 100.0;

        /**
         * 最低成功率阈值（百分比）
         */
        private Double minSuccessRateThreshold = 95.0;

        /**
         * 是否启用性能警告
         */
        private Boolean enablePerformanceWarning = true;

        /**
         * 详细日志记录间隔（批次数）
         */
        private Integer detailedLogInterval = 10;

        /**
         * 流式处理日志间隔（批次数）
         */
        private Integer streamingLogInterval = 50;
    }

    /**
     * 超时配置
     */
    @Data
    public static class TimeoutConfig {
        /**
         * 生产者超时时间（分钟）
         */
        private Integer producerTimeoutMinutes = 30;

        /**
         * 消费者超时时间（分钟）
         */
        private Integer consumerTimeoutMinutes = 30;

        /**
         * 数据库查询超时时间（秒）
         */
        private Integer queryTimeoutSeconds = 300;

        /**
         * 批次处理超时时间（秒）
         */
        private Integer batchProcessTimeoutSeconds = 60;
    }

    /**
     * 初始化后验证配置
     */
    @PostConstruct
    public void validateConfig() {
        log.info("开始验证优化迁移服务配置...");

        // 验证HR读取批次配置
        if (hrReadBatch.getMinSize() > hrReadBatch.getMaxSize()) {
            throw new IllegalArgumentException("HR读取最小批次大小不能大于最大批次大小");
        }

        if (hrReadBatch.getDefaultSize() < hrReadBatch.getMinSize() || 
            hrReadBatch.getDefaultSize() > hrReadBatch.getMaxSize()) {
            throw new IllegalArgumentException("HR读取默认批次大小必须在最小和最大值之间");
        }

        // 验证本地写入批次大小
        if (localWriteBatchSize <= 0 || localWriteBatchSize > 5000) {
            throw new IllegalArgumentException("本地写入批次大小必须在1-5000之间");
        }

        // 验证队列容量
        if (streamingQueueCapacity <= 0 || streamingQueueCapacity > 20) {
            throw new IllegalArgumentException("流式处理队列容量必须在1-20之间");
        }

        // 验证性能阈值
        if (performance.getMinProcessingSpeedThreshold() <= 0) {
            throw new IllegalArgumentException("最低处理速度阈值必须大于0");
        }

        if (performance.getMinSuccessRateThreshold() < 0 || performance.getMinSuccessRateThreshold() > 100) {
            throw new IllegalArgumentException("最低成功率阈值必须在0-100之间");
        }

        // 验证超时配置
        if (timeout.getProducerTimeoutMinutes() <= 0 || timeout.getConsumerTimeoutMinutes() <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }

        log.info("优化迁移服务配置验证通过");
        logCurrentConfig();
    }

    /**
     * 记录当前配置信息
     */
    private void logCurrentConfig() {
        log.info("=== 优化迁移服务配置信息 ===");
        log.info("HR读取批次 - 默认: {}, 最小: {}, 最大: {}", 
                hrReadBatch.getDefaultSize(), hrReadBatch.getMinSize(), hrReadBatch.getMaxSize());
        log.info("本地写入批次大小: {}", localWriteBatchSize);
        log.info("流式处理队列容量: {}", streamingQueueCapacity);
        log.info("HRMS数据源名称: {}", hrmsDataSourceName);
        log.info("批次错误时继续处理: {}", errorHandling.getContinueOnBatchError());
        log.info("最大重试次数: {}", errorHandling.getMaxRetries());
        log.info("性能警告阈值 - 速度: {}条/秒, 成功率: {}%", 
                performance.getMinProcessingSpeedThreshold(), performance.getMinSuccessRateThreshold());
        log.info("超时配置 - 生产者: {}分钟, 消费者: {}分钟", 
                timeout.getProducerTimeoutMinutes(), timeout.getConsumerTimeoutMinutes());
        log.info("=== 配置信息结束 ===");
    }

    /**
     * 根据数据量计算最优HR读取批次大小
     */
    public int calculateOptimalHrReadBatchSize(long totalCount) {
        if (!hrReadBatch.getEnableDynamicSizing()) {
            return hrReadBatch.getDefaultSize();
        }

        if (totalCount < hrReadBatch.getSmallDatasetThreshold()) {
            return hrReadBatch.getMinSize();  // 小数据集，减少内存占用
        } else if (totalCount < hrReadBatch.getLargeDatasetThreshold()) {
            return hrReadBatch.getDefaultSize();  // 中等数据集，平衡性能和内存
        } else {
            return hrReadBatch.getMaxSize();  // 大数据集，优先考虑性能
        }
    }

    /**
     * 检查是否需要发出性能警告
     */
    public boolean shouldWarnPerformance(double processingSpeed, double successRate) {
        if (!performance.getEnablePerformanceWarning()) {
            return false;
        }

        return processingSpeed < performance.getMinProcessingSpeedThreshold() || 
               successRate < performance.getMinSuccessRateThreshold();
    }

    /**
     * 获取配置摘要信息
     */
    public String getConfigSummary() {
        return String.format("HR批次: %d-%d-%d, 本地批次: %d, 队列: %d, 错误继续: %s", 
                hrReadBatch.getMinSize(), hrReadBatch.getDefaultSize(), hrReadBatch.getMaxSize(),
                localWriteBatchSize, streamingQueueCapacity, errorHandling.getContinueOnBatchError());
    }

    /**
     * 动态更新配置（运行时调整）
     */
    public void updateHrReadBatchSize(int minSize, int defaultSize, int maxSize) {
        if (minSize > 0 && defaultSize > 0 && maxSize > 0 && minSize <= defaultSize && defaultSize <= maxSize) {
            hrReadBatch.setMinSize(minSize);
            hrReadBatch.setDefaultSize(defaultSize);
            hrReadBatch.setMaxSize(maxSize);
            log.info("HR读取批次大小配置已更新: 最小={}, 默认={}, 最大={}", minSize, defaultSize, maxSize);
        } else {
            throw new IllegalArgumentException("无效的批次大小配置");
        }
    }

    /**
     * 动态更新本地写入批次大小
     */
    public void updateLocalWriteBatchSize(int batchSize) {
        if (batchSize > 0 && batchSize <= 5000) {
            this.localWriteBatchSize = batchSize;
            log.info("本地写入批次大小已更新: {}", batchSize);
        } else {
            throw new IllegalArgumentException("本地写入批次大小必须在1-5000之间");
        }
    }
}
