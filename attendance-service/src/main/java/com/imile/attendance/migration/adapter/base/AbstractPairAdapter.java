package com.imile.attendance.migration.adapter.base;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/17 
 * @Description
 */
@Slf4j
public abstract class AbstractPairAdapter<MIGRATE, REAL> extends AbstractMigrationAdapter {


    private final DataConverter<MIGRATE, REAL> converter;
    private final Class<MIGRATE> newType;
    private final Class<REAL> oldType;

    @Autowired
    public AbstractPairAdapter(List<DataConverter<MIGRATE, REAL>> converters) {
        // 在构造函数中完成类型解析和converter选择
        TypeResolver typeResolver = new TypeResolver(getClass());
        this.newType = typeResolver.resolveType(0);
        this.oldType = typeResolver.resolveType(1);
        this.converter = selectConverter(converters);
    }

    private DataConverter<MIGRATE, REAL> selectConverter(List<DataConverter<MIGRATE, REAL>> converters) {
        return converters.stream()
                .filter(c -> c.getMigrateType().equals(newType) && c.getRealType().equals(oldType))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException(
                        String.format("No suitable DataConverter found for NEW type: %s and OLD type: %s",
                                newType.getName(), oldType.getName())
                ));
    }

    /**
     * 获取转换器实例
     * 供子类使用
     */
    protected DataConverter<MIGRATE, REAL> getConverter() {
        return converter;
    }

    //============================返回REAL==============================================

    // 通用读方法(返回REAL)
    protected REAL readWrapper(Supplier<MIGRATE> migrateSupplier, Supplier<REAL> realSupplier) {
        return isShouldUseMigrate()
                ? Optional.ofNullable(migrateSupplier.get())
                .map(converter::convertFromMigrate)
                .orElse(null) //如果数据为空，不报错
                : realSupplier.get();
    }


    // 通用批量读方法(返回REAL)
    protected List<REAL> readBatchWrapper(Supplier<List<MIGRATE>> migrateBatchSupplier,
                                          Supplier<List<REAL>> realBatchSupplier) {
        if (!isShouldUseMigrate()) {
            return realBatchSupplier.get();
        }

        return Optional.ofNullable(migrateBatchSupplier.get())
                .map(this::convertBatchToReal)
                .orElseGet(Collections::emptyList);
    }

    // 通用条件查询方法(返回Real)
    protected List<REAL> convertBatchToReal(List<MIGRATE> migrateDataList) {
        return migrateDataList.parallelStream()
                .map(converter::convertFromMigrate)
                .collect(Collectors.toList());
    }

    //============================返回MIGRATE==============================================

    // 通用读方法(返回NEW)
    protected MIGRATE readNewWrapper(Supplier<MIGRATE> migrateSupplier, Supplier<REAL> realSupplier) {
        return isShouldUseMigrate() ?
                migrateSupplier.get() :
                Optional.ofNullable(realSupplier.get())
                        .map(converter::convertFromReal)
                        .orElse(null); //如果数据为空，不报错
    }


    // 通用批量读方法(返回NEW)
    protected List<MIGRATE> readNewBatchWrapper(Supplier<List<MIGRATE>> migrateBatchSupplier,
                                                Supplier<List<REAL>> realBatchSupplier) {
        if (isShouldUseMigrate()) {
            return migrateBatchSupplier.get();
        }
        return Optional.ofNullable(realBatchSupplier.get())
                .map(this::convertBatchToNew)
                .orElseGet(Collections::emptyList);
    }

    // 通用条件查询方法(返回NEW)
    protected List<MIGRATE> convertBatchToNew(List<REAL> newDataList) {
        return newDataList.parallelStream()
                .map(converter::convertFromReal)
                .collect(Collectors.toList());
    }

    // 写入操作添加异常处理
    protected void saveOrUpdateOneWrapper(REAL realData,
                                          Consumer<MIGRATE> migrateConsumer,
                                          Consumer<REAL> realConsumer) {
        saveOrUpdateOneNoTx(realData, migrateConsumer, realConsumer);
    }


    protected void saveOrUpdateBatchWrapper(Collection<REAL> realDataList,
                                            Consumer<Collection<MIGRATE>> migrateBatchConsumer,
                                            Consumer<Collection<REAL>> realBatchConsumer) {
        saveOrUpdateBatchWrapperNoTx(realDataList, migrateBatchConsumer, realBatchConsumer);
    }

    private void saveOrUpdateOneNoTx(REAL realData,
                                     Consumer<MIGRATE> migrateConsumer,
                                     Consumer<REAL> realConsumer) {
        if (isShouldUseMigrate()) {
            MIGRATE migrateData = converter.convertFromReal(realData);
            migrateConsumer.accept(migrateData);
        } else {
            realConsumer.accept(realData);
        }
    }

    /**
     * 批量保存(带事务)
     */
    private void saveOrUpdateBatchWrapperNoTx(Collection<REAL> realDataList,
                                                Consumer<Collection<MIGRATE>> migrateBatchConsumer,
                                                Consumer<Collection<REAL>> realBatchConsumer) {
        if (isShouldUseMigrate()) {
            Collection<MIGRATE> newDataList = realDataList.stream()
                    .map(converter::convertFromReal)
                    .collect(Collectors.toList());
            migrateBatchConsumer.accept(newDataList);
        } else {
            realBatchConsumer.accept(realDataList);
        }
    }


}
