package com.imile.attendance.migration.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description HR考勤班次时间配置DTO
 */
@Data
public class HrAttendanceClassItemConfigDTO {

    /**
     * 时间配置ID
     */
    private Long id;

    /**
     * 班次规则ID
     */
    private Long punchClassId;

    /**
     * 序号
     */
    private Integer sortNo;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private Date punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */
    private Date latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private Date latestPunchOutTime;

    /**
     * 是否跨天 跨天是指打卡时间是第二天：0：不跨天，1：跨天
     */
    private Integer isAcross;

    /**
     * 上下班打卡时间间隔 单位：小时
     */
    private BigDecimal punchTimeInterval;

    /**
     * 状态
     */
    private String status;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 上班和最早打卡时间间隔
     */
    private BigDecimal punchInTimeInterval;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private Date restEndTime;

    /**
     * 是否跨天
     */
    public Boolean isAcrossDay() {
        return isAcross != null && isAcross.equals(1);
    }
}
