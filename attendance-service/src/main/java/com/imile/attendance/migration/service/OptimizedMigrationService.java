package com.imile.attendance.migration.service;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendanceClassEmployeeConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsAttendancePunchConfigDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigDO;
import com.imile.attendance.migration.adapter.UserShiftConfigAdapter;
import com.imile.attendance.migration.config.OptimizedMigrationConfig;
import com.imile.attendance.migration.converter.UserShiftConfigDataConverter;
import com.imile.attendance.migration.dto.OptimizedMigrationStatistics;
import com.imile.attendance.migration.processor.UserShiftConfigMigrationProcessor;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 优化的迁移服务
 * 采用大批量读取 + 小批量写入的策略，显著减少数据源切换次数
 * <p>
 * 核心优化：
 * 1. 手动控制数据源切换，避免@DSTransactional与@Transactional冲突
 * 2. HR数据源大批量读取（5000-10000条），减少数据源切换次数
 * 3. 本地数据源小批量写入（1000条），保持事务粒度合理
 * 4. 支持流式处理，控制内存占用
 * 5. 完善的错误处理和统计监控
 *
 * <AUTHOR> chen
 * @since 2025/7/25
 */
@Slf4j
@Service
public class OptimizedMigrationService {

    @Resource
    private HrmsAttendanceClassEmployeeConfigDao hrmsDao;
    @Resource
    private HrmsAttendancePunchConfigDao hrmsAttendancePunchConfigDao;
    @Resource
    private UserShiftConfigDataConverter dataConverter;
    @Resource
    private UserShiftConfigAdapter userShiftConfigAdapter;
    @Resource
    private OptimizedMigrationConfig optimizedMigrationConfig;

    /**
     * 优化的当前数据迁移方法
     * 采用大批量读取 + 小批量写入策略
     *
     * @param country    国家代码
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果统计
     */
    public OptimizedMigrationStatistics migrateCurrentDataOptimized(String country, Long startDayId, Long endDayId) {
        log.info("开始执行优化的当前数据迁移, country: {}, dayId范围: {} - {}", country, startDayId, endDayId);
        XxlJobLogger.log("开始执行优化的当前数据迁移, country: {}, dayId范围: {} - {}", country, startDayId, endDayId);

        OptimizedMigrationStatistics statistics = new OptimizedMigrationStatistics();
        statistics.setCountry(country);
        statistics.setStartTime(System.currentTimeMillis());

        try {
            // 参数验证
            if (StringUtils.isBlank(country)) {
                log.error("国家参数不能为空");
                XxlJobLogger.log("国家参数不能为空");
                statistics.setSuccess(false);
                statistics.setErrorMessage("国家参数不能为空");
                return statistics;
            }

            // 第一阶段：大批量读取HRMS数据
            List<HrmsAttendanceClassEmployeeConfigDO> allHrmsData = readHrmsDataInLargeBatches(
                    country, startDayId, endDayId, statistics);

            if (CollectionUtils.isEmpty(allHrmsData)) {
                log.info("未找到需要迁移的数据, country: {}", country);
                XxlJobLogger.log("未找到需要迁移的数据, country: {}", country);
                statistics.setSuccess(true);
                return statistics;
            }

            // 第二阶段：小批量写入本地数据
            boolean writeResult = writeDataInSmallBatches(allHrmsData, statistics);

            statistics.setSuccess(writeResult);
            statistics.setEndTime(System.currentTimeMillis());
            statistics.calculateDuration();

            // 记录最终统计信息
            logFinalStatistics(statistics);

            return statistics;

        } catch (Exception e) {
            statistics.setSuccess(false);
            statistics.setErrorMessage(e.getMessage());
            statistics.setEndTime(System.currentTimeMillis());
            statistics.calculateDuration();

            log.error("优化迁移失败, country: {}", country, e);
            XxlJobLogger.log("优化迁移失败, country: {}, error: {}", country, e.getMessage());
            return statistics;
        }
    }

    /**
     * 流式处理的迁移方法
     * 使用生产者-消费者模式，进一步优化内存使用
     *
     * @param country    国家代码
     * @param startDayId 开始日期ID
     * @param endDayId   结束日期ID
     * @return 迁移结果统计
     */
    public OptimizedMigrationStatistics migrateCurrentDataWithStreaming(String country, Long startDayId, Long endDayId) {
        log.info("开始执行流式优化迁移, country: {}, dayId范围: {} - {}", country, startDayId, endDayId);
        XxlJobLogger.log("开始执行流式优化迁移, country: {}, dayId范围: {} - {}", country, startDayId, endDayId);

        OptimizedMigrationStatistics statistics = new OptimizedMigrationStatistics();
        statistics.setCountry(country);
        statistics.setStartTime(System.currentTimeMillis());

        try {
            // 参数验证
            if (StringUtils.isBlank(country)) {
                statistics.setSuccess(false);
                statistics.setErrorMessage("国家参数不能为空");
                return statistics;
            }

            // 使用阻塞队列实现生产者-消费者模式
            BlockingQueue<List<HrmsAttendanceClassEmployeeConfigDO>> dataQueue =
                    new LinkedBlockingQueue<>(optimizedMigrationConfig.getStreamingQueueCapacity());

            // 生产者：读取HRMS数据
            CompletableFuture<Void> producer = CompletableFuture.runAsync(() -> {
                try {
                    produceHrmsData(country, startDayId, endDayId, dataQueue, statistics);
                } catch (Exception e) {
                    log.error("生产者线程异常", e);
                    statistics.setErrorMessage("生产者线程异常: " + e.getMessage());
                }
            });

            // 消费者：写入本地数据
            CompletableFuture<Boolean> consumer = CompletableFuture.supplyAsync(() -> {
                try {
                    return consumeAndWriteData(dataQueue, statistics);
                } catch (Exception e) {
                    log.error("消费者线程异常", e);
                    statistics.setErrorMessage("消费者线程异常: " + e.getMessage());
                    return false;
                }
            });

            // 等待生产者完成
            producer.get(30, TimeUnit.MINUTES);

            // 等待消费者完成
            boolean result = consumer.get(30, TimeUnit.MINUTES);

            statistics.setSuccess(result);
            statistics.setEndTime(System.currentTimeMillis());
            statistics.calculateDuration();

            logFinalStatistics(statistics);
            return statistics;

        } catch (Exception e) {
            statistics.setSuccess(false);
            statistics.setErrorMessage(e.getMessage());
            statistics.setEndTime(System.currentTimeMillis());
            statistics.calculateDuration();

            log.error("流式迁移失败, country: {}", country, e);
            XxlJobLogger.log("流式迁移失败, country: {}, error: {}", country, e.getMessage());
            return statistics;
        }
    }

    /**
     * 大批量读取HRMS数据
     * 手动控制数据源切换，避免注解冲突
     */
    private List<HrmsAttendanceClassEmployeeConfigDO> readHrmsDataInLargeBatches(
            String country, Long startDayId, Long endDayId, OptimizedMigrationStatistics statistics) {

        List<HrmsAttendanceClassEmployeeConfigDO> allData = new ArrayList<>();

        // 手动切换到HRMS数据源
        DynamicDataSourceContextHolder.push(optimizedMigrationConfig.getHrmsDataSourceName());
        log.info("已切换到HRMS数据源进行大批量读取");

        try {
            // 查询考勤组ID列表
            long queryStartTime = System.currentTimeMillis();
            List<Long> punchConfigIds = queryPunchConfigIdsByCountry(country);
            statistics.setPunchConfigCount(punchConfigIds.size());

            if (CollectionUtils.isEmpty(punchConfigIds)) {
                log.warn("未找到国家{}的考勤组配置", country);
                return allData;
            }

            // 统计总数据量
            Long totalCount = hrmsDao.countByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId);
            statistics.setTotalRecordCount(totalCount);

            long queryEndTime = System.currentTimeMillis();
            log.info("HRMS数据统计完成, country: {}, 考勤组数量: {}, 总记录数: {}, 耗时: {}ms",
                    country, punchConfigIds.size(), totalCount, queryEndTime - queryStartTime);

            if (totalCount == 0) {
                return allData;
            }

            // 动态计算批次大小
            int hrReadBatchSize = optimizedMigrationConfig.calculateOptimalHrReadBatchSize(totalCount);
            statistics.setHrReadBatchSize(hrReadBatchSize);

            log.info("采用动态批次大小: {} 条/批次", hrReadBatchSize);

            // 分页读取数据
            int currentPage = 1;
            int totalPages = (int) Math.ceil((double) totalCount / hrReadBatchSize);

            while (true) {
                long pageStartTime = System.currentTimeMillis();

                PageInfo<HrmsAttendanceClassEmployeeConfigDO> pageInfo = PageHelper.startPage(currentPage, hrReadBatchSize)
                        .doSelectPageInfo(() -> hrmsDao.pageByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId));

                List<HrmsAttendanceClassEmployeeConfigDO> pageData = pageInfo.getList();
                if (CollectionUtils.isEmpty(pageData)) {
                    log.info("HRMS数据读取完成，当前页无数据, currentPage: {}", currentPage);
                    break;
                }

                allData.addAll(pageData);
                statistics.incrementHrReadBatchCount();

                long pageEndTime = System.currentTimeMillis();
                log.info("HRMS大批量读取进度, 页码: {}/{}, 本页数量: {}, 累计: {}/{}, 耗时: {}ms",
                        currentPage, totalPages, pageData.size(), allData.size(), totalCount, pageEndTime - pageStartTime);
                XxlJobLogger.log("HRMS大批量读取进度, 页码: {}/{}, 本页数量: {}, 累计: {}/{}, 耗时: {}ms",
                        currentPage, totalPages, pageData.size(), allData.size(), totalCount, pageEndTime - pageStartTime);

                currentPage++;
            }

        } finally {
            // 恢复默认数据源
            DynamicDataSourceContextHolder.poll();
            log.info("已恢复到默认数据源");
        }

        statistics.setActualReadCount((long) allData.size());
        log.info("HRMS数据读取阶段完成, 实际读取: {} 条记录", allData.size());
        return allData;
    }

    /**
     * 小批量写入本地数据
     * 使用Spring事务管理，保持事务粒度合理
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean writeDataInSmallBatches(List<HrmsAttendanceClassEmployeeConfigDO> allHrmsData,
                                           OptimizedMigrationStatistics statistics) {

        log.info("开始小批量写入本地数据, 总数据量: {} 条", allHrmsData.size());

        // 按小批次分割数据
        List<List<HrmsAttendanceClassEmployeeConfigDO>> batches = Lists.partition(
                allHrmsData, optimizedMigrationConfig.getLocalWriteBatchSize());

        statistics.setLocalWriteBatchSize(optimizedMigrationConfig.getLocalWriteBatchSize());
        statistics.setTotalWriteBatchCount(batches.size());

        int totalSuccess = 0;
        int totalFailed = 0;

        for (int i = 0; i < batches.size(); i++) {
            List<HrmsAttendanceClassEmployeeConfigDO> batch = batches.get(i);

            try {
                long batchStartTime = System.currentTimeMillis();

                // 处理当前批次
                UserShiftConfigMigrationProcessor.MigrationResult result = processCurrentDataBatch(batch);
                totalSuccess += result.getSuccessCount();
                totalFailed += result.getFailedCount();

                statistics.incrementLocalWriteBatchCount();
                statistics.addSuccessCount(result.getSuccessCount());
                statistics.addFailedCount(result.getFailedCount());

                long batchEndTime = System.currentTimeMillis();
                log.info("本地小批量写入进度, 批次: {}/{}, 本批成功: {}, 本批失败: {}, 累计成功: {}, 耗时: {}ms",
                        i + 1, batches.size(), result.getSuccessCount(), result.getFailedCount(),
                        totalSuccess, batchEndTime - batchStartTime);

                // 每10个批次记录一次详细日志
                if ((i + 1) % 10 == 0 || i == batches.size() - 1) {
                    XxlJobLogger.log("本地写入进度: {}/{}, 累计成功: {}, 累计失败: {}",
                            i + 1, batches.size(), totalSuccess, totalFailed);
                }

            } catch (Exception e) {
                log.error("本地小批量写入失败, 批次: {}/{}, 批次大小: {}", i + 1, batches.size(), batch.size(), e);
                totalFailed += batch.size();
                statistics.addFailedCount(batch.size());

                // 根据策略决定是否继续
                if (!optimizedMigrationConfig.getErrorHandling().getContinueOnBatchError()) {
                    throw e;
                }
            }
        }

        log.info("本地数据写入阶段完成, 总成功: {}, 总失败: {}", totalSuccess, totalFailed);
        return totalFailed == 0;
    }

    /**
     * 生产者方法：读取HRMS数据并放入队列
     */
    private void produceHrmsData(String country, Long startDayId, Long endDayId,
                                 BlockingQueue<List<HrmsAttendanceClassEmployeeConfigDO>> dataQueue,
                                 OptimizedMigrationStatistics statistics) {

        // 手动切换到HRMS数据源
        DynamicDataSourceContextHolder.push(optimizedMigrationConfig.getHrmsDataSourceName());
        log.info("生产者线程：已切换到HRMS数据源");

        try {
            // 查询考勤组ID列表
            List<Long> punchConfigIds = queryPunchConfigIdsByCountry(country);
            statistics.setPunchConfigCount(punchConfigIds.size());

            if (CollectionUtils.isEmpty(punchConfigIds)) {
                log.warn("生产者线程：未找到国家{}的考勤组配置", country);
                return;
            }

            // 统计总数据量
            Long totalCount = hrmsDao.countByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId);
            statistics.setTotalRecordCount(totalCount);

            if (totalCount == 0) {
                log.info("生产者线程：无数据需要处理");
                return;
            }

            // 动态计算批次大小
            int hrReadBatchSize = optimizedMigrationConfig.calculateOptimalHrReadBatchSize(totalCount);
            statistics.setHrReadBatchSize(hrReadBatchSize);

            // 分页读取并放入队列
            int currentPage = 1;
            int totalPages = (int) Math.ceil((double) totalCount / hrReadBatchSize);

            while (true) {
                long pageStartTime = System.currentTimeMillis();

                PageInfo<HrmsAttendanceClassEmployeeConfigDO> pageInfo = PageHelper.startPage(currentPage, hrReadBatchSize)
                        .doSelectPageInfo(() -> hrmsDao.pageByPunchConfigIdsAndDateRange(punchConfigIds, startDayId, endDayId));

                List<HrmsAttendanceClassEmployeeConfigDO> pageData = pageInfo.getList();
                if (CollectionUtils.isEmpty(pageData)) {
                    log.info("生产者线程：数据读取完成，当前页无数据, currentPage: {}", currentPage);
                    break;
                }

                try {
                    // 将数据放入队列，如果队列满了会阻塞
                    dataQueue.put(pageData);
                    statistics.incrementHrReadBatchCount();

                    long pageEndTime = System.currentTimeMillis();
                    log.info("生产者线程：数据已放入队列, 页码: {}/{}, 本页数量: {}, 耗时: {}ms",
                            currentPage, totalPages, pageData.size(), pageEndTime - pageStartTime);

                } catch (InterruptedException e) {
                    log.error("生产者线程被中断", e);
                    Thread.currentThread().interrupt();
                    break;
                }

                currentPage++;
            }

        } finally {
            // 恢复默认数据源
            DynamicDataSourceContextHolder.poll();
            log.info("生产者线程：已恢复到默认数据源");

            // 放入结束标记
            try {
                dataQueue.put(new ArrayList<>()); // 空列表作为结束标记
                log.info("生产者线程：已放入结束标记");
            } catch (InterruptedException e) {
                log.error("生产者线程：放入结束标记时被中断", e);
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 消费者方法：从队列中取数据并写入本地
     */
    private boolean consumeAndWriteData(BlockingQueue<List<HrmsAttendanceClassEmployeeConfigDO>> dataQueue,
                                        OptimizedMigrationStatistics statistics) {

        log.info("消费者线程：开始处理数据");
        statistics.setLocalWriteBatchSize(optimizedMigrationConfig.getLocalWriteBatchSize());

        int totalSuccess = 0;
        int totalFailed = 0;
        int batchIndex = 0;

        while (true) {
            try {
                // 从队列中取数据，如果队列空会阻塞
                List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataBatch = dataQueue.take();

                // 检查是否为结束标记
                if (CollectionUtils.isEmpty(hrmsDataBatch)) {
                    log.info("消费者线程：收到结束标记，处理完成");
                    break;
                }

                // 将大批次数据按小批次处理
                List<List<HrmsAttendanceClassEmployeeConfigDO>> smallBatches = Lists.partition(
                        hrmsDataBatch, optimizedMigrationConfig.getLocalWriteBatchSize());

                for (List<HrmsAttendanceClassEmployeeConfigDO> smallBatch : smallBatches) {
                    try {
                        long batchStartTime = System.currentTimeMillis();

                        // 在事务中处理小批次
                        UserShiftConfigMigrationProcessor.MigrationResult result = processCurrentDataBatchInTransaction(smallBatch);
                        totalSuccess += result.getSuccessCount();
                        totalFailed += result.getFailedCount();

                        statistics.incrementLocalWriteBatchCount();
                        statistics.addSuccessCount(result.getSuccessCount());
                        statistics.addFailedCount(result.getFailedCount());

                        long batchEndTime = System.currentTimeMillis();
                        batchIndex++;

                        log.debug("消费者线程：小批次处理完成, 批次: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                                batchIndex, result.getSuccessCount(), result.getFailedCount(),
                                batchEndTime - batchStartTime);

                        // 每50个小批次记录一次详细日志
                        if (batchIndex % 50 == 0) {
                            log.info("消费者线程：处理进度, 已处理批次: {}, 累计成功: {}, 累计失败: {}",
                                    batchIndex, totalSuccess, totalFailed);
                            XxlJobLogger.log("流式处理进度: 已处理批次: {}, 累计成功: {}, 累计失败: {}",
                                    batchIndex, totalSuccess, totalFailed);
                        }

                    } catch (Exception e) {
                        log.error("消费者线程：小批次处理失败, 批次: {}, 批次大小: {}", batchIndex + 1, smallBatch.size(), e);
                        totalFailed += smallBatch.size();
                        statistics.addFailedCount(smallBatch.size());

                        if (!optimizedMigrationConfig.getErrorHandling().getContinueOnBatchError()) {
                            return false;
                        }
                    }
                }

            } catch (InterruptedException e) {
                log.error("消费者线程被中断", e);
                Thread.currentThread().interrupt();
                return false;
            }
        }

        log.info("消费者线程：数据处理完成, 总成功: {}, 总失败: {}", totalSuccess, totalFailed);
        return totalFailed == 0;
    }

    /**
     * 在事务中处理小批次数据
     */
    @Transactional(rollbackFor = Exception.class)
    public UserShiftConfigMigrationProcessor.MigrationResult processCurrentDataBatchInTransaction(List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList) {
        return processCurrentDataBatch(hrmsDataList);
    }

    /**
     * 处理当前数据批次（复用现有逻辑）
     */
    private UserShiftConfigMigrationProcessor.MigrationResult processCurrentDataBatch(List<HrmsAttendanceClassEmployeeConfigDO> hrmsDataList) {
        int successCount = 0;
        int failedCount = 0;
        int filteredCount = 0;

        try {
            long convertStartTime = System.currentTimeMillis();

            // 使用批量转换方法
            var newDataList = dataConverter.convertFromHrmsBatch(hrmsDataList);

            // 过滤验证数据
            var validDataList = newDataList.stream()
                    .filter(Objects::nonNull)
                    .filter(dataConverter::validateConvertedData)
                    .collect(Collectors.toList());

            long convertEndTime = System.currentTimeMillis();
            log.debug("数据批次转换完成, 输入: {}, 转换后: {}, 验证通过: {}, 转换耗时: {}ms",
                    hrmsDataList.size(), newDataList.size(), validDataList.size(),
                    convertEndTime - convertStartTime);

            // 批量插入新数据
            if (CollectionUtils.isNotEmpty(validDataList)) {
                batchInsertNew(validDataList);
                successCount = validDataList.size();
                filteredCount = hrmsDataList.size() - successCount;
            } else {
                filteredCount = hrmsDataList.size();
            }

        } catch (Exception e) {
            log.error("处理数据批次失败, 批次大小: {}", hrmsDataList.size(), e);
            failedCount = hrmsDataList.size();
        }

        return new UserShiftConfigMigrationProcessor.MigrationResult(successCount, failedCount, filteredCount);
    }

    /**
     * 批量插入新数据（复用现有适配器逻辑）
     */
    private void batchInsertNew(List<com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO> newDataList) {
        List<List<com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO>> partitionList =
                Lists.partition(newDataList, BusinessConstant.MAX_BATCH_SIZE);

        for (List<com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO> batch : partitionList) {
            try {
                // 使用适配器进行批量保存，支持配置驱动的表切换
                userShiftConfigAdapter.saveBatch(batch);
                log.debug("批量插入新数据成功, 数量: {}, 当前使用{}表",
                        batch.size(), userShiftConfigAdapter.isShouldUseMigrate() ? "migrate" : "原始");
            } catch (Exception e) {
                log.error("批量插入新数据失败, 数量: {}, 当前使用{}表",
                        batch.size(), userShiftConfigAdapter.isShouldUseMigrate() ? "migrate" : "原始", e);
                throw e;
            }
        }
    }

    /**
     * 查询国家对应的考勤组ID列表（复用现有逻辑）
     */
    private List<Long> queryPunchConfigIdsByCountry(String country) {
        long startTime = System.currentTimeMillis();

        try {
            List<HrmsAttendancePunchConfigDO> punchConfigs = hrmsAttendancePunchConfigDao.listByCountry(country);

            if (CollectionUtils.isEmpty(punchConfigs)) {
                log.warn("未找到国家{}的考勤组配置", country);
                return new ArrayList<>();
            }

            List<Long> punchConfigIds = punchConfigs.stream()
                    .map(HrmsAttendancePunchConfigDO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            long endTime = System.currentTimeMillis();
            log.info("查询国家{}的考勤组ID列表完成, 数量: {}, 耗时: {}ms",
                    country, punchConfigIds.size(), endTime - startTime);

            return punchConfigIds;

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("查询国家{}的考勤组ID列表失败, 耗时: {}ms", country, endTime - startTime, e);
            throw e;
        }
    }


    /**
     * 记录最终统计信息
     */
    private void logFinalStatistics(OptimizedMigrationStatistics statistics) {
        log.info("=== 优化迁移完成统计 ===");
        log.info("国家: {}", statistics.getCountry());
        log.info("总耗时: {} ms ({} 秒)", statistics.getDurationMs(), statistics.getDurationMs() / 1000.0);
        log.info("考勤组数量: {}", statistics.getPunchConfigCount());
        log.info("总记录数: {}", statistics.getTotalRecordCount());
        log.info("实际读取: {}", statistics.getActualReadCount());
        log.info("HR读取批次大小: {}", statistics.getHrReadBatchSize());
        log.info("HR读取批次数: {}", statistics.getHrReadBatchCount());
        log.info("本地写入批次大小: {}", statistics.getLocalWriteBatchSize());
        log.info("本地写入批次数: {}", statistics.getLocalWriteBatchCount());
        log.info("成功记录数: {}", statistics.getSuccessCount());
        log.info("失败记录数: {}", statistics.getFailedCount());
        log.info("成功率: {}%", statistics.getSuccessRate());
        log.info("迁移结果: {}", statistics.isSuccess() ? "成功" : "失败");

        if (StringUtils.isNotBlank(statistics.getErrorMessage())) {
            log.info("错误信息: {}", statistics.getErrorMessage());
        }

        log.info("=== 统计结束 ===");

        // 记录到XXL-JOB日志
        XxlJobLogger.log("优化迁移完成 - 国家: {}, 耗时: {}s, 成功: {}, 失败: {}, 成功率: {}%",
                statistics.getCountry(), statistics.getDurationMs() / 1000.0,
                statistics.getSuccessCount(), statistics.getFailedCount(), statistics.getSuccessRate());
    }

    /**
     * 获取优化迁移的性能对比
     * 可用于与原有方法进行性能对比分析
     */
    public String getPerformanceComparison(OptimizedMigrationStatistics statistics) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 性能优化效果分析 ===\n");
        sb.append(String.format("数据源切换次数: 2次 (原方法: %d次)\n",
                statistics.getHrReadBatchCount() + statistics.getLocalWriteBatchCount()));
        sb.append(String.format("HR读取批次大小: %d条 (原方法: 1000条)\n", statistics.getHrReadBatchSize()));
        sb.append(String.format("总处理时间: %.2f秒\n", statistics.getDurationMs() / 1000.0));
        sb.append(String.format("平均处理速度: %.2f条/秒\n",
                (double) statistics.getSuccessCount() / (statistics.getDurationMs() / 1000.0)));
        sb.append(String.format("内存优化: 使用流式处理，峰值内存占用约 %d 条记录\n",
                Math.max(statistics.getHrReadBatchSize(), statistics.getLocalWriteBatchSize() * optimizedMigrationConfig.getStreamingQueueCapacity())));

        return sb.toString();
    }
}
