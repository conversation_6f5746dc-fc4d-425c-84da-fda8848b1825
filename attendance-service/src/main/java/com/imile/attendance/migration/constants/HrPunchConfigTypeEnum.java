package com.imile.attendance.migration.constants;

import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11 
 * @Description
 */
@Getter
public enum HrPunchConfigTypeEnum {

    FIXED_WORK("FIXED_WORK", "固定班次打卡", "Fixed commute"),
    FREE_WORK("FREE_WORK", "灵活打卡两次", "Flexible commuting 2"),
    CLASS_WORK("CLASS_WORK", "多班次打卡", "Shift commuting"),
    FIXED_WORK_ONCE("FIXED_WORK_ONCE", "灵活打卡一次", "Flexible commuting 1"),
            ;

    private String code;

    private String desc;

    private String descEn;

    HrPunchConfigTypeEnum(String code, String desc, String descEn) {
        this.code = code;
        this.desc = desc;
        this.descEn = descEn;
    }

    private static final Map<String, HrPunchConfigTypeEnum> cacheMap = new ConcurrentHashMap<>();

    static {
        for (HrPunchConfigTypeEnum value : HrPunchConfigTypeEnum.values()) {
            cacheMap.put(value.getCode(), value);
        }
    }


    public static HrPunchConfigTypeEnum getInstance(String code) {
        return cacheMap.get(code);
    }
}
