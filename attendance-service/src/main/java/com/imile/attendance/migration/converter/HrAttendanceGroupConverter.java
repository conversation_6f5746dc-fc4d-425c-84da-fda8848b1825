package com.imile.attendance.migration.converter;

import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendancePunchConfigRangeDO;
import com.imile.attendance.migration.dto.HrAttendanceClassConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceClassItemConfigDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;
import com.imile.attendance.migration.dto.HrAttendanceGroupRangeDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description HR考勤组转换器
 */
@Component
public class HrAttendanceGroupConverter {

    /**
     * 转换HR考勤组
     */
    public HrAttendanceGroupDTO convertToDTO(HrmsAttendancePunchConfigDO hrConfig) {
        if (hrConfig == null) {
            return null;
        }
        
        HrAttendanceGroupDTO dto = new HrAttendanceGroupDTO();
        BeanUtils.copyProperties(hrConfig, dto);
        return dto;
    }

    /**
     * 批量转换HR考勤组
     */
    public List<HrAttendanceGroupDTO> convertToDTO(List<HrmsAttendancePunchConfigDO> hrConfigs) {
        if (CollectionUtils.isEmpty(hrConfigs)) {
            return null;
        }
        
        return hrConfigs.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换HR考勤组范围
     */
    public HrAttendanceGroupRangeDTO convertRangeToDTO(HrmsAttendancePunchConfigRangeDO hrRange) {
        if (hrRange == null) {
            return null;
        }
        
        HrAttendanceGroupRangeDTO dto = new HrAttendanceGroupRangeDTO();
        BeanUtils.copyProperties(hrRange, dto);
        return dto;
    }

    /**
     * 批量转换HR考勤组范围
     */
    public List<HrAttendanceGroupRangeDTO> convertRangeToDTO(List<HrmsAttendancePunchConfigRangeDO> hrRanges) {
        if (hrRanges == null) {
            return null;
        }
        
        return hrRanges.stream()
                .map(this::convertRangeToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换HR班次配置
     */
    public HrAttendanceClassConfigDTO convertClassToDTO(HrmsAttendancePunchClassConfigDO hrClass) {
        if (hrClass == null) {
            return null;
        }
        
        HrAttendanceClassConfigDTO dto = new HrAttendanceClassConfigDTO();
        BeanUtils.copyProperties(hrClass, dto);
        return dto;
    }

    /**
     * 批量转换HR班次配置
     */
    public List<HrAttendanceClassConfigDTO> convertClassToDTO(List<HrmsAttendancePunchClassConfigDO> hrClasses) {
        if (hrClasses == null) {
            return null;
        }
        
        return hrClasses.stream()
                .map(this::convertClassToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换HR班次时间配置
     */
    public HrAttendanceClassItemConfigDTO convertClassItemToDTO(HrmsAttendancePunchClassItemConfigDO hrClassItem) {
        if (hrClassItem == null) {
            return null;
        }
        
        HrAttendanceClassItemConfigDTO dto = new HrAttendanceClassItemConfigDTO();
        BeanUtils.copyProperties(hrClassItem, dto);
        return dto;
    }

    /**
     * 批量转换HR班次时间配置
     */
    public List<HrAttendanceClassItemConfigDTO> convertClassItemToDTO(List<HrmsAttendancePunchClassItemConfigDO> hrClassItems) {
        if (hrClassItems == null) {
            return null;
        }
        
        return hrClassItems.stream()
                .map(this::convertClassItemToDTO)
                .collect(Collectors.toList());
    }
}
