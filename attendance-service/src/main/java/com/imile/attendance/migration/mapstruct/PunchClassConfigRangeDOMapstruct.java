package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.PunchClassConfigRangeMigrateDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 班次配置范围DO映射器
 * 负责PunchClassConfigRangeDO和PunchClassConfigRangeMigrateDO之间的字段映射
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Mapper
public interface PunchClassConfigRangeDOMapstruct extends BaseMapstruct {

    PunchClassConfigRangeDOMapstruct INSTANCE = Mappers.getMapper(PunchClassConfigRangeDOMapstruct.class);

    /**
     * 设置模型ID（migrate表）
     */
    default Long setModelId(PunchClassConfigRangeMigrateDO module) {
        return setModelId(module,
                PunchClassConfigRangeMigrateDO::getId,
                PunchClassConfigRangeMigrateDO::setId);
    }

    /**
     * 设置模型ID（原始表）
     */
    default Long setModelId(PunchClassConfigRangeDO module) {
        return setModelId(module,
                PunchClassConfigRangeDO::getId,
                PunchClassConfigRangeDO::setId);
    }

    /**
     * 将migrate表DO转换为原始表DO
     * 
     * @param punchClassConfigRangeMigrateDO migrate表DO对象
     * @return 原始表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchClassConfigRangeMigrateDO))")
    PunchClassConfigRangeDO mapToReal(PunchClassConfigRangeMigrateDO punchClassConfigRangeMigrateDO);

    /**
     * 批量将migrate表DO转换为原始表DO
     * 
     * @param punchClassConfigRangeMigrateDOList migrate表DO列表
     * @return 原始表DO列表
     */
    List<PunchClassConfigRangeDO> mapToReal(List<PunchClassConfigRangeMigrateDO> punchClassConfigRangeMigrateDOList);

    /**
     * 将原始表DO转换为migrate表DO
     * 
     * @param punchClassConfigRangeDO 原始表DO对象
     * @return migrate表DO对象
     */
    @Mapping(target = "id", expression = "java(setModelId(punchClassConfigRangeDO))")
    PunchClassConfigRangeMigrateDO mapToMigrate(PunchClassConfigRangeDO punchClassConfigRangeDO);

    /**
     * 批量将原始表DO转换为migrate表DO
     * 
     * @param punchClassConfigRangeDOList 原始表DO列表
     * @return migrate表DO列表
     */
    List<PunchClassConfigRangeMigrateDO> mapToMigrate(List<PunchClassConfigRangeDO> punchClassConfigRangeDOList);
}
