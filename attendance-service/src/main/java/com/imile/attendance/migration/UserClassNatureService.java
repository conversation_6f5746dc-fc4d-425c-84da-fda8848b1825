package com.imile.attendance.migration;

import com.imile.attendance.enums.ClassNatureEnum;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/12 
 * @Description
 */
public interface UserClassNatureService {

    /**
     * 获取多班次用户编码列表
     */
    List<String> getMultiClassUserCodes();

    /**
     * 初始化国家下所有用户的班次性质
     */
    void initUserClassNatureByCountry(String country);

    /**
     * 清空非灰度国家下所有用户的班次性质
     */
    void clearUserClassNatureForAllNoGrayscaleCountry();

    /**
     * 刷新指定国家的人员班次类型
     * @param countryCode 国家代码
     * @param targetClassNature 目标班次类型（MULTIPLE_CLASS 或 FIXED_CLASS）
     * @param configUserCodes 配置的特定用户编码列表，这些用户将被设置为目标班次类型
     */
    Boolean refreshUserClassNatureByCountry(String countryCode, ClassNatureEnum targetClassNature, List<String> configUserCodes);
}
