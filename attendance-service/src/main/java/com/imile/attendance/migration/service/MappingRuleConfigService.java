package com.imile.attendance.migration.service;

import com.imile.attendance.infrastructure.repository.migration.model.MappingRuleConfigDO;
import com.imile.attendance.migration.dto.HrAttendanceGroupDTO;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/11
 * @Description 考勤规则映射服务接口
 */
public interface MappingRuleConfigService {

    /**
     * 保存规则映射
     * 
     * @param mappingRuleConfig 规则映射
     * @return 是否成功
     */
    boolean save(MappingRuleConfigDO mappingRuleConfig);

    /**
     * 批量保存规则映射
     * 
     * @param mappingRuleConfigs 规则映射列表
     * @return 是否成功
     */
    boolean batchSave(List<MappingRuleConfigDO> mappingRuleConfigs);

    /**
     * 根据HR考勤组ID查询映射配置
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @return 映射配置列表
     */
    List<MappingRuleConfigDO> listByHrPunchConfigId(Long hrPunchConfigId);

    /**
     * 根据HR考勤组ID和规则类型查询映射配置
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @param ruleType 规则类型
     * @return 映射配置
     */
    MappingRuleConfigDO getByHrPunchConfigIdAndRuleType(Long hrPunchConfigId, String ruleType);

    /**
     * 根据新考勤规则ID查询映射配置
     * 
     * @param ruleId 新考勤规则ID
     * @return 映射配置
     */
    MappingRuleConfigDO getByRuleId(Long ruleId);

    /**
     * 根据国家查询映射配置
     * 
     * @param country 国家
     * @return 映射配置列表
     */
    List<MappingRuleConfigDO> listByCountry(String country);

    /**
     * 根据规则类型查询映射配置
     * 
     * @param ruleType 规则类型
     * @return 映射配置列表
     */
    List<MappingRuleConfigDO> listByRuleType(String ruleType);

    /**
     * 创建HR考勤组到新考勤规则的映射记录
     *
     * @param ruleType 新考勤规则类型
     * @param ruleId 新考勤规则ID
     * @return 映射配置
     */
    MappingRuleConfigDO createMapping(HrAttendanceGroupDTO group, String ruleType, Long ruleId);

    /**
     * 检查映射是否已存在
     * 
     * @param hrPunchConfigId HR考勤组ID
     * @param ruleType 规则类型
     * @return 是否存在
     */
    boolean existsMapping(Long hrPunchConfigId, String ruleType);
}
