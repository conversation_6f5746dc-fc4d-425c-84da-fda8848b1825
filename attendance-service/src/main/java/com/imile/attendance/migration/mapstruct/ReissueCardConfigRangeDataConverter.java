package com.imile.attendance.migration.mapstruct;

import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.migrate.ReissueCardConfigRangeMigrateDO;
import com.imile.attendance.migration.adapter.base.DataConverter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 补卡配置范围DO转换器
 * 实现ReissueCardConfigRangeDO和ReissueCardConfigRangeMigrateDO之间的相互转换
 * 
 * <AUTHOR> chen
 * @since 2025/6/17
 */
@Slf4j
@Component
public class ReissueCardConfigRangeDataConverter implements DataConverter<ReissueCardConfigRangeMigrateDO, ReissueCardConfigRangeDO> {

    @Override
    public ReissueCardConfigRangeDO convertFromMigrate(ReissueCardConfigRangeMigrateDO migrateDO) {
        if (migrateDO == null) {
            return null;
        }
        
        log.debug("转换补卡配置范围: migrate -> real, rangeId: {}", migrateDO.getId());
        return ReissueCardConfigRangeDOMapstruct.INSTANCE.mapToReal(migrateDO);
    }

    @Override
    public ReissueCardConfigRangeMigrateDO convertFromReal(ReissueCardConfigRangeDO realDO) {
        if (realDO == null) {
            return null;
        }
        
        log.debug("转换补卡配置范围: real -> migrate, rangeId: {}", realDO.getId());
        return ReissueCardConfigRangeDOMapstruct.INSTANCE.mapToMigrate(realDO);
    }

    @Override
    public Class<ReissueCardConfigRangeMigrateDO> getMigrateType() {
        return ReissueCardConfigRangeMigrateDO.class;
    }

    @Override
    public Class<ReissueCardConfigRangeDO> getRealType() {
        return ReissueCardConfigRangeDO.class;
    }
}
