package com.imile.attendance.rule.dto;

import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/14 
 * @Description
 */
@Data
public class RuleConfigApplyUserDTO {

    private List<AttendanceUser> userLevelUserList;

    private List<AttendanceUser> deptLevelUserList;

    private List<AttendanceUser> countryLevelUserList;

    public static RuleConfigApplyUserDTO init() {
        RuleConfigApplyUserDTO ruleConfigApplyUserDTO = new RuleConfigApplyUserDTO();
        ruleConfigApplyUserDTO.setUserLevelUserList(new ArrayList<>());
        ruleConfigApplyUserDTO.setDeptLevelUserList(new ArrayList<>());
        ruleConfigApplyUserDTO.setCountryLevelUserList(new ArrayList<>());
        return ruleConfigApplyUserDTO;
    }

    public Integer getApplyUserSize() {
        if (CollectionUtils.isNotEmpty(countryLevelUserList)) {
            return this.countryLevelUserList.size();
        }
        return this.userLevelUserList.size() + this.deptLevelUserList.size();
    }
}
