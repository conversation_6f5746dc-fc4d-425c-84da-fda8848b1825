package com.imile.attendance.rule.factory;

import cn.hutool.core.text.StrPool;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.ReissueCardConfigQueryService;
import com.imile.attendance.rule.RuleChangeLogService;
import com.imile.attendance.rule.bo.CountryReissueCardConfig;
import com.imile.attendance.rule.bo.ReissueCardConfig;
import com.imile.attendance.rule.command.ReissueCardConfigAddCommand;
import com.imile.attendance.rule.command.ReissueCardConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.ReissueCardConfigUpdateCommand;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.dto.ConfigUpdateType;
import com.imile.attendance.rule.dto.RuleConfigApplyUserCountDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigRangeChangeDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.handler.ReissueCardConfigRangeHandler;
import com.imile.attendance.rule.mapstruct.ReissueCardConfigMapstruct;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description
 */
@Slf4j
@Component
public class ReissueCardConfigFactory {

    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private CountryService countryService;
    @Resource
    private ReissueCardConfigRangeHandler reissueCardConfigRangeHandler;
    @Resource
    private ReissueCardConfigQueryService reissueCardConfigQueryService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendanceUserService userService;

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private RuleChangeLogService ruleChangeLogService;
    @Resource
    private MigrationService migrationService;

    /**
     * 补卡规则日志字段列表
     */
    public static final List<String> ruleLogFieldList = Arrays.asList(
            ReissueCardConfigDO.Fields.configName,
            ReissueCardConfigDO.Fields.maxRepunchNumber,
            ReissueCardConfigDO.Fields.status);

    @Transactional
    public RuleConfigChangeCheckDTO add(ReissueCardConfigAddCommand addCommand) {
        List<String> enableNewAttendanceCountry = migrationService.getEnableNewAttendanceCountry();
        if (!enableNewAttendanceCountry.contains(addCommand.getCountry())) {
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.THE_NON_GRAY_SCALE_COUNTRY_NEED_TO_OPERATE_IN_THE_HRMS_SYSTEM, addCommand.getCountry());
        }
        // 1.判断名称是否重复
        String configName = addCommand.getConfigName();
        ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigDao.getByName(configName);
        if (null != reissueCardConfigDO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.REISSUE_CARD_CONFIG_NAME_EXIST);
        }
        // 获取该国家的补卡配置
        CountryReissueCardConfig countryConfig = reissueCardConfigManage.getCountryConfig(addCommand.getCountry());

        // 获取国家级别的补卡配置
        ReissueCardConfigDO countryLevelConfig = countryConfig.queryCountryLevelConfig();

        // 2.国家部门人员检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(addCommand, countryConfig.getCountryConfigs(),
                countryLevelConfig);
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return ruleConfigChangeCheckDTO;
        }

        // 3.构造ReissueCardConfig
        CountryDTO countryDTO = countryService.queryCountry(addCommand.getCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        ReissueCardConfig reissueCardConfig = ReissueCardConfig.builder()
                .id(defaultIdWorker.nextId())
                .country(addCommand.getCountry())
                .configNo(defaultIdWorker.nextReissueCardConfigNo())
                .configName(addCommand.getConfigName())
                .maxRepunchNumber(addCommand.getMaxRepunchNumber())
                .status(StatusEnum.ACTIVE.getCode())
                .deptIds(StringUtils.join(addCommand.getDeptIds(), StrPool.COMMA))
                .isLatest(BusinessConstant.Y)
                .effectDateAndTimeZoneDate(currentDateAndTimeZoneDate)
                .expireDateAndTimeZoneDate(DateAndTimeZoneDate.ofEndDate())
                .build();
        if (countryLevelConfig == null && addCommand.isCountryLevelRangeFlag()) {
            reissueCardConfig.setIsCountryLevel(BusinessConstant.Y);
        }

        ReissueCardConfigDO model = ReissueCardConfigMapstruct.INSTANCE.toReissueCardConfigDO(reissueCardConfig);
        BaseDOUtil.fillDOInsert(model);
        reissueCardConfigDao.save(model);

        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.addConfigRangeHandler(
                currentDateAndTimeZoneDate, model, addCommand.getCountry(),
                addCommand.getDeptIds(), addCommand.getUserIds());

        log.info("新增补卡规则:{}，规则影响人员为:{}", addCommand.getConfigName(), ruleConfigRangeChangeDTO);

        logRecordService.recordOperation(model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.REISSUE_CARD_CONFIG_ADD.getCode())
                        .country(model.getCountry())
                        .bizName(model.getConfigName())
                        .fieldNameList(ruleLogFieldList)
                        .build());
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 检查更新补卡规则
     *
     * @param updateCommand 更新补卡规则命令
     * @return 更新补卡规则结果
     */
    public UpdateRuleReflectResult checkUpdateRule(ReissueCardConfigUpdateCommand updateCommand) {
        // 获取当前配置
        ReissueCardConfigDO currentConfig = reissueCardConfigDao.getLatestByConfigNo(updateCommand.getConfigNo());
        if (null == currentConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }

        // 获取该国家的补卡规则
        CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(updateCommand.getCountry());

        // 获取国家级别的配置，可以为空
        ReissueCardConfigDO countryLevelConfig = countryReissueCardConfig.queryCountryLevelConfig();

        // 范围检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = checkRangeIsValid(
                updateCommand,
                countryReissueCardConfig.getCountryConfigs(),
                countryLevelConfig
        );
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return UpdateRuleReflectResult.rangeCheckFail(ruleConfigChangeCheckDTO);
        }

        // 确定补卡配置的更新类型
        ConfigUpdateType updateType = determineUpdateType(updateCommand, currentConfig);
        // 配置和适用范围都未发生变动,直接返回
        if (updateType == ConfigUpdateType.NO_CHANGES) {
            return UpdateRuleReflectResult.noReflect();
        }

        // 当前更新的为国家级别配置，特殊处理（部门和人员可以不为空）
        if (countryLevelConfig != null && Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            // 部门和人员都为空, 则范围没有变动，说明只修改了配置，国家级规则版本升级
            if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                    CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(false)
                        .updateConfigReflectUserSize(reissueCardConfigQueryService
                                .queryApplyUserCount(updateCommand.getConfigNo()).getCountryLevelUserSize())
                        .updateRangeReflectAddUserSize(0)
                        .updateRangeReflectRemoveUserSize(0)
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            }
            // 部门或人员变动，从国家级别配置升级

            // 查询部门和人员级别配置的变动影响的人
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.checkConfigRangeHandler(
                    currentConfig,
                    countryReissueCardConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds());

            // 当前updateType只可能为RANGE_ONLY和BOTH
            if (updateType == ConfigUpdateType.RANGE_ONLY) {
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(false)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(0)
                        // 部门和人员级别当前配置的影响人员
                        .updateRangeReflectAddUserSize(ruleConfigRangeChangeDTO.getAddUserIdList().size())
                        // 查询国家级别配置的变动影响的人作为移除的人数
                        .updateRangeReflectRemoveUserSize(reissueCardConfigQueryService
                                .queryApplyUserCount(updateCommand.getConfigNo()).getCountryLevelUserSize())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            } else if (updateType == ConfigUpdateType.BOTH) {
                Integer countryLevelUserSize = reissueCardConfigQueryService
                        .queryApplyUserCount(updateCommand.getConfigNo()).getCountryLevelUserSize();
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(countryLevelUserSize)
                        // 部门和人员级别当前配置的影响人员
                        .updateRangeReflectAddUserSize(ruleConfigRangeChangeDTO.getAddUserIdList().size())
                        // 查询国家级别配置的变动影响的人作为移除的人数
                        .updateRangeReflectRemoveUserSize(countryLevelUserSize)
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            }
        }

        switch (updateType) {
            case RANGE_ONLY:
                // 配置无变动，人员范围变动
                RuleConfigRangeChangeDTO onlyRuleRangeChangeDTO = reissueCardConfigRangeHandler.checkConfigRangeHandler(
                        currentConfig,
                        countryReissueCardConfig,
                        updateCommand.getCountry(),
                        updateCommand.getDeptIds(),
                        updateCommand.getUserIds());
                // 回退到国家级别的处理
                if (countryLevelConfig == null) {
                    // 如果国家级别的配置不存在，添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置
                    if (CollectionUtils.isEmpty(updateCommand.getDeptIds())
                            && CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                        // 需要看国家添加的人员
                        List<UserInfoDO> countryUserList = reissueCardConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                                RuleRangeUserQuery.builder()
                                        .country(updateCommand.getCountry()).build()
                        );
                        List<Long> countryLevelAddUserIds = countryUserList.stream()
                                .map(UserInfoDO::getId)
                                .collect(Collectors.toList());
                        // 当前规则移除人员进入到回退的国家级规则的用户
                        List<Long> intoCountryLevelOrNoRuleUserIdList = onlyRuleRangeChangeDTO.getIntoCountryLevelOrNoRuleUserIdList();
                        // 两者相加为真正的回退到国家级的人数
                        if (CollectionUtils.isNotEmpty(intoCountryLevelOrNoRuleUserIdList)) {
                            countryLevelAddUserIds.addAll(intoCountryLevelOrNoRuleUserIdList);
                        }
                        onlyRuleRangeChangeDTO.getAddUserIdList().addAll(countryLevelAddUserIds);
                    }
                }
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(false)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(0)
                        .updateRangeReflectAddUserSize(onlyRuleRangeChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(onlyRuleRangeChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            case CONFIG_ONLY:
                // 配置改动，人员范围无变动
                RuleConfigApplyUserCountDTO ruleConfigApplyUserCountDTO = reissueCardConfigQueryService
                        .queryApplyUserCount(updateCommand.getConfigNo());
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(false)
                        .updateConfigReflectUserSize(ruleConfigApplyUserCountDTO.getApplyUserSize())
                        .updateRangeReflectAddUserSize(0)
                        .updateRangeReflectRemoveUserSize(0)
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            case BOTH:
                // 配置改动.人员范围改动
                RuleConfigRangeChangeDTO ruleConfigAndRangeChangeDTO = reissueCardConfigRangeHandler.checkConfigRangeHandler(
                        currentConfig,
                        countryReissueCardConfig,
                        updateCommand.getCountry(),
                        updateCommand.getDeptIds(),
                        updateCommand.getUserIds()
                );
                // 回退到国家级别的处理
                if (countryLevelConfig == null) {
                    // 如果国家级别的配置不存在，添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置
                    if (CollectionUtils.isEmpty(updateCommand.getDeptIds())
                            && CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                        // 需要看国家添加的人员
                        List<UserInfoDO> countryUserList = reissueCardConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                                RuleRangeUserQuery.builder()
                                        .country(updateCommand.getCountry()).build()
                        );
                        List<Long> countryLevelAddUserIds = countryUserList.stream()
                                .map(UserInfoDO::getId)
                                .collect(Collectors.toList());
                        // 当前规则移除人员进入到回退的国家级规则的用户
                        List<Long> intoCountryLevelOrNoRuleUserIdList = ruleConfigAndRangeChangeDTO.getIntoCountryLevelOrNoRuleUserIdList();
                        // 两者相加为真正的回退到国家级的人数
                        if (CollectionUtils.isNotEmpty(intoCountryLevelOrNoRuleUserIdList)) {
                            countryLevelAddUserIds.addAll(intoCountryLevelOrNoRuleUserIdList);
                        }
                        ruleConfigAndRangeChangeDTO.getAddUserIdList().addAll(countryLevelAddUserIds);
                    }
                }
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(reissueCardConfigQueryService.queryApplyUserCount(updateCommand.getConfigNo()).getApplyUserSize())
                        .updateRangeReflectAddUserSize(ruleConfigAndRangeChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(ruleConfigAndRangeChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(ruleConfigChangeCheckDTO)
                        .build();
            default:
                return UpdateRuleReflectResult.noReflect();
        }
    }

    /**
     * 验证更新命令的有效性
     *
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     */
    private void validateUpdateCommand(ReissueCardConfigUpdateCommand updateCommand, ReissueCardConfigDO currentConfig) {
        if (null == currentConfig) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // 国家不能修改
        if (!StringUtils.equals(currentConfig.getCountry(), updateCommand.getCountry())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.REISSUE_CARD_CONFIG_COUNTRY_NOT_CHANGE);
        }
        // 停用的规则不可编辑
        if (StringUtils.equals(currentConfig.getStatus(), StatusEnum.DISABLED.getCode())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.REISSUE_CARD_CONFIG_DISABLE_NOT_UPDATE);
        }
        // 判断修改后的名称是否和其他存在的配置重复
        String configName = updateCommand.getConfigName();
        ReissueCardConfigDO configDaoByName = reissueCardConfigDao.getByName(configName);
        if (null != configDaoByName &&
                !StringUtils.equals(configDaoByName.getConfigNo(), updateCommand.getConfigNo())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.REISSUE_CARD_CONFIG_NAME_EXIST);
        }
    }

    /**
     * 验证范围并获取结果
     *
     * @param updateCommand            更新命令
     * @param countryReissueCardConfig 国家补卡配置
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO validateRangeAndGetResult(ReissueCardConfigUpdateCommand updateCommand,
                                                               CountryReissueCardConfig countryReissueCardConfig) {
        // 获取国家级别的配置，可以为空
        ReissueCardConfigDO countryLevelConfig = countryReissueCardConfig.queryCountryLevelConfig();

        // 范围检查
        return checkRangeIsValid(
                updateCommand,
                countryReissueCardConfig.getCountryConfigs(),
                countryLevelConfig
        );
    }

    /**
     * 验证范围并获取结果
     *
     * @param currentConfig 更新命令
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO validateRangeAndGetResult(ReissueCardConfigDO currentConfig) {
        // 获取国家级别的配置，可以为空
        CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(currentConfig.getCountry());
        ReissueCardConfigDO countryLevelConfig = countryReissueCardConfig.queryCountryLevelConfig();

        // 范围检查
        return checkRangeIsValid(
                currentConfig,
                countryReissueCardConfig.getCountryConfigs(),
                countryLevelConfig
        );
    }

    /**
     * 处理国家级别用户
     *
     * @param country                  国家
     * @param ruleConfigRangeChangeDTO 规则配置范围变更DTO
     * @return 国家级别添加的用户ID列表
     */
    private List<Long> processCountryLevelUsers(String country, RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        List<UserInfoDO> countryUserList = reissueCardConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                RuleRangeUserQuery.builder()
                        .country(country).build()
        );
        List<Long> countryLevelAddUserIds = countryUserList.stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());

        //移除人员进入到回退的国家级规则的用户已经在上面的流程添加了，无需处理

//        // 当前规则移除人员进入到回退的国家级规则的用户
//        List<Long> intoCountryLevelOrNoRuleUserIdList = ruleConfigRangeChangeDTO.getIntoCountryLevelOrNoRuleUserIdList();
//        // 两者相加为真正的回退到国家级的人数
//        if (CollectionUtils.isNotEmpty(intoCountryLevelOrNoRuleUserIdList)) {
//            countryLevelAddUserIds.addAll(intoCountryLevelOrNoRuleUserIdList);
//        }
        return countryLevelAddUserIds;
    }

    /**
     * 处理回退到国家级别的情况
     *
     * @param updateCommand              更新命令
     * @param currentConfig              当前配置
     * @param currentDateAndTimeZoneDate 当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleBackToCountryLevel(ReissueCardConfigUpdateCommand updateCommand,
                                                              ReissueCardConfigDO currentConfig,
                                                              DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        // 获取原国家级配置的适用范围
        List<ReissueCardConfigRangeDO> currentRangeDOList = reissueCardConfigRangeDao.listByConfigId(currentConfig.getId());
        // 标记当前配置的适用范围为过期
        markOldConfigRangeAsExpired(currentRangeDOList, currentDateAndTimeZoneDate);

        // 构建新的国家级补卡配置数据
        ReissueCardConfigDO newConfig = buildNewConfig(updateCommand, currentDateAndTimeZoneDate);
        newConfig.setIsCountryLevel(BusinessConstant.Y);

        //更新和新增主配置
        reissueCardConfigManage.configUpdateAndAdd(currentConfig, newConfig);

        // 该国家的补卡配置已经修改了，重新获取
        CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(updateCommand.getCountry());

        // 回退到国家级后，部门和人员的流动情况
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.updateConfigRangeHandler(
                currentDateAndTimeZoneDate,
                currentConfig,
                newConfig,
                countryReissueCardConfig,
                updateCommand.getCountry(),
                updateCommand.getDeptIds(),
                updateCommand.getUserIds()
        );

        // 处理国家级别用户
        List<Long> countryLevelAddUserIds = processCountryLevelUsers(updateCommand.getCountry(), ruleConfigRangeChangeDTO);

        // 创建国家级别范围记录
        List<ReissueCardConfigRangeDO> newRangeDOList = countryLevelAddUserIds.stream()
                .map(addUserId -> {
                    ReissueCardConfigRangeDO configRangeDO = new ReissueCardConfigRangeDO();
                    configRangeDO.setId(defaultIdWorker.nextId());
                    configRangeDO.setRuleConfigId(newConfig.getId());
                    configRangeDO.setRuleConfigNo(newConfig.getConfigNo());
                    configRangeDO.setBizId(addUserId);
                    configRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
                    configRangeDO.setEffectTime(newConfig.getEffectTime());
                    configRangeDO.setExpireTime(newConfig.getExpireTime());
                    configRangeDO.setEffectTimestamp(newConfig.getEffectTimestamp());
                    configRangeDO.setExpireTimestamp(newConfig.getExpireTimestamp());
                    configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                    configRangeDO.setIsLatest(BusinessConstant.Y);
                    configRangeDO.setRemark("非国家级规则回退到国家级规则添加的人员");
                    BaseDOUtil.fillDOInsert(configRangeDO);
                    return configRangeDO;
                }).collect(Collectors.toList());

        reissueCardConfigManage.configRangeUpdateOrAdd(currentRangeDOList, newRangeDOList);

        log.info("更新补卡规则:{},回退到国家级，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);

        // 记录操作日志
        logRecordService.recordOperation(
                newConfig,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.REISSUE_CARD_CONFIG_UPDATE.getCode(),
                        ruleChangeLogService.buildReissueConfigChangeLog(newConfig, currentConfig)));
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 处理国家级别配置更新
     *
     * @param updateCommand              更新命令
     * @param currentConfig              当前配置
     * @param currentDateAndTimeZoneDate 当前日期
     * @param countryReissueCardConfig   国家补卡配置
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleCountryLevelConfigUpdate(ReissueCardConfigUpdateCommand updateCommand,
                                                                    ReissueCardConfigDO currentConfig,
                                                                    DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                                    CountryReissueCardConfig countryReissueCardConfig) {
        // 部门和人员都为空，则范围没有更新，只修改了配置，只修改主配置
        if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(updateCommand.getUserIds())) {
            // 标记当前国家级补卡配置为过期
            markOldConfigAsExpired(currentConfig, currentDateAndTimeZoneDate);
            // 构建新的国家级补卡配置数据
            ReissueCardConfigDO newCountryLevelConfig = buildNewConfig(updateCommand, currentDateAndTimeZoneDate);
            newCountryLevelConfig.setIsCountryLevel(BusinessConstant.Y);

            // 获取原国家级配置的适用范围
            List<ReissueCardConfigRangeDO> oldConfigRanges = reissueCardConfigRangeDao.listByConfigId(currentConfig.getId());
            // 标记当前配置的适用范围为过期
            markOldConfigRangeAsExpired(oldConfigRanges, currentDateAndTimeZoneDate);
            // 构建新的配置适用范围
            List<ReissueCardConfigRangeDO> addConfigRanges = buildNewRangeForConfig(oldConfigRanges, newCountryLevelConfig);

            reissueCardConfigManage.configUpdateAndAdd(
                    currentConfig,
                    newCountryLevelConfig,
                    oldConfigRanges,
                    addConfigRanges
            );
            log.info("更新国家级补卡规则:{}，配置发生改动,修改为：{}", currentConfig.getConfigName(), newCountryLevelConfig);
            logRecordService.recordOperation(
                    newCountryLevelConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.REISSUE_CARD_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildReissueConfigChangeLog(newCountryLevelConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }

        // 部门或人员变动，从国家级别配置升级

        // 将当前的国家级别配置设置为过期
        markOldConfigAsExpired(currentConfig, currentDateAndTimeZoneDate);
        // 获取原国家级配置的适用范围
        List<ReissueCardConfigRangeDO> oldConfigRanges = reissueCardConfigRangeDao.listByConfigId(currentConfig.getId());
        // 标记原国家级配置的适用范围为过期
        markOldConfigRangeAsExpired(oldConfigRanges, currentDateAndTimeZoneDate);

        // 构建新的补卡配置数据(非国家级)
        ReissueCardConfigDO newConfig = buildNewConfig(updateCommand, currentDateAndTimeZoneDate);

        reissueCardConfigManage.configUpdateAndAdd(currentConfig, newConfig, oldConfigRanges, null);

        // 该国家的补卡配置已经修改了，需要重新查询
        countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(updateCommand.getCountry());

        // 处理适用范围变更
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.updateConfigRangeHandler(
                currentDateAndTimeZoneDate,
                currentConfig,
                newConfig,
                countryReissueCardConfig,
                updateCommand.getCountry(),
                updateCommand.getDeptIds(),
                updateCommand.getUserIds());
        log.info("更新补卡规则:{}，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);
        logRecordService.recordOperation(
                newConfig,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.REISSUE_CARD_CONFIG_UPDATE.getCode(),
                        ruleChangeLogService.buildReissueConfigChangeLog(newConfig, currentConfig)));
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 处理仅范围变更
     *
     * @param updateCommand              更新命令
     * @param currentConfig              当前配置
     * @param currentDateAndTimeZoneDate 当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleRangeOnlyUpdate(ReissueCardConfigUpdateCommand updateCommand,
                                                           ReissueCardConfigDO currentConfig,
                                                           DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        log.info("更新补卡规则:{}，仅变动了适用范围", updateCommand.getConfigName());
        boolean isBackToCountryLevelFlag = updateCommand.isCountryLevelRangeFlag();
        markOldConfigAsExpired(currentConfig, currentDateAndTimeZoneDate);

        if (isBackToCountryLevelFlag) {
            // 回退到国家级别
            return handleBackToCountryLevel(updateCommand, currentConfig, currentDateAndTimeZoneDate);
        } else {
            // 规则的适用范围变更，配置也需要变更, 将当前配置设置为过期
            // 构建新的补卡配置数据
            ReissueCardConfigDO newConfig = buildNewConfig(updateCommand, currentDateAndTimeZoneDate);
            //更新和新增主配置
            reissueCardConfigManage.configUpdateAndAdd(currentConfig, newConfig);

            // 该国家的补卡配置已经修改了，需要重新查
            CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(updateCommand.getCountry());

            // 处理适用范围变更
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.updateConfigRangeHandler(
                    currentDateAndTimeZoneDate,
                    currentConfig,
                    newConfig,
                    countryReissueCardConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds()
            );
            log.info("更新补卡规则:{},仅变动适用范围，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);
            logRecordService.recordOperation(
                    newConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.REISSUE_CARD_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildReissueConfigChangeLog(newConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
    }

    /**
     * 处理仅配置变更
     *
     * @param updateCommand              更新命令
     * @param currentConfig              当前配置
     * @param currentDateAndTimeZoneDate 当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleConfigOnlyUpdate(ReissueCardConfigUpdateCommand updateCommand,
                                                            ReissueCardConfigDO currentConfig,
                                                            DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        log.info("更新补卡规则:{}，仅变动配置，适用范围不变", updateCommand.getConfigName());
        // 标记当前配置为旧配置
        markOldConfigAsExpired(currentConfig, currentDateAndTimeZoneDate);
        // 获取原配置的适用范围
        List<ReissueCardConfigRangeDO> currentRangeDOList = reissueCardConfigRangeDao.listByConfigId(currentConfig.getId());
        // 标记当前配置的适用范围为过期
        markOldConfigRangeAsExpired(currentRangeDOList, currentDateAndTimeZoneDate);

        // 构建新的打卡配置数据
        ReissueCardConfigDO newConfig = buildNewConfig(updateCommand, currentDateAndTimeZoneDate);
        // 构建新的配置适用范围
        List<ReissueCardConfigRangeDO> addConfigRanges = buildNewRangeForConfig(currentRangeDOList, newConfig);
        //更新和新增主配置
        reissueCardConfigManage.configUpdateAndAdd(currentConfig, newConfig, currentRangeDOList, addConfigRanges);
        // 添加日志
        logRecordService.recordOperation(
                newConfig,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.REISSUE_CARD_CONFIG_UPDATE.getCode(),
                        ruleChangeLogService.buildReissueConfigChangeLog(newConfig, currentConfig)));
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 处理配置和范围都变更
     *
     * @param updateCommand              更新命令
     * @param currentConfig              当前配置
     * @param currentDateAndTimeZoneDate 当前日期
     * @return 规则配置变更检查结果
     */
    private RuleConfigChangeCheckDTO handleBothUpdate(ReissueCardConfigUpdateCommand updateCommand,
                                                      ReissueCardConfigDO currentConfig,
                                                      DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        log.info("更新补卡规则:{}，配置和适用范围都发生变动", updateCommand.getConfigName());
        boolean isBackToCountryLevelFlag = updateCommand.isCountryLevelRangeFlag();
        // 标记当前配置为旧配置
        markOldConfigAsExpired(currentConfig, currentDateAndTimeZoneDate);

        if (isBackToCountryLevelFlag) {
            // 回退到国家级别
            return handleBackToCountryLevel(updateCommand, currentConfig, currentDateAndTimeZoneDate);
        } else {
            // 规则的适用范围变更，配置也需要变更, 将当前配置设置为过期
            // 构建新的补卡配置数据
            ReissueCardConfigDO newConfig = buildNewConfig(updateCommand, currentDateAndTimeZoneDate);
            //更新和新增主配置
            reissueCardConfigManage.configUpdateAndAdd(currentConfig, newConfig);

            // 该国家的补卡配置已经修改了，需要重新查
            CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(updateCommand.getCountry());

            // 处理适用范围变更
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.updateConfigRangeHandler(
                    currentDateAndTimeZoneDate,
                    currentConfig,
                    newConfig,
                    countryReissueCardConfig,
                    updateCommand.getCountry(),
                    updateCommand.getDeptIds(),
                    updateCommand.getUserIds()
            );
            log.info("更新补卡规则:{},配置和适用范围都变更，规则影响人员为：{}", updateCommand.getConfigName(), ruleConfigRangeChangeDTO);
            logRecordService.recordOperation(
                    newConfig,
                    LogRecordOptions.buildWithRemark(OperationTypeEnum.REISSUE_CARD_CONFIG_UPDATE.getCode(),
                            ruleChangeLogService.buildReissueConfigChangeLog(newConfig, currentConfig)));
            return RuleConfigChangeCheckDTO.buildSuccess();
        }
    }

    @Transactional
    public RuleConfigChangeCheckDTO update(ReissueCardConfigUpdateCommand updateCommand) {
        List<String> enableNewAttendanceCountry = migrationService.getEnableNewAttendanceCountry();
        if (!enableNewAttendanceCountry.contains(updateCommand.getCountry())) {
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.THE_NON_GRAY_SCALE_COUNTRY_NEED_TO_OPERATE_IN_THE_HRMS_SYSTEM, updateCommand.getCountry());
        }
        // 获取当前配置
        ReissueCardConfigDO currentConfig = reissueCardConfigDao.getLatestByConfigNo(updateCommand.getConfigNo());

        // 验证更新命令
        validateUpdateCommand(updateCommand, currentConfig);

        // 获取该国家的补卡配置
        CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(updateCommand.getCountry());

        // 范围检查
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(updateCommand, countryReissueCardConfig);
        if (!ruleConfigChangeCheckDTO.getSuccess()) {
            return ruleConfigChangeCheckDTO;
        }

        // 确定补卡配置的更新类型
        ConfigUpdateType configUpdateType = determineUpdateType(updateCommand, currentConfig);

        // 配置和适用范围都未发生变动,直接返回
        if (configUpdateType == ConfigUpdateType.NO_CHANGES) {
            log.info("更新补卡规则:{}，配置和适用范围都未发生变动", updateCommand.getConfigName());
            return RuleConfigChangeCheckDTO.buildSuccess();
        }

        // 获取当前国家的时间
        CountryDTO countryDTO = countryService.queryCountry(updateCommand.getCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        // 获取国家级别的配置
        ReissueCardConfigDO countryLevelConfig = countryReissueCardConfig.queryCountryLevelConfig();

        // 当前更新的为国家级别配置，特殊处理
        if (countryLevelConfig != null && Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            return handleCountryLevelConfigUpdate(updateCommand, currentConfig, currentDateAndTimeZoneDate, countryReissueCardConfig);
        }

        // 根据更新类型选择处理逻辑
        switch (configUpdateType) {
            case RANGE_ONLY:
                return handleRangeOnlyUpdate(updateCommand, currentConfig, currentDateAndTimeZoneDate);
            case CONFIG_ONLY:
                return handleConfigOnlyUpdate(updateCommand, currentConfig, currentDateAndTimeZoneDate);
            case BOTH:
                return handleBothUpdate(updateCommand, currentConfig, currentDateAndTimeZoneDate);
            default:
                return RuleConfigChangeCheckDTO.buildSuccess();
        }
    }

    public UpdateRuleReflectResult checkStatusSwitch(ReissueCardConfigStatusSwitchCommand statusSwitchCommand) {
        List<ReissueCardConfigDO> reissueCardConfigDOList = reissueCardConfigDao.listByConfigNo(statusSwitchCommand.getConfigNo());
        if (CollectionUtils.isEmpty(reissueCardConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (null == statusEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "status=" + statusSwitchCommand.getStatus() + " not support");
        }
        ReissueCardConfigDO currentConfig = reissueCardConfigDOList.get(0);
        switch (statusEnum) {
            case ACTIVE:
                // 启用前范围检查
                RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(currentConfig);
                if (!ruleConfigChangeCheckDTO.getSuccess()) {
                    return UpdateRuleReflectResult.rangeCheckFail(ruleConfigChangeCheckDTO);
                }
                RuleConfigRangeChangeDTO enableConfigChangeDTO = reissueCardConfigRangeHandler.checkEnableConfig(currentConfig);
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(enableConfigChangeDTO.getUpdateUserIdList().size())
                        .updateRangeReflectAddUserSize(enableConfigChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(enableConfigChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            case DISABLED:
                RuleConfigRangeChangeDTO disableConfigChangeDTO = reissueCardConfigRangeHandler.checkDisableConfig(currentConfig);
                return UpdateRuleReflectResult.builder()
                        .isNeedUpdateConfig(true)
                        .isNeedUpdateRange(true)
                        .updateConfigReflectUserSize(disableConfigChangeDTO.getUpdateUserIdList().size())
                        .updateRangeReflectAddUserSize(disableConfigChangeDTO.getAddUserIdList().size())
                        .updateRangeReflectRemoveUserSize(disableConfigChangeDTO.getRemoveUserIdList().size())
                        .ruleConfigChangeCheckDTO(RuleConfigChangeCheckDTO.buildSuccess())
                        .build();
            default:
                return UpdateRuleReflectResult.noReflect();
        }
    }

    @Transactional
    public RuleConfigChangeCheckDTO statusSwitch(ReissueCardConfigStatusSwitchCommand statusSwitchCommand) {
        // 获取当前配置
        List<ReissueCardConfigDO> reissueCardConfigDOList = reissueCardConfigDao.listByConfigNo(statusSwitchCommand.getConfigNo());
        if (CollectionUtils.isEmpty(reissueCardConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        StatusEnum statusEnum = StatusEnum.getStatusEnum(statusSwitchCommand.getStatus());
        if (null == statusEnum) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "status=" + statusSwitchCommand.getStatus() + " not support");
        }
        ReissueCardConfigDO currentConfig = reissueCardConfigDOList.get(0);
        List<String> enableNewAttendanceCountry = migrationService.getEnableNewAttendanceCountry();
        if (!enableNewAttendanceCountry.contains(currentConfig.getCountry())) {
            throw BusinessLogicException.getException(
                    ErrorCodeEnum.THE_NON_GRAY_SCALE_COUNTRY_NEED_TO_OPERATE_IN_THE_HRMS_SYSTEM, currentConfig.getCountry());
        }
        switch (statusEnum) {
            case ACTIVE:
                // 启用前范围检查
                RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = validateRangeAndGetResult(currentConfig);
                if (!ruleConfigChangeCheckDTO.getSuccess()) {
                    return ruleConfigChangeCheckDTO;
                }
                activeConfig(currentConfig);
                break;
            case DISABLED:
                disableConfig(currentConfig);
                break;
            default:
                break;
        }
        return RuleConfigChangeCheckDTO.buildSuccess();
    }

    /**
     * 启用补卡规则
     *
     * @param ruleConfigUpdateDTO 当前配置
     */
    public void userReissueCardConfigRangeUpdate(AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO) {
        if (Objects.isNull(ruleConfigUpdateDTO.getNewConfigId())
                || Objects.isNull(ruleConfigUpdateDTO.getUserId())
                || Objects.isNull(ruleConfigUpdateDTO.getStartDateAndTimeZoneDate())) {
            return;
        }

        List<ReissueCardConfigRangeDO> reissueCardConfigRangeDOList = reissueCardConfigRangeDao.listConfigRanges(Collections.singletonList(ruleConfigUpdateDTO.getUserId()));
        if (CollectionUtils.isNotEmpty(reissueCardConfigRangeDOList)) {
            reissueCardConfigRangeDOList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(ruleConfigUpdateDTO.getStartDateAndTimeZoneDate().getTimeZoneDate());
                range.setExpireTimestamp(ruleConfigUpdateDTO.getStartDateAndTimeZoneDate().getDateTimeStamp());
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }

        // 查找新打卡规则记录
        List<ReissueCardConfigDO> reissueCardConfigDOList = reissueCardConfigDao.listLatestByConfigIds(Collections.singletonList(ruleConfigUpdateDTO.getNewConfigId()));
        if (CollectionUtils.isEmpty(reissueCardConfigDOList)) {
            log.error("员工档案变更补卡规则,新的补卡规则配置不存在");
            return;
        }

        ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigDOList.get(0);
        if (reissueCardConfigDO.areCountryLevel()) {
            log.error("员工档案变更补卡规则,不允许变更到国家级规则");
            return;
        }

        ReissueCardConfigRangeDO rangeDO = new ReissueCardConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(ruleConfigUpdateDTO.getUserId());
        rangeDO.setRuleConfigId(reissueCardConfigDO.getId());
        rangeDO.setRuleConfigNo(reissueCardConfigDO.getConfigNo());
        rangeDO.setRangeType(RuleRangeTypeEnum.USER.getCode());
        rangeDO.setEffectTime(ruleConfigUpdateDTO.getStartDateAndTimeZoneDate().getTimeZoneDate());
        rangeDO.setEffectTimestamp(ruleConfigUpdateDTO.getStartDateAndTimeZoneDate().getDateTimeStamp());
        rangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
        rangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        rangeDO.setRemark("考勤档案用户补卡规则变更");
        BaseDOUtil.fillDOInsertByUsrOrSystem(rangeDO);

        reissueCardConfigManage.configRangeUpdateOrAdd(reissueCardConfigRangeDOList, Collections.singletonList(rangeDO));
    }

    private void activeConfig(ReissueCardConfigDO currentConfig) {
        String country = currentConfig.getCountry();

        CountryDTO countryDTO = countryService.queryCountry(country);
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        boolean isCountryLevel = currentConfig.areCountryLevel();
        if (isCountryLevel) {
            log.info("启用国家级补卡规则:{}", currentConfig.getConfigName());
            List<UserInfoDO> userInfoDOList = reissueCardConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder().country(country).build()
            );
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                return;
            }
            List<Long> userIdList = userInfoDOList.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
            List<ReissueCardConfigRangeDO> addConfigRangeList = new ArrayList<>();
            List<ReissueCardConfigRangeDO> updateConfigRangeList = new ArrayList<>();
            //新增的国家级范围
            for (Long userId : userIdList) {
                ReissueCardConfigRangeDO configRangeDO = new ReissueCardConfigRangeDO();
                configRangeDO.setId(defaultIdWorker.nextId());
                configRangeDO.setRuleConfigId(currentConfig.getId());
                configRangeDO.setRuleConfigNo(currentConfig.getConfigNo());
                configRangeDO.setBizId(userId);
                configRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
                configRangeDO.setEffectTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                configRangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
                configRangeDO.setEffectTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                configRangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
                configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                configRangeDO.setIsLatest(BusinessConstant.Y);
                BaseDOUtil.fillDOInsert(configRangeDO);
                addConfigRangeList.add(configRangeDO);
            }
            //处理停用的国家级范围，修改为非最新
            List<ReissueCardConfigRangeDO> oldCountryLevelRangeList = reissueCardConfigRangeDao.listDisabledByConfigId(currentConfig.getId());
            if (CollectionUtils.isNotEmpty(oldCountryLevelRangeList)) {
                for (ReissueCardConfigRangeDO item : oldCountryLevelRangeList) {
                    item.setIsLatest(BusinessConstant.N);
                    BaseDOUtil.fillDOUpdate(item);
                    updateConfigRangeList.add(item);
                }
            }
            reissueCardConfigManage.configRangeUpdateOrAdd(updateConfigRangeList, addConfigRangeList);
            log.info("启用国家级补卡规则:{}，新增补卡规则的影响人数为:{}", currentConfig.getConfigName(), addConfigRangeList.size());
        } else {
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.enableConfigRangeHandler(
                    currentDateAndTimeZoneDate,
                    currentConfig,
                    currentConfig);
            log.info("启用补卡规则:{},规则影响人员为：{}", currentConfig.getConfigName(), ruleConfigRangeChangeDTO);
        }

        currentConfig.setStatus(StatusEnum.ACTIVE.getCode());
        currentConfig.setEffectTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        currentConfig.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
        currentConfig.setEffectTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        currentConfig.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
        currentConfig.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOUpdate(currentConfig);
        reissueCardConfigManage.configUpdateAndAdd(currentConfig, null);

        logRecordService.recordOperation(
                currentConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ACTIVE)
                        .operationType(OperationTypeEnum.REISSUE_CARD_CONFIG_ACTIVE.getCode())
                        .country(currentConfig.getCountry())
                        .bizName(currentConfig.getConfigName())
                        .fieldNameList(ruleLogFieldList)
                        .build());
    }

    /**
     * 停用补卡规则
     *
     * @param currentConfig 当前配置
     */
    private void disableConfig(ReissueCardConfigDO currentConfig) {
        String country = currentConfig.getCountry();

        CountryDTO countryDTO = countryService.queryCountry(country);
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        boolean isCountryLevel = currentConfig.areCountryLevel();
        // 国家级规则停用特殊处理
        if (isCountryLevel) {
            log.info("停用国家级补卡规则:{}", currentConfig.getConfigName());
            List<ReissueCardConfigRangeDO> countryLevelRangeList = reissueCardConfigRangeDao.listByConfigId(currentConfig.getId());
            if (CollectionUtils.isEmpty(countryLevelRangeList)) {
                return;
            }
            for (ReissueCardConfigRangeDO reissueCardConfigRangeDO : countryLevelRangeList) {
                reissueCardConfigRangeDO.setStatus(StatusEnum.DISABLED.getCode());
                reissueCardConfigRangeDO.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                reissueCardConfigRangeDO.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                reissueCardConfigRangeDO.setRemark("页面停用作导致旧规则停用");
                BaseDOUtil.fillDOUpdate(reissueCardConfigRangeDO);
            }
            reissueCardConfigManage.configRangeUpdateOrAdd(countryLevelRangeList, null);
            log.info("停用国家级补卡规则:{}，规则影响人员为：{}", currentConfig.getConfigName(), countryLevelRangeList.size());
        } else {
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = reissueCardConfigRangeHandler.disableConfigRangeHandler(
                    currentDateAndTimeZoneDate,
                    currentConfig,
                    currentConfig,
                    reissueCardConfigManage.getCountryConfig(country),
                    country
            );
            log.info("停用补卡规则:{},规则影响人员为：{}", currentConfig.getConfigName(), ruleConfigRangeChangeDTO);
        }

        currentConfig.setStatus(StatusEnum.DISABLED.getCode());
        currentConfig.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        currentConfig.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        BaseDOUtil.fillDOUpdate(currentConfig);
        reissueCardConfigManage.configUpdateAndAdd(currentConfig, null);

        logRecordService.recordOperation(
                currentConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DISABLE)
                        .operationType(OperationTypeEnum.REISSUE_CARD_CONFIG_DISABLED.getCode())
                        .country(currentConfig.getCountry())
                        .bizName(currentConfig.getConfigName())
                        .fieldNameList(ruleLogFieldList)
                        .build());
    }

    /**
     * 确定更新类型
     *
     * @param updateCommand 更新命令
     * @param currentConfig 当前配置
     * @return 更新类型
     */
    private ConfigUpdateType determineUpdateType(ReissueCardConfigUpdateCommand updateCommand,
                                                 ReissueCardConfigDO currentConfig) {
        boolean configChanged = checkConfigRuleNeedUpdate(updateCommand, currentConfig);
        boolean rangeChanged = checkConfigRangeNeedUpdate(updateCommand, currentConfig);

        if (configChanged && rangeChanged) {
            return ConfigUpdateType.BOTH;
        }
        if (configChanged) {
            return ConfigUpdateType.CONFIG_ONLY;
        }
        if (rangeChanged) {
            return ConfigUpdateType.RANGE_ONLY;
        }
        return ConfigUpdateType.NO_CHANGES;
    }

    /**
     * 检查补卡配置是否需要更新
     *
     * @param updateCommand 更新命令
     * @param configDO      配置
     * @return 是否需要更新
     */
    public boolean checkConfigRuleNeedUpdate(ReissueCardConfigUpdateCommand updateCommand,
                                             ReissueCardConfigDO configDO) {
        if (!StringUtils.equals(updateCommand.getConfigName(), configDO.getConfigName())) {
            return true;
        }
        if (!Objects.equals(updateCommand.getMaxRepunchNumber(), configDO.getMaxRepunchNumber())) {
            return true;
        }
        return false;
    }

    /**
     * 检查补卡适用范围是否需要更新
     *
     * @param updateCommand 更新命令
     * @param configDO      配置
     * @return 是否需要更新
     */
    public boolean checkConfigRangeNeedUpdate(ReissueCardConfigUpdateCommand updateCommand,
                                              ReissueCardConfigDO configDO) {
        // 获取更新后的部门ID和用户ID列表
        List<Long> updatedDeptIds = CollectionUtils.isEmpty(updateCommand.getDeptIds()) ? new ArrayList<>()
                : updateCommand.getDeptIds();
        List<Long> updatedUserIds = CollectionUtils.isEmpty(updateCommand.getUserIds()) ? new ArrayList<>()
                : updateCommand.getUserIds();

        // 获取已有的部门ID列表
        List<Long> existingDeptIds = configDO.listDeptIds();

        // 检查部门ID差异
        if (hasDifference(updatedDeptIds, existingDeptIds)) {
            return true;
        }

        // 获取已有的用户ID列表
        List<Long> existingUserIds = getExistingUserIds(configDO.getId());

        // 检查用户ID差异
        return hasDifference(updatedUserIds, existingUserIds);
    }

    /**
     * 检查两个列表是否存在差异
     *
     * @param firstList  第一个列表
     * @param secondList 第二个列表
     * @return 是否存在差异
     */
    private boolean hasDifference(List<Long> firstList, List<Long> secondList) {
        // 检查firstList中是否有secondList没有的元素
        if (firstList.stream().anyMatch(id -> !secondList.contains(id))) {
            return true;
        }
        // 检查secondList中是否有firstList没有的元素
        return secondList.stream().anyMatch(id -> !firstList.contains(id));
    }

    /**
     * 获取已有的用户级的适用范围列表
     *
     * @param configId 配置ID
     * @return 已有的用户ID列表
     */
    private List<Long> getExistingUserIds(Long configId) {
        List<ReissueCardConfigRangeDO> rangeList = reissueCardConfigRangeDao.listByConfigId(configId);
        return rangeList.stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .map(ReissueCardConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 检查补卡适用范围是否有效(添加)
     *
     * @param addCommand         添加命令
     * @param countryConfigs     国家配置列表
     * @param countryLevelConfig 国家级别配置
     * @return 是否有效
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(ReissueCardConfigAddCommand addCommand,
                                                       List<ReissueCardConfigDO> countryConfigs,
                                                       ReissueCardConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();

        // 如果国家级别的配置存在,添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置，国家级规则重复添加
        if (countryLevelConfig != null &&
                CollectionUtils.isEmpty(addCommand.getDeptIds()) &&
                CollectionUtils.isEmpty(addCommand.getUserIds())) {
            rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                    addCommand.getCountry(), countryLevelConfig.getConfigName()));
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, addCommand.getDeptIds(), null, rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        validateUserUsage(addCommand.getUserIds(), null, rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ? RuleConfigChangeCheckDTO.buildSuccess()
                : RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 检查补卡适用范围是否有效(更新)
     *
     * @param updateCommand      更新命令
     * @param countryConfigs     国家配置列表
     * @param countryLevelConfig 国家级别配置
     * @return 是否有效
     */
    private RuleConfigChangeCheckDTO checkRangeIsValid(ReissueCardConfigUpdateCommand updateCommand,
                                                       List<ReissueCardConfigDO> countryConfigs,
                                                       ReissueCardConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();
        // 如果国家级别的配置存在,添加的部门和用户id都为空,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null) {
            if (CollectionUtils.isEmpty(updateCommand.getDeptIds()) &&
                    CollectionUtils.isEmpty(updateCommand.getUserIds())) {
                // 如果编辑后满足国家级别，且前端传的规则编码和国家级编码不一致，则冲突（不是更新国家级规则）
                if (!StringUtils.equals(updateCommand.getConfigNo(), countryLevelConfig.getConfigNo())) {
                    rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                            updateCommand.getCountry(), countryLevelConfig.getConfigName()));
                }
            }
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, updateCommand.getDeptIds(), updateCommand.getConfigNo(), rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        validateUserUsage(updateCommand.getUserIds(), updateCommand.getConfigNo(), rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ? RuleConfigChangeCheckDTO.buildSuccess()
                : RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    private RuleConfigChangeCheckDTO checkRangeIsValid(ReissueCardConfigDO currentConfig,
                                                       List<ReissueCardConfigDO> countryConfigs,
                                                       ReissueCardConfigDO countryLevelConfig) {
        // 1. 检查国家级别的规则重复
        List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos = new ArrayList<>();
        // 如果国家级别的配置存在,则说明本次添加或更新的是国家级别的配置，需要校验是否冲突
        if (countryLevelConfig != null && currentConfig.areCountryLevel() &&
                !Objects.equals(currentConfig.getId(), countryLevelConfig.getId())) {
            rangeDuplicateInfos.add(RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                    currentConfig.getCountry(), countryLevelConfig.getConfigName()));
        }

        // 2. 检查部门级别的规则重复
        validateDeptUsage(countryConfigs, currentConfig.listDeptIds(), currentConfig.getConfigNo(), rangeDuplicateInfos);

        // 3. 检查人员是否已绑定其他规则
        List<Long> currentConfigUserIds = reissueCardConfigRangeDao.listDisabledByConfigId(currentConfig.getId())
                .stream()
                .map(ReissueCardConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        validateUserUsage(currentConfigUserIds, currentConfig.getConfigNo(), rangeDuplicateInfos);

        return CollectionUtils.isEmpty(rangeDuplicateInfos) ?
                RuleConfigChangeCheckDTO.buildSuccess() :
                RuleConfigChangeCheckDTO.buildFailure(rangeDuplicateInfos);
    }

    /**
     * 部门校验逻辑
     */
    private void validateDeptUsage(List<ReissueCardConfigDO> configDOList, List<Long> deptIds, String configNo,
                                   List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos) {
        if (CollectionUtils.isEmpty(configDOList)) {
            return;
        }
        configDOList.forEach(configDO -> {
            if (!StringUtils.equals(configNo, configDO.getConfigNo())) {
                List<Long> deptIdList = configDO.listDeptIds();
                List<Long> duplicateDeptIds = deptIdList.stream()
                        .filter(deptIds::contains)
                        .collect(Collectors.toList());

                duplicateDeptIds.forEach(deptId -> {
                    AttendanceDept attendanceDept = deptService.getByDeptId(deptId);
                    if (attendanceDept != null) {
                        rangeDuplicateInfos.add(
                                RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                                        attendanceDept.getLocalizeName(), configDO.getConfigName()));
                    }
                });
            }
        });
    }

    /**
     * 用户校验逻辑
     */
    private void validateUserUsage(List<Long> userIds,
                                   String configNo,
                                   List<RuleConfigChangeCheckDTO.RangeDuplicateInfo> rangeDuplicateInfos) {
        List<ReissueCardConfigRangeDO> existedUserLevelConfigRecords = reissueCardConfigRangeDao.listConfigRanges(userIds)
                .stream()
                .filter(ReissueCardConfigRangeDO::areUserRange)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(existedUserLevelConfigRecords)) {
            return;
        }
        // 收集所有需要查询的用户ID
        Set<Long> allUserIds = existedUserLevelConfigRecords.stream()
                .map(ReissueCardConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        // 一次性批量查询所有用户信息
        Map<Long, AttendanceUser> userMap = userService.listUsersByIds(new ArrayList<>(allUserIds))
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getId, user -> user, (a, b) -> a));

        Map<Long, List<Long>> configIdToUserIdsMap = existedUserLevelConfigRecords.stream()
                .collect(Collectors.groupingBy(
                        ReissueCardConfigRangeDO::getRuleConfigId,
                        Collectors.mapping(ReissueCardConfigRangeDO::getBizId, Collectors.toList())));

        List<Long> configIdList = new ArrayList<>(configIdToUserIdsMap.keySet());
        List<ReissueCardConfigDO> configDOS = reissueCardConfigDao.listLatestByConfigIds(configIdList);

        configDOS.stream()
                .filter(config -> !StringUtils.equals(config.getConfigNo(), configNo))
                .forEach(config -> {
                    List<Long> repeatUserIds = configIdToUserIdsMap.getOrDefault(config.getId(), Collections.emptyList());
                    for (Long repeatUserId : repeatUserIds) {
                        AttendanceUser attendanceUser = userMap.get(repeatUserId);
                        if (null != attendanceUser) {
                            rangeDuplicateInfos.add(
                                    RuleConfigChangeCheckDTO.RangeDuplicateInfo.of(
                                            attendanceUser.getLocaleName(), config.getConfigName()));
                        }
                    }
                });
    }

    /**
     * 构建新的补卡配置(非国家级)
     *
     * @param updateCommand              更新命令
     * @param currentDateAndTimeZoneDate 当前日期
     * @return 新的补卡配置
     */
    private ReissueCardConfigDO buildNewConfig(ReissueCardConfigUpdateCommand updateCommand, DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        ReissueCardConfig reissueCardConfig = ReissueCardConfig.builder()
                .id(defaultIdWorker.nextId())
                .country(updateCommand.getCountry())
                .configNo(defaultIdWorker.nextReissueCardConfigNo())
                .configName(updateCommand.getConfigName())
                .maxRepunchNumber(updateCommand.getMaxRepunchNumber())
                .status(StatusEnum.ACTIVE.getCode())
                .deptIds(StringUtils.join(updateCommand.getDeptIds(), StrPool.COMMA))
                .isLatest(BusinessConstant.Y)
                .effectDateAndTimeZoneDate(currentDateAndTimeZoneDate)
                .expireDateAndTimeZoneDate(DateAndTimeZoneDate.ofEndDate())
                .isCountryLevel(BusinessConstant.N)
                .build();
        ReissueCardConfigDO model = ReissueCardConfigMapstruct.INSTANCE.toReissueCardConfigDO(reissueCardConfig);
        BaseDOUtil.fillDOInsert(model);
        return model;
    }

    /**
     * 构建新的补卡适用范围
     *
     * @param rangeDOList 适用范围列表
     * @param newConfig   新的补卡配置
     * @return 新的补卡适用范围列表
     */
    private List<ReissueCardConfigRangeDO> buildNewRangeForConfig(List<ReissueCardConfigRangeDO> rangeDOList,
                                                                  ReissueCardConfigDO newConfig) {
        return rangeDOList.stream()
                .map(rangeDO -> {
                    ReissueCardConfigRangeDO configRangeDO = new ReissueCardConfigRangeDO();
                    configRangeDO.setId(defaultIdWorker.nextId());
                    configRangeDO.setRuleConfigId(newConfig.getId());
                    configRangeDO.setRuleConfigNo(newConfig.getConfigNo());
                    configRangeDO.setBizId(rangeDO.getBizId());
                    configRangeDO.setRangeType(rangeDO.getRangeType());
                    configRangeDO.setEffectTime(newConfig.getEffectTime());
                    configRangeDO.setExpireTime(newConfig.getExpireTime());
                    configRangeDO.setEffectTimestamp(newConfig.getEffectTimestamp());
                    configRangeDO.setExpireTimestamp(newConfig.getExpireTimestamp());
                    configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
                    configRangeDO.setIsLatest(BusinessConstant.Y);
                    BaseDOUtil.fillDOInsert(configRangeDO);
                    return configRangeDO;
                }).collect(Collectors.toList());
    }

    /**
     * 标记旧的补卡配置为过期
     *
     * @param currentConfig              当前配置
     * @param currentDateAndTimeZoneDate 当前时间
     */
    private void markOldConfigAsExpired(ReissueCardConfigDO currentConfig, DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        currentConfig.setIsLatest(BusinessConstant.N);
        currentConfig.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        currentConfig.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        BaseDOUtil.fillDOUpdate(currentConfig);
    }

    /**
     * 标记旧的补卡适用范围为过期
     *
     * @param rangeDOList                适用范围列表
     * @param currentDateAndTimeZoneDate 当前日期
     */
    private void markOldConfigRangeAsExpired(List<ReissueCardConfigRangeDO> rangeDOList, DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        for (ReissueCardConfigRangeDO configRangeDO : rangeDOList) {
            configRangeDO.setIsLatest(BusinessConstant.N);
            configRangeDO.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
            configRangeDO.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
            configRangeDO.setRemark("页面操作导致旧规则过期");
            BaseDOUtil.fillDOUpdate(configRangeDO);
        }
    }

}
