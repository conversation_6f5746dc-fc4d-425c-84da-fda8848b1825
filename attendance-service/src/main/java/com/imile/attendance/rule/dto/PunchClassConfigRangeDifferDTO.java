package com.imile.attendance.rule.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/4/15
 */
@Data
public class PunchClassConfigRangeDifferDTO implements Serializable {

    /**
     * 适用范围变更标识
     */
    private Boolean rangeUpdate = Boolean.FALSE;


    /**
     * 移除适用单位用户集合
     */
    private Set<Long> removeRangeUserList;

    /**
     * 不变的适用单位用户集合
     */
    private Set<Long> noChangeRangeUserList;

    /**
     * 新增适用范围用户集合
     * 当用适用范围没变化时若时段信息变更也会加进来
     */
    private Set<Long> addRangeUserList;

}
