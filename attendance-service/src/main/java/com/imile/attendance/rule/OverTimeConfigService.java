package com.imile.attendance.rule;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.OverTimeConfigPageQuery;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.rule.bo.OverTimeConfigBO;
import com.imile.attendance.rule.command.OverTimeConfigAddCommand;
import com.imile.attendance.rule.command.OverTimeConfigStatusSwitchCommand;
import com.imile.attendance.rule.command.OverTimeConfigUpdateCommand;
import com.imile.attendance.rule.dto.AttendanceArchiveRuleConfigUpdateDTO;
import com.imile.attendance.rule.dto.ConfigRangeDTO;
import com.imile.attendance.rule.dto.OverTimeConfigDetailDTO;
import com.imile.attendance.rule.dto.OverTimeConfigPageDTO;
import com.imile.attendance.rule.dto.RuleConfigChangeCheckDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.dto.UpdateRuleReflectResult;
import com.imile.attendance.rule.factory.OverTimeConfigFactory;
import com.imile.attendance.rule.mapstruct.OverTimeConfigMapstruct;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import com.imile.attendance.rule.query.RuleConfigUserQuery;
import com.imile.attendance.util.PageUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.PaginationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16
 * @Description 加班规则服务
 */
@Slf4j
@Service
public class OverTimeConfigService {

    @Resource
    private OverTimeConfigFactory overTimeConfigFactory;
    @Resource
    private OverTimeConfigDao overTimeConfigDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private OverTimeConfigQueryService overTimeConfigQueryService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private ConverterService converterService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendancePermissionService attendancePermissionService;


    public RuleConfigChangeCheckDTO add(OverTimeConfigAddCommand addCommand) {
        return overTimeConfigFactory.add(addCommand);
    }

    public UpdateRuleReflectResult checkUpdateRule(OverTimeConfigUpdateCommand updateCommand) {
        return overTimeConfigFactory.checkUpdateRule(updateCommand);
    }

    public RuleConfigChangeCheckDTO update(OverTimeConfigUpdateCommand updateCommand) {
        return overTimeConfigFactory.update(updateCommand);
    }

    public UpdateRuleReflectResult checkStatusSwitch(OverTimeConfigStatusSwitchCommand statusSwitchCommand) {
        return overTimeConfigFactory.checkStatusSwitch(statusSwitchCommand);
    }

    public RuleConfigChangeCheckDTO statusSwitch(OverTimeConfigStatusSwitchCommand statusSwitchCommand) {
        return overTimeConfigFactory.statusSwitch(statusSwitchCommand);
    }

    /**
     * 用户加班规则适用范围变更
     * 若旧加班规则适用范围为用户级别则移除
     * 若旧加班规则适用范围为非用户级别更新为历史版本
     * 添加新用户级别的加班规则适用范围
     */
    public void userOverTimeConfigRangeUpdate(AttendanceArchiveRuleConfigUpdateDTO ruleConfigUpdateDTO) {
        overTimeConfigFactory.userOverTimeConfigRangeUpdate(ruleConfigUpdateDTO);
    }

    /**
     * 加班规则列表
     */
    public PaginationResult<OverTimeConfigPageDTO> page(OverTimeConfigPageQuery query) {
        // 更改为常驻国权限
        List<String> countryAuthList = attendancePermissionService.filterUserCountryAuth(query.getCountry(), null);
        log.info("加班规则列表当前用户:{}，有权限的国家列表:{}", RequestInfoHolder.getUserCode(), countryAuthList);
        if (CollectionUtils.isEmpty(countryAuthList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setCountryList(countryAuthList);
        //如果传入了用户ID，才做用户维度的范围判断
        List<Long> userIds = query.getUserIdList();
        if (CollectionUtils.isNotEmpty(userIds)) {
            Set<Long> configIdSet = new HashSet<>();
            //查询用户
            List<AttendanceUser> users = userService.listUsersByIds(userIds);
            if (CollectionUtils.isEmpty(users)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            //根据 range 表判断哪些用户已有规则
            List<OverTimeConfigRangeDO> rangeList = overTimeConfigRangeDao.listNotDeletedConfigRanges(userIds);
            //用户已有范围的规则ID(包含启用和停用的)
            rangeList.stream()
                    .map(OverTimeConfigRangeDO::getRuleConfigId)
                    .forEach(configIdSet::add);
            if (CollectionUtils.isEmpty(configIdSet)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            query.setConfigIds(new ArrayList<>(configIdSet));
        }
        PageInfo<OverTimeConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount()).doSelectPageInfo(
                () -> overTimeConfigDao.pageQuery(query));
        List<OverTimeConfigDO> configDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(configDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 获取所有加班配置id
        List<Long> configIds = configDOList.stream()
                .map(OverTimeConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<OverTimeConfigRangeDO> allRangeList = overTimeConfigRangeDao.listNotDeletedByConfigIds(configIds);

        List<OverTimeConfigPageDTO> dtoList = new ArrayList<>();
        for (OverTimeConfigDO overTimeConfigDO : configDOList) {
            OverTimeConfigPageDTO dto = OverTimeConfigMapstruct.INSTANCE.toPageDTO(overTimeConfigDO);
            setRangeAndUserInfo(dto, overTimeConfigDO, allRangeList);
            dtoList.add(dto);
        }
        return PageUtil.getPageResult(dtoList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 分页查询加班配置的用户列表
     * 支持两种场景：
     * 1. 国家级别配置：查询该国家下所有在职非司机且未配置规则的用户
     * 2. 普通配置：查询已配置的用户列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PaginationResult<RuleConfigUserInfoDTO> pageConfigUserList(RuleConfigUserQuery query) {
        String configNo = query.getConfigNo();
        if (Objects.isNull(configNo)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        OverTimeConfigBO configBO = overTimeConfigManage.getConfigBO(configNo);
        if (null == configBO) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<OverTimeConfigRangeDO> configRangeDOAllList = configBO.getRangeDOList();
        Map<Long, List<OverTimeConfigRangeDO>> userRangeMap = configRangeDOAllList.stream()
                .collect(Collectors.groupingBy(OverTimeConfigRangeDO::getBizId));
        List<Long> userIds = new ArrayList<>(userRangeMap.keySet());
        if (CollectionUtils.isEmpty(userIds)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        //查询员工
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .deptIds(query.getDeptIds())
                .locationCountry(query.getLocationCountry())
                .codeOrNameLike(query.getUserCodeOrName())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .isDelete(IsDeleteEnum.NO.getCode())
                .build();
        if (Objects.isNull(query.getDeptId())) {
            userDaoQuery.setDeptId(null);
        }
        PageInfo<UserInfoDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount()).doSelectPageInfo(
                () -> userInfoDao.userList(userDaoQuery));
        List<UserInfoDO> userList = pageInfo.getList();

        List<RuleConfigUserInfoDTO> ruleConfigUserInfoDTOS = transferRuleConfigUserDTO(userList, userRangeMap);
        return PageUtil.getPageResult(ruleConfigUserInfoDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    /**
     * 查询加班配置详情
     *
     * @param configNo 配置编码
     * @return 加班配置详情
     */
    public OverTimeConfigDetailDTO queryConfigDetail(String configNo) {
        return overTimeConfigQueryService.queryConfigDetail(configNo);
    }


    /**
     * 设置范围和员工信息
     */
    private void setRangeAndUserInfo(OverTimeConfigPageDTO dto,
                                     OverTimeConfigDO configDO,
                                     List<OverTimeConfigRangeDO> configRangeDOAllList) {
        List<ConfigRangeDTO> configRanges = new ArrayList<>();

        // 获取用户IDs
        List<Long> userIds = configDO.areActive() ?
                getActiveUserIds(configRangeDOAllList, configDO.getId()) :
                getInactiveUserIds(configRangeDOAllList, configDO.getId());

        // 处理配置范围
        if (configDO.areCountryLevel()) {
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
                //停用规则的员工数量为0
                dto.setEmployeeCount(configDO.areActive() ? activeUsers.size() : 0);
            } else {
                dto.setEmployeeCount(0);
            }
            configRanges.add(ConfigRangeDTO.buildCountryRangeDTO(configDO.getCountry()));
        } else {
            // 处理用户范围
            if (CollectionUtils.isNotEmpty(userIds)) {
                List<AttendanceUser> activeUsers = userService.listActiveAndOnJobUsers(userIds);
                //停用规则的员工数量为0
                dto.setEmployeeCount(configDO.areActive() ? activeUsers.size() : 0);
                configRanges.addAll(buildUserRangeRecords(activeUsers, configRangeDOAllList));
            }
            // 处理部门范围
            configRanges.addAll(buildDeptRangeRecords(configDO));
        }
        dto.setRangeRecords(configRanges);
    }

    private List<Long> getActiveUserIds(List<OverTimeConfigRangeDO> configRangeDOAllList, Long configId) {
        return configRangeDOAllList.stream()
                .filter(OverTimeConfigRangeDO::areActive)
                .filter(o -> o.getRuleConfigId().equals(configId))
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    private List<Long> getInactiveUserIds(List<OverTimeConfigRangeDO> configRangeDOAllList, Long configId) {
        return configRangeDOAllList.stream()
                .filter(OverTimeConfigRangeDO::areDisabled)
                .filter(o -> o.getRuleConfigId().equals(configId))
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 构建用户范围记录
     */
    private List<ConfigRangeDTO> buildUserRangeRecords(List<AttendanceUser> activeUsers,
                                                       List<OverTimeConfigRangeDO> configRangeDOS) {

        Set<Long> existingUserIds = configRangeDOS.stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toSet());

        return activeUsers.stream()
                .filter(user -> existingUserIds.contains(user.getId()))
                .map(ConfigRangeDTO::buildUserRangeDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建部门范围记录
     */
    private List<ConfigRangeDTO> buildDeptRangeRecords(OverTimeConfigDO configDO) {
        if (configDO == null || StringUtils.isBlank(configDO.getDeptIds())) {
            return Collections.emptyList();
        }

        List<Long> deptIdList = configDO.listDeptIds();
        if (CollectionUtils.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }

        return deptService.listByDeptIds(deptIdList).stream()
                .map(ConfigRangeDTO::buildDeptRangeDTO)
                .collect(Collectors.toList());
    }

    private ConfigRangeDTO buildCountryRangeRecords(OverTimeConfigDO configDO) {
        ConfigRangeDTO configRangeDTO = new ConfigRangeDTO();
        configRangeDTO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
        configRangeDTO.setBizId(null);
        configRangeDTO.setBizNameByLang(configDO.getCountry());
        return configRangeDTO;
    }

    /**
     * 转换规则配置用户信息
     *
     * @param userList     用户列表
     * @param userRangeMap 用户范围映射
     * @return 规则配置用户信息列表
     */
    private List<RuleConfigUserInfoDTO> transferRuleConfigUserDTO(List<UserInfoDO> userList,
                                                                  Map<Long, List<OverTimeConfigRangeDO>> userRangeMap) {
        // 获取部门信息
        List<Long> deptIds = userList.stream()
                .map(UserInfoDO::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIds)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, item -> item, (oldVal, newVal) -> oldVal));

        // 转换用户信息
        List<RuleConfigUserInfoDTO> ruleConfigUserInfoList = PunchConfigMapstruct.INSTANCE.toRuleConfigUserInfoDTO(userList);

        // 设置部门信息和创建时间
        for (RuleConfigUserInfoDTO ruleConfigUserInfoDTO : ruleConfigUserInfoList) {
            // 设置部门信息
            AttendanceDept attendanceDept = deptMap.get(ruleConfigUserInfoDTO.getDeptId());
            if (Objects.nonNull(attendanceDept)) {
                ruleConfigUserInfoDTO.setCountry(attendanceDept.getCountry());
                ruleConfigUserInfoDTO.setDeptCode(attendanceDept.getDeptCode());
                ruleConfigUserInfoDTO.setDeptName(attendanceDept.getLocalizeName());
            }
            // 设置规则范围的绑定时间
            if (Objects.nonNull(userRangeMap)) {
                // 非国家级规则
                List<OverTimeConfigRangeDO> userRangeList = userRangeMap.getOrDefault(ruleConfigUserInfoDTO.getId(), Collections.emptyList());
                if (CollectionUtils.isNotEmpty(userRangeList)) {
                    ruleConfigUserInfoDTO.setCreateDate(userRangeList.get(0).getEffectTime());
                }
            }
        }

        converterService.withAnnotation(ruleConfigUserInfoList);
        return ruleConfigUserInfoList;
    }


}
