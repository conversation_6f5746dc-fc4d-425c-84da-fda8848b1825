package com.imile.attendance.rule.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigVO implements Serializable {

    /**
     * 班次ID
     */
    private Long id;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 当前应用员工数
     */
    private Integer employeeCount;

    /**
     * 今日排班应用员工数
     */
    private Integer employeeSchedulingCount;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 适用范围
     */
    private List<PunchClassConfigRangeVO> rangeRecords;
}
