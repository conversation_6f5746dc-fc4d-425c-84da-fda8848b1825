package com.imile.attendance.rule.dto;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PunchTimeCalculator;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/20 
 * @Description
 */
@Slf4j
@Data
public class DayPunchTimeDTO {

    private Date dayPunchStartTime;

    private Date dayPunchEndTime;


    /**
     * 创建打卡时间DTO实例
     */
    public static DayPunchTimeDTO create(Date startTime, Date endTime) {
        DayPunchTimeDTO dto = new DayPunchTimeDTO();
        dto.setDayPunchStartTime(startTime);
        dto.setDayPunchEndTime(endTime);
        return dto;
    }

    /**
     * 记录日志
     */
    public void logTimePeriod(String methodName) {
        if (dayPunchStartTime == null || dayPunchEndTime == null) {
            return;
        }
        log.info("{} | dayPunchStartTime的值为:{}, dayPunchEndTime的值为:{}",
                methodName,
                DateHelper.formatYYYYMMDDHHMMSS(dayPunchStartTime),
                DateHelper.formatYYYYMMDDHHMMSS(dayPunchEndTime));
    }


    /**
     * 计算班次的打卡时间范围
     * @param baseDate 基准日期
     * @return 打卡时间范围DTO
     */
    public static DayPunchTimeDTO calculatePunchTimeFrame(Date baseDate,
                                                          PunchClassItemConfigDO itemConfigDO) {
        if (itemConfigDO.notHasValidTimes()) {
            return null;
        }
        Date startDateTime = PunchTimeCalculator.getDateFromDateAndTime(baseDate, itemConfigDO.getEarliestPunchInTime());
        Date endDateTime = PunchTimeCalculator.getDateFromDateAndTime(baseDate, itemConfigDO.getLatestPunchOutTime());

        return create(startDateTime, endDateTime);
    }
}
