package com.imile.attendance.rule.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigExportDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigRangeDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.dto.DayNormalPunchTimeDTO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.dto.RuleConfigUserInfoDTO;
import com.imile.attendance.rule.mapstruct.PunchClassConfigApiMapstruct;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import com.imile.attendance.rule.query.PunchClassConfigDetailQuery;
import com.imile.attendance.rule.query.PunchClassConfigListQuery;
import com.imile.attendance.rule.query.PunchClassConfigUserQuery;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.rule.vo.PunchClassConfigDetailVO;
import com.imile.attendance.rule.vo.PunchClassConfigExportVO;
import com.imile.attendance.rule.vo.PunchClassConfigRangeVO;
import com.imile.attendance.rule.vo.PunchClassConfigVO;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.util.PunchTimeCalculator;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.lang.I18nUtils;
import com.imile.util.user.UserEvnHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 班次查询服务
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
@Slf4j
@Service
public class PunchClassConfigQueryServiceImpl implements PunchClassConfigQueryService {
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private PunchClassConfigRangeDao punchClassConfigRangeDao;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private CountryService countryService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AttendanceDeptService attendanceDeptService;
    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private AttendancePermissionService attendancePermissionService;

    @Override
    public PaginationResult<PunchClassConfigVO> list(PunchClassConfigListQuery query) {
        PunchClassConfigQuery punchClassConfigQuery = PunchClassConfigApiMapstruct.INSTANCE.toQuery(query);
        // 更改为常驻国权限
        List<String> countryAuthList = attendancePermissionService.filterUserCountryAuth(punchClassConfigQuery.getCountry(), null);
        log.info("班次列表当前用户:{}，有权限的国家列表:{}", RequestInfoHolder.getUserCode(), countryAuthList);
        if (CollectionUtils.isEmpty(countryAuthList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        punchClassConfigQuery.setCountryList(countryAuthList);

        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            Set<Long> classIds = punchClassConfigManage.selectLatestClassIdListByUserIds(query.getUserIdList(), query.getClassNature());
            if (CollectionUtils.isEmpty(classIds)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            punchClassConfigQuery.setIds(classIds);
        }

        PageInfo<PunchClassConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> punchClassConfigDao.pageQuery(punchClassConfigQuery));

        List<PunchClassConfigDO> punchClassConfigDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<PunchClassConfigVO> result = new ArrayList<>(punchClassConfigDOList.size());
        List<Long> classIds = punchClassConfigDOList.stream().map(PunchClassConfigDO::getId).collect(Collectors.toList());
        Map<Long, PunchClassConfigDTO> punchClassConfigMap = punchClassConfigManage.selectByIds(classIds);

        List<String> countryList = punchClassConfigDOList.stream().map(PunchClassConfigDO::getCountry).distinct().collect(Collectors.toList());
        Map<String, Date> countryDateMap = getDateMapByCountryTimeZone(countryList);

        for (PunchClassConfigDO punchClassConfigDO : punchClassConfigDOList) {
            PunchClassConfigVO classConfigVO = new PunchClassConfigVO();
            result.add(classConfigVO);
            classConfigVO.setId(punchClassConfigDO.getId());
            classConfigVO.setClassName(punchClassConfigDO.getClassName());
            classConfigVO.setClassNature(punchClassConfigDO.getClassNature());
            classConfigVO.setCountry(punchClassConfigDO.getCountry());
            classConfigVO.setStatus(punchClassConfigDO.getStatus());
            classConfigVO.setCreateDate(punchClassConfigDO.getCreateDate());
            classConfigVO.setCreateUserName(punchClassConfigDO.getCreateUserName());
            classConfigVO.setLastUpdDate(punchClassConfigDO.getLastUpdDate());
            classConfigVO.setLastUpdUserName(punchClassConfigDO.getLastUpdUserName());
            Date currentDate = countryDateMap.getOrDefault(punchClassConfigDO.getCountry(), new Date());
            Long dayId = Long.valueOf(DateUtil.format(currentDate, DateFormatterUtil.FORMAT_YYYYMMDD));
            List<Long> userIds = userShiftConfigManage.getUserIdsByPunchClassIdAndDayId(punchClassConfigDO.getId(), dayId);
            classConfigVO.setEmployeeSchedulingCount(userIds.size());

            PunchClassConfigDTO punchClassConfigDTO = punchClassConfigMap.get(punchClassConfigDO.getId());

            //适用范围应用员工数
            classConfigVO.setEmployeeCount(calculateClassRangeUserCount(punchClassConfigDTO));

            //班次适用范围
            classConfigVO.setRangeRecords(convertClassRangeList(punchClassConfigDTO));
        }

        return PageUtil.getPageResult(result, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public PaginationResult<PunchClassConfigExportVO> export(PunchClassConfigListQuery query) {
        PunchClassConfigQuery punchClassConfigQuery = PunchClassConfigApiMapstruct.INSTANCE.toQuery(query);
        // 更改为常驻国权限
        List<String> countryAuthList = attendancePermissionService.filterUserCountryAuth(punchClassConfigQuery.getCountry(), null);
        log.info("班次列表当前用户:{}，有权限的国家列表:{}", RequestInfoHolder.getUserCode(), countryAuthList);
        if (CollectionUtils.isEmpty(countryAuthList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        punchClassConfigQuery.setCountryList(countryAuthList);
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            Set<Long> classIds = punchClassConfigManage.selectLatestClassIdListByUserIds(query.getUserIdList(), query.getClassNature());
            if (CollectionUtils.isEmpty(classIds)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            punchClassConfigQuery.setIds(classIds);
        }
        PageInfo<PunchClassConfigExportDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> punchClassConfigDao.export(punchClassConfigQuery));

        List<PunchClassConfigExportDTO> punchClassConfigExportList = pageInfo.getList();
        if (CollectionUtils.isEmpty(punchClassConfigExportList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<PunchClassConfigExportVO> result = new ArrayList<>(punchClassConfigExportList.size());
        List<Long> classIds = punchClassConfigExportList.stream().map(PunchClassConfigExportDTO::getId).collect(Collectors.toList());
        Map<Long, PunchClassConfigDTO> punchClassConfigMap = punchClassConfigManage.selectByIds(classIds);

        for (PunchClassConfigExportDTO exportDTO : punchClassConfigExportList) {
            PunchClassConfigExportVO exportVO = new PunchClassConfigExportVO();
            result.add(exportVO);
            exportVO.setStatus(exportDTO.getStatus());
            exportVO.setClassName(exportDTO.getClassName());
            exportVO.setClassType(exportDTO.getClassType());
            exportVO.setCountry(exportDTO.getCountry());
            exportVO.setCreateDateStr(DateHelper.formatYYYYMMDDHHMMSS(exportDTO.getCreateDate()));
            exportVO.setCreateUserName(exportDTO.getCreateUserName());
            exportVO.setLastUpdDateStr(DateHelper.formatYYYYMMDDHHMMSS(exportDTO.getLastUpdDate()));
            exportVO.setLastUpdUserName(exportDTO.getLastUpdUserName());
            exportVO.setTotalLegalWorkingHours(exportDTO.getTotalLegalWorkingHours());
            exportVO.setTotalAttendanceHours(exportDTO.getTotalAttendanceHours());
            exportVO.setSortNo(exportDTO.getSortNo());
            exportVO.setPunchInTime(DateHelper.formatHHMMSS(exportDTO.getPunchInTime()));
            exportVO.setPunchOutTime(DateHelper.formatHHMMSS(exportDTO.getPunchOutTime()));
            exportVO.setEarliestPunchInTime(DateHelper.formatHHMMSS(exportDTO.getEarliestPunchInTime()));
            exportVO.setLatestPunchInTime(DateHelper.formatHHMMSS(exportDTO.getLatestPunchInTime()));
            exportVO.setLatestPunchOutTime(DateHelper.formatHHMMSS(exportDTO.getLatestPunchOutTime()));
            exportVO.setRestStartTime(DateHelper.formatHHMMSS(exportDTO.getRestStartTime()));
            exportVO.setRestEndTime(DateHelper.formatHHMMSS(exportDTO.getRestEndTime()));
            exportVO.setElasticTime(exportDTO.getElasticTime());
            exportVO.setAttendanceHours(exportDTO.getAttendanceHours());
            exportVO.setLegalWorkingHours(exportDTO.getLegalWorkingHours());

            PunchClassConfigDTO punchClassConfigDTO = punchClassConfigMap.get(exportDTO.getId());
            if (punchClassConfigDTO.countryLevel()) {
                continue;
            }
            if (StringUtils.isNotEmpty(punchClassConfigDTO.getDeptIds())) {
                List<Long> deptIds = punchClassConfigDTO.convertDeptList();
                List<AttendanceDept> attendanceDeptList = attendanceDeptService.listByDeptIds(deptIds);
                if (Locale.CHINA.equals(UserEvnHolder.getLocal())) {
                    exportVO.setDeptStr(attendanceDeptList.stream().map(AttendanceDept::getDeptNameCn).distinct().collect(Collectors.toList()).toString());
                } else {
                    exportVO.setDeptStr(attendanceDeptList.stream().map(AttendanceDept::getDeptNameEn).distinct().collect(Collectors.toList()).toString());
                }
            }

            if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassConfigRangeList())) {
                continue;
            }

            List<Long> userIds = punchClassConfigDTO.getClassConfigRangeList().stream()
                    .filter(range -> Objects.equals(BusinessConstant.Y, range.getIsLatest()) && Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                    .map(PunchClassConfigRangeDTO::getBizId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userIds)) {
                continue;
            }
            List<String> userNameList = userInfoDao.getByUserIds(userIds).stream().map(UserInfoDO::getUserName).distinct().collect(Collectors.toList());
            exportVO.setUserStr(userNameList.toString());
        }

        // 添加导出的操作日志
        logRecordService.recordOperation(new PunchClassConfigDO(), LogRecordOptions.builder()
                .operationType(OperationTypeEnum.PUNCH_CLASS_CONFIG_EXPORT.getCode())
                .remark(OperationTypeEnum.PUNCH_CLASS_CONFIG_EXPORT.getDesc())
                .build());

        return PageUtil.getPageResult(result, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    @Override
    public PunchClassConfigDetailVO detail(PunchClassConfigDetailQuery query) {
        PunchClassConfigDTO punchClassConfigDTO = punchClassConfigManage.selectById(query.getId());
        if (Objects.isNull(punchClassConfigDTO)) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc()));
        }

        PunchClassConfigDetailVO result = PunchClassConfigApiMapstruct.INSTANCE.toDetailVO(punchClassConfigDTO);

        if (Objects.nonNull(punchClassConfigDTO.getClassType())) {
            result.setClassType(punchClassConfigDTO.getClassType().toString());
        }

        if (punchClassConfigDTO.countryLevel()) {
            return result;
        }

        if (StringUtils.isNotEmpty(punchClassConfigDTO.getDeptIds())) {
            result.setDeptIds(punchClassConfigDTO.convertDeptList());
            Map<Long, AttendanceDept> attendanceDeptMap = attendanceDeptService.listByDeptIds(punchClassConfigDTO.convertDeptList())
                    .stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
            List<PunchClassConfigDetailVO.PunchClassConfigApplyRangeVO> applyDeptList = new ArrayList<>();
            for (Long deptId : punchClassConfigDTO.convertDeptList()) {
                AttendanceDept attendanceDept = attendanceDeptMap.get(deptId);
                if (Objects.isNull(attendanceDept)) {
                    continue;
                }
                PunchClassConfigDetailVO.PunchClassConfigApplyRangeVO applyRangeVO = new PunchClassConfigDetailVO.PunchClassConfigApplyRangeVO();
                applyRangeVO.setBizId(deptId);
                String deptName;
                if (RequestInfoHolder.isChinese()) {
                    deptName = attendanceDept.getDeptNameCn();
                } else {
                    deptName = attendanceDept.getDeptNameEn();
                }
                applyRangeVO.setBizNameByLang(deptName);
                applyDeptList.add(applyRangeVO);
            }
            result.setApplyDeptList(applyDeptList);
        }

        if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassConfigRangeList())) {
            return result;
        }

        List<Long> userIds = punchClassConfigDTO.getClassConfigRangeList()
                .stream()
                .filter(range -> Objects.equals(BusinessConstant.Y, range.getIsLatest())
                        && Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return result;
        }

        Map<Long, AttendanceUser> attendanceUserMap = attendanceUserService.listUsersByIds(userIds)
                .stream().collect(Collectors.toMap(AttendanceUser::getId, Function.identity()));

        List<PunchClassConfigDetailVO.PunchClassConfigApplyRangeVO> applyUserList = new ArrayList<>();
        for (Long userId : userIds) {
            AttendanceUser attendanceUser = attendanceUserMap.get(userId);
            if (Objects.isNull(attendanceUser)) {
                continue;
            }
            PunchClassConfigDetailVO.PunchClassConfigApplyRangeVO applyRangeVO = new PunchClassConfigDetailVO.PunchClassConfigApplyRangeVO();
            applyRangeVO.setBizId(userId);
            String userName;
            if (RequestInfoHolder.isChinese()) {
                userName = attendanceUser.getUserName();
            } else {
                userName = attendanceUser.getUserNameEn();
            }
            applyRangeVO.setBizNameByLang(userName);
            applyUserList.add(applyRangeVO);
        }
        result.setApplyUserList(applyUserList);
        result.setUserIdList(userIds);
        return result;
    }

    @Override
    public PaginationResult<RuleConfigUserInfoDTO> pagePunchClassConfigUserList(PunchClassConfigUserQuery query) {
        if (Objects.isNull(query.getClassId())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        PunchClassConfigDTO punchClassConfigDTO = punchClassConfigManage.selectById(query.getClassId());

        if (Objects.isNull(punchClassConfigDTO) || Objects.equals(StatusEnum.DISABLED.getCode(), punchClassConfigDTO.getStatus())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassConfigRangeList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        if (punchClassConfigDTO.countryLevel()) {
            RuleRangeUserQuery ruleRangeUserQuery = RuleRangeUserQuery.builder()
                    .codeOrNameLike(query.getUserCodeOrName())
                    .classNature(punchClassConfigDTO.getClassNature())
                    .classId(query.getClassId())
                    .rangeTypeList(Lists.newArrayList(RuleRangeTypeEnum.COUNTRY.getCode()))
                    .build();
            PageInfo<UserInfoDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount())
                    .doSelectPageInfo(() -> punchClassConfigRangeDao.listClassRangeApplyUser(ruleRangeUserQuery));
            List<UserInfoDO> userList = pageInfo.getList();

            Map<Long, List<PunchClassConfigRangeDTO>> userRangeMap = punchClassConfigDTO.getClassConfigRangeList().stream()
                    .filter(range -> Objects.equals(RuleRangeTypeEnum.COUNTRY.getCode(), range.getRangeType())
                            && Objects.equals(BusinessConstant.Y, range.getIsLatest()))
                    .collect(Collectors.groupingBy(PunchClassConfigRangeDTO::getBizId));

            List<RuleConfigUserInfoDTO> ruleConfigUserInfoList = transferRuleConfigUserDTO(userList, userRangeMap);
            return PageUtil.getPageResult(ruleConfigUserInfoList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
        }

        List<PunchClassConfigRangeDTO> classConfigRangeList = punchClassConfigDTO.getClassConfigRangeList();
        Map<Long, List<PunchClassConfigRangeDTO>> userRangeMap = classConfigRangeList.stream()
                .filter(range -> Objects.equals(BusinessConstant.Y, range.getIsLatest()))
                .collect(Collectors.groupingBy(PunchClassConfigRangeDTO::getBizId));

        if (MapUtils.isEmpty(userRangeMap)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<Long> userIds = new ArrayList<>(userRangeMap.keySet());
        //查询员工
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .userIds(userIds)
                .codeOrNameLike(query.getUserCodeOrName())
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .build();
        PageInfo<UserInfoDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount())
                .doSelectPageInfo(() -> userInfoDao.userList(userDaoQuery));
        List<UserInfoDO> userList = pageInfo.getList();
        List<RuleConfigUserInfoDTO> ruleConfigUserInfoDTOS = transferRuleConfigUserDTO(userList, userRangeMap);
        return PageUtil.getPageResult(ruleConfigUserInfoDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    private List<RuleConfigUserInfoDTO> transferRuleConfigUserDTO(List<UserInfoDO> userList,
                                                                  Map<Long, List<PunchClassConfigRangeDTO>> userRangeMap) {
        List<Long> deptIds = userList.stream()
                .map(UserInfoDO::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = attendanceDeptService.listByDeptIds(deptIds)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, item -> item, (oldVal, newVal) -> oldVal));

        List<RuleConfigUserInfoDTO> ruleConfigUserInfoList = PunchConfigMapstruct.INSTANCE.toRuleConfigUserInfoDTO(userList);

        for (RuleConfigUserInfoDTO ruleConfigUserInfoDTO : ruleConfigUserInfoList) {
            AttendanceDept attendanceDept = deptMap.get(ruleConfigUserInfoDTO.getDeptId());
            if (Objects.nonNull(attendanceDept)) {
                ruleConfigUserInfoDTO.setCountry(attendanceDept.getCountry());
                ruleConfigUserInfoDTO.setDeptCode(attendanceDept.getDeptCode());
                ruleConfigUserInfoDTO.setDeptName(attendanceDept.getLocalizeName());
            }

            if (Objects.nonNull(userRangeMap)) {
                List<PunchClassConfigRangeDTO> userRangeList = userRangeMap.get(ruleConfigUserInfoDTO.getId());
                if (CollectionUtils.isNotEmpty(userRangeList)) {
                    ruleConfigUserInfoDTO.setCreateDate(userRangeList.get(0).getCreateDate());
                }
            }
        }
        return ruleConfigUserInfoList;
    }

    @Override
    public List<PunchClassConfigSelectDTO> selectLatestByClassNature(String classNature) {
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestByClassNature(classNature);
        return punchClassConfigDOList.stream().map(config -> PunchClassConfigSelectDTO.builder().id(config.getId()).className(config.getClassName()).build()).collect(Collectors.toList());
    }

    @Override
    public List<UserClassConfigDTO> selectUserClassConfigList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        List<UserInfoDO> userInfoDOList = userInfoDao.getByUserIds(userIdList);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            return Collections.emptyList();
        }

        Map<String, List<Long>> groupByClassNature = userInfoDOList.stream()
                .filter(user -> StringUtils.isNotBlank(user.getClassNature()))
                .collect(Collectors.groupingBy(UserInfoDO::getClassNature, Collectors.mapping(UserInfoDO::getId, Collectors.toList())));

        //固定班次人员
        List<Long> fixedClassUserIds = groupByClassNature.get(ClassNatureEnum.FIXED_CLASS.name());
        Map<Long, PunchClassConfigDO> fixedClassUserMap = punchClassConfigManage.selectTopPriorityByUserIds(fixedClassUserIds);

        //多班次人员
        List<Long> multipleClassUserIds = groupByClassNature.get(ClassNatureEnum.MULTIPLE_CLASS.name());
        Map<Long, List<PunchClassConfigDO>> multipleClassUserMap = punchClassConfigManage.selectUserMultipleClassConfigList(multipleClassUserIds);

        Set<Long> classIds = new HashSet<>();
        classIds.addAll(fixedClassUserMap.keySet());
        classIds.addAll(multipleClassUserMap.keySet());
        Map<Long, List<PunchClassItemConfigDO>> itemGroupByClassId = punchClassItemConfigDao.selectByClassIds(new ArrayList<>(classIds))
                .stream().collect(Collectors.groupingBy(PunchClassItemConfigDO::getPunchClassId));

        List<UserClassConfigDTO> result = new ArrayList<>();
        for (Map.Entry<Long, PunchClassConfigDO> entry : fixedClassUserMap.entrySet()) {
            PunchClassConfigDO punchClassConfigDO = entry.getValue();
            UserClassConfigDTO userClassConfigDTO = new UserClassConfigDTO();
            userClassConfigDTO.setUserId(entry.getKey());
            userClassConfigDTO.setClassNature(ClassNatureEnum.FIXED_CLASS.name());
            List<PunchClassItemConfigDO> punchClassItemConfigDOList = itemGroupByClassId.get(punchClassConfigDO.getId());
            List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList);
            PunchClassConfigSelectDTO classConfigSelectDTO = PunchClassConfigSelectDTO.builder()
                    .id(punchClassConfigDO.getId())
                    .className(punchClassConfigDO.getClassName())
                    .classItemConfigDTOList(punchClassItemConfigDTOList)
                    .build();
            userClassConfigDTO.setClassConfigSelectList(Collections.singletonList(classConfigSelectDTO));
            result.add(userClassConfigDTO);
        }

        for (Map.Entry<Long, List<PunchClassConfigDO>> entry : multipleClassUserMap.entrySet()) {
            List<PunchClassConfigDO> punchClassConfigList = entry.getValue();
            UserClassConfigDTO userClassConfigDTO = new UserClassConfigDTO();
            userClassConfigDTO.setUserId(entry.getKey());
            userClassConfigDTO.setClassNature(ClassNatureEnum.MULTIPLE_CLASS.name());
            List<PunchClassConfigSelectDTO> classConfigSelectList = punchClassConfigList.stream()
                    .map(config -> {
                        List<PunchClassItemConfigDO> punchClassItemConfigDOList = itemGroupByClassId.get(config.getId());
                        List<PunchClassItemConfigDTO> punchClassItemConfigDTOList = PunchClassItemConfigMapstruct.INSTANCE.toPunchClassItemConfigDTOList(punchClassItemConfigDOList);
                        return PunchClassConfigSelectDTO.builder()
                                .id(config.getId())
                                .className(config.getClassName())
                                .classItemConfigDTOList(punchClassItemConfigDTOList)
                                .build();
                    })
                    .collect(Collectors.toList());
            userClassConfigDTO.setClassConfigSelectList(classConfigSelectList);
            result.add(userClassConfigDTO);
        }
        return result;
    }

    @Override
    public Set<Long> selectAllUserIds(String classNature, List<Long> classIds) {
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectLatestAndActiveByIds(classIds);
        if (CollectionUtils.isEmpty(punchClassConfigDOList)) {
            return Collections.emptySet();
        }

        List<Long> classIdList = punchClassConfigDOList.stream().map(PunchClassConfigDO::getId).collect(Collectors.toList());
        return punchClassConfigRangeDao.selectLatestAndActiveByRuleConfigIds(classIdList).stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());
    }

    @Override
    public Set<Long> selectMatchAllClassIdsUserIds(String classNature, List<Long> classIds) {
        if (Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), classNature)) {
            return Collections.emptySet();
        }
        // key: classId value: List<UserId>
        Map<Long, List<Long>> userIdMap = punchClassConfigRangeDao.selectLatestAndActiveByRuleConfigIds(classIds)
                .stream().collect(Collectors.groupingBy(PunchClassConfigRangeDO::getRuleConfigId, Collectors.mapping(PunchClassConfigRangeDO::getBizId, Collectors.toList())));
        if (MapUtils.isEmpty(userIdMap)) {
            return Collections.emptySet();
        }

        // 获取第一个列表作为初始交集
        Set<Long> result = new HashSet<>(userIdMap.values().iterator().next());

        // 遍历剩余列表，逐步缩小交集
        for (List<Long> userIdList : userIdMap.values()) {
            if (CollectionUtils.isEmpty(userIdList)) {
                return Collections.emptySet();
            }
            result.retainAll(userIdList);
            if (result.isEmpty()) {
                break;
            }
        }
        return result;
    }

    @Override
    public void transferItemConfigTimeFormat(List<PunchClassItemConfigDO> itemConfigList, Long dayId) {
        if (CollectionUtils.isEmpty(itemConfigList)) {
            return;
        }
        for (PunchClassItemConfigDO itemConfigDO : itemConfigList) {
            DayPunchTimeDTO dayPunchTimeDTO = getUserPunchClassItemDayTime(dayId, itemConfigDO.getId(), itemConfigList);
            // 如果固定/班次时间为空，则处理自由排班情况
            if (Objects.isNull(dayPunchTimeDTO)) {
                dayPunchTimeDTO = getUserFreeWorkPunchClassItemDayTime(dayId, itemConfigDO);
                if (Objects.isNull(dayPunchTimeDTO)) {
                    log.info("transferItemConfigTimeFormat | itemConfigDO:{}, dayId:{}, 时段时间格式转换为年月日+时分秒设置异常", JSON.toJSON(itemConfigDO), dayId);
                    return;
                }
                // 自由排班只需更新最早最晚打卡时间
                itemConfigDO.setEarliestPunchInTime(dayPunchTimeDTO.getDayPunchStartTime());
                itemConfigDO.setLatestPunchOutTime(dayPunchTimeDTO.getDayPunchEndTime());
                continue;
            }

            // 验证时间有效性
            if (dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                log.info("transferItemConfigTimeFormat | itemConfigDO:{},dayPunchTimeDTO={} dayId:{}, 时段时间格式转换为年月日+时分秒设置异常",
                        JSON.toJSON(itemConfigDO), dayPunchTimeDTO, dayId);
                return;
            }
            //最早上班打卡时间
            Date earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();

            // 上班时间
            Date punchInTime = PunchTimeCalculator.calculateFullDateTime(
                    earliestPunchInTime,
                    itemConfigDO.getPunchInTime(),
                    itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime()) ? 1 : 0
            );

            // 最晚上班打卡时间
            Date latestPunchInTime = PunchTimeCalculator.calculateFullDateTime(
                    punchInTime,
                    itemConfigDO.getLatestPunchInTime(),
                    itemConfigDO.getLatestPunchInTime().before(itemConfigDO.getPunchInTime()) ? 1 : 0
            );

            // 休息时间处理
            Date restStartTime = null;
            Date restEndTime = null;
            if (itemConfigDO.getRestStartTime() != null) {
                restStartTime = PunchTimeCalculator.calculateFullDateTime(
                        punchInTime,
                        itemConfigDO.getRestStartTime(),
                        itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime()) ? 1 : 0
                );

                restEndTime = PunchTimeCalculator.calculateFullDateTime(
                        restStartTime,
                        itemConfigDO.getRestEndTime(),
                        itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime()) ? 1 : 0
                );
            }

            //最晚下班打卡时间
            Date latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();

            // 下班时间
            Date punchOutTime = PunchTimeCalculator.calculateFullDateTime(
                    latestPunchOutTime,
                    itemConfigDO.getPunchOutTime(),
                    itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime()) ? -1 : 0
            );

            /// 更新对象属性
            itemConfigDO.setEarliestPunchInTime(earliestPunchInTime);
            itemConfigDO.setPunchInTime(punchInTime);
            itemConfigDO.setLatestPunchInTime(latestPunchInTime);
            itemConfigDO.setRestStartTime(restStartTime);
            itemConfigDO.setRestEndTime(restEndTime);
            itemConfigDO.setPunchOutTime(punchOutTime);
            itemConfigDO.setLatestPunchOutTime(latestPunchOutTime);
        }
    }

    /**
     * 班次适用范围转换
     */
    private List<PunchClassConfigRangeVO> convertClassRangeList(PunchClassConfigDTO punchClassConfigDTO) {
        if (Objects.isNull(punchClassConfigDTO)) {
            return Collections.emptyList();
        }

        List<PunchClassConfigRangeVO> rangeRecords = new ArrayList<>();

        //国家级适用范围
        if (punchClassConfigDTO.countryLevel()) {
            PunchClassConfigRangeVO classConfigRangeVO = new PunchClassConfigRangeVO();
            classConfigRangeVO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
            classConfigRangeVO.setBizName(punchClassConfigDTO.getCountry());
            rangeRecords.add(classConfigRangeVO);
            return rangeRecords;
        }

        //部门级适用范围
        if (StringUtils.isNotEmpty(punchClassConfigDTO.getDeptIds())) {
            List<Long> deptIds = punchClassConfigDTO.convertDeptList();

            List<AttendanceDept> attendanceDeptList = attendanceDeptService.listByDeptIds(deptIds);
            for (AttendanceDept attendanceDept : attendanceDeptList) {
                PunchClassConfigRangeVO classConfigRangeVO = new PunchClassConfigRangeVO();
                classConfigRangeVO.setRangeType(RuleRangeTypeEnum.DEPT.getCode());
                classConfigRangeVO.setBizId(attendanceDept.getId());
                if (Locale.CHINA.equals(UserEvnHolder.getLocal())) {
                    classConfigRangeVO.setBizName(attendanceDept.getDeptNameCn());
                } else {
                    classConfigRangeVO.setBizName(attendanceDept.getDeptNameEn());
                }
                rangeRecords.add(classConfigRangeVO);
            }
        }

        if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassConfigRangeList())) {
            return rangeRecords;
        }

        //用户级适用范围
        List<Long> userIds = punchClassConfigDTO.getClassConfigRangeList()
                .stream()
                .filter(range -> Objects.equals(BusinessConstant.Y, range.getIsLatest()) && Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType()))
                .map(PunchClassConfigRangeDTO::getBizId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            return rangeRecords;
        }

        List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserIdsAndClassNature(userIds, punchClassConfigDTO.getClassNature());
        for (UserInfoDO userInfoDO : userInfoDOList) {
            PunchClassConfigRangeVO classConfigRangeVO = new PunchClassConfigRangeVO();
            classConfigRangeVO.setRangeType(RuleRangeTypeEnum.USER.getCode());
            classConfigRangeVO.setBizId(userInfoDO.getId());
            classConfigRangeVO.setBizName(userInfoDO.getUserName());
            rangeRecords.add(classConfigRangeVO);
        }
        return rangeRecords;
    }

    private Integer calculateClassRangeUserCount(PunchClassConfigDTO punchClassConfigDTO) {
        if (Objects.isNull(punchClassConfigDTO) || Objects.equals(StatusEnum.DISABLED.getCode(), punchClassConfigDTO.getStatus())) {
            return 0;
        }
        if (CollectionUtils.isEmpty(punchClassConfigDTO.getClassConfigRangeList())) {
            return 0;
        }
        return (int) punchClassConfigDTO.getClassConfigRangeList().stream().filter(range -> Objects.equals(BusinessConstant.Y, range.getIsLatest())).count();
    }

    /**
     * 根据国家转换时区获取对应的当地时间
     *
     * @param countryList 国家三字码列表
     * @return Map<String, Date>
     */
    private Map<String, Date> getDateMapByCountryTimeZone(List<String> countryList) {
        Map<String, Date> result = new HashMap<>();
        if (CollectionUtils.isEmpty(countryList)) {
            return result;
        }
        Date date = new Date();
        for (String country : countryList) {
            CountryDTO countryDTO = countryService.queryCountry(country);
            Date currentDate = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), date);
            result.put(country, currentDate);
        }
        return result;
    }


    @Override
    public DayPunchTimeDTO getUserPunchDayTime(Long dayId, List<PunchClassItemConfigDO> itemConfigDOList) {
        if (dayId == null || CollectionUtils.isEmpty(itemConfigDOList) ||
                itemConfigDOList.get(0).notHasValidPunchClassId()) {
            return null;
        }
        // 获取排序后的配置列表
        List<PunchClassItemConfigDO> sortedConfigs = itemConfigDOList.stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .collect(Collectors.toList());
        PunchClassItemConfigDO firstConfig = sortedConfigs.get(0);
        PunchClassItemConfigDO lastConfig = sortedConfigs.get(sortedConfigs.size() - 1);
        // 检查配置是否有效（是否包含必要的时间字段）
        if (firstConfig.notHasValidTimes() || lastConfig.notHasValidTimes()) {
            return null;
        }
        //获取用户考勤日的开始截止时间
        DayPunchTimeDTO dayPunchTimeDTO = new DayPunchTimeDTO();
        Date dayDate = DateHelper.transferDayIdToDate(dayId);

        // 判断是否跨天并计算时间
        boolean isPunchInAcrossDay = firstConfig.isPunchInAcrossDay();

        // 设置当日最早打卡时间
        dayPunchTimeDTO.setDayPunchStartTime(
                firstConfig.calculateEarliestPunchInDateTime(dayDate, isPunchInAcrossDay));

        // 设置当日最晚打卡时间（如果上班不跨天，则下班通常跨天）
        dayPunchTimeDTO.setDayPunchEndTime(
                lastConfig.calculateLatestPunchOutDateTime(dayDate, isPunchInAcrossDay));

        dayPunchTimeDTO.logTimePeriod("getUserPunchDayTime");
        return dayPunchTimeDTO;
    }

    @Override
    public DayPunchTimeDTO getUserPunchClassItemDayTime(Long dayId, Long classItemId,
                                                        List<PunchClassItemConfigDO> itemConfigDOList) {
        if (dayId == null || classItemId == null ||
                CollectionUtils.isEmpty(itemConfigDOList) ||
                itemConfigDOList.get(0).notHasValidPunchClassId()) {
            return null;
        }

        //2.查找并验证目标班次配置
        PunchClassItemConfigDO itemConfigDO = itemConfigDOList.stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .filter(item -> item.getId().equals(classItemId))
                .findFirst()
                .orElse(null);

        if (itemConfigDO == null || itemConfigDO.notHasValidTimes()) {
            return null;
        }

        // 3. 获取整天的排班时间
        DayPunchTimeDTO dayPunchTimeDTO = getUserPunchDayTime(dayId, itemConfigDOList);
        if (dayPunchTimeDTO == null) {
            return null;
        }

        // 4. 根据班次是否跨天，计算打卡时间范围
        DayPunchTimeDTO result;

        if (itemConfigDO.isTimeCrossingDay()) {
            // 跨天情况
            result = DayPunchTimeDTO.create(
                    PunchTimeCalculator.getDateFromDateAndTime(dayPunchTimeDTO.getDayPunchStartTime(), itemConfigDO.getEarliestPunchInTime()),
                    PunchTimeCalculator.getDateFromDateAndTime(dayPunchTimeDTO.getDayPunchEndTime(), itemConfigDO.getLatestPunchOutTime())
            );
        } else {
            // 非跨天情况，需确定是起始日还是结束日
            Date dayPunchStartConvertTime = PunchTimeCalculator.convertToStandardTimeFormat(dayPunchTimeDTO.getDayPunchStartTime());

            if (itemConfigDO.getEarliestPunchInTime().compareTo(dayPunchStartConvertTime) > -1) {
                // 属于起始那天
                result = DayPunchTimeDTO.calculatePunchTimeFrame(dayPunchTimeDTO.getDayPunchStartTime(), itemConfigDO);
            } else {
                // 属于结束那天
                result = DayPunchTimeDTO.calculatePunchTimeFrame(dayPunchTimeDTO.getDayPunchEndTime(), itemConfigDO);
            }
        }

        // 5. 记录日志
        if (null != result) {
            result.logTimePeriod("getUserPunchClassItemDayTime");
        }
        return result;
    }

    @Override
    public DayNormalPunchTimeDTO getUserPunchClassNormalItemDayTime(Long dayId, Long classItemId, List<PunchClassItemConfigDO> itemConfigDOList) {
        // 输入参数日志记录
        log.info("getUserPunchClassNormalItemDayTime - 开始处理 dayId:{}, classItemId:{}, itemConfigSize:{}",
                dayId, classItemId, itemConfigDOList != null ? itemConfigDOList.size() : 0);
        if (dayId == null || classItemId == null ||
                CollectionUtils.isEmpty(itemConfigDOList) ||
                itemConfigDOList.get(0).notHasValidPunchClassId()) {
            log.info("getUserPunchClassNormalItemDayTime - 参数无效: dayId:{}, classItemId:{}, 班次配置列表为空或无效",
                    dayId, classItemId);
            return null;
        }
        // 排序班次配置
        itemConfigDOList = itemConfigDOList.stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .collect(Collectors.toList());

        // 查找目标班次配置
        PunchClassItemConfigDO targetItemConfig = itemConfigDOList.stream()
                .filter(item -> item.matchesId(classItemId))
                .findFirst()
                .orElse(null);
        if (targetItemConfig == null) {
            log.info("getUserPunchClassNormalItemDayTime - 未找到目标班次配置，classItemId: {}", classItemId);
            return null;
        }
        // 获取整天的排班起始结束时间
        DayPunchTimeDTO dayPunchTimeDTO = getUserPunchDayTime(dayId, itemConfigDOList);
        if (dayPunchTimeDTO == null) {
            return null;
        }
        log.info("getUserPunchClassNormalItemDayTime - 获取整天排班时间成功，开始时间: {}, 结束时间: {}",
                dayPunchTimeDTO.getDayPunchStartTime(), dayPunchTimeDTO.getDayPunchEndTime());
        // 使用班次配置对象计算具体上下班时间
        return DayNormalPunchTimeDTO.calculateNormalPunchTime(dayPunchTimeDTO, targetItemConfig);
    }

    @Override
    public DayPunchTimeDTO getUserFreeWorkPunchClassItemDayTime(Long dayId, PunchClassItemConfigDO itemConfigDO) {
        if (Objects.isNull(dayId)) {
            return null;
        }
        String earliestPunchInTimeString = DateHelper.formatHHMMSS(itemConfigDO.getEarliestPunchInTime());
        String latestPunchOutTimeString = DateHelper.formatHHMMSS(itemConfigDO.getLatestPunchOutTime());
        Date date = DateHelper.transferDayIdToDate(dayId);
        String earliestPunchInTimeDayString = DateHelper.formatYYYYMMDD(date);
        String latestPunchOutTimeDayString = DateHelper.formatYYYYMMDD(DateUtil.offsetDay(date, 1));

        Date dayPunchStartTime = PunchTimeCalculator.getDateFromDateAndTimeStr(
                earliestPunchInTimeDayString,
                earliestPunchInTimeString
        );
        Date dayPunchEndTime = PunchTimeCalculator.getDateFromDateAndTimeStr(
                latestPunchOutTimeDayString,
                latestPunchOutTimeString
        );
        log.info("getUserFreeWorkPunchClassItemDayTime - 计算结果: 开始时间={}, 结束时间={}",
                dayPunchStartTime, dayPunchEndTime);
        return DayPunchTimeDTO.create(dayPunchStartTime, dayPunchEndTime);
    }
}
