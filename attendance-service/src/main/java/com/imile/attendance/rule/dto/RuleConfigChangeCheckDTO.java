package com.imile.attendance.rule.dto;

import lombok.Data;
import java.util.Collections;
import java.util.List;

/**
 * 规则配置变更检查结果
 * 用于检查规则配置变更时的合法性，并返回相关的检查结果
 * 主要用于处理规则适用范围的重复性检查，确保同一范围不会被多个规则同时覆盖
 *
 * <AUTHOR> chen
 * @since 2025/4/22
 */
@Data
public class RuleConfigChangeCheckDTO {

    /**
     * 规则配置检查是否成功
     * true: 检查通过，规则配置可以应用
     * false: 检查不通过，存在冲突需要处理
     */
    private Boolean success = Boolean.TRUE;

    /**
     * 规则适用范围重复信息列表
     * 当success为false时，此列表包含所有发现的重复项
     * 为空表示没有重复项
     */
    private List<RangeDuplicateInfo> rangeDuplicateInfoList;

    /**
     * 规则范围重复信息
     * 用于记录具体的重复项详情，包括重复的范围信息和已存在的规则名称
     */
    @Data
    public static class RangeDuplicateInfo {

        /**
         * 重复的范围信息
         * 可能是以下几种类型：
         * 1. 国家名称 - 当国家级别的规则发生重复
         * 2. 部门名称 - 当部门级别的规则发生重复
         * 3. 人员名称 - 当人员级别的规则发生重复
         */
        private String repeatName;

        /**
         * 已存在的规则名称
         * 记录与当前配置发生冲突的现有规则名称
         */
        private String ruleName;

        /**
         * 创建重复信息对象
         *
         * @param repeatName 重复的范围信息（国家/部门/人员名称）
         * @param ruleName 已存在的规则名称
         * @return 重复信息对象
         */
        public static RangeDuplicateInfo of(String repeatName, String ruleName) {
            RangeDuplicateInfo rangeDuplicateInfo = new RangeDuplicateInfo();
            rangeDuplicateInfo.setRepeatName(repeatName);
            rangeDuplicateInfo.setRuleName(ruleName);
            return rangeDuplicateInfo;
        }
    }

    /**
     * 创建规则配置检查结果对象
     *
     * @param success 检查是否成功
     * @param rangeDuplicateInfoList 重复信息列表
     * @return 规则配置检查结果对象
     */
    public static RuleConfigChangeCheckDTO of(Boolean success, List<RangeDuplicateInfo> rangeDuplicateInfoList) {
        RuleConfigChangeCheckDTO ruleConfigChangeCheckDTO = new RuleConfigChangeCheckDTO();
        ruleConfigChangeCheckDTO.setSuccess(success);
        ruleConfigChangeCheckDTO.setRangeDuplicateInfoList(rangeDuplicateInfoList);
        return ruleConfigChangeCheckDTO;
    }

    /**
     * 创建检查成功的结果对象
     * 用于表示规则配置检查通过，没有发现任何冲突
     *
     * @return 成功的检查结果对象，不包含任何重复信息
     */
    public static RuleConfigChangeCheckDTO buildSuccess() {
        return RuleConfigChangeCheckDTO.of(
                Boolean.TRUE,
                Collections.emptyList()
        );
    }

    /**
     * 创建检查失败的结果对象
     * 用于表示规则配置检查不通过，存在冲突需要处理
     *
     * @param rangeDuplicateInfoList 发现的重复信息列表
     * @return 失败的检查结果对象，包含所有重复信息
     */
    public static RuleConfigChangeCheckDTO buildFailure(List<RangeDuplicateInfo> rangeDuplicateInfoList) {
        return RuleConfigChangeCheckDTO.of(
                Boolean.FALSE,
                rangeDuplicateInfoList
        );
    }
}