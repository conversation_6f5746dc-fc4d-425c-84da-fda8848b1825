package com.imile.attendance.rule.event.listener;


import com.imile.attendance.abnormal.UserAttendanceAbnormalTrigger;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.dto.PunchClassConfigSwitchStatusDTO;
import com.imile.attendance.rule.event.domain.PunchClassConfigSwitchStatusEvent;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PunchClassConfigSwitchStatusEventListener {
    private final PunchClassConfigManage punchClassConfigManage;
    private final AutoShiftConfigFactory autoShiftConfigFactory;
    private final CountryService countryService;
    private final UserAttendanceAbnormalTrigger userAttendanceAbnormalTrigger;

    @Async("bizTaskThreadPool")
    @EventListener
    public void onPunchClassConfigSwitchStatusEvent(PunchClassConfigSwitchStatusEvent punchClassConfigEnableEvent) {
        if (Objects.isNull(punchClassConfigEnableEvent.getData())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        PunchClassConfigSwitchStatusDTO configSwitchStatusDTO = punchClassConfigEnableEvent.getData();
        long startTime = System.currentTimeMillis();
        log.info("PunchClassConfigSwitchStatusEvent | startTime:{},classId:{},status:{}",
                startTime, configSwitchStatusDTO.getClassId(), configSwitchStatusDTO.getStatus());
        PunchClassConfigDTO punchClassConfigDTO = punchClassConfigManage.selectById(configSwitchStatusDTO.getClassId());
        if (Objects.isNull(punchClassConfigDTO)) {
            log.error("班次规则不存在");
            return;
        }

        Set<Long> userIds = configSwitchStatusDTO.getUserIds();

        CountryDTO countryDTO = countryService.queryCountry(punchClassConfigDTO.getCountry());
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        Long dayId = DateHelper.getDayId(date);

        UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
        userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.getByCode(punchClassConfigDTO.getClassNature()));
        //启用
        if (Objects.equals(StatusEnum.ACTIVE.getCode(), configSwitchStatusDTO.getStatus())) {
            if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), punchClassConfigDTO.getClassNature())) {
                log.info("多班次启用跳过处理");
                return;
            }
            UserAutoShiftParam.PunchClassAddUserParam punchClassAddUserParam = new UserAutoShiftParam.PunchClassAddUserParam();
            punchClassAddUserParam.setUserIdList(new ArrayList<>(userIds));
            punchClassAddUserParam.setTargetClassId(punchClassConfigDTO.getId());
            punchClassAddUserParam.setShiftStartDayId(dayId);
            userAutoShiftParam.setPunchClassAddUserParam(punchClassAddUserParam);
        } else {
            UserAutoShiftParam.PunchClassRemoveUserParam punchClassRemoveUserParam = new UserAutoShiftParam.PunchClassRemoveUserParam();
            List<UserAutoShiftParam.PunchClassRemoveSingleUserParam> classRemoveSingleUserParams = new ArrayList<>();

            if (Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), punchClassConfigDTO.getClassNature())) {
                Map<Long, PunchClassConfigDO> punchClassConfigIdMap = punchClassConfigManage.selectTopPriorityByUserIds(userIds);
                for (Long userId : userIds) {
                    UserAutoShiftParam.PunchClassRemoveSingleUserParam removeSingleUserParam = new UserAutoShiftParam.PunchClassRemoveSingleUserParam();
                    removeSingleUserParam.setUserId(userId);
                    PunchClassConfigDO punchClassConfigDO = punchClassConfigIdMap.get(userId);
                    if (Objects.nonNull(punchClassConfigDO)) {
                        removeSingleUserParam.setTargetClassId(punchClassConfigDO.getId());
                        if (Objects.equals(punchClassConfigDTO.getCountry(), punchClassConfigDO.getCountry())) {
                            removeSingleUserParam.setShiftStartDayId(dayId);
                        } else {
                            CountryDTO country = countryService.queryCountry(punchClassConfigDO.getCountry());
                            removeSingleUserParam.setShiftStartDayId(DateHelper.getDayId(CommonUtil.convertDateByTimeZonePlus(country.getTimeZone(), new Date())));
                        }
                    } else {
                        removeSingleUserParam.setShiftStartDayId(dayId);
                    }
                    classRemoveSingleUserParams.add(removeSingleUserParam);
                }
            } else {
                //指定班次ID进行清理
                for (Long userId : userIds) {
                    UserAutoShiftParam.PunchClassRemoveSingleUserParam removeSingleUserParam = new UserAutoShiftParam.PunchClassRemoveSingleUserParam();
                    removeSingleUserParam.setUserId(userId);
                    removeSingleUserParam.setShiftStartDayId(dayId);
                    removeSingleUserParam.setClearClassIdList(Collections.singletonList(configSwitchStatusDTO.getClassId()));
                    classRemoveSingleUserParams.add(removeSingleUserParam);
                }
                punchClassRemoveUserParam.setIsOnlyClearShift(Boolean.TRUE);
            }

            punchClassRemoveUserParam.setClassRemoveSingleUserParams(classRemoveSingleUserParams);
            userAutoShiftParam.setPunchClassRemoveUserParam(punchClassRemoveUserParam);
        }
        autoShiftConfigFactory.autoShift(userAutoShiftParam);
        log.info("PunchClassConfigSwitchStatusEvent | 耗时:{}", System.currentTimeMillis() - startTime);

        Set<Long> userIdSet = new HashSet<>();
        if (Objects.nonNull(userAutoShiftParam.getPunchClassAddUserParam())
                && CollectionUtils.isNotEmpty(userAutoShiftParam.getPunchClassAddUserParam().getUserIdList())) {
            userIdSet.addAll(userAutoShiftParam.getPunchClassAddUserParam().getUserIdList());
        }
        if (Objects.nonNull(userAutoShiftParam.getPunchClassRemoveUserParam())
                && CollectionUtils.isNotEmpty(userAutoShiftParam.getPunchClassRemoveUserParam().getClassRemoveSingleUserParams())) {
            List<UserAutoShiftParam.PunchClassRemoveSingleUserParam> classRemoveSingleUserParams = userAutoShiftParam.getPunchClassRemoveUserParam().getClassRemoveSingleUserParams();
            Set<Long> removeUserIds = classRemoveSingleUserParams.stream().map(UserAutoShiftParam.PunchClassRemoveSingleUserParam::getUserId).collect(Collectors.toSet());
            userIdSet.addAll(removeUserIds);
        }

        //排班触发当日异常计算
        userAttendanceAbnormalTrigger.userShiftAbnormalCalculateHandler(new ArrayList<>(userIdSet), dayId);
    }
}