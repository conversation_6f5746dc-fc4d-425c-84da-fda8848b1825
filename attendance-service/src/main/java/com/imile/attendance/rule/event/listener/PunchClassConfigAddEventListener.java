package com.imile.attendance.rule.event.listener;


import com.imile.attendance.abnormal.UserAttendanceAbnormalTrigger;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.rule.dto.PunchClassConfigAddAutoShiftDTO;
import com.imile.attendance.rule.event.domain.PunchClassConfigAddEvent;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PunchClassConfigAddEventListener {
    private final PunchClassConfigDao punchClassConfigDao;
    private final AutoShiftConfigFactory autoShiftConfigFactory;
    private final UserAttendanceAbnormalTrigger userAttendanceAbnormalTrigger;


    @Async("bizTaskThreadPool")
    @EventListener
    public void onPunchClassConfigAddEvent(PunchClassConfigAddEvent punchClassConfigAddEvent) {
        if (Objects.isNull(punchClassConfigAddEvent.getData())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO = punchClassConfigAddEvent.getData();
        long startTime = System.currentTimeMillis();
        log.info("PunchClassConfigAddEvent | startTime:{},punchClassConfigId:{}", startTime, classConfigAddAutoShiftDTO.getClassId());

        PunchClassConfigDO punchClassConfigDO = punchClassConfigDao.selectById(classConfigAddAutoShiftDTO.getClassId());
        if (Objects.isNull(punchClassConfigDO)) {
            log.error("onPunchClassConfigAddEvent,班次规则不存在");
            return;
        }

        Long dayId = classConfigAddAutoShiftDTO.getDayId();

        UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
        userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.FIXED_CLASS);
        UserAutoShiftParam.PunchClassAddUserParam punchClassAddUserParam = new UserAutoShiftParam.PunchClassAddUserParam();
        punchClassAddUserParam.setUserIdList(new ArrayList<>(classConfigAddAutoShiftDTO.getNewRangeUserIdList()));
        punchClassAddUserParam.setTargetClassId(classConfigAddAutoShiftDTO.getClassId());
        punchClassAddUserParam.setShiftStartDayId(dayId);
        userAutoShiftParam.setPunchClassAddUserParam(punchClassAddUserParam);
        autoShiftConfigFactory.autoShift(userAutoShiftParam);

        log.info("PunchClassConfigAddEvent | 耗时:{}", System.currentTimeMillis() - startTime);

        //排班触发当日异常计算
        userAttendanceAbnormalTrigger.userShiftAbnormalCalculateHandler(new ArrayList<>(classConfigAddAutoShiftDTO.getNewRangeUserIdList()), dayId);
    }
}
