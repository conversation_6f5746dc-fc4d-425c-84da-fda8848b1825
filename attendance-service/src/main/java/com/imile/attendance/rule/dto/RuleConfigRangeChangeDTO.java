package com.imile.attendance.rule.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 打卡规则范围变更DTO
 * 用于记录规则适用范围变更时的用户变动情况
 *
 * <AUTHOR> chen
 * @since 2025/4/14
 */
@Data
public class RuleConfigRangeChangeDTO {

    /**
     * 当前规则应用的人员用户ID列表
     */
    List<Long> currentRuleApplyUserIdList = new ArrayList<>();

    /**
     * 需要更新规则的用户ID列表
     * 包含所有受影响的用户（新增和移除的用户）
     */
    List<Long> updateUserIdList = new ArrayList<>();

    /**
     * 新增到规则的用户ID列表
     */
    List<Long> addUserIdList = new ArrayList<>();

    /**
     * 从规则移除的用户ID列表
     */
    List<Long> removeUserIdList = new ArrayList<>();

    /**
     * 新增到国家级别配置或无规则的用户ID列表（记录）
     */
    List<Long> intoCountryLevelOrNoRuleUserIdList = new ArrayList<>();

    /**
     * 添加用户到规则
     * 同时更新updateUserIdList和addUserIdList
     *
     * @param userId 要添加的用户ID
     */
    public void addUser(Long userId) {
        this.addUserIdList.add(userId);
        this.updateUserIdList.add(userId);
    }

    /**
     * 添加用户列表到规则
     * 同时更新updateUserIdList和addUserIdList
     *
     * @param userIdList 要添加的用户ID列表
     */
    public void addUser(List<Long> userIdList) {
        this.addUserIdList.addAll(userIdList);
        this.updateUserIdList.addAll(userIdList);
    }

    /**
     * 检查用户是否在更新列表中
     *
     * @param userId 要检查的用户ID
     * @return true:用户在更新列表中；false:用户不在更新列表中
     */
    public boolean containsUser(Long userId) {
        return this.updateUserIdList.contains(userId);
    }

    /**
     * 添加用户到被规则移除的列表
     * 同时更新updateUserIdList和removeUserIdList
     *
     * @param userId 要添加的用户ID
     */
    public void addRemovedUser(Long userId) {
        this.removeUserIdList.add(userId);
        this.updateUserIdList.add(userId);
    }

    /**
     * 添加用户列表到被规则移除的列表
     * 同时更新updateUserIdList和removeUserIdList
     *
     * @param userIdList 要添加的用户ID列表
     */
    public void addRemovedUser(List<Long> userIdList) {
        this.removeUserIdList.addAll(userIdList);
        this.updateUserIdList.addAll(userIdList);
    }

    /**
     * 检查是否存在需要更新的用户
     *
     * @return true:没有需要更新的用户；false:存在需要更新的用户
     */
    public boolean isUpdateUsersEmpty() {
        return this.updateUserIdList.isEmpty();
    }


    /** 
     * 添加用户到国家级别配置或无规则
     *
     * @param userId 要添加的用户ID
     */
    public void addIntoCountryLevelOrNoRuleUser(Long userId) {
        this.intoCountryLevelOrNoRuleUserIdList.add(userId);
    }

    /**
     * 添加用户到国家级别配置或无规则
     *
     * @param userIdList 要添加的用户ID列表
     */
    public void addIntoCountryLevelOrNoRuleUser(List<Long> userIdList) {
        this.intoCountryLevelOrNoRuleUserIdList.addAll(userIdList);
    }

    /**
     * 重写toString方法，清晰展示人员变动情况
     * 格式：
     * 规则范围变更统计[更新:x人, 新增:y人, 移除:z人]
     * - 更新用户: [1, 2, 3]
     * - 新增用户: [1, 2]
     * - 移除用户: [3]
     *
     * @return 格式化的人员变动信息
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();

        // 添加统计信息
        sb.append("规则范围变更统计[")
                .append("更新:").append(updateUserIdList.size()).append("人, ")
                .append("新增:").append(addUserIdList.size()).append("人, ")
                .append("移除:").append(removeUserIdList.size()).append("人")
                .append("新增到国家级别配置:").append(intoCountryLevelOrNoRuleUserIdList.size()).append("人")
                .append("]\n");

        // 添加详细的用户ID列表
        sb.append("- 更新用户: ").append(updateUserIdList).append("\n");
        sb.append("- 新增用户: ").append(addUserIdList).append("\n");
        sb.append("- 移除用户: ").append(removeUserIdList).append("\n");
        sb.append("- 新增到国家级别配置: ").append(intoCountryLevelOrNoRuleUserIdList).append("\n");

        return sb.toString();
    }
}
