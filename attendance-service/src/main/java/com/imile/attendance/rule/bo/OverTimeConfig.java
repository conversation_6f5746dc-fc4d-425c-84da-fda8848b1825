package com.imile.attendance.rule.bo;

import java.math.BigDecimal;
import java.util.Date;

import com.imile.attendance.dto.DateAndTimeZoneDate;
import org.apache.commons.lang3.StringUtils;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description 加班规则
 */
@Data
public class OverTimeConfig {

    private Long id;

    private String country;

    private String configNo;

    private String configName;

    private OverTime overTime;

    private Integer isCountryLevel;

    private String status;

    private String deptIds;

    private Integer isLatest;

    private Date effectTime;

    private Date expireTime;

    private Long effectTimestamp;

    private Long expireTimestamp;

    private Date createDate;

    private String createUserCode;

    private String createUserName;

    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;

    private Integer isDelete;

    /**
     * 工作加班开始时间
     */
    private BigDecimal workingOutStartTime;

    /**
     * 工作日最长有效加班时间
     */
    private BigDecimal workingEffectiveTime;

    /**
     * 工作日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String workingSubsidyType;

    //==========休息日加班时长的判定====================

    /**
     * 休息日最长有效加班时间
     *
     */
    private BigDecimal restEffectiveTime;

    /**
     * 休息日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String restSubsidyType;

    //==========节假日加班时长的判定====================

    /**
     * 节假日最长有效加班时间
     *
     */
    private BigDecimal holidayEffectiveTime;

    /**
     * 节假日加班补贴方式
     * 转薪资：TRANSFER_SALARY
     * 转调休：TRANSFER_TIME_OFF
     */
    private String holidaySubsidyType;

    @Builder
    public OverTimeConfig(Long id, String country, String configNo, String configName, OverTime overTime, Integer isCountryLevel,
                          String status, String deptIds, Integer isLatest, DateAndTimeZoneDate effectDateAndTimeZoneDate, DateAndTimeZoneDate expireDateAndTimeZoneDate,
                          Date createDate, String createUserCode, String createUserName, Date lastUpdDate,
                          String lastUpdUserCode, String lastUpdUserName, Integer isDelete,
                          BigDecimal workingOutStartTime, BigDecimal workingEffectiveTime, String workingSubsidyType,
                          BigDecimal restEffectiveTime, String restSubsidyType, 
                          BigDecimal holidayEffectiveTime, String holidaySubsidyType) {
        this.id = id;
        setCountry(country);
        this.configNo = configNo;
        setConfigName(configName);
        this.overTime = overTime;
        this.isCountryLevel = isCountryLevel;
        this.status = status;
        this.deptIds = deptIds;
        this.isLatest = isLatest;
        setEffectTime(effectDateAndTimeZoneDate);
        setExpireTime(expireDateAndTimeZoneDate);
        this.createDate = createDate;
        this.createUserCode = createUserCode;
        this.createUserName = createUserName;
        this.lastUpdDate = lastUpdDate;
        this.lastUpdUserCode = lastUpdUserCode;
        this.lastUpdUserName = lastUpdUserName;
        this.isDelete = isDelete;
        this.workingOutStartTime = workingOutStartTime;
        this.workingEffectiveTime = workingEffectiveTime;
        this.workingSubsidyType = workingSubsidyType;
        this.restEffectiveTime = restEffectiveTime;
        this.restSubsidyType = restSubsidyType;
        this.holidayEffectiveTime = holidayEffectiveTime;
        this.holidaySubsidyType = holidaySubsidyType;
    }

    public void setCountry(String country) {
        if (StringUtils.isEmpty(country)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "country can not be null");
        }
        this.country = country;
    }

    public void setConfigName(String configName) {
        if (StringUtils.isEmpty(configName)) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configName can not be null");
        }
        if (StringUtils.length(configName) > 100) {
            //todo
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "configName length can not be more than 100");
        }
        this.configName = configName;
    }

    public void setEffectTime(DateAndTimeZoneDate effectDateAndTimeZoneDate) {
        if (null == effectDateAndTimeZoneDate) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "effectTime can not be null");
        }
        this.effectTime = effectDateAndTimeZoneDate.getTimeZoneDate();
        this.effectTimestamp = effectDateAndTimeZoneDate.getDateTimeStamp();
    }

    public void setExpireTime(DateAndTimeZoneDate expireDateAndTimeZoneDate) {
        if (null == expireDateAndTimeZoneDate) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "expireTime can not be null");
        }
        this.expireTime = expireDateAndTimeZoneDate.getTimeZoneDate();
        this.expireTimestamp = expireDateAndTimeZoneDate.getDateTimeStamp();
    }

}
