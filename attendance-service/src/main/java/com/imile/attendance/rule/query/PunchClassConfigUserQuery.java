package com.imile.attendance.rule.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;


/**
 * <AUTHOR>
 * @since 2025/4/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PunchClassConfigUserQuery extends ResourceQuery {

    /**
     * 班次规则ID
     */
    @NotNull(message = "classId cannot be empty")
    private Long classId;

    /**
     * 员工姓名/工号
     */
    private String userCodeOrName;
}
