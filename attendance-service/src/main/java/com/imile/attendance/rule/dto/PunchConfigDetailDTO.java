package com.imile.attendance.rule.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/10 
 * @Description 打卡规则详情
 */
@Data
public class PunchConfigDetailDTO {

    /**
     *   主键id
     */
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则名称
     */
    private String configName;

    /**
     * 打卡规则类型
     */
    private String configType;

    /**
     * 上下班打卡时间间隔 单位：小时（类型为灵活打卡两次时需设置）
     */
    private BigDecimal punchTimeInterval;


    /**
     * 是否为国家级配置
     */
    private Integer isCountryLevel;

    /**
     * 适用部门
     */
    private List<ConfigRangeDTO> applyDeptList;

    /**
     * 适用用户
     */
    private List<ConfigRangeDTO> applyUserList;

    /**
     * 当部门/用户已存在其他打卡方案时，是否强制覆盖 1：强制覆盖  0：不覆盖
     */
    private Integer isCoverOld;
}
