package com.imile.attendance.rule.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PunchClassConfigListQuery extends ResourceQuery {

    /**
     * 状态
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 部门
     */
    private List<Long> deptIds;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 账号ID
     */
    private List<Long> userIdList;

    /**
     * 班次性质
     */
    @NotNull(message = "classNature cannot be empty")
    private String classNature;
}
