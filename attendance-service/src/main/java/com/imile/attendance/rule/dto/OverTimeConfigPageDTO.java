package com.imile.attendance.rule.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17 
 * @Description
 */
@Data
public class OverTimeConfigPageDTO {

    private Long id;

    /**
     * 状态
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 加班规则编码
     */
    private String configNo;

    /**
     * 是否为国家级别规则
     */
    private Integer isCountryLevel;

    /**
     * 加班规则名称
     */
    private String configName;

    /**
     * 适用范围
     */
    private List<ConfigRangeDTO> rangeRecords;

    /**
     * 已绑定员工数
     */
    private Integer employeeCount;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改日期
     */
    private Date lastUpdDate;
}
