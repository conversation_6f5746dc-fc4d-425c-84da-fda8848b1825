package com.imile.attendance.rule;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.ReissueCardConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigRangeDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.rule.bo.CountryOverTimeConfig;
import com.imile.attendance.rule.bo.CountryPunchConfig;
import com.imile.attendance.rule.bo.CountryReissueCardConfig;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/24
 * @Description
 */
@Slf4j
@Service
public class RuleConfigRangeService {

    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private ReissueCardConfigRangeDao reissueCardConfigRangeDao;
    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private ReissueCardConfigDao reissueCardConfigDao;
    @Resource
    private OverTimeConfigDao overTimeConfigDao;
    @Resource
    private CountryService countryService;
    @Resource
    private MigrationService migrationService;

    /**
     * 为员工关联考勤规则
     * 1. 关联打卡规则：优先使用部门规则，无部门规则则使用国家级规则
     * 2. 关联补卡规则：优先使用部门规则，无部门规则则使用国家级规则
     * 3. 关联加班规则：优先使用部门规则，无部门规则则使用国家级规则
     * 4. 生效时间以员工入职确认时间为准
     *
     * @param attendanceUser            考勤用户信息，包含用户ID、部门ID、国家等基础信息
     * @param attendanceUserEntryRecord 考勤用户入职记录，包含入职确认时间等信息
     */
    public void addRuleConfigRange(AttendanceUser attendanceUser,
                                   AttendanceUserEntryRecord attendanceUserEntryRecord) {
        // 过滤人员是否在灰度国家中
        Boolean userIsEnableNewAttendance = migrationService.verifyUserIsEnableAttendanceForCountry(attendanceUser.getId());
        if (!userIsEnableNewAttendance) {
            log.info("userCode:{} 不在灰度国家中，无需为其关联打卡，补卡，加班规则", attendanceUser.getUserCode());
            return;
        }

        Long userId = attendanceUser.getId();
        String locationCountry = attendanceUser.getLocationCountry();
        String userCode = attendanceUser.getUserCode();
        Long deptId = attendanceUser.getDeptId();
        Date startDate = attendanceUserEntryRecord.getConfirmDate();

        // 关联打卡规则
        associatePunchConfig(userId, locationCountry, userCode, deptId, startDate);

        // 关联补卡规则
        associateReissueCardConfig(userId, locationCountry, userCode, deptId, startDate);

        // 关联加班规则
        associateOverTimeConfig(userId, locationCountry, userCode, deptId, startDate);
    }

    /**
     * 员工离职更新关联考勤规则
     */
    public void updateRuleConfigRange(UserInfoDO userInfoDO,
                                      Date actualDimissionDate,
                                      boolean isDelayConfirm) {
        if (!migrationService.verifyUserIsEnableAttendanceForCountry(userInfoDO.getId())) {
            log.info("updateRuleConfigRange | userInfo is not in new attendance country, userCode:{}", userInfoDO.getUserCode());
            return;
        }

        // 更新打卡规则
        updatePunchConfigRange(userInfoDO.getId(), actualDimissionDate, isDelayConfirm);

        // 更新补卡规则
        updateReissueCardConfigRange(userInfoDO.getId(), actualDimissionDate, isDelayConfirm);

        // 更新加班规则
        updateOverTimeConfigRange(userInfoDO.getId(), actualDimissionDate, isDelayConfirm);
    }

    public void userRuleConfigRangeHandler(CalendarAndPunchHandlerDTO calendarHandler) {
        log.info("userRuleConfigRangeHandler:{}", JSON.toJSON(calendarHandler));
//        if (!migrationService.verifyUserIsEnableAttendanceForCountry(calendarHandler.getUserId())) {
//            log.info("doNotice2Attendance | userInfo is not in new attendance country, userId:{}", calendarHandler.getUserId());
//            return;
//        }
        if (calendarHandler.getUserId() == null || calendarHandler.getNewDeptId() == null
                || calendarHandler.getOldDeptId() == null || ObjectUtil.isEmpty(calendarHandler.getUserCode())
                || StringUtils.isBlank(calendarHandler.getNewCountry())
                || StringUtils.isBlank(calendarHandler.getOldCountry())) {
            log.info("userRuleConfigRangeHandler | 参数异常，handlerDTO:{}", JSON.toJSON(calendarHandler));
            return;
        }
        // 用户不能为空
        Long userId = calendarHandler.getUserId();
        if (Objects.isNull(userId)) {
            log.info("userRuleConfigRangeHandler | userIdList is empty");
            return;
        }
        // 国家与部门都没有变更，不进行处理
        if (!calendarHandler.areCountryChanged() && !calendarHandler.areDeptChanged()) {
            return;
        }
        List<PunchConfigRangeDO> addPunchConfigRanges = new ArrayList<>();
        List<PunchConfigRangeDO> updatePunchConfigRanges = new ArrayList<>();
        List<ReissueCardConfigRangeDO> addReissueCardConfigRanges = new ArrayList<>();
        List<ReissueCardConfigRangeDO> updateReissueCardConfigRanges = new ArrayList<>();
        List<OverTimeConfigRangeDO> addOverTimeConfigRanges = new ArrayList<>();
        List<OverTimeConfigRangeDO> updateOverTimeConfigRanges = new ArrayList<>();

        userPunchConfigRangeHandler(calendarHandler, addPunchConfigRanges, updatePunchConfigRanges);
        userReissueCardConfigRangeHandler(calendarHandler, addReissueCardConfigRanges, updateReissueCardConfigRanges);
        userOverTimeConfigRangeHandler(calendarHandler, addOverTimeConfigRanges, updateOverTimeConfigRanges);

        punchConfigManage.configRangeUpdateOrAdd(updatePunchConfigRanges, addPunchConfigRanges);
        reissueCardConfigManage.configRangeUpdateOrAdd(updateReissueCardConfigRanges, addReissueCardConfigRanges);
        overTimeConfigManage.configRangeUpdateOrAdd(updateOverTimeConfigRanges, addOverTimeConfigRanges);
    }

    /**
     * 用户打卡规则处理
     *
     * @param calendarHandler         日历和打卡规则处理DTO
     * @param addPunchConfigRanges    新增打卡规则范围配置列表
     * @param updatePunchConfigRanges 更新打卡规则范围配置列表
     */
    public void userPunchConfigRangeHandler(CalendarAndPunchHandlerDTO calendarHandler,
                                            List<PunchConfigRangeDO> addPunchConfigRanges,
                                            List<PunchConfigRangeDO> updatePunchConfigRanges) {
        Long userId = calendarHandler.getUserId();
        // 获取用户的打卡规则配置
        List<PunchConfigRangeDO> userPunchConfigRangeList =
                punchConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        // 查询用户的打卡规则范围配置(旧的)
        PunchConfigRangeDO oldUserPunchConfigRange = null;
        if (CollectionUtils.isNotEmpty(userPunchConfigRangeList)) {
            oldUserPunchConfigRange = userPunchConfigRangeList.get(0);
        }
        // 如果当前用户绑定的为用户级别规则，已经为优先级最高的规则，不处理
        if (null != oldUserPunchConfigRange && oldUserPunchConfigRange.areUserRange()) {
            log.info(":userPunchConfigRangeHandler | userId:{} 已经绑定了用户级别打卡规则:{}，无需处理", userId, oldUserPunchConfigRange);
            return;
        }

        PunchConfigDO newPunchConfigDO = null;
        // 获取员工当前国家（新国家）的所有打卡规则
        CountryPunchConfig countryPunchConfig = punchConfigManage.getCountryConfig(calendarHandler.getNewCountry());
        List<PunchConfigDO> countryConfigs = countryPunchConfig.getCountryConfigs();
        if (CollectionUtils.isEmpty(countryConfigs)) {
            log.info("userPunchConfigRangeHandler | userId:{}当前国家:{}的打卡规则不存在,不绑定", userId, calendarHandler.getNewCountry());
        } else {
            // 员工当前部门（新部门）是否有规则，有为部门级规则，无看是否有国家级规则，有为国家级，无则不绑定规则
            Long userDeptId = calendarHandler.getNewDeptId();
            PunchConfigDO userDeptPunchConfigDO = countryPunchConfig.queryConfigByDeptId(userDeptId);
            if (null == userDeptPunchConfigDO) {
                newPunchConfigDO = backToCountryLevelOrNoRule(
                        countryPunchConfig,
                        userId,
                        userDeptId,
                        calendarHandler.getNewCountry()
                );
            } else {
                // 用户部门有规则，为部门规则，绑定规则
                newPunchConfigDO = userDeptPunchConfigDO;
            }
        }
        buildPunchConfigRange(calendarHandler, addPunchConfigRanges, updatePunchConfigRanges,
                oldUserPunchConfigRange, newPunchConfigDO);
    }

    /**
     * 回退到国家级别打卡规则或没有规则
     */
    private PunchConfigDO backToCountryLevelOrNoRule(CountryPunchConfig countryPunchConfig,
                                                     Long userId,
                                                     Long newDeptId,
                                                     String newCountry) {
        PunchConfigDO countryLevelConfig = countryPunchConfig.queryCountryLevelConfig();
        if (null != countryLevelConfig) {
            log.info("userId:{} deptId:{} 进入country:{}的国家级打卡规则", userId, newDeptId, newCountry);
            return countryLevelConfig;
        } else {
            log.info("userId:{} deptId:{}的country：{}没有国家级打卡规则，没有规则", userId, newDeptId, newCountry);
            return null;
        }
    }

    /**
     * 回退到国家级别补卡规则或没有规则
     */
    private ReissueCardConfigDO backToCountryLevelOrNoRule(CountryReissueCardConfig countryReissueCardConfig,
                                                           Long userId,
                                                           Long newDeptId,
                                                           String newCountry) {
        ReissueCardConfigDO countryLevelConfig = countryReissueCardConfig.queryCountryLevelConfig();
        if (null != countryLevelConfig) {
            log.info("userId:{} deptId:{} 进入country:{}的国家级补卡规则", userId, newDeptId, newCountry);
            return countryLevelConfig;
        } else {
            log.info("userId:{} deptId:{}的country：{}没有国家级补卡规则，没有规则", userId, newDeptId, newCountry);
            return null;
        }
    }

    /**
     * 回退到国家级别加班规则或没有规则
     */
    private OverTimeConfigDO backToCountryLevelOrNoRule(CountryOverTimeConfig countryOverTimeConfig,
                                                        Long userId,
                                                        Long newDeptId,
                                                        String newCountry) {
        OverTimeConfigDO countryLevelConfig = countryOverTimeConfig.queryCountryLevelConfig();
        if (null != countryLevelConfig) {
            log.info("userId:{} deptId:{} 进入country:{}的国家级加班规则", userId, newDeptId, newCountry);
            return countryLevelConfig;
        } else {
            log.info("userId:{} deptId:{}的country：{}没有国家级加班规则，没有规则", userId, newDeptId, newCountry);
            return null;
        }
    }

    /**
     * 用户补卡规则处理
     */
    public void userReissueCardConfigRangeHandler(CalendarAndPunchHandlerDTO calendarHandler,
                                                  List<ReissueCardConfigRangeDO> addReissueCardConfigRanges,
                                                  List<ReissueCardConfigRangeDO> updateReissueCardConfigRanges) {
        Long userId = calendarHandler.getUserId();
        // 获取用户的补卡规则配置
        List<ReissueCardConfigRangeDO> userReissueCardConfigRangeList =
                reissueCardConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        // 查询用户的补卡规则范围配置(旧的)
        ReissueCardConfigRangeDO oldUserReissueCardConfigRange = null;
        if (CollectionUtils.isNotEmpty(userReissueCardConfigRangeList)) {
            oldUserReissueCardConfigRange = userReissueCardConfigRangeList.get(0);
        }
        // 如果当前用户绑定的为用户级别规则，已经为优先级最高的规则，不处理
        if (null != oldUserReissueCardConfigRange && oldUserReissueCardConfigRange.areUserRange()) {
            log.info(":userReissueCardConfigRangeHandler | userId:{} 已经绑定了用户级别补卡规则:{}，无需处理", userId,
                    oldUserReissueCardConfigRange);
            return;
        }

        ReissueCardConfigDO newReissueCardConfigDO = null;
        // 获取员工当前国家（新国家）的所有补卡规则
        CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(calendarHandler.getNewCountry());
        List<ReissueCardConfigDO> countryConfigs = countryReissueCardConfig.getCountryConfigs();
        if (CollectionUtils.isEmpty(countryConfigs)) {
            log.info("userReissueCardConfigRangeHandler | userId:{}当前国家:{}的补卡规则不存在,不绑定",
                    calendarHandler.getNewCountry(), userId);
        } else {
            // 员工当前部门（新部门）是否有规则，有为部门级规则，无看是否有国家级规则，有为国家级，无则不绑定规则
            Long userDeptId = calendarHandler.getNewDeptId();
            ReissueCardConfigDO userDeptReissueCardConfigDO = countryReissueCardConfig.queryConfigByDeptId(userDeptId);
            if (null == userDeptReissueCardConfigDO) {
                newReissueCardConfigDO = backToCountryLevelOrNoRule(
                        countryReissueCardConfig,
                        userId,
                        userDeptId,
                        calendarHandler.getNewCountry()
                );
            } else {
                // 用户部门有规则，为部门规则，绑定规则
                newReissueCardConfigDO = userDeptReissueCardConfigDO;
            }
        }
        buildReissueCardConfigRange(calendarHandler, addReissueCardConfigRanges, updateReissueCardConfigRanges,
                oldUserReissueCardConfigRange, newReissueCardConfigDO);
    }

    /**
     * 用户加班规则处理
     */
    private void userOverTimeConfigRangeHandler(CalendarAndPunchHandlerDTO calendarHandler,
                                                List<OverTimeConfigRangeDO> addOverTimeConfigRanges,
                                                List<OverTimeConfigRangeDO> updateOverTimeConfigRanges) {
        Long userId = calendarHandler.getUserId();
        // 获取用户的加班规则配置
        List<OverTimeConfigRangeDO> userOverTimeConfigRangeList =
                overTimeConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        // 查询用户的加班规则范围配置(旧的)
        OverTimeConfigRangeDO oldUserOverTimeConfigRange = null;
        if (CollectionUtils.isNotEmpty(userOverTimeConfigRangeList)) {
            oldUserOverTimeConfigRange = userOverTimeConfigRangeList.get(0);
        }
        // 如果当前用户绑定的为用户级别规则，已经为优先级最高的规则，不处理
        if (null != oldUserOverTimeConfigRange && oldUserOverTimeConfigRange.areUserRange()) {
            log.info(":userOverTimeConfigRangeHandler | userId:{} 已经绑定了用户级别规则:{}，无需处理", userId,
                    oldUserOverTimeConfigRange);
            return;
        }

        OverTimeConfigDO newOverTimeConfigDO = null;
        // 获取员工当前国家（新国家）的所有加班规则
        CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(calendarHandler.getNewCountry());
        List<OverTimeConfigDO> countryConfigs = countryOverTimeConfig.getCountryConfigs();
        if (CollectionUtils.isEmpty(countryConfigs)) {
            log.info("userOverTimeConfigRangeHandler | 当前国家:{}的加班规则不存在，userId:{},不绑定",
                    calendarHandler.getNewCountry(), userId);
        } else {
            // 员工当前部门（新部门）是否有规则，有为部门级规则，无看是否有国家级规则，有为国家级，无则不绑定规则
            Long userDeptId = calendarHandler.getNewDeptId();
            OverTimeConfigDO userDeptOverTimeConfigDO = countryOverTimeConfig.queryConfigByDeptId(userDeptId);
            if (null == userDeptOverTimeConfigDO) {
                newOverTimeConfigDO = backToCountryLevelOrNoRule(
                        countryOverTimeConfig,
                        userId,
                        userDeptId,
                        calendarHandler.getNewCountry()
                );
            } else {
                // 用户部门有规则，为部门规则，绑定规则
                newOverTimeConfigDO = userDeptOverTimeConfigDO;
            }
        }
        buildOverTimeConfigRange(calendarHandler, addOverTimeConfigRanges, updateOverTimeConfigRanges,
                oldUserOverTimeConfigRange, newOverTimeConfigDO);
    }

    /**
     * 构建打卡规则范围,旧规则过期，新规则生效
     */
    private void buildPunchConfigRange(CalendarAndPunchHandlerDTO calendarHandler,
                                       List<PunchConfigRangeDO> addPunchConfigRanges,
                                       List<PunchConfigRangeDO> updatePunchConfigRanges,
                                       PunchConfigRangeDO oldUserPunchConfigRange,
                                       PunchConfigDO newPunchConfigDO) {
        // 构造变动时间
        Date nowDate = calendarHandler.getCurrentDate();
        if (null != oldUserPunchConfigRange) {
            // 旧规则的过期时间设置为旧规则所属国家的当前时间
            CountryDTO oldCountryDTO = countryService.queryCountry(calendarHandler.getOldCountry());
            DateAndTimeZoneDate nowDateAndTimeZoneDate = oldCountryDTO.getDateAndTimeZoneDate(nowDate);
            oldUserPunchConfigRange.setExpireTime(nowDateAndTimeZoneDate.getTimeZoneDate());
            oldUserPunchConfigRange.setExpireTimestamp(nowDateAndTimeZoneDate.getDateTimeStamp());
            oldUserPunchConfigRange.setIsLatest(BusinessConstant.N);
            oldUserPunchConfigRange.setRemark("员工国家部门变动导致旧打卡规则过期");
            BaseDOUtil.fillDOUpdateByUserOrSystem(oldUserPunchConfigRange);
            updatePunchConfigRanges.add(oldUserPunchConfigRange);
            log.info("buildPunchConfigRange | userId:{} 旧打卡规则过期，旧规则:{}", calendarHandler.getUserId(), oldUserPunchConfigRange);
        }
        if (null != newPunchConfigDO) {
            // 新规则的生效时间为新规则所属国家的当前时间
            CountryDTO newCountryDTO = countryService.queryCountry(calendarHandler.getNewCountry());
            DateAndTimeZoneDate newCountryNowDate = newCountryDTO.getDateAndTimeZoneDate(nowDate);

            PunchConfigRangeDO addPunchConfigRangeDO = new PunchConfigRangeDO();
            // 国家级规则
            if (newPunchConfigDO.areCountryLevel()) {
                addPunchConfigRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
            } else {
                //部门级规则
                addPunchConfigRangeDO.setRangeType(RuleRangeTypeEnum.DEPT.getCode());
            }
            addPunchConfigRangeDO.setId(defaultIdWorker.nextId());
            addPunchConfigRangeDO.setRuleConfigId(newPunchConfigDO.getId());
            addPunchConfigRangeDO.setRuleConfigNo(newPunchConfigDO.getConfigNo());
            addPunchConfigRangeDO.setBizId(calendarHandler.getUserId());
            addPunchConfigRangeDO.setEffectTime(newCountryNowDate.getTimeZoneDate());
            addPunchConfigRangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
            addPunchConfigRangeDO.setEffectTimestamp(newCountryNowDate.getDateTimeStamp());
            addPunchConfigRangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
            addPunchConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
            addPunchConfigRangeDO.setIsLatest(BusinessConstant.Y);
            addPunchConfigRangeDO.setRemark("员工国家部门变动导致新打卡规则生效");
            BaseDOUtil.fillDOInsertByUsrOrSystem(addPunchConfigRangeDO);
            addPunchConfigRanges.add(addPunchConfigRangeDO);
            log.info("buildPunchConfigRange | userId:{} 新打卡规则生效，新规则:{}", calendarHandler.getUserId(), addPunchConfigRangeDO);
        }
    }

    /**
     * 构建补卡规则范围,旧规则过期，新规则生效
     */
    private void buildReissueCardConfigRange(CalendarAndPunchHandlerDTO calendarHandler,
                                             List<ReissueCardConfigRangeDO> addReissueCardConfigRanges,
                                             List<ReissueCardConfigRangeDO> updateReissueCardConfigRanges,
                                             ReissueCardConfigRangeDO oldReissueCardConfigRange,
                                             ReissueCardConfigDO newReissueCardConfigDO) {
        Date nowDate = calendarHandler.getCurrentDate();
        if (null != oldReissueCardConfigRange) {
            // 旧规则的过期时间设置为旧规则所属国家的当前时间
            CountryDTO oldCountryDTO = countryService.queryCountry(calendarHandler.getOldCountry());
            DateAndTimeZoneDate oldCountryNowDate = oldCountryDTO.getDateAndTimeZoneDate(nowDate);
            oldReissueCardConfigRange.setExpireTime(oldCountryNowDate.getTimeZoneDate());
            oldReissueCardConfigRange.setExpireTimestamp(oldCountryNowDate.getDateTimeStamp());
            oldReissueCardConfigRange.setIsLatest(BusinessConstant.N);
            oldReissueCardConfigRange.setRemark("员工国家部门变动导致旧补卡规则过期");
            BaseDOUtil.fillDOUpdate(oldReissueCardConfigRange);
            updateReissueCardConfigRanges.add(oldReissueCardConfigRange);
            log.info("buildReissueCardConfigRange | userId:{} 旧补卡规则过期，旧规则:{}", calendarHandler.getUserId(), oldReissueCardConfigRange);
        }

        if (null != newReissueCardConfigDO) {
            // 新规则的生效时间为新规则所属国家的当前时间
            CountryDTO newCountryDTO = countryService.queryCountry(calendarHandler.getNewCountry());
            DateAndTimeZoneDate newCountryNowDate = newCountryDTO.getDateAndTimeZoneDate(nowDate);

            ReissueCardConfigRangeDO addReissueCardConfigRangeDO = new ReissueCardConfigRangeDO();
            // 国家级规则
            if (newReissueCardConfigDO.areCountryLevel()) {
                addReissueCardConfigRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
            } else {
                //部门级规则
                addReissueCardConfigRangeDO.setRangeType(RuleRangeTypeEnum.DEPT.getCode());
            }
            addReissueCardConfigRangeDO.setId(defaultIdWorker.nextId());
            addReissueCardConfigRangeDO.setRuleConfigId(newReissueCardConfigDO.getId());
            addReissueCardConfigRangeDO.setRuleConfigNo(newReissueCardConfigDO.getConfigNo());
            addReissueCardConfigRangeDO.setBizId(calendarHandler.getUserId());
            addReissueCardConfigRangeDO.setEffectTime(newCountryNowDate.getTimeZoneDate());
            addReissueCardConfigRangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
            addReissueCardConfigRangeDO.setEffectTimestamp(newCountryNowDate.getDateTimeStamp());
            addReissueCardConfigRangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
            addReissueCardConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
            addReissueCardConfigRangeDO.setIsLatest(BusinessConstant.Y);
            addReissueCardConfigRangeDO.setRemark("员工国家部门变动导致新补卡规则生效");
            BaseDOUtil.fillDOInsertByUsrOrSystem(addReissueCardConfigRangeDO);
            addReissueCardConfigRanges.add(addReissueCardConfigRangeDO);
            log.info("buildReissueCardConfigRange | userId:{} 新补卡规则生效，新规则:{}", calendarHandler.getUserId(), addReissueCardConfigRangeDO);
        }
    }

    /**
     * 构建加班规则范围,旧规则过期，新规则生效
     */
    private void buildOverTimeConfigRange(CalendarAndPunchHandlerDTO calendarHandler,
                                          List<OverTimeConfigRangeDO> addOverTimeConfigRanges,
                                          List<OverTimeConfigRangeDO> updateOverTimeConfigRanges,
                                          OverTimeConfigRangeDO oldOverTimeConfigRange,
                                          OverTimeConfigDO newOverTimeConfig) {
        Date nowDate = calendarHandler.getCurrentDate();
        if (null != oldOverTimeConfigRange) {
            // 旧规则的过期时间设置为旧规则所属国家的当前时间
            CountryDTO oldCountryDTO = countryService.queryCountry(calendarHandler.getOldCountry());
            DateAndTimeZoneDate oldCountryNowDate = oldCountryDTO.getDateAndTimeZoneDate(nowDate);
            oldOverTimeConfigRange.setExpireTime(oldCountryNowDate.getTimeZoneDate());
            oldOverTimeConfigRange.setExpireTimestamp(oldCountryNowDate.getDateTimeStamp());
            oldOverTimeConfigRange.setRemark("员工国家部门变动导致旧加班规则过期");
            oldOverTimeConfigRange.setIsLatest(BusinessConstant.N);
            BaseDOUtil.fillDOUpdate(oldOverTimeConfigRange);
            updateOverTimeConfigRanges.add(oldOverTimeConfigRange);
            log.info("buildOverTimeConfigRange | userId:{} 旧加班规则过期，旧规则:{}", calendarHandler.getUserId(), oldOverTimeConfigRange);
        }
        if (null != newOverTimeConfig) {
            // 新规则的生效时间为新规则所属国家的当前时间
            CountryDTO newCountryDTO = countryService.queryCountry(calendarHandler.getNewCountry());
            DateAndTimeZoneDate newCountryNowDate = newCountryDTO.getDateAndTimeZoneDate(nowDate);

            OverTimeConfigRangeDO addOverTimeConfigRangeDO = new OverTimeConfigRangeDO();
            // 国家级规则
            if (newOverTimeConfig.areCountryLevel()) {
                addOverTimeConfigRangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
            } else {
                //部门级规则
                addOverTimeConfigRangeDO.setRangeType(RuleRangeTypeEnum.DEPT.getCode());
            }
            addOverTimeConfigRangeDO.setId(defaultIdWorker.nextId());
            addOverTimeConfigRangeDO.setRuleConfigId(newOverTimeConfig.getId());
            addOverTimeConfigRangeDO.setRuleConfigNo(newOverTimeConfig.getConfigNo());
            addOverTimeConfigRangeDO.setBizId(calendarHandler.getUserId());
            addOverTimeConfigRangeDO.setEffectTime(newCountryNowDate.getTimeZoneDate());
            addOverTimeConfigRangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
            addOverTimeConfigRangeDO.setEffectTimestamp(newCountryNowDate.getDateTimeStamp());
            addOverTimeConfigRangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
            addOverTimeConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
            addOverTimeConfigRangeDO.setIsLatest(BusinessConstant.Y);
            addOverTimeConfigRangeDO.setRemark("员工国家部门变动导致新加班规则生效");
            BaseDOUtil.fillDOInsertByUsrOrSystem(addOverTimeConfigRangeDO);
            addOverTimeConfigRanges.add(addOverTimeConfigRangeDO);
            log.info("buildOverTimeConfigRange | userId:{} 新加班规则生效，新规则:{}", calendarHandler.getUserId(), addOverTimeConfigRangeDO);
        }
    }

    /**
     * 更新打卡规则适用范围
     */
    private void updatePunchConfigRange(Long userId, Date actualDimissionDate, boolean isDelayConfirm) {
        List<PunchConfigRangeDO> updatePunchConfigRangeList = new ArrayList<>();
        // 根据用户id获取启用的打卡规则配置适用范围(不区分是否最新)
        List<PunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.listActivedConfigRanges(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(punchConfigRangeDOList)) {
            log.info("userId:{} 不存在启用的打卡规则配置适用范围,不处理", userId);
            return;
        }
        Date finalDateNow = DateUtil.endOfDay(actualDimissionDate);
        long finalDateNowTimeStamp = finalDateNow.getTime();
        // 查询非删除记录过滤启用且起始时间在确认离职内的所有记录
        Optional<PunchConfigRangeDO> punchConfigRangeOptional = punchConfigRangeDOList
                .stream()
                .filter(item -> item.getEffectTimestamp().compareTo(finalDateNowTimeStamp) < 1 &&
                        item.getExpireTimestamp().compareTo(finalDateNowTimeStamp) > -1)
                .findFirst();

        if (!punchConfigRangeOptional.isPresent()) {
            return;
        }
        PunchConfigRangeDO punchConfigRangeDO = punchConfigRangeOptional.get();
        punchConfigRangeDO.setExpireTime(finalDateNow);
        punchConfigRangeDO.setExpireTimestamp(finalDateNowTimeStamp);
        // 延后离职直接更新状态未历史版本，当天离职还需要处理异常保留最新版本
        punchConfigRangeDO.setIsLatest(isDelayConfirm ? BusinessConstant.N : BusinessConstant.Y);
        punchConfigRangeDO.setRemark("员工延迟离职导致打卡规则变动:isDelayConfirm=" + isDelayConfirm);
        BaseDOUtil.fillDOUpdateByUserOrSystem(punchConfigRangeDO);
        updatePunchConfigRangeList.add(punchConfigRangeDO);

        // 存在多次修改删除后面的所有版本
        List<PunchConfigRangeDO> expireConfigRangeList = punchConfigRangeDOList.stream()
                .filter(range -> range.getId().compareTo(punchConfigRangeDO.getId()) > 0).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireConfigRangeList)) {
            expireConfigRangeList.forEach(range -> {
                range.setIsDelete(IsDeleteEnum.YES.getCode());
                range.setRemark("员工延迟离职存在多次修改，删除后面的所有版本打卡规则");
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }
        updatePunchConfigRangeList.addAll(expireConfigRangeList);
        punchConfigRangeDao.updateBatchById(updatePunchConfigRangeList);
    }

    /**
     * 更新补卡规则适用范围
     */
    private void updateReissueCardConfigRange(Long userId, Date actualDimissionDate, boolean isDelayConfirm) {
        List<ReissueCardConfigRangeDO> updateReissueCardConfigRangeList = new ArrayList<>();
        // 根据用户id获取启用的补卡规则配置适用范围(不区分是否最新)
        List<ReissueCardConfigRangeDO> reissueCardConfigRangeDOList = reissueCardConfigRangeDao
                .listActivedConfigRanges(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(reissueCardConfigRangeDOList)) {
            log.info("userId:{} 不存在启用的补卡规则配置适用范围,不处理", userId);
            return;
        }
        Date finalDateNow = DateUtil.endOfDay(actualDimissionDate);
        long finalDateNowTimeStamp = finalDateNow.getTime();
        Optional<ReissueCardConfigRangeDO> configRangeOptional = reissueCardConfigRangeDOList
                .stream()
                .filter(item -> item.getEffectTimestamp().compareTo(finalDateNowTimeStamp) < 1 &&
                        item.getExpireTimestamp().compareTo(finalDateNowTimeStamp) > -1)
                .findFirst();

        if (!configRangeOptional.isPresent()) {
            return;
        }
        ReissueCardConfigRangeDO reissueCardConfigRangeDO = configRangeOptional.get();
        reissueCardConfigRangeDO.setExpireTime(finalDateNow);
        reissueCardConfigRangeDO.setExpireTimestamp(finalDateNowTimeStamp);
        reissueCardConfigRangeDO.setIsLatest(isDelayConfirm ? BusinessConstant.N : BusinessConstant.Y);
        reissueCardConfigRangeDO.setRemark("员工延迟离职导致补卡规则变动:isDelayConfirm=" + isDelayConfirm);
        BaseDOUtil.fillDOUpdateByUserOrSystem(reissueCardConfigRangeDO);
        updateReissueCardConfigRangeList.add(reissueCardConfigRangeDO);

        List<ReissueCardConfigRangeDO> expireReissueCardConfigRangeList = reissueCardConfigRangeDOList.stream()
                .filter(range -> range.getId().compareTo(reissueCardConfigRangeDO.getId()) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireReissueCardConfigRangeList)) {
            expireReissueCardConfigRangeList.forEach(range -> {
                range.setIsDelete(IsDeleteEnum.YES.getCode());
                range.setRemark("员工延迟离职存在多次修改，删除后面的所有版本补卡规则");
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }
        updateReissueCardConfigRangeList.addAll(expireReissueCardConfigRangeList);
        reissueCardConfigRangeDao.updateBatchById(updateReissueCardConfigRangeList);
    }

    /**
     * 更新加班规则适用范围
     */
    private void updateOverTimeConfigRange(Long userId, Date actualDimissionDate, boolean isDelayConfirm) {
        List<OverTimeConfigRangeDO> updateOverTimeConfigRangeList = new ArrayList<>();
        // 根据用户id获取启用的加班规则配置适用范围(不区分是否最新)
        List<OverTimeConfigRangeDO> overTimeConfigRangeDOList = overTimeConfigRangeDao
                .listActivedConfigRanges(Collections.singletonList(userId));
        if (CollectionUtils.isEmpty(overTimeConfigRangeDOList)) {
            log.info("userId:{} 不存在启用的加班规则配置适用范围,不处理", userId);
            return;
        }
        Date finalDateNow = DateUtil.endOfDay(actualDimissionDate);
        long finalDateNowTimeStamp = finalDateNow.getTime();
        Optional<OverTimeConfigRangeDO> punchConfigRangeOptional = overTimeConfigRangeDOList
                .stream()
                .filter(item -> Objects.equals(StatusEnum.ACTIVE.getCode(), item.getStatus())
                        && item.getEffectTimestamp().compareTo(finalDateNowTimeStamp) < 1
                        && item.getExpireTimestamp().compareTo(finalDateNowTimeStamp) > -1)
                .findFirst();

        if (!punchConfigRangeOptional.isPresent()) {
            return;
        }
        OverTimeConfigRangeDO overTimeConfigRangeDO = punchConfigRangeOptional.get();
        overTimeConfigRangeDO.setExpireTime(finalDateNow);
        overTimeConfigRangeDO.setExpireTimestamp(finalDateNowTimeStamp);
        overTimeConfigRangeDO.setIsLatest(isDelayConfirm ? BusinessConstant.N : BusinessConstant.Y);
        overTimeConfigRangeDO.setRemark("员工延迟离职导致加班规则变动:isDelayConfirm=" + isDelayConfirm);
        BaseDOUtil.fillDOUpdateByUserOrSystem(overTimeConfigRangeDO);
        updateOverTimeConfigRangeList.add(overTimeConfigRangeDO);

        List<OverTimeConfigRangeDO> expireOverTimeConfigRangeList = overTimeConfigRangeDOList.stream()
                .filter(range -> range.getId().compareTo(overTimeConfigRangeDO.getId()) > 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(expireOverTimeConfigRangeList)) {
            expireOverTimeConfigRangeList.forEach(range -> {
                range.setIsDelete(IsDeleteEnum.YES.getCode());
                range.setRemark("员工延迟离职存在多次修改，删除后面的所有版本加班规则");
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }
        updateOverTimeConfigRangeList.addAll(expireOverTimeConfigRangeList);
        overTimeConfigRangeDao.updateBatchById(updateOverTimeConfigRangeList);
    }

    /**
     * 用户关联打卡规则
     */
    private void associatePunchConfig(Long userId, String locationCountry, String userCode,
                                      Long deptId, Date startDate) {
        CountryPunchConfig countryPunchConfig = punchConfigManage.getCountryConfig(locationCountry);
        List<PunchConfigDO> countryConfigs = countryPunchConfig.getCountryConfigs();

        if (CollectionUtils.isEmpty(countryConfigs)) {
            log.info("userCode:{} country:{} 不存在打卡规则,无法为其关联打卡规则", userCode, locationCountry);
            return;
        }

        // 判断用户是否已经添加了规则
        List<PunchConfigRangeDO> userPunchConfigRangeList =
                punchConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        if (CollectionUtils.isNotEmpty(userPunchConfigRangeList)) {
            log.info("userCode:{} country:{} 已经添加了打卡规则：{},不处理", userCode, locationCountry,
                    userPunchConfigRangeList.get(0).getRuleConfigNo());
            return;
        }

        // 用户所属的部门是否有规则
        PunchConfigDO userDeptPunchConfigDO = countryPunchConfig.queryConfigByDeptId(deptId);
        if (userDeptPunchConfigDO == null) {
            PunchConfigDO countryLevelConfig = countryPunchConfig.queryCountryLevelConfig();
            if (null == countryLevelConfig) {
                log.info("userCode:{} country:{} 没有符合的规则可以匹配（国家级规则为空）,无法为其关联打卡规则",
                        userCode, locationCountry);
                return;
            }
            // 绑定国家级规则
            PunchConfigRangeDO configRangeDO = buildPunchConfigRange(
                    userId,
                    startDate,
                    RuleRangeTypeEnum.COUNTRY.getCode(),
                    countryLevelConfig
            );
            punchConfigManage.configRangeUpdateOrAdd(null, Collections.singletonList(configRangeDO));
            log.info("userCode:{} country:{} 添加到国家级打卡规则：{}",
                    userCode, locationCountry, countryLevelConfig);
            return;
        }
        // 绑定部门级规则
        PunchConfigRangeDO configRangeDO = buildPunchConfigRange(
                userId,
                startDate,
                RuleRangeTypeEnum.DEPT.getCode(),
                userDeptPunchConfigDO
        );
        punchConfigManage.configRangeUpdateOrAdd(null, Collections.singletonList(configRangeDO));
        log.info("userCode:{} country:{} 添加到打卡规则：{}", userCode, locationCountry, configRangeDO);
    }

    /**
     * 用户关联补卡规则
     */
    private void associateReissueCardConfig(Long userId, String locationCountry, String userCode,
                                            Long deptId, Date startDate) {
        CountryReissueCardConfig countryReissueCardConfig = reissueCardConfigManage.getCountryConfig(locationCountry);
        List<ReissueCardConfigDO> countryConfigs = countryReissueCardConfig.getCountryConfigs();

        if (CollectionUtils.isEmpty(countryConfigs)) {
            log.info("userCode:{} country:{} 不存在补卡规则,无法为其关联补卡规则", userCode, locationCountry);
            return;
        }

        // 判断用户是否已经添加了规则
        List<ReissueCardConfigRangeDO> userReissueCardConfigRangeList =
                reissueCardConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        if (CollectionUtils.isNotEmpty(userReissueCardConfigRangeList)) {
            log.info("userCode:{} country:{} 已经添加了补卡规则：{},不处理", userCode, locationCountry,
                    userReissueCardConfigRangeList.get(0).getRuleConfigNo());
            return;
        }

        // 用户所属的部门是否有规则
        ReissueCardConfigDO userDeptReissueCardConfigDO = countryReissueCardConfig.queryConfigByDeptId(deptId);
        if (userDeptReissueCardConfigDO == null) {
            ReissueCardConfigDO countryLevelConfig = countryReissueCardConfig.queryCountryLevelConfig();
            if (countryLevelConfig == null) {
                log.info("userCode:{} country:{} 没有符合的规则可以匹配（国家级规则为空）,无法为其关联补卡规则",
                        userCode, locationCountry);
                return;
            }
            // 绑定国家级规则
            ReissueCardConfigRangeDO configRangeDO = buildReissueCardConfigRange(
                    userId,
                    startDate,
                    RuleRangeTypeEnum.COUNTRY.getCode(),
                    countryLevelConfig
            );
            reissueCardConfigManage.configRangeUpdateOrAdd(null, Collections.singletonList(configRangeDO));
            log.info("userCode:{} country:{} 添加到国家级补卡规则：{}", userCode, locationCountry, countryLevelConfig);
            return;
        }
        // 绑定部门级规则
        ReissueCardConfigRangeDO configRangeDO = buildReissueCardConfigRange(
                userId,
                startDate,
                RuleRangeTypeEnum.DEPT.getCode(),
                userDeptReissueCardConfigDO
        );
        reissueCardConfigManage.configRangeUpdateOrAdd(null, Collections.singletonList(configRangeDO));
        log.info("userCode:{} country:{} 添加到补卡规则：{}", userCode, locationCountry, configRangeDO);
    }

    /**
     * 用户关联加班规则
     */
    private void associateOverTimeConfig(Long userId, String locationCountry, String userCode,
                                         Long deptId, Date startDate) {
        CountryOverTimeConfig countryOverTimeConfig = overTimeConfigManage.getCountryConfig(locationCountry);
        List<OverTimeConfigDO> countryConfigs = countryOverTimeConfig.getCountryConfigs();

        if (CollectionUtils.isEmpty(countryConfigs)) {
            log.info("userCode:{} country:{} 不存在加班规则,无法为其关联加班规则", userCode, locationCountry);
            return;
        }

        // 判断用户是否已经添加了规则
        List<OverTimeConfigRangeDO> userOverTimeConfigRangeList =
                overTimeConfigRangeDao.listConfigRanges(Collections.singletonList(userId));
        if (CollectionUtils.isNotEmpty(userOverTimeConfigRangeList)) {
            log.info("userCode:{} country:{} 已经添加了加班规则：{},不处理", userCode, locationCountry,
                    userOverTimeConfigRangeList.get(0).getRuleConfigNo());
            return;
        }

        // 用户所属的部门是否有规则
        OverTimeConfigDO userOverTimeConfigDO = countryOverTimeConfig.queryConfigByDeptId(deptId);
        if (userOverTimeConfigDO == null) {
            OverTimeConfigDO countryLevelConfig = countryOverTimeConfig.queryCountryLevelConfig();
            if (countryLevelConfig == null) {
                log.info("userCode:{} country:{} 没有符合的规则可以匹配（国家级规则为空）,无法为其关联加班规则",
                        userCode, locationCountry);
                return;
            }
            // 绑定国家级规则
            OverTimeConfigRangeDO configRangeDO = buildOverTimeConfigRange(
                    userId,
                    startDate,
                    RuleRangeTypeEnum.COUNTRY.getCode(),
                    countryLevelConfig
            );
            overTimeConfigManage.configRangeUpdateOrAdd(null, Collections.singletonList(configRangeDO));
            log.info("userCode:{} country:{} 添加到国家级加班规则：{}", userCode, locationCountry, countryLevelConfig);
            return;
        }
        // 绑定部门级规则
        OverTimeConfigRangeDO configRangeDO = buildOverTimeConfigRange(
                userId,
                startDate,
                RuleRangeTypeEnum.DEPT.getCode(),
                userOverTimeConfigDO
        );
        overTimeConfigManage.configRangeUpdateOrAdd(null, Collections.singletonList(configRangeDO));
        log.info("userCode:{} country:{} 添加到加班规则：{}", userCode, locationCountry, configRangeDO);
    }

    /**
     * 构建用户打卡规则范围对象
     */
    private PunchConfigRangeDO buildPunchConfigRange(Long userId,
                                                     Date startDate,
                                                     String rangeType,
                                                     PunchConfigDO punchConfigDO) {
        PunchConfigRangeDO configRangeDO = new PunchConfigRangeDO();
        configRangeDO.setId(defaultIdWorker.nextId());
        configRangeDO.setRuleConfigId(punchConfigDO.getId());
        configRangeDO.setRuleConfigNo(punchConfigDO.getConfigNo());
        configRangeDO.setBizId(userId);
        configRangeDO.setRangeType(rangeType);
        configRangeDO.setEffectTime(startDate);
        configRangeDO.setEffectTimestamp(startDate.getTime());
        configRangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        configRangeDO.setExpireTimestamp(BusinessConstant.DEFAULT_END_TIMESTAMP);
        configRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        configRangeDO.setIsLatest(BusinessConstant.Y);
        configRangeDO.setRemark("员工入职导致新打卡规则生效");
        BaseDOUtil.fillDOInsertByUsrOrSystem(configRangeDO);
        return configRangeDO;
    }

    /**
     * 构建用户补卡规则范围对象
     */
    public ReissueCardConfigRangeDO buildReissueCardConfigRange(Long userId,
                                                                Date startDate,
                                                                String rangeType,
                                                                ReissueCardConfigDO reissueCardConfigDO) {
        ReissueCardConfigRangeDO reissueCardConfigRangeDO = new ReissueCardConfigRangeDO();
        reissueCardConfigRangeDO.setId(defaultIdWorker.nextId());
        reissueCardConfigRangeDO.setRuleConfigId(reissueCardConfigDO.getId());
        reissueCardConfigRangeDO.setRuleConfigNo(reissueCardConfigDO.getConfigNo());
        reissueCardConfigRangeDO.setBizId(userId);
        reissueCardConfigRangeDO.setRangeType(rangeType);
        reissueCardConfigRangeDO.setEffectTime(startDate);
        reissueCardConfigRangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
        reissueCardConfigRangeDO.setEffectTimestamp(startDate.getTime());
        reissueCardConfigRangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
        reissueCardConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        reissueCardConfigRangeDO.setIsLatest(BusinessConstant.Y);
        reissueCardConfigRangeDO.setRemark("员工入职导致新补卡规则生效");
        BaseDOUtil.fillDOInsertByUsrOrSystem(reissueCardConfigRangeDO);
        return reissueCardConfigRangeDO;
    }

    /**
     * 构建用户加班规则范围对象
     */
    public OverTimeConfigRangeDO buildOverTimeConfigRange(Long userId,
                                                          Date startDate,
                                                          String rangeType,
                                                          OverTimeConfigDO overTimeConfigDO) {
        OverTimeConfigRangeDO overTimeConfigRangeDO = new OverTimeConfigRangeDO();
        overTimeConfigRangeDO.setId(defaultIdWorker.nextId());
        overTimeConfigRangeDO.setRuleConfigId(overTimeConfigDO.getId());
        overTimeConfigRangeDO.setRuleConfigNo(overTimeConfigDO.getConfigNo());
        overTimeConfigRangeDO.setBizId(userId);
        overTimeConfigRangeDO.setRangeType(rangeType);
        overTimeConfigRangeDO.setEffectTime(startDate);
        overTimeConfigRangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
        overTimeConfigRangeDO.setEffectTimestamp(startDate.getTime());
        overTimeConfigRangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
        overTimeConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        overTimeConfigRangeDO.setIsLatest(BusinessConstant.Y);
        overTimeConfigRangeDO.setRemark("员工入职导致新加班规则生效");
        BaseDOUtil.fillDOInsertByUsrOrSystem(overTimeConfigRangeDO);
        return overTimeConfigRangeDO;
    }

}