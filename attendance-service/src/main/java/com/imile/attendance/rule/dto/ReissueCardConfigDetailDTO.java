package com.imile.attendance.rule.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/16 
 * @Description
 */
@Data
public class ReissueCardConfigDetailDTO {

    /**
     *   主键id
     */
    private Long id;

    /**
     * 规则名称
     */
    private String configName;

    /**
     * 每月最大补卡次数
     */
    private Integer maxRepunchNumber;

    /**
     * 是否为国家级别规则
     */
    private Integer isCountryLevel;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<ConfigRangeDTO> applyDeptList;

    /**
     * 适用用户
     */
    private List<ConfigRangeDTO> applyUserList;

    /**
     * 当部门/用户已存在其他打卡方案时，是否强制覆盖 1：强制覆盖  0：不覆盖
     */
    private Integer isCoverOld;
}
