package com.imile.attendance.rule.handler;

import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.OverTimeConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.query.RuleRangeUserQuery;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.bo.ConfigRangeDiff;
import com.imile.attendance.rule.bo.CountryOverTimeConfig;
import com.imile.attendance.rule.dto.RuleConfigRangeChangeDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/17
 * @Description 加班配置范围处理
 */
@Slf4j
@Component
public class OverTimeConfigRangeHandler {

    @Resource
    private OverTimeConfigRangeDao overTimeConfigRangeDao;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    /**
     * 确保集合非空
     */
    private <T> List<T> ensureNonNull(List<T> list) {
        return Optional.ofNullable(list).orElseGet(Lists::newArrayList);
    }

    /**
     * 处理新增配置的用户/部门范围
     */
    public RuleConfigRangeChangeDTO addConfigRangeHandler(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                          OverTimeConfigDO addConfigDO,
                                                          String country,
                                                          List<Long> deptIds,
                                                          List<Long> userIds) {
        // 创建和处理配置范围
        List<OverTimeConfigRangeDO> addConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 特殊处理国家级别
        if (addConfigDO.areCountryLevel()) {
            // 查询当前国家下未加入规则的在职非司机员工
            List<UserInfoDO> userInfoDOList = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder().country(country).build()
            );
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                return ruleConfigRangeChangeDTO;
            }
            List<Long> userIdList = userInfoDOList.stream()
                    .map(UserInfoDO::getId)
                    .collect(Collectors.toList());
            ruleConfigRangeChangeDTO.addUser(userIdList);
            for (Long userId : userIdList) {
                buildConfigRange(
                        userId,
                        addConfigDO.getId(),
                        addConfigDO.getConfigNo(),
                        RuleRangeTypeEnum.COUNTRY.getCode(),
                        currentDateAndTimeZoneDate,
                        addConfigRangeList
                );
            }
        } else {
            // 确保集合非空
            userIds = ensureNonNull(userIds);
            deptIds = ensureNonNull(deptIds);

            // 处理部门级别用户
            List<Long> deptUserIds = getDepartmentUsers(deptIds, country);

            // 处理用户级别关联
            processUserLevelAssociations(userIds, addConfigDO, currentDateAndTimeZoneDate, addConfigRangeList,
                    ruleConfigRangeChangeDTO);

            // 处理部门级别关联
            processDeptLevelAssociations(deptUserIds, addConfigDO, currentDateAndTimeZoneDate, addConfigRangeList,
                    ruleConfigRangeChangeDTO);
        }

        // 更新现有配置范围并添加新的配置范围
        updateExistingAndAddNewRanges(ruleConfigRangeChangeDTO, addConfigRangeList, currentDateAndTimeZoneDate);

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 检查更新配置的用户/部门范围
     */
    public RuleConfigRangeChangeDTO checkConfigRangeHandler(OverTimeConfigDO oldConfigDO,
                                                            CountryOverTimeConfig countryOverTimeConfig,
                                                            String country,
                                                            List<Long> deptIds,
                                                            List<Long> userIds) {
        // 确保集合非空
        userIds = ensureNonNull(userIds);
        deptIds = ensureNonNull(deptIds);

        // 获取差异分析
        ConfigRangeDiff diff = calculateConfigRangeDiff(oldConfigDO, userIds, deptIds);

        // 获取受影响的部门用户
        List<Long> addDeptUserIds = getDepartmentUsers(diff.getAddDeptIds(), country);
        List<Long> delDeptUserIds = getDepartmentUsers(diff.getDelDeptIds(), country);

        // 创建和处理配置范围
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 处理新增的用户和部门关联
        for (Long addUserId : diff.getAddUserIds()) {
            ruleConfigRangeChangeDTO.addUser(addUserId);
        }
        if (CollectionUtils.isNotEmpty(addDeptUserIds)) {
            // 查询部门级别新增的用户已存在的记录，并过滤
            List<Long> existUserIdList = filterExistingUserLevelUsers(addDeptUserIds);
            List<Long> filteredDeptUserIds = addDeptUserIds.stream()
                    .filter(userId -> !existUserIdList.contains(userId))
                    .collect(Collectors.toList());
            for (Long userId : filteredDeptUserIds) {
                if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                    continue;
                }
                ruleConfigRangeChangeDTO.addUser(userId);
            }
        }
        // 处理删除的用户和部门关联
        List<Long> delUserIds = diff.getDelUserIds();
        if (CollectionUtils.isNotEmpty(delUserIds)) {
            List<AttendanceUser> delUserInfoList = userService.listUsersByIds(delUserIds);
            for (AttendanceUser user : delUserInfoList) {
                if (ruleConfigRangeChangeDTO.containsUser(user.getId())) {
                    continue;
                }
                ruleConfigRangeChangeDTO.addRemovedUser(user.getId());
                // 判断用户是否应该进入兜底规则
                String userCountry = user.getLocationCountry();

                // 处理同国家的用户
                if (StringUtils.equals(countryOverTimeConfig.getCountry(), userCountry)) {
                    // 如果用户属于被删除的部门，直接进入国家级别配置或无规则
                    if (delDeptUserIds.contains(user.getId())) {
                        ruleConfigRangeChangeDTO.addIntoCountryLevelOrNoRuleUser(user.getId());
                    }
                    // 检查用户的部门是否被其他规则指定
                    boolean userDeptIsInOtherConfig = false;
                    Long userDeptId = user.getDeptId();
                    for (OverTimeConfigDO configDO : countryOverTimeConfig.getCountryConfigs()) {
                        if (StringUtils.isBlank(configDO.getDeptIds())) {
                            continue;
                        }
                        if (configDO.listDeptIds().contains(userDeptId)) {
                            userDeptIsInOtherConfig = true;
                        }
                    }
                    // 如果用户的部门没有被其他规则指定，使用国家级别配置或无规则
                    if (!userDeptIsInOtherConfig) {
                        ruleConfigRangeChangeDTO.addIntoCountryLevelOrNoRuleUser(user.getId());
                    }
                }
                // 移除的用户不是当前国家的，进入用户所属国家对应的规则不做统计
            }
        }
        if (CollectionUtils.isNotEmpty(delDeptUserIds)) {
            // 过滤已经被用户级别指定的用户
            List<Long> existUserIdList = filterExistingUserLevelUsers(delDeptUserIds);
            List<Long> filteredDelDeptUserIds = delDeptUserIds.stream()
                    .filter(userId -> !existUserIdList.contains(userId))
                    .collect(Collectors.toList());
            for (Long userId : filteredDelDeptUserIds) {
                if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                    continue;
                }
                ruleConfigRangeChangeDTO.addRemovedUser(userId);
                // 被删除部门下的员工没有在其他规则中被指定为员工级，降级为国家级别配置或无规则
                ruleConfigRangeChangeDTO.addIntoCountryLevelOrNoRuleUser(userId);
            }
        }
        return ruleConfigRangeChangeDTO;
    }

    /**
     * 处理更新配置的用户/部门范围
     */
    public RuleConfigRangeChangeDTO updateConfigRangeHandler(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                             OverTimeConfigDO oldConfigDO,
                                                             OverTimeConfigDO newConfigDO,
                                                             CountryOverTimeConfig countryConfig,
                                                             String country,
                                                             List<Long> deptIds,
                                                             List<Long> userIds) {
        // 确保集合非空
        userIds = ensureNonNull(userIds);
        deptIds = ensureNonNull(deptIds);

        // 获取差异分析
        ConfigRangeDiff diff = calculateConfigUpdateRangeDiff(oldConfigDO, userIds, deptIds);

        // 获取受影响的部门用户
        List<Long> addDeptUserIds = getDepartmentUsers(diff.getAddDeptIds(), country);
        List<Long> delDeptUserIds = getDepartmentUsers(diff.getDelDeptIds(), country);

        // 创建和处理配置范围
        List<OverTimeConfigRangeDO> addConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 处理新增的用户和部门关联
        processNewAssociations(diff.getAddUserIds(), addDeptUserIds, newConfigDO, currentDateAndTimeZoneDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 处理删除的用户和部门关联
        processRemovedAssociations(diff.getDelUserIds(), delDeptUserIds,
                countryConfig, currentDateAndTimeZoneDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 更新现有配置范围并添加新的配置范围
        updateExistingAndAddNewRanges(ruleConfigRangeChangeDTO, addConfigRangeList, currentDateAndTimeZoneDate);

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 检查启用配置的影响
     */
    public RuleConfigRangeChangeDTO checkEnableConfig(OverTimeConfigDO currentConfig) {
        if (currentConfig.areCountryLevel()) {
            List<UserInfoDO> userInfoDOList = overTimeConfigRangeDao.listOnJobNoDriverUsersExcludeConfigured(
                    RuleRangeUserQuery.builder()
                            .country(currentConfig.getCountry()).build()
            );
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();
            ruleConfigRangeChangeDTO.addUser(
                    userInfoDOList.stream()
                            .map(UserInfoDO::getId)
                            .collect(Collectors.toList())
            );
            return ruleConfigRangeChangeDTO;
        }
        // 1.查询停用的员工级别的人员没有在其他启用中的规则中
        List<OverTimeConfigRangeDO> disabledConfigRangeDOS = overTimeConfigRangeDao.listDisabledByConfigId(currentConfig.getId());
        List<Long> disabledUserIds = disabledConfigRangeDOS.stream()
                .filter(OverTimeConfigRangeDO::areUserRange)
                .map(OverTimeConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(disabledUserIds)) {
            List<OverTimeConfigRangeDO> hasExistInOtherConfigUsers = overTimeConfigRangeDao.listConfigRanges(disabledUserIds)
                    .stream()
                    .filter(OverTimeConfigRangeDO::areUserRange)
                    .filter(i -> !Objects.equals(i.getRuleConfigId(), currentConfig.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasExistInOtherConfigUsers)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.DISABLED_CONFIG_USER_IN_OTHER_CONFIG);
            }
        }
        // 2.将有【适配人数】名人员会自动匹配该规则并重新计算考勤结果
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 2.1 绑定部门的员工匹配
        String deptIds = currentConfig.getDeptIds();
        if (StringUtils.isNotBlank(deptIds)) {
            List<Long> deptIdList = currentConfig.listDeptIds();
            List<AttendanceUser> deptUsers = userService.listOnJobNonDriverUserByDeptIdList(deptIdList, currentConfig.getCountry());
            List<Long> deptUserIds = deptUsers.stream()
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());
            //查询非国家级的人员
            List<Long> existDeptUserIds = overTimeConfigRangeDao.listConfigRanges(deptUserIds)
                    .stream()
                    .filter(OverTimeConfigRangeDO::areNotCountryRange)
                    .map(OverTimeConfigRangeDO::getBizId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptUserIds)) {
                deptUserIds = deptUserIds.stream()
                        .filter(deptUserId -> !existDeptUserIds.contains(deptUserId))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(deptUserIds)) {
                for (Long deptUserId : deptUserIds) {
                    ruleConfigRangeChangeDTO.addUser(deptUserId);
                }
            }
        }
        // 2.2 绑定员工级别的员工匹配
        for (Long noLatestUserId : disabledUserIds) {
            ruleConfigRangeChangeDTO.addUser(noLatestUserId);
        }
        return ruleConfigRangeChangeDTO;
    }

    /**
     * 检查停用配置的影响
     */
    public RuleConfigRangeChangeDTO checkDisableConfig(OverTimeConfigDO currentConfig) {
        if (currentConfig.areCountryLevel()){
            List<OverTimeConfigRangeDO> countryLevelConfigRangeList = overTimeConfigRangeDao.listByConfigId(currentConfig.getId());
            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();
            ruleConfigRangeChangeDTO.addRemovedUser(
                    countryLevelConfigRangeList.stream()
                            //国家下的范围类型都是国家，这里过滤无用
                            .filter(OverTimeConfigRangeDO::areCountryRange)
                            .map(OverTimeConfigRangeDO::getBizId)
                            .collect(Collectors.toList())
            );
            return ruleConfigRangeChangeDTO;
        }
        String country = currentConfig.getCountry();
        return checkConfigRangeHandler(
                currentConfig,
                overTimeConfigManage.getCountryConfig(country),
                country,
                null,
                null
        );
    }

    /**
     * 启用加班规则
     */
    public RuleConfigRangeChangeDTO enableConfigRangeHandler(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                             OverTimeConfigDO oldConfigDO,
                                                             OverTimeConfigDO newConfigDO) {
        // 1.员工级别的人员没有在其他启用中的规则中
        List<OverTimeConfigRangeDO> disabledConfigRangeDOS = overTimeConfigRangeDao.listDisabledByConfigId(oldConfigDO.getId());
        List<Long> disabledUserIds = disabledConfigRangeDOS.stream()
                .filter(OverTimeConfigRangeDO::areUserRange)
                .map(OverTimeConfigRangeDO::getBizId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(disabledUserIds)) {
            List<OverTimeConfigRangeDO> hasExistInOtherConfigUsers = overTimeConfigRangeDao.listConfigRanges(disabledUserIds)
                    .stream()
                    .filter(OverTimeConfigRangeDO::areUserRange)
                    .filter(i -> !Objects.equals(i.getRuleConfigId(), oldConfigDO.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hasExistInOtherConfigUsers)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.DISABLED_CONFIG_USER_IN_OTHER_CONFIG);
            }
        }
        // 2.将有【适配人数】名人员会自动匹配该规则并重新计算考勤结果
        List<OverTimeConfigRangeDO> addConfigRangeList = new ArrayList<>();
        List<OverTimeConfigRangeDO> updateConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 2.1 绑定部门的员工匹配
        String deptIds = oldConfigDO.getDeptIds();
        if (StringUtils.isNotBlank(deptIds)) {
            List<Long> deptIdList = oldConfigDO.listDeptIds();
            List<AttendanceUser> deptUsers = userService.listOnJobNonDriverUserByDeptIdList(deptIdList, oldConfigDO.getCountry());
            List<Long> deptUserIds = deptUsers.stream()
                    .map(AttendanceUser::getId)
                    .collect(Collectors.toList());
            //查询非国家级别的人员
            List<Long> existDeptUserIds = overTimeConfigRangeDao.listConfigRanges(deptUserIds)
                    .stream()
                    .filter(OverTimeConfigRangeDO::areNotCountryRange)
                    .map(OverTimeConfigRangeDO::getBizId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(existDeptUserIds)) {
                deptUserIds = deptUserIds.stream()
                        .filter(deptUserId -> !existDeptUserIds.contains(deptUserId))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(deptUserIds)) {
                for (Long deptUserId : deptUserIds) {
                    ruleConfigRangeChangeDTO.addUser(deptUserId);
                    buildConfigRange(deptUserId, newConfigDO.getId(),
                            newConfigDO.getConfigNo(),
                            RuleRangeTypeEnum.DEPT.getCode(), currentDateAndTimeZoneDate,
                            addConfigRangeList);
                }
            }
        }
        // 2.2 绑定员工级别的员工匹配
        for (Long noLatestUserId : disabledUserIds) {
            ruleConfigRangeChangeDTO.addUser(noLatestUserId);
            buildConfigRange(noLatestUserId, newConfigDO.getId(),
                    newConfigDO.getConfigNo(),
                    RuleRangeTypeEnum.USER.getCode(), currentDateAndTimeZoneDate,
                    addConfigRangeList);
        }

        // 更新现有配置范围
        if (!ruleConfigRangeChangeDTO.isUpdateUsersEmpty()) {
            // 查询启用最新的范围，并更新为过期
            List<OverTimeConfigRangeDO> updateList = overTimeConfigRangeDao.listConfigRanges(
                    ruleConfigRangeChangeDTO.getUpdateUserIdList());
            updateList.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                item.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                item.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                item.setRemark("页面操作导致旧规则过期");
                BaseDOUtil.fillDOUpdate(item);
            });
            updateConfigRangeList.addAll(updateList);
        }
        // 将历史的停用范围数据修改为非最新
        disabledConfigRangeDOS.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            item.setRemark("页面启用操作导致旧的停用规则为非最新");
            BaseDOUtil.fillDOUpdate(item);
        });
        updateConfigRangeList.addAll(disabledConfigRangeDOS);

        overTimeConfigManage.configRangeUpdateOrAdd(updateConfigRangeList, addConfigRangeList);

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 停用加班规则
     */
    public RuleConfigRangeChangeDTO disableConfigRangeHandler(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                              OverTimeConfigDO oldConfigDO,
                                                              OverTimeConfigDO newConfigDO,
                                                              CountryOverTimeConfig countryConfig,
                                                              String country) {
        // 确保集合非空
        List<Long> userIds = ensureNonNull(null);
        List<Long> deptIds = ensureNonNull(null);

        // 获取差异分析
        ConfigRangeDiff diff = calculateConfigUpdateRangeDiff(oldConfigDO, userIds, deptIds);

        // 获取受影响的部门用户
        List<Long> addDeptUserIds = getDepartmentUsers(diff.getAddDeptIds(), country);
        List<Long> delDeptUserIds = getDepartmentUsers(diff.getDelDeptIds(), country);

        // 创建和处理配置范围
        List<OverTimeConfigRangeDO> addConfigRangeList = new ArrayList<>();
        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO = new RuleConfigRangeChangeDTO();

        // 处理新增的用户和部门关联,todo 走不到这
        processNewAssociations(diff.getAddUserIds(), addDeptUserIds, newConfigDO, currentDateAndTimeZoneDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 处理删除的用户和部门关联
        processRemovedAssociations(diff.getDelUserIds(), delDeptUserIds,
                countryConfig, currentDateAndTimeZoneDate,
                addConfigRangeList, ruleConfigRangeChangeDTO);

        // 更新现有配置范围(无需更新最新标记，更新为停用和失效时间即可)，并添加新的配置范围
        if (!ruleConfigRangeChangeDTO.isUpdateUsersEmpty()) {
            List<OverTimeConfigRangeDO> updateList = overTimeConfigRangeDao.listConfigRanges(
                    ruleConfigRangeChangeDTO.getUpdateUserIdList());

            Map<Boolean, List<OverTimeConfigRangeDO>> updateRangeMap = updateList.stream()
                    .collect(Collectors.groupingBy(i -> Objects.equals(i.getRuleConfigId(), oldConfigDO.getId())));

            updateRangeMap.forEach(
                    (isBelongOldConfig, rangeDOList) -> {
                        if (isBelongOldConfig) {
                            rangeDOList.forEach(item -> {
                                item.setStatus(StatusEnum.DISABLED.getCode());
                                item.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                                item.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                                item.setRemark("页面操作导致旧规则停用和失效");
                                BaseDOUtil.fillDOUpdate(item);
                            });
                        }
                    });
            overTimeConfigManage.configRangeUpdateOrAdd(updateList, addConfigRangeList);
        }

        return ruleConfigRangeChangeDTO;
    }

    /**
     * 根据部门ID列表获取部门用户
     */
    private List<Long> getDepartmentUsers(List<Long> deptIds, String country) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return Lists.newArrayList();
        }

        List<AttendanceUser> users = userService.listOnJobNonDriverUserByDeptIdList(new ArrayList<>(deptIds), country);
        return users.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());
    }

    /**
     * 处理用户级别关联
     */
    private void processUserLevelAssociations(List<Long> userIds,
                                              OverTimeConfigDO configDO,
                                              DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                              List<OverTimeConfigRangeDO> addConfigRangeList,
                                              RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        for (Long userId : userIds) {
            ruleConfigRangeChangeDTO.addUser(userId);
            buildConfigRange(userId, configDO.getId(),
                    configDO.getConfigNo(),
                    RuleRangeTypeEnum.USER.getCode(), currentDateAndTimeZoneDate,
                    addConfigRangeList);
        }
    }

    /**
     * 构建配置范围对象
     */
    private void buildConfigRange(Long userId,
                                  Long configId,
                                  String configNo,
                                  String rangeType,
                                  DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                  List<OverTimeConfigRangeDO> addRangeList) {
        OverTimeConfigRangeDO rangeDO = new OverTimeConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(userId);
        rangeDO.setRuleConfigId(configId);
        rangeDO.setRuleConfigNo(configNo);
        rangeDO.setRangeType(rangeType);
        rangeDO.setEffectTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        rangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
        rangeDO.setEffectTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        rangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        rangeDO.setRemark("页面操作导致新规则生效");
        BaseDOUtil.fillDOInsert(rangeDO);
        addRangeList.add(rangeDO);
    }

    /**
     * 处理部门级别关联
     */
    private void processDeptLevelAssociations(List<Long> deptUserIds,
                                              OverTimeConfigDO configDO,
                                              DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                              List<OverTimeConfigRangeDO> addConfigRangeList,
                                              RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        if (CollectionUtils.isEmpty(deptUserIds)) {
            return;
        }

        // 查询部门级别新增的用户已存在的记录，并过滤
        List<Long> existUserIdList = filterExistingUserLevelUsers(deptUserIds);

        List<Long> filteredDeptUserIds = deptUserIds.stream()
                .filter(userId -> !existUserIdList.contains(userId))
                .collect(Collectors.toList());

        for (Long userId : filteredDeptUserIds) {
            if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                continue;
            }
            ruleConfigRangeChangeDTO.addUser(userId);
            buildConfigRange(userId, configDO.getId(),
                    configDO.getConfigNo(),
                    RuleRangeTypeEnum.DEPT.getCode(), currentDateAndTimeZoneDate,
                    addConfigRangeList);
        }
    }

    /**
     * 过滤已存在用户级别关联的用户
     */
    private List<Long> filterExistingUserLevelUsers(List<Long> userIds) {
        return overTimeConfigRangeDao.listConfigRanges(userIds)
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toList());
    }

    /**
     * 更新现有配置范围并添加新的配置范围
     */
    private void updateExistingAndAddNewRanges(RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO,
                                               List<OverTimeConfigRangeDO> addConfigRangeList,
                                               DateAndTimeZoneDate currentDateAndTimeZoneDate) {
        if (ruleConfigRangeChangeDTO.isUpdateUsersEmpty()) {
            return;
        }

        List<OverTimeConfigRangeDO> updateList = overTimeConfigRangeDao.listNotDeletedConfigRanges(
                ruleConfigRangeChangeDTO.getUpdateUserIdList());

        for (OverTimeConfigRangeDO item : updateList) {
            item.setIsLatest(BusinessConstant.N);
            item.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
            item.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
            item.setRemark("页面操作导致旧规则过期");
            BaseDOUtil.fillDOUpdate(item);
        }

        overTimeConfigManage.configRangeUpdateOrAdd(updateList, addConfigRangeList);
    }

    /**
     * 计算配置变动导致范围的差异
     */
    private ConfigRangeDiff calculateConfigUpdateRangeDiff(OverTimeConfigDO oldConfigDO,
                                                           List<Long> newUserIds,
                                                           List<Long> newDeptIds) {
        // 获取旧配置的用户级别人员
        List<Long> oldUserIds = overTimeConfigRangeDao.listByConfigIds(Collections.singletonList(oldConfigDO.getId()))
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        // 计算用户级别的增删
        List<Long> addUserIds = new ArrayList<>(newUserIds);

        List<Long> delUserIds = oldUserIds.stream()
                .filter(id -> !newUserIds.contains(id))
                .collect(Collectors.toList());

        // 获取旧配置的部门
        List<Long> oldDeptIds = oldConfigDO.listDeptIds();

        // 计算部门级别的增删
        List<Long> addDeptIds = new ArrayList<>(newDeptIds);

        List<Long> delDeptIds = oldDeptIds.stream()
                .filter(id -> !newDeptIds.contains(id))
                .collect(Collectors.toList());

        return new ConfigRangeDiff(addUserIds, delUserIds, addDeptIds, delDeptIds);
    }

    /**
     * 计算实际配置变动导致的范围差异
     */
    private ConfigRangeDiff calculateConfigRangeDiff(OverTimeConfigDO oldConfigDO,
                                                     List<Long> newUserIds,
                                                     List<Long> newDeptIds) {
        // 获取旧配置的用户级别人员
        List<Long> oldUserIds = overTimeConfigRangeDao.listByConfigIds(Collections.singletonList(oldConfigDO.getId()))
                .stream()
                .filter(item -> StringUtils.equals(item.getRangeType(), RuleRangeTypeEnum.USER.getCode()))
                .map(OverTimeConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        // 计算用户级别的增删
        List<Long> addUserIds = newUserIds.stream()
                .filter(id -> !oldUserIds.contains(id))
                .collect(Collectors.toList());

        List<Long> delUserIds = oldUserIds.stream()
                .filter(id -> !newUserIds.contains(id))
                .collect(Collectors.toList());

        // 获取旧配置的部门
        List<Long> oldDeptIds = oldConfigDO.listDeptIds();

        // 计算部门级别的增删
        List<Long> addDeptIds = newDeptIds.stream()
                .filter(id -> !oldDeptIds.contains(id))
                .collect(Collectors.toList());

        List<Long> delDeptIds = oldDeptIds.stream()
                .filter(id -> !newDeptIds.contains(id))
                .collect(Collectors.toList());

        return new ConfigRangeDiff(addUserIds, delUserIds, addDeptIds, delDeptIds);
    }

    /**
     * 处理新增的用户/部门关联
     */
    private void processNewAssociations(List<Long> addUserIds,
                                        List<Long> addDeptUserIds,
                                        OverTimeConfigDO configDO,
                                        DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                        List<OverTimeConfigRangeDO> addConfigRangeList,
                                        RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        // 处理用户级别关联
        processUserLevelAssociations(addUserIds, configDO, currentDateAndTimeZoneDate, addConfigRangeList, ruleConfigRangeChangeDTO);

        // 处理部门级别关联
        processDeptLevelAssociations(addDeptUserIds, configDO, currentDateAndTimeZoneDate, addConfigRangeList,
                ruleConfigRangeChangeDTO);
    }

    /**
     * 处理被移除的用户/部门关联
     */
    private void processRemovedAssociations(List<Long> delUserIds,
                                            List<Long> delDeptUserIds,
                                            CountryOverTimeConfig countryConfig,
                                            DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                            List<OverTimeConfigRangeDO> addConfigRangeList,
                                            RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        // 处理被移除的用户级别关联
        if (CollectionUtils.isNotEmpty(delUserIds)) {
            processRemovedUserLevelAssociations(
                    delUserIds, countryConfig,
                    delDeptUserIds, currentDateAndTimeZoneDate,
                    addConfigRangeList, ruleConfigRangeChangeDTO);
        }

        // 处理被移除的部门级别关联
        if (CollectionUtils.isNotEmpty(delDeptUserIds)) {
            processRemovedDeptLevelAssociations(
                    delDeptUserIds,
                    countryConfig,
                    currentDateAndTimeZoneDate,
                    addConfigRangeList, ruleConfigRangeChangeDTO);
        }
    }

    /**
     * 处理被移除的用户级别关联
     */
    private void processRemovedUserLevelAssociations(List<Long> delUserIds,
                                                     CountryOverTimeConfig countryConfig,
                                                     List<Long> delDeptUserIds,
                                                     DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                     List<OverTimeConfigRangeDO> addConfigRangeList,
                                                     RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        List<AttendanceUser> delUserInfoList = userService.listUsersByIds(delUserIds);

        // 获取所有可能需要的国家配置
        Map<String, CountryOverTimeConfig> countryConfigMap = getCountryPunchConfigMap(delUserInfoList, countryConfig);

        for (AttendanceUser user : delUserInfoList) {
            if (ruleConfigRangeChangeDTO.containsUser(user.getId())) {
                continue;
            }
            ruleConfigRangeChangeDTO.addRemovedUser(user.getId());

            String userCountry = user.getLocationCountry();

            // 判断用户是否应该进入兜底规则
            if (StringUtils.equals(countryConfig.getCountry(), userCountry)) {
                // 处理同国家的用户
                handleSameCountryUserRemoval(
                        user,
                        delDeptUserIds,
                        countryConfig,
                        currentDateAndTimeZoneDate,
                        addConfigRangeList
                );
            } else {
                // 处理不同国家的用户
                handleDifferentCountryUserRemoval(user, countryConfigMap, currentDateAndTimeZoneDate, addConfigRangeList);
            }
        }
    }

    /**
     * 处理同国家被移除用户的重新分配
     */
    private void handleSameCountryUserRemoval(AttendanceUser user,
                                              List<Long> delDeptUserIds,
                                              CountryOverTimeConfig countryConfig,
                                              DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                              List<OverTimeConfigRangeDO> addConfigRangeList) {
        // 如果用户属于被删除的部门，直接进入兜底规则
        if (delDeptUserIds.contains(user.getId())) {
            buildFallbackConfigRange(
                    user.getId(),
                    currentDateAndTimeZoneDate,
                    countryConfig,
                    addConfigRangeList
            );
            log.info("被删除员工={}属于被删除的部门中，降级为国家级规则", user.getId());
            return;
        }

        // 检查用户的部门是否被其他规则指定
        for (OverTimeConfigDO configDO : countryConfig.getCountryConfigs()) {
            if (StringUtils.isBlank(configDO.getDeptIds())) {
                continue;
            }
            List<Long> deptIds = configDO.listDeptIds();
            if (deptIds.contains(user.getDeptId())) {
                buildConfigRange(user.getId(), configDO.getId(),
                        configDO.getConfigNo(), RuleRangeTypeEnum.DEPT.getCode(),
                        currentDateAndTimeZoneDate, addConfigRangeList);
                return;
            }
        }

        // 如果部门没有被其他规则指定，使用兜底规则
        log.info("被删除员工={}的部门不属于其他规则，降级为国家级规则", user.getId());
        buildFallbackConfigRange(
                user.getId(),
                currentDateAndTimeZoneDate,
                countryConfig,
                addConfigRangeList
        );
    }

    /**
     * 处理不同国家被移除用户的重新分配
     */
    private void handleDifferentCountryUserRemoval(AttendanceUser user,
                                                   Map<String, CountryOverTimeConfig> countryConfigMap,
                                                   DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                   List<OverTimeConfigRangeDO> addConfigRangeList) {
        String userCountry = user.getLocationCountry();
        CountryOverTimeConfig userCountryConfig = countryConfigMap.getOrDefault(userCountry, CountryOverTimeConfig.empty());

        List<OverTimeConfigDO> userCountryConfigs = userCountryConfig.getCountryConfigs();
        // 如果用户国家没有打卡规则配置，记录日志并跳过
        if (CollectionUtils.isEmpty(userCountryConfigs)) {
            log.info("被删除用户：{}，国家：{}，没有对应的补卡规则，故用户当前无补卡规则",
                    user.getUserCode(), userCountry);
            return;
        }

        // 检查用户的部门是否被用户国家的其他规则指定
        boolean departmentRuleFound = false;
        for (OverTimeConfigDO configDO : userCountryConfigs) {
            if (StringUtils.isBlank(configDO.getDeptIds())) {
                continue;
            }

            List<Long> deptIds = configDO.listDeptIds();
            if (deptIds.contains(user.getDeptId())) {
                buildConfigRange(user.getId(), configDO.getId(),
                        configDO.getConfigNo(), RuleRangeTypeEnum.DEPT.getCode(),
                        currentDateAndTimeZoneDate, addConfigRangeList);
                departmentRuleFound = true;
                break;
            }
        }

        // 如果没有找到部门规则，使用用户国家的兜底规则
        if (!departmentRuleFound) {
            buildFallbackConfigRange(
                    user.getId(),
                    currentDateAndTimeZoneDate,
                    userCountryConfig,
                    addConfigRangeList
            );
            log.info("被删除用户：{}，国家：{}，用户所在部门没有绑定规则，用户进入国家级规则",
                    user.getUserCode(), userCountry);
        }
    }

    /**
     * 处理被移除的部门级别关联
     */
    private void processRemovedDeptLevelAssociations(List<Long> delDeptUserIds,
                                                     CountryOverTimeConfig countryConfig,
                                                     DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                     List<OverTimeConfigRangeDO> addConfigRangeList,
                                                     RuleConfigRangeChangeDTO ruleConfigRangeChangeDTO) {
        // 过滤已经被用户级别指定的用户
        List<Long> existUserIdList = filterExistingUserLevelUsers(delDeptUserIds);

        List<Long> filteredDelDeptUserIds = delDeptUserIds.stream()
                .filter(userId -> !existUserIdList.contains(userId))
                .collect(Collectors.toList());

        for (Long userId : filteredDelDeptUserIds) {
            if (ruleConfigRangeChangeDTO.containsUser(userId)) {
                continue;
            }
            ruleConfigRangeChangeDTO.addRemovedUser(userId);
            buildFallbackConfigRange(
                    userId,
                    currentDateAndTimeZoneDate,
                    countryConfig,
                    addConfigRangeList
            );
            log.info("被删除部门下的员工：{}没有在其他规则中被指定为员工级，降级为国家级规则", userId);
        }
    }

    /**
     * 获取删除用户所在国家的打卡规则配置映射
     */
    private Map<String, CountryOverTimeConfig> getCountryPunchConfigMap(List<AttendanceUser> delUserInfoList,
                                                                        CountryOverTimeConfig countryConfig) {
        // 获取需要删除的用户所在的所有国家
        Set<String> delUserCountrySet = delUserInfoList.stream()
                .map(AttendanceUser::getLocationCountry)
                .collect(Collectors.toSet());
        Map<String, CountryOverTimeConfig> countryConfigMap = new HashMap<>();
        // 添加当前国家的配置
        countryConfigMap.put(countryConfig.getCountry(), countryConfig);
        // 获取其他国家的配置
        for (String delUserCountry : delUserCountrySet) {
            if (!StringUtils.equals(countryConfig.getCountry(), delUserCountry)) {
                CountryOverTimeConfig delUserCountryConfig = overTimeConfigManage.getCountryConfig(delUserCountry);
                countryConfigMap.put(delUserCountry, delUserCountryConfig);
            }
        }
        return countryConfigMap;
    }

    /**
     * 构建兜底配置范围对象
     */
    private void buildFallbackConfigRange(Long userId,
                                          DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                          CountryOverTimeConfig countryConfig,
                                          List<OverTimeConfigRangeDO> addConfigRangeList) {
        if (countryConfig == null) {
            return;
        }
        OverTimeConfigDO countryLevelConfig = countryConfig.queryCountryLevelConfig();
        if (null == countryLevelConfig) {
            log.info("userId:{}的常驻国:{}没有国家级加班规则，无法为其分配规则", userId, countryConfig.getCountry());
            return;
        }
        buildBackToCountryConfigRange(
                userId,
                countryLevelConfig.getId(),
                countryLevelConfig.getConfigNo(),
                currentDateAndTimeZoneDate,
                addConfigRangeList
        );
    }

    /**
     * 构建回退到国家级范围对象
     */
    private void buildBackToCountryConfigRange(Long userId,
                                               Long configId,
                                               String configNo,
                                               DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                               List<OverTimeConfigRangeDO> addRangeList) {
        OverTimeConfigRangeDO rangeDO = new OverTimeConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(userId);
        rangeDO.setRuleConfigId(configId);
        rangeDO.setRuleConfigNo(configNo);
        rangeDO.setRangeType(RuleRangeTypeEnum.COUNTRY.getCode());
        rangeDO.setEffectTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        rangeDO.setExpireTime(DateAndTimeZoneDate.ofEndDate().getTimeZoneDate());
        rangeDO.setEffectTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
        rangeDO.setExpireTimestamp(DateAndTimeZoneDate.ofEndDate().getDateTimeStamp());
        rangeDO.setIsLatest(BusinessConstant.Y);
        rangeDO.setStatus(StatusEnum.ACTIVE.getCode());
        rangeDO.setRemark("页面操作导致回退到国家级规则");
        BaseDOUtil.fillDOInsert(rangeDO);
        addRangeList.add(rangeDO);
    }

}
