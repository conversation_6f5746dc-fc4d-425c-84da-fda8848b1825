package com.imile.attendance.rule.query;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/5/6
 * @Description 规则配置下拉选择查询
 */
@Data
public class RuleConfigSelectQuery {

    /**
     * 规则类型
     */
    @NotNull(message = "ruleType cannot be empty")
    private String ruleType;


    /**
     * 国家三字码
     */
    private String country;

    /**
     * 规则名称(用于模糊查询)
     */
    private String ruleName;
}