package com.imile.attendance.rule.dto;

import lombok.Data;

/**
 * 考勤规则适用用户数量统计
 * 用于统计不同级别配置下的适用用户数量
 *
 * <AUTHOR> chen
 * @since 2025/4/15
 */
@Data
public class RuleConfigApplyUserCountDTO {

    /**
     * 用户级别配置的用户数量
     * 直接指定具体用户的配置数量
     */
    private Integer userLevelUserCount;

    /**
     * 部门级别配置的用户数量
     * 通过部门批量配置的用户数量
     */
    private Integer deptLevelUserCount;

    /**
     * 国家级别配置的用户数量
     * 按国家维度配置的用户数量（在职非司机且未配置规则的用户）
     */
    private Integer countryLevelUserCount;

    /**
     * 创建一个计数器初始对象
     * 所有计数字段初始化为0
     *
     * @return 初始化的计数对象
     */
    public static RuleConfigApplyUserCountDTO init() {
        RuleConfigApplyUserCountDTO applyUserCountDTO = new RuleConfigApplyUserCountDTO();
        applyUserCountDTO.setUserLevelUserCount(0);
        applyUserCountDTO.setDeptLevelUserCount(0);
        applyUserCountDTO.setCountryLevelUserCount(0);
        return applyUserCountDTO;
    }

    /**
     * 获取规则适用的总用户数量
     * 计算所有级别配置的用户总和
     *
     * @return 规则适用的总用户数量
     */
    public Integer getApplyUserSize() {
        return userLevelUserCount + deptLevelUserCount + countryLevelUserCount;
    }

    /**
     * 获取国家级别配置的用户数量
     *
     * @return 国家级别配置的用户数量
     */
    public Integer getCountryLevelUserSize(){
        return countryLevelUserCount;
    }
}