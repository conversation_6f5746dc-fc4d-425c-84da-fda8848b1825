package com.imile.attendance.rule.dto;

import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.util.PunchTimeCalculator;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/20 
 * @Description
 */
@Slf4j
@Data
public class DayNormalPunchTimeDTO {

    private Date dayPunchStartTime;

    private Date dayPunchEndTime;

    private Date dayPunchRestStartTime;

    private Date dayPunchRestEndTime;

    /**
     * 计算班次项目的具体上下班时间
     * @param dayPunchTimeDTO 天打卡时间框架
     * @return 具体上下班时间
     * @description
     * 开始
     * │
     * ├── 1. 判空检查
     * │   ├── 若 `punchInTime` 或 `punchOutTime` 为空 → 返回 `null`
     * │   └── 否则继续
     * │
     * ├── 2. 判断班次是否跨天（核心条件：`punchInTime >= punchOutTime`）
     * │   ├── 是（如：23:00-05:00）
     * │   │   ├── 设置起始时间：`dayPunchStartTime` + `punchInTime`
     * │   │   ├── 设置结束时间：`dayPunchEndTime` + `punchOutTime`
     * │   │   └── 处理休息时间（可能跨天）
     * │   └── 否（如：09:00-17:00）
     * │       ├── 判断 `punchInTime` 是否 ≥ `earliestPunchInTime`
     * │       │   ├── 是（如：09:00 ≥ 08:00）
     * │       │   │   ├── 起始和结束时间均基于 `dayPunchStartTime`
     * │       │   │   └── 处理休息时间（不跨天）
     * │       │   └── 否（如：07:00 < 08:00）
     * │       │       ├── 起始和结束时间基于 `dayPunchEndTime`
     * │       │       └── 处理休息时间（可能跨天）
     * │       └── 处理休息时间
     * │
     * └── 返回结果
     *
     * | **场景类型**       | **时间范围示例**          | **适用规则**                     |
     * |--------------------|--------------------------|----------------------------------|
     * | 夜班（跨天）       | 23:00-05:00             | 起始时间在前一天，结束在次日      |
     * | 白班（非跨天）     | 09:00-17:00             | 起始和结束在同一天                |
     * | 跨午夜休息         | 23:30-00:30             | 休息时间跨越两天                  |
     * | 常规午休           | 12:00-13:00             | 休息时间在班次内，不跨天          |
     * | 特殊排班（如早班） | 05:00-13:00             | 根据 `earliestPunchInTime` 调整  |
     */
    public static DayNormalPunchTimeDTO calculateNormalPunchTime(DayPunchTimeDTO dayPunchTimeDTO,
                                                                 PunchClassItemConfigDO punchClassItemConfigDO) {
        if (punchClassItemConfigDO.getPunchInTime() == null || punchClassItemConfigDO.getPunchOutTime() == null) {
            log.info("calculateNormalPunchTime - 上下班时间配置缺失，班次ID: {}", punchClassItemConfigDO.getId());
            return null;
        }

        DayNormalPunchTimeDTO result = new DayNormalPunchTimeDTO();
        Date punchInTime = punchClassItemConfigDO.getPunchInTime();
        Date punchOutTime = punchClassItemConfigDO.getPunchOutTime();

        // 处理本时刻跨天的情况
        if (punchInTime.compareTo(punchOutTime) > -1) {
            log.info("calculateNormalPunchTime - 检测到班次跨天配置");
            result.setDayPunchStartTime(PunchTimeCalculator.getDateFromDateAndTime(
                    dayPunchTimeDTO.getDayPunchStartTime(), punchInTime));
            result.setDayPunchEndTime(PunchTimeCalculator.getDateFromDateAndTime(
                    dayPunchTimeDTO.getDayPunchEndTime(), punchOutTime));
            handleRestTime(punchInTime, punchOutTime, punchClassItemConfigDO, result);
            log.info("calculateNormalPunchTime - 跨天班次计算完成，上班时间: {}, 下班时间: {}",
                    result.getDayPunchStartTime(), result.getDayPunchEndTime());
            return result;
        }

        // 处理本时刻在一天内的情况,需要判断是整天的起始那天，还是结束那天
        // 处理本时刻在一天内的情况
        boolean isStartDay = punchInTime.compareTo(punchClassItemConfigDO.getEarliestPunchInTime()) > -1;
        log.info("calculateNormalPunchTime - 班次在一天内，属于{}的那天",
                isStartDay ? "起始时间" : "截止时间");
        if (isStartDay) {
            // 属于起始时间的那天
            result.setDayPunchStartTime(PunchTimeCalculator.getDateFromDateAndTime(
                    dayPunchTimeDTO.getDayPunchStartTime(), punchInTime));
            result.setDayPunchEndTime(PunchTimeCalculator.getDateFromDateAndTime(
                    dayPunchTimeDTO.getDayPunchStartTime(), punchOutTime));
        } else {
            // 属于截止时间的那天
            result.setDayPunchStartTime(PunchTimeCalculator.getDateFromDateAndTime(
                    dayPunchTimeDTO.getDayPunchEndTime(), punchInTime));
            result.setDayPunchEndTime(PunchTimeCalculator.getDateFromDateAndTime(
                    dayPunchTimeDTO.getDayPunchEndTime(), punchOutTime));
        }

        handleRestTime(punchInTime, punchOutTime, punchClassItemConfigDO, result);
        log.debug("calculateNormalPunchTime - 同天班次计算完成，上班时间: {}, 下班时间: {}",
                result.getDayPunchStartTime(), result.getDayPunchEndTime());
        return result;
    }

    /**
     * 处理休息时间
     */
    private static void handleRestTime(Date punchInTime,
                                       Date punchOutTime,
                                       PunchClassItemConfigDO punchClassItemConfigDO,
                                       DayNormalPunchTimeDTO result) {
        try {
            Date restStartTime = punchClassItemConfigDO.getRestStartTime();
            Date restEndTime = punchClassItemConfigDO.getRestEndTime();

            if (restStartTime == null) {
                log.debug("handleRestTime - 休息开始时间为空，跳过休息时间处理");
                return;
            }

            if (restStartTime.compareTo(punchInTime) > -1) {
                Date dayRestStartTime = PunchTimeCalculator.getDateFromDateAndTime(
                        result.getDayPunchStartTime(), restStartTime);
                result.setDayPunchRestStartTime(dayRestStartTime);

                Date dayRestEndTime = PunchTimeCalculator.getDateFromDateAndTime(
                        result.getDayPunchStartTime(), restEndTime);
                result.setDayPunchRestEndTime(dayRestEndTime);

                // 休息时间跨天了
                if (restStartTime.compareTo(restEndTime) > -1) {
                    log.info("handleRestTime - 检测到休息时间跨天配置");
                    dayRestEndTime = PunchTimeCalculator.getDateFromDateAndTime(
                            result.getDayPunchEndTime(), restEndTime);
                    result.setDayPunchRestEndTime(dayRestEndTime);
                }
                log.debug("handleRestTime - 休息时间处理完成，开始: {}, 结束: {}",
                        result.getDayPunchRestStartTime(), result.getDayPunchRestEndTime());
                return;
            }
            log.info("handleRestTime - 休息时间跨天处理");
            // 肯定跨天了
            Date dayRestStartTime = PunchTimeCalculator.getDateFromDateAndTime(
                    result.getDayPunchEndTime(), restStartTime);
            result.setDayPunchRestStartTime(dayRestStartTime);

            Date dayRestEndTime = PunchTimeCalculator.getDateFromDateAndTime(
                    result.getDayPunchEndTime(), restEndTime);
            result.setDayPunchRestEndTime(dayRestEndTime);
            log.debug("handleRestTime - 休息时间处理完成，开始: {}, 结束: {}",
                    result.getDayPunchRestStartTime(), result.getDayPunchRestEndTime());
        } catch (Exception e) {
            log.error("handleRestTime - 处理休息时间异常: {}", e.getMessage(), e);
        }
    }
}
