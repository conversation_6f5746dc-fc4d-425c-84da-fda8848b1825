package com.imile.attendance.rule;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigRangeDO;
import com.imile.attendance.rule.bo.PunchConfigBO;
import com.imile.attendance.rule.dto.ConfigRangeDTO;
import com.imile.attendance.rule.dto.PunchConfigDetailDTO;
import com.imile.attendance.rule.dto.RuleConfigApplyUserCountDTO;
import com.imile.attendance.rule.dto.RuleConfigApplyUserDTO;
import com.imile.attendance.rule.mapstruct.PunchConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/10 
 * @Description
 */
@Slf4j
@Service
public class PunchConfigQueryService {

    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchConfigDao punchConfigDao;
    @Resource
    private PunchConfigRangeDao punchConfigRangeDao;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;

    /**
     * 查询打卡配置的适用用户列表
     * 支持三种场景：
     * 1. 国家级别配置：返回该国家下所有在职非司机且未配置规则的用户
     * 2. 部门级别配置：返回配置的部门下的所有在职用户
     * 3. 用户级别配置：返回直接配置的在职用户列表
     *
     * @param configNo 打卡配置编号
     * @return 适用用户列表，包含：
     *         - userLevelUserList: 用户级别配置的用户列表
     *         - deptLevelUserList: 部门级别配置的用户列表
     *         - countryLevelUserList: 国家级别配置的用户列表
     * @throws BusinessLogicException 当配置不存在时抛出异常
     */
    public RuleConfigApplyUserDTO queryApplyUser(String configNo) {
        // 获取打卡配置和范围
        PunchConfigBO punchConfigBO = punchConfigManage.getPunchConfigBO(configNo);
        if (null == punchConfigBO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        RuleConfigApplyUserDTO ruleConfigApplyUserDTO = RuleConfigApplyUserDTO.init();
        List<Long> countryRangeIds = punchConfigBO.queryCountryRangeIds();
        if (CollectionUtils.isNotEmpty(countryRangeIds)) {
            ruleConfigApplyUserDTO.setCountryLevelUserList(userService.listOnJobUsers(countryRangeIds));
        }
        List<Long> userRangeIds = punchConfigBO.queryUserRangeIds();
        if (CollectionUtils.isNotEmpty(userRangeIds)) {
            ruleConfigApplyUserDTO.setUserLevelUserList(userService.listOnJobUsers(userRangeIds));
        }
        List<Long> deptRangeIds = punchConfigBO.queryDeptRangeIds();
        if (CollectionUtils.isNotEmpty(deptRangeIds)) {
            ruleConfigApplyUserDTO.setDeptLevelUserList(userService.listOnJobUsers(deptRangeIds));
        }
        return ruleConfigApplyUserDTO;
    }


    /**
     * 查询打卡规则适用用户数量统计
     * 根据配置编号统计不同级别下的适用用户数量
     *
     * 支持三种统计场景：
     * 1. 国家级别：统计指定国家下所有在职非司机且未配置规则的用户数量
     * 2. 部门级别：统计配置的部门下所有在职用户数量
     * 3. 用户级别：统计直接配置的在职用户数量
     *
     * @param configNo 打卡配置编码
     * @return 用户数量统计结果
     * @throws BusinessLogicException 当配置不存在时抛出异常
     */
    public RuleConfigApplyUserCountDTO queryApplyUserCount(String configNo) {
        // 获取打卡配置和范围
        PunchConfigBO punchConfigBO = punchConfigManage.getPunchConfigBO(configNo);
        if (null == punchConfigBO) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        RuleConfigApplyUserCountDTO ruleConfigApplyUserDTO = RuleConfigApplyUserCountDTO.init();
        List<Long> countryRangeIds = punchConfigBO.queryCountryRangeIds();
        if (CollectionUtils.isNotEmpty(countryRangeIds)) {
            ruleConfigApplyUserDTO.setCountryLevelUserCount(userService.listOnJobUsers(countryRangeIds).size());
        }
        List<Long> userRangeIds = punchConfigBO.queryUserRangeIds();
        if (CollectionUtils.isNotEmpty(userRangeIds)) {
            ruleConfigApplyUserDTO.setUserLevelUserCount(userService.listOnJobUsers(userRangeIds).size());
        }
        List<Long> deptRangeIds = punchConfigBO.queryDeptRangeIds();
        if (CollectionUtils.isNotEmpty(deptRangeIds)) {
            ruleConfigApplyUserDTO.setDeptLevelUserCount(userService.listOnJobUsers(deptRangeIds).size());
        }
        return ruleConfigApplyUserDTO;
    }


    /**
     * 查询打卡配置详情
     * @param configNo 配置编码
     * @return 打卡配置详情
     */
    public PunchConfigDetailDTO queryPunchConfigDetail(String configNo) {
        // 获取打卡配置和范围(不需要最新启用的)
        List<PunchConfigDO> punchConfigDOList = punchConfigDao.listByConfigNo(configNo);
        if (CollectionUtils.isEmpty(punchConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        PunchConfigDO configDO = punchConfigDOList.get(0);
        PunchConfigDetailDTO punchConfigDetailDTO = PunchConfigMapstruct.INSTANCE.toDetailDTO(configDO);

        List<PunchConfigRangeDO> punchConfigRangeDOList = punchConfigRangeDao.listNotDeletedByConfigId(configDO.getId());

        List<Long> userRangeList = punchConfigRangeDOList.stream()
                .filter(PunchConfigRangeDO::areUserRange)
                .map(PunchConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(userRangeList)) {
            punchConfigDetailDTO.setApplyUserList(userService.listOnJobUsers(userRangeList)
                    .stream()
                    .map(ConfigRangeDTO::buildUserRangeDTO)
                    .collect(Collectors.toList()));
        }
        // 设置 deptRecords
        if (StringUtils.isNotBlank(configDO.getDeptIds())) {
            List<Long> deptIds = configDO.listDeptIds();
            punchConfigDetailDTO.setApplyDeptList(deptService.listByDeptIds(deptIds).stream()
                    .map(ConfigRangeDTO::buildDeptRangeDTO)
                    .collect(Collectors.toList()));
        }
        return punchConfigDetailDTO;
    }

}
