package com.imile.attendance.rule.vo;

import com.imile.attendance.annon.WithDict;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigExportVO implements Serializable {

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型
     */
    @WithDict(typeCode = "PunchClassType", ref = "classTypeDesc")
    private String classType;

    /**
     * 班次类型描述
     */
    private String classTypeDesc;

    /**
     * 状态
     */
    private String status;

    /**
     * 时段
     */
    private Integer sortNo;

    /**
     * 总法定工作时长（不包含休息时间）
     */
    private BigDecimal totalLegalWorkingHours;

    /**
     * 总出勤时长（包含休息时间）
     */
    private BigDecimal totalAttendanceHours;

    /**
     * 上班时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private String punchInTime;

    /**
     * 下班时间 仅有时分秒信息，形如：1970-01-01 18:00:00
     */
    private String punchOutTime;

    /**
     * 最早上班打卡时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private String earliestPunchInTime;

    /**
     * 最晚上班打卡时间 仅有时分秒信息
     */

    private String latestPunchInTime;

    /**
     * 最晚下班打卡时间 仅有时分秒信息，形如：1970-01-01 19:00:00
     */
    private String latestPunchOutTime;

    /**
     * 弹性时间
     */
    private BigDecimal elasticTime;

    /**
     * 休息开始时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private String restStartTime;

    /**
     * 休息结束时间 仅有时分秒信息，形如：1970-01-01 09:00:00
     */
    private String restEndTime;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private String deptStr;

    /**
     * 适用人员
     */
    private String userStr;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private String createDateStr;

    /**
     * 最近修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改时间
     */
    private String lastUpdDateStr;

}
