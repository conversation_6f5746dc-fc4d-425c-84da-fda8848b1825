package com.imile.attendance.rule.dto;

import java.util.Date;
import java.util.List;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/10
 * @Description
 */
@Data
public class PunchConfigPageDTO {

    private Long id;

    /**
     * 状态
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型
     */
    @WithDict(typeCode = BusinessConstant.HermesDict.PUNCH_RULE_TYPE, ref = "punchConfigTypeDesc")
    private String punchConfigType;

    /**
     * 打卡规则类型描述
     */
    private String punchConfigTypeDesc;

    /**
     * 适用范围
     */
    private List<ConfigRangeDTO> rangeRecords;

    /**
     * 已绑定员工数
     */
    private Integer employeeCount;

    /**
     * 创建人
     */
    private String createUserName;

    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;

    /**
     * 最近修改日期
     */
    private Date lastUpdDate;

    // ========导出字段================
    /**
     * 适用范围-国家 导出使用
     */
    private String countryRangeStr;

    /**
     * 适用范围-部门 导出使用
     */
    private String deptRangeStr;

    /**
     * 适用范围-人员 导出使用
     */
    private String userRangeStr;

    /**
     * 创建日期 导出使用
     */
    private String createDateStr;

    /**
     * 最近修改日期 导出使用
     */
    private String lastUpdDateStr;
}
