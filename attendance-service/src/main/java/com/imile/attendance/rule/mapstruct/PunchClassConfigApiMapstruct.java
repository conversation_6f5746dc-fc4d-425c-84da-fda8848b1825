package com.imile.attendance.rule.mapstruct;

import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.query.PunchClassConfigQuery;
import com.imile.attendance.rule.command.PunchClassConfigAddCommand;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;

import com.imile.attendance.rule.query.PunchClassConfigListQuery;
import com.imile.attendance.rule.vo.PunchClassConfigDetailVO;
import com.imile.attendance.rule.vo.PunchClassItemConfigVO;
import com.imile.attendance.util.DateHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@Mapper(config = MapperConfiguration.class)
public interface PunchClassConfigApiMapstruct {

    PunchClassConfigApiMapstruct INSTANCE = Mappers.getMapper(PunchClassConfigApiMapstruct.class);


    @Mapping(target = "classItemConfigList", ignore = true)
    PunchClassConfigAddDTO toDTO(PunchClassConfigAddCommand command);

    @Mapping(target = "classItemConfigList", ignore = true)
    @Mapping(target = "deptIds", ignore = true)
    PunchClassConfigDetailVO toVO(PunchClassConfigDTO dto);

    PunchClassConfigQuery toQuery(PunchClassConfigListQuery query);

    PunchClassConfigDO toDO(PunchClassConfigDTO dto);

    PunchClassItemConfigDTO toPunchClassItemConfigDTO(PunchClassItemConfigDO punchClassItemConfigDO);

    List<PunchClassItemConfigDTO> toPunchClassItemConfigDTO(List<PunchClassItemConfigDO> punchClassItemConfigDOList);


    default PunchClassConfigAddDTO toAddDTO(PunchClassConfigAddCommand command) {
        PunchClassConfigAddDTO result = toDTO(command);
        if (CollectionUtils.isEmpty(command.getClassItemConfigList())) {
            return result;
        }
        List<PunchClassItemConfigDTO> classItemConfigList = command.getClassItemConfigList().stream().map(itemConfig -> {
            PunchClassItemConfigDTO itemConfigDTO = new PunchClassItemConfigDTO();
            itemConfigDTO.setId(itemConfig.getId());
            itemConfigDTO.setPunchClassId(itemConfig.getPunchClassId());
            itemConfigDTO.setSortNo(itemConfig.getSortNo());
            itemConfigDTO.setPunchInTime(DateHelper.appendDefaultDateToTime(itemConfig.getPunchInTime()));
            itemConfigDTO.setPunchOutTime(DateHelper.appendDefaultDateToTime(itemConfig.getPunchOutTime()));
            itemConfigDTO.setEarliestPunchInTime(DateHelper.appendDefaultDateToTime(itemConfig.getEarliestPunchInTime()));
            itemConfigDTO.setLatestPunchInTime(DateHelper.appendDefaultDateToTime(itemConfig.getLatestPunchInTime()));
            itemConfigDTO.setLatestPunchOutTime(DateHelper.appendDefaultDateToTime(itemConfig.getLatestPunchOutTime()));
            itemConfigDTO.setRestStartTime(DateHelper.appendDefaultDateToTime(itemConfig.getRestStartTime()));
            itemConfigDTO.setRestEndTime(DateHelper.appendDefaultDateToTime(itemConfig.getRestEndTime()));
            itemConfigDTO.setIsAcross(itemConfig.getIsAcross());
            itemConfigDTO.setElasticTime(itemConfig.getElasticTime());
            itemConfigDTO.setLegalWorkingHours(itemConfig.getLegalWorkingHours());
            itemConfigDTO.setAttendanceHours(itemConfig.getAttendanceHours());
            return itemConfigDTO;
        }).collect(Collectors.toList());
        result.setClassItemConfigList(classItemConfigList);
        return result;
    }

    default PunchClassConfigDetailVO toDetailVO(PunchClassConfigDTO dto) {
        PunchClassConfigDetailVO result = toVO(dto);
        if (CollectionUtils.isEmpty(dto.getClassItemConfigList())) {
            return result;
        }
        List<PunchClassItemConfigVO> classItemConfigList = dto.getClassItemConfigList().stream().map(itemConfig -> {
            PunchClassItemConfigVO itemConfigVO = new PunchClassItemConfigVO();
            itemConfigVO.setId(itemConfig.getId());
            itemConfigVO.setPunchClassId(itemConfig.getPunchClassId());
            itemConfigVO.setSortNo(itemConfig.getSortNo());
            itemConfigVO.setPunchInTime(DateHelper.formatHHMMSS(itemConfig.getPunchInTime()));
            itemConfigVO.setPunchOutTime(DateHelper.formatHHMMSS(itemConfig.getPunchOutTime()));
            itemConfigVO.setEarliestPunchInTime(DateHelper.formatHHMMSS(itemConfig.getEarliestPunchInTime()));
            itemConfigVO.setLatestPunchInTime(DateHelper.formatHHMMSS(itemConfig.getLatestPunchInTime()));
            itemConfigVO.setLatestPunchOutTime(DateHelper.formatHHMMSS(itemConfig.getLatestPunchOutTime()));
            itemConfigVO.setRestStartTime(DateHelper.formatHHMMSS(itemConfig.getRestStartTime()));
            itemConfigVO.setRestEndTime(DateHelper.formatHHMMSS(itemConfig.getRestEndTime()));
            itemConfigVO.setIsAcross(itemConfig.getIsAcross());
            itemConfigVO.setElasticTime(itemConfig.getElasticTime());
            itemConfigVO.setLegalWorkingHours(itemConfig.getLegalWorkingHours());
            itemConfigVO.setAttendanceHours(itemConfig.getAttendanceHours());
            return itemConfigVO;
        }).collect(Collectors.toList());
        result.setClassItemConfigList(classItemConfigList);
        return result;
    }

    default PunchClassItemConfigVO toItemVO(PunchClassItemConfigDO itemConfig) {
        if (Objects.isNull(itemConfig)) {
            return null;
        }
        PunchClassItemConfigVO itemConfigVO = new PunchClassItemConfigVO();
        itemConfigVO.setId(itemConfig.getId());
        itemConfigVO.setPunchClassId(itemConfig.getPunchClassId());
        itemConfigVO.setSortNo(itemConfig.getSortNo());
        itemConfigVO.setPunchInTime(DateHelper.formatHHMMSS(itemConfig.getPunchInTime()));
        itemConfigVO.setPunchOutTime(DateHelper.formatHHMMSS(itemConfig.getPunchOutTime()));
        itemConfigVO.setEarliestPunchInTime(DateHelper.formatHHMMSS(itemConfig.getEarliestPunchInTime()));
        itemConfigVO.setLatestPunchInTime(DateHelper.formatHHMMSS(itemConfig.getLatestPunchInTime()));
        itemConfigVO.setLatestPunchOutTime(DateHelper.formatHHMMSS(itemConfig.getLatestPunchOutTime()));
        itemConfigVO.setRestStartTime(DateHelper.formatHHMMSS(itemConfig.getRestStartTime()));
        itemConfigVO.setRestEndTime(DateHelper.formatHHMMSS(itemConfig.getRestEndTime()));
        itemConfigVO.setIsAcross(itemConfig.getIsAcross());
        itemConfigVO.setElasticTime(itemConfig.getElasticTime());
        itemConfigVO.setLegalWorkingHours(itemConfig.getLegalWorkingHours());
        itemConfigVO.setAttendanceHours(itemConfig.getAttendanceHours());
        return itemConfigVO;
    }

}
