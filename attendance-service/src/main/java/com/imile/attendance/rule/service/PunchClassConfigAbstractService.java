package com.imile.attendance.rule.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.UserAttendanceAbnormalTrigger;
import com.imile.attendance.calendar.dto.CalendarAndPunchHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.dto.DateAndTimeZoneDate;
import com.imile.attendance.employee.UserClassNatureModifyRecordManage;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ClassTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.employee.dao.UserClassNatureModifyRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserClassNatureModifyRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigRangeDao;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigRangeDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigRangeDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.dto.PunchClassConfigAddAutoShiftDTO;
import com.imile.attendance.rule.dto.PunchClassConfigAddDTO;
import com.imile.attendance.rule.dto.PunchClassConfigRangeDifferDTO;
import com.imile.attendance.rule.dto.PunchClassConfigUpdateAutoShiftDTO;
import com.imile.attendance.rule.event.PunchClassConfigEventPublisher;
import com.imile.attendance.rule.mapstruct.PunchClassConfigApiMapstruct;
import com.imile.attendance.rule.mapstruct.PunchClassConfigRangeMapstruct;
import com.imile.attendance.rule.vo.PunchClassConfigAddConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigAddVO;
import com.imile.attendance.rule.vo.PunchClassConfigDisabledCheckConfirmVO;
import com.imile.attendance.rule.vo.PunchClassConfigUpdateConfirmVO;
import com.imile.attendance.shift.ShiftTaskService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 班次服务抽象Service
 *
 * <AUTHOR>
 * @since 2025/4/14
 */

public abstract class PunchClassConfigAbstractService implements PunchClassConfigService {

    private static final Logger log = LoggerFactory.getLogger(PunchClassConfigAbstractService.class);

    @Autowired
    protected PunchClassConfigDao punchClassConfigDao;
    @Autowired
    protected PunchClassItemConfigDao punchClassItemConfigDao;
    @Autowired
    protected PunchClassConfigRangeDao punchClassConfigRangeDao;
    @Autowired
    protected UserInfoDao userInfoDao;
    @Autowired
    protected UserInfoManage userInfoManage;
    @Autowired
    protected AttendanceDeptService attendanceDeptService;
    @Autowired
    protected PunchClassConfigManage punchClassConfigManage;
    @Resource
    protected UserShiftConfigDao userShiftConfigDao;
    @Autowired
    protected DefaultIdWorker defaultIdWorker;
    @Autowired
    protected PunchClassConfigEventPublisher punchClassConfigEventPublisher;
    @Resource
    protected LogRecordService logRecordService;
    @Resource
    protected UserShiftConfigManage userShiftConfigManage;
    @Resource
    protected CountryService countryService;
    @Resource
    protected ShiftTaskService shiftTaskService;
    @Resource
    protected AutoShiftConfigFactory autoShiftConfigFactory;
    @Resource
    protected UserClassNatureModifyRecordDao userClassNatureModifyRecordDao;
    @Resource
    protected UserClassNatureModifyRecordManage userClassNatureModifyRecordManage;
    @Resource
    protected UserAttendanceAbnormalTrigger userAttendanceAbnormalTrigger;

    private static final String CLASS_NAME_REGEX = "^.{0,100}$";


    /**
     * 班次新增前置处理
     */
    @Override
    public PunchClassConfigAddConfirmVO addPreProcessor(PunchClassConfigAddDTO dto) {
        //班次保存基础参数校验
        saveOrUpdateBaseCheck(dto);

        //查询改国家下的班次配置信息
        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigManage.selectByCountries(dto.getCountry(), dto.getClassNature());

        //班次时段信息重复性校验
        classRuleDuplicateCheck(dto.getItemNum(), dto.getClassType(), dto.getLegalWorkingHours(), dto.getAttendanceHours(), dto.getClassItemConfigList(), punchClassConfigDTOList);

        PunchClassConfigAddConfirmVO result = new PunchClassConfigAddConfirmVO();

        //固定班次参数校验
        PunchClassConfigAddVO punchClassConfigAddVO = customCheck(dto);
        if (!punchClassConfigAddVO.getSuccess()) {
            result.setPunchClassConfigAddVO(punchClassConfigAddVO);
            return result;
        }

        result.setEmployeeCount(getClassRangeUserList(dto.getCountry(), dto.getClassNature(), dto.getUserIdList(), dto.getDeptIds()).size());
        return result;
    }


    /**
     * 班次新增
     */
    @Override
    public PunchClassConfigAddVO add(PunchClassConfigAddDTO dto) {
        //班次保存基础参数校验
        saveOrUpdateBaseCheck(dto);

        //查询改国家下的班次配置信息
        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigManage.selectByCountries(dto.getCountry(), dto.getClassNature());

        //班次时段信息重复性校验
        classRuleDuplicateCheck(dto.getItemNum(), dto.getClassType(), dto.getLegalWorkingHours(), dto.getAttendanceHours(), dto.getClassItemConfigList(), punchClassConfigDTOList);

        //自定义参数校验
        PunchClassConfigAddVO result = customCheck(dto);
        if (!result.getSuccess()) {
            return result;
        }

        PunchClassConfigDO addPunchClassConfigDO = new PunchClassConfigDO();
        List<PunchClassItemConfigDO> addPunchClassItemConfigDOList = new ArrayList<>();
        List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList = new ArrayList<>();
        List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList = new ArrayList<>();

        CountryDTO countryDTO = countryService.queryCountry(dto.getCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        //构建班次规则、班次适应范围对象
        classConfigAddDataBuild(currentDateAndTimeZoneDate, dto, addPunchClassConfigDO, addPunchClassItemConfigDOList,
                addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);
        dto.setId(addPunchClassConfigDO.getId());

        //构建自动排班影响适用范围
        PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO = buildPunchClassConfigAddAutoShiftDTO(dto);

        //校验班次适用范围内用户是否存在未结束的其他排班任务
        if (checkIsOccupiedOtherSchedulingTasks(classConfigAddAutoShiftDTO.getNewRangeUserIdList(), OperationTypeEnum.PUNCH_CLASS_CONFIG_ADD)) {
            throw BusinessException.get(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getMessage()));
        }

        //持久化
        punchClassConfigManage.punchClassConfigAdd(addPunchClassConfigDO, addPunchClassItemConfigDOList, addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);

        //日志
        logRecordService.recordOperation(addPunchClassConfigDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.PUNCH_CLASS_CONFIG_ADD.getCode())
                        .country(addPunchClassConfigDO.getCountry())
                        .bizName(addPunchClassConfigDO.getClassName())
                        .build());

        //班次新增自动排班
        addPostProcessor(classConfigAddAutoShiftDTO);
        return result;
    }


    /**
     * 班次编辑前置处理
     */
    @Override
    public PunchClassConfigUpdateConfirmVO updatePreProcessor(PunchClassConfigAddDTO dto) {
        //班次保存基础参数校验
        saveOrUpdateBaseCheck(dto);

        //查询改国家下的班次配置信息（不包含自己）
        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigManage.selectByCountries(dto.getCountry(), dto.getClassNature())
                .stream().filter(config -> !Objects.equals(config.getId(), dto.getId())).collect(Collectors.toList());

        //班次时段信息重复性校验
        classRuleDuplicateCheck(dto.getItemNum(), dto.getClassType(), dto.getLegalWorkingHours(), dto.getAttendanceHours(), dto.getClassItemConfigList(), punchClassConfigDTOList);

        //班次信息查询
        PunchClassConfigDTO punchClassConfigDTO = Optional.ofNullable(punchClassConfigManage.selectLatestAndActiveById(dto.getId()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc())));

        PunchClassConfigUpdateConfirmVO result = new PunchClassConfigUpdateConfirmVO();
        result.setClassNature(dto.getClassNature());

        //自定义班次参数校验
        PunchClassConfigAddVO punchClassConfigAddVO = customCheck(dto);
        if (!punchClassConfigAddVO.getSuccess()) {
            result.setPunchClassConfigAddVO(punchClassConfigAddVO);
            return result;
        }

        //班次信息是否变更
        Boolean isClassInfoUpdate = checkPunchClassConfigDiffer(dto.getItemNum(), dto.getClassType(), dto.getLegalWorkingHours(),
                dto.getAttendanceHours(), dto.getClassItemConfigList(), punchClassConfigDTO);

        //设置班次编辑适用范围人数变动
        buildChangeClassRangeCount(isClassInfoUpdate, punchClassConfigDTO, dto, result);
        return result;
    }


    /**
     * 班次编辑
     */
    @Override
    public PunchClassConfigAddVO update(PunchClassConfigAddDTO updateDto) {
        //班次保存基础参数校验
        saveOrUpdateBaseCheck(updateDto);

        //班次信息查询
        PunchClassConfigDTO oldDto = Optional.ofNullable(punchClassConfigManage.selectLatestAndActiveById(updateDto.getId()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc())));

        //查询改国家下的班次配置信息（不包含自己）
        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigManage.selectByCountries(updateDto.getCountry(), updateDto.getClassNature())
                .stream().filter(config -> !Objects.equals(config.getId(), updateDto.getId())).collect(Collectors.toList());

        //班次时段信息重复性校验
        classRuleDuplicateCheck(updateDto.getItemNum(), updateDto.getClassType(), updateDto.getLegalWorkingHours(), updateDto.getAttendanceHours(), updateDto.getClassItemConfigList(), punchClassConfigDTOList);

        //自定义班次参数校验
        PunchClassConfigAddVO result = customCheck(updateDto);
        if (!result.getSuccess()) {
            return result;
        }

        PunchClassConfigDO addPunchClassConfigDO = new PunchClassConfigDO();
        List<PunchClassConfigDO> updatePunchClassConfigDOList = new ArrayList<>();
        List<PunchClassItemConfigDO> addPunchClassItemConfigDOList = new ArrayList<>();
        List<PunchClassItemConfigDO> udpatePunchClassItemConfigDOList = new ArrayList<>();
        List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList = new ArrayList<>();
        List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList = new ArrayList<>();

        //班次规则信息是否变更（不包含班次名称判断）
        Boolean isClassInfoUpdate = checkPunchClassConfigDiffer(updateDto.getItemNum(), updateDto.getClassType(), updateDto.getLegalWorkingHours(),
                updateDto.getAttendanceHours(), updateDto.getClassItemConfigList(), oldDto);
        //班次适用范围是否变更
        PunchClassConfigRangeDifferDTO configRangeDifferDTO = judgePunchClassConfigRangeUpdate(updateDto, oldDto, isClassInfoUpdate);

        log.info("班次编辑适用范围变更: {}", JSON.toJSONString(configRangeDifferDTO));

        if (!isClassInfoUpdate && !configRangeDifferDTO.getRangeUpdate()) {
            if (!Objects.equals(updateDto.getClassName(), oldDto.getClassName())) {
                //班次名称变化
                punchClassConfigDao.updateClassName(updateDto.getId(), updateDto.getClassName());
                //日志
                logRecordService.recordOperation(
                        addPunchClassConfigDO,
                        LogRecordOptions.buildWithRemark(OperationTypeEnum.PUNCH_CLASS_CONFIG_UPDATE.getCode(),
                                buildClassUpdateLogRecord(updateDto, oldDto)));
            }
            return result;
        }

        //校验班次适用范围内用户是否存在未结束的其他排班任务
        Set<Long> userIds = getAllCheckUserIds(configRangeDifferDTO);
        if (checkIsOccupiedOtherSchedulingTasks(userIds, OperationTypeEnum.PUNCH_CLASS_CONFIG_UPDATE)) {
            throw BusinessException.get(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getMessage()));
        }

        CountryDTO countryDTO = countryService.queryCountry(oldDto.getCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        if (isClassInfoUpdate) {
            updateConfigToHistoryVersion(currentDateAndTimeZoneDate, oldDto, updatePunchClassConfigDOList, udpatePunchClassItemConfigDOList, updatePunchClassConfigRangeDOList);
        } else {
            buildNewPunchClassConfig(updateDto, oldDto, updatePunchClassConfigDOList);
        }
        punchClassConfigManage.punchClassConfigUpdate(updatePunchClassConfigDOList, udpatePunchClassItemConfigDOList, updatePunchClassConfigRangeDOList);

        //新版本构造
        classConfigUpdateBuild(isClassInfoUpdate, currentDateAndTimeZoneDate, oldDto, updateDto, configRangeDifferDTO.getRemoveRangeUserList(), addPunchClassConfigDO,
                addPunchClassItemConfigDOList, addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);

        punchClassConfigManage.punchClassConfigAdd(addPunchClassConfigDO, addPunchClassItemConfigDOList, addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);

        //日志
        logRecordService.recordOperation(
                addPunchClassConfigDO,
                LogRecordOptions.buildWithRemark(OperationTypeEnum.PUNCH_CLASS_CONFIG_UPDATE.getCode(),
                        buildClassUpdateLogRecord(updateDto, oldDto))
        );

        //班次编辑自动排班
        PunchClassConfigUpdateAutoShiftDTO punchClassConfigUpdateAutoShiftDTO = PunchClassConfigUpdateAutoShiftDTO.builder()
                .oldClassId(oldDto.getId())
                .newClassId(isClassInfoUpdate ? addPunchClassConfigDO.getId() : oldDto.getId())
                .classRuleUpdate(isClassInfoUpdate)
                .isOnlyClearSchedule(updateDto.getClearSchedule())
                .addRangeUserIdList(configRangeDifferDTO.getAddRangeUserList())
                .removeRangeUserIdList(configRangeDifferDTO.getRemoveRangeUserList())
                .noChangeRangeUserIdList(configRangeDifferDTO.getNoChangeRangeUserList())
                .build();
        updatePostProcessor(punchClassConfigUpdateAutoShiftDTO);

        return result;
    }

    /**
     * 班次停用校验
     */
    @Override
    public PunchClassConfigDisabledCheckConfirmVO disabledCheck(Long classId) {
        PunchClassConfigDisabledCheckConfirmVO result = new PunchClassConfigDisabledCheckConfirmVO();
        PunchClassConfigDO punchClassConfigDO = punchClassConfigDao.selectById(classId);
        if (Objects.isNull(punchClassConfigDO)) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc()));
        }
        if (Objects.equals(StatusEnum.DISABLED.getCode(), punchClassConfigDO.getStatus())) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_STATUS_ALREADY_DISABLED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_STATUS_ALREADY_DISABLED.getDesc()));
        }

        Long dayId = getDayIdByCountryTimeZone(punchClassConfigDO.getCountry());
        List<Long> existShiftUserIds = userShiftConfigManage.getUserIdsByPunchClassIdAndDayId(punchClassConfigDO.getId(), dayId);
        if (existShiftUserIds.size() > 0) {
            throw BusinessException.get(ErrorCodeEnum.PROHIBIT_DISABLED_CLASS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.PROHIBIT_DISABLED_CLASS.getDesc()));
        }

        List<Long> existFutureShiftUserIds = userShiftConfigManage.getUserIdsByPunchClassIdAndAfterDayId(punchClassConfigDO.getId(), dayId);
        result.setEmployeeCount(existFutureShiftUserIds.size());
        return result;
    }

    /**
     * 班次启用校验
     */
    @Override
    public void enableCheck(PunchClassConfigDTO classConfigDTO) {
        if (Objects.equals(StatusEnum.ACTIVE.getCode(), classConfigDTO.getStatus())) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_STATUS_ALREADY_ENABLE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_STATUS_ALREADY_ENABLE.getDesc()));
        }

        String classNature = classConfigDTO.getClassNature();

        //班次名称重复
        List<PunchClassConfigDO> punchClassConfigList = punchClassConfigDao.selectLatestByClassName(classConfigDTO.getClassName(), classNature);
        if (CollectionUtils.isNotEmpty(punchClassConfigList)) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_ENABLE_CLASS_NAME_DUPLICATE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_ENABLE_CLASS_NAME_DUPLICATE.getDesc()));
        }

        //查询改国家下的班次配置信息
        List<PunchClassConfigDTO> punchClassConfigDTOList = punchClassConfigManage.selectByCountries(classConfigDTO.getCountry(), classNature);

        //班次时段信息重复性校验
        classRuleDuplicateCheck(classConfigDTO.getItemNum(), classConfigDTO.getClassType(), classConfigDTO.getLegalWorkingHours(),
                classConfigDTO.getAttendanceHours(), classConfigDTO.getClassItemConfigList(), punchClassConfigDTOList);

        //班次适用范围自定义校验
        classRangeCustomCheck(classConfigDTO, punchClassConfigDTOList);
    }


    @Override
    public Boolean enableStatus(Long classId) {
        PunchClassConfigDTO classConfigDTO = Optional.ofNullable(punchClassConfigManage.selectById(classId))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc())));
        //启用检查
        this.enableCheck(classConfigDTO);

        Map<String, Set<Long>> userIdMap = getClassRangeUserIds(classConfigDTO);

        Set<Long> allUserIds = userIdMap.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        if (checkIsOccupiedOtherSchedulingTasks(allUserIds, OperationTypeEnum.PUNCH_CLASS_CONFIG_ACTIVE)) {
            throw BusinessException.get(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getMessage()));
        }

        CountryDTO countryDTO = countryService.queryCountry(classConfigDTO.getCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        //固定班次启用查询低优先级的适用范围进行更新
        if (ClassNatureEnum.FIXED_CLASS.name().equals(classConfigDTO.getClassNature())) {
            Set<Long> userRangeIds = userIdMap.get(RuleRangeTypeEnum.USER.getCode());
            punchClassConfigRangeDao.updateToOldByBizIds(userRangeIds, Lists.newArrayList(RuleRangeTypeEnum.COUNTRY.getCode(), RuleRangeTypeEnum.DEPT.getCode()));

            Set<Long> deptRangeIds = userIdMap.get(RuleRangeTypeEnum.DEPT.getCode());
            punchClassConfigRangeDao.updateToOldByBizIds(deptRangeIds, Lists.newArrayList(RuleRangeTypeEnum.COUNTRY.getCode()));
        }

        List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList = new ArrayList<>();
        buildClassEnableRangeAddData(currentDateAndTimeZoneDate, classConfigDTO, addPunchClassConfigRangeDOList);

        //更新启用状态
        punchClassConfigManage.enableStatus(classId, addPunchClassConfigRangeDOList);

        //日志
        logRecordService.recordOperation(PunchClassConfigApiMapstruct.INSTANCE.toDO(classConfigDTO),
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ACTIVE)
                        .operationType(OperationTypeEnum.PUNCH_CLASS_CONFIG_ACTIVE.getCode())
                        .country(classConfigDTO.getCountry())
                        .bizName(classConfigDTO.getClassName())
                        .build());

        //后置处理
        Set<Long> userIds = addPunchClassConfigRangeDOList.stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toSet());
        switchStatusPostProcessor(classId, userIds, StatusEnum.ACTIVE.getCode());

        return Boolean.TRUE;
    }


    @Override
    public Boolean disabledStatus(Long classId) {
        //停用检查
        this.disabledCheck(classId);

        PunchClassConfigDTO classConfigDTO = Optional.ofNullable(punchClassConfigManage.selectLatestAndActiveById(classId))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.CLASS_NOT_EXISTS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NOT_EXISTS.getDesc())));

        //获取适用范围人员, key: rangeType,value: 用户ID集合
        Map<String, Set<Long>> userIdMap = getClassRangeUserIds(classConfigDTO);

        Set<Long> allUserIds = userIdMap.values().stream()
                .flatMap(Set::stream)
                .collect(Collectors.toSet());
        if (checkIsOccupiedOtherSchedulingTasks(allUserIds, OperationTypeEnum.PUNCH_CLASS_CONFIG_ACTIVE)) {
            throw BusinessException.get(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getMessage()));
        }

        CountryDTO countryDTO = countryService.queryCountry(classConfigDTO.getCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(new Date());

        //固定班次停用降级查询更低优先级的班次规则并插入新的适用范围
        List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList = new ArrayList<>();
        Set<Long> downgradeUserIdList = new HashSet<>();
        Set<Long> userRangeIds = userIdMap.get(RuleRangeTypeEnum.USER.getCode());
        if (CollectionUtils.isNotEmpty(userRangeIds)) {
            downgradeUserIdList.addAll(userRangeIds);
        }
        Set<Long> deptRangeIds = userIdMap.get(RuleRangeTypeEnum.DEPT.getCode());
        if (CollectionUtils.isNotEmpty(deptRangeIds)) {
            downgradeUserIdList.addAll(deptRangeIds);
        }
        downgradeBindingClassRangeList(classConfigDTO.getClassNature(), downgradeUserIdList, currentDateAndTimeZoneDate, "班次停用降级重新绑定", addPunchClassConfigRangeDOList);

        if (CollectionUtils.isNotEmpty(addPunchClassConfigRangeDOList)) {
            punchClassConfigRangeDao.saveBatch(addPunchClassConfigRangeDOList);
        }

        //更新停用状态
        punchClassConfigManage.disabledStatus(classId, currentDateAndTimeZoneDate);

        //日志
        logRecordService.recordOperation(PunchClassConfigApiMapstruct.INSTANCE.toDO(classConfigDTO),
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DISABLE)
                        .operationType(OperationTypeEnum.PUNCH_CLASS_CONFIG_DISABLED.getCode())
                        .country(classConfigDTO.getCountry())
                        .bizName(classConfigDTO.getClassName())
                        .build());

        //后置处理
        switchStatusPostProcessor(classId, allUserIds, StatusEnum.DISABLED.getCode());
        return Boolean.TRUE;
    }


    @Override
    public void classNatureSwitchHandler(UserInfoDO userInfoDO,
                                         String oldClassNature,
                                         String newClassNature,
                                         DateAndTimeZoneDate currentDateAndTimeZoneDate) {

        if (checkIsOccupiedOtherSchedulingTasks(new HashSet<>(Collections.singletonList(userInfoDO.getId())), OperationTypeEnum.CLASS_NATURE_SWITCH)) {
            throw BusinessException.get(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getCode(), I18nUtils.getMessage(ErrorCodeEnum.USER_SHIFT_IS_OCCUPIED.getMessage()));
        }

        userInfoDO.setClassNature(newClassNature);
        userInfoDao.updateById(userInfoDO);

        if (StringUtils.isNotBlank(oldClassNature)) {
            //查询用户旧的班次性质关联的所有班次
            List<PunchClassConfigDTO> punchClassConfigList = punchClassConfigManage.selectUserClassConfigList(userInfoDO.getId(), oldClassNature);
            if (CollectionUtils.isNotEmpty(punchClassConfigList)) {
                List<Long> classIds = punchClassConfigList.stream().map(PunchClassConfigDTO::getId).collect(Collectors.toList());

                //更新关联的班次适用范围为历史版本
                punchClassConfigManage.updateRangeByBizIdAndClassId(userInfoDO.getId(), classIds, currentDateAndTimeZoneDate);

                //清理排班
                UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
                userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.getByCode(oldClassNature));
                UserAutoShiftParam.PunchClassRemoveUserParam punchClassRemoveUserParam = new UserAutoShiftParam.PunchClassRemoveUserParam();
                UserAutoShiftParam.PunchClassRemoveSingleUserParam removeSingleUserParam = new UserAutoShiftParam.PunchClassRemoveSingleUserParam();
                removeSingleUserParam.setUserId(userInfoDO.getId());
                removeSingleUserParam.setShiftStartDayId(getDayIdByCountryTimeZone(punchClassConfigList.get(0).getCountry()));
                if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), oldClassNature)) {
                    removeSingleUserParam.setClearClassIdList(punchClassConfigList.stream().map(PunchClassConfigDTO::getId).collect(Collectors.toList()));
                }
                punchClassRemoveUserParam.setClassRemoveSingleUserParams(Collections.singletonList(removeSingleUserParam));
                punchClassRemoveUserParam.setIsOnlyClearShift(Boolean.TRUE);
                userAutoShiftParam.setPunchClassRemoveUserParam(punchClassRemoveUserParam);
                autoShiftConfigFactory.autoShift(userAutoShiftParam);
            }
        }

        //添加到新的班次适用范围中
        bindNewClassRange(userInfoDO, currentDateAndTimeZoneDate);

        UserClassNatureModifyRecordDO oldUserClassNatureModifyRecord = userClassNatureModifyRecordDao.selectLatestByUserId(userInfoDO.getId());
        if (Objects.nonNull(oldUserClassNatureModifyRecord)) {
            oldUserClassNatureModifyRecord.setIsLatest(BusinessConstant.N);
            oldUserClassNatureModifyRecord.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        }

        UserClassNatureModifyRecordDO userClassNatureModifyRecordDO = new UserClassNatureModifyRecordDO();
        userClassNatureModifyRecordDO.setId(defaultIdWorker.nextId());
        userClassNatureModifyRecordDO.setUserId(userInfoDO.getId());
        userClassNatureModifyRecordDO.setClassNature(newClassNature);
        userClassNatureModifyRecordDO.setEffectTime(currentDateAndTimeZoneDate.getTimeZoneDate());
        userClassNatureModifyRecordDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
        userClassNatureModifyRecordDO.setIsLatest(BusinessConstant.Y);
        userClassNatureModifyRecordDO.setRemark("员工档案班次性质变更");
        BaseDOUtil.fillDOInsertByUsrOrSystem(userClassNatureModifyRecordDO);
        userClassNatureModifyRecordManage.userClassNatureUpdate(oldUserClassNatureModifyRecord, userClassNatureModifyRecordDO);

        //固定班次判断是否重新排班
        classNatureSwitchPostProcessor(userInfoDO.getId());
    }

    @Override
    public void classSchedulingHandler(CalendarAndPunchHandlerDTO dto, String classNature) {
        //查询用户旧的最新班次记录
        List<PunchClassConfigDTO> oldPunchClassConfigList = punchClassConfigManage.selectUserClassConfigList(dto.getUserId(), classNature);

        //查询用户新的最新班次记录
        List<PunchClassConfigDTO> newPunchClassConfigList = punchClassConfigManage.selectByCountries(dto.getNewCountry(), classNature);

        log.info("oldPunchClassConfigList : {}", JSON.toJSONString(oldPunchClassConfigList));
        log.info("newPunchClassConfigList : {}", JSON.toJSONString(newPunchClassConfigList));

        List<PunchClassConfigRangeDO> updateRunchClassConfigRangeList = new ArrayList<>();
        List<PunchClassConfigRangeDO> addRunchClassConfigRangeList = new ArrayList<>();

        CountryDTO countryDTO = countryService.queryCountry(dto.getOldCountry());
        DateAndTimeZoneDate currentDateAndTimeZoneDate = countryDTO.getDateAndTimeZoneDate(dto.getCurrentDate());

        //需要更新排班为历史版本的班次ID集合
        Set<Long> waitUpdateShiftClassIds = new HashSet<>();

        //非用户级别的班次适用范围需要更新成历史版本,排班也需要更新为历史版本
        oldPunchClassConfigList.forEach(oldRecord -> {
            if (CollectionUtils.isEmpty(oldRecord.getClassConfigRangeList())) {
                return;
            }

            List<PunchClassConfigRangeDTO> noUserRangeTypeList = oldRecord.getClassConfigRangeList()
                    .stream().filter(range -> !Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType())
                            && Objects.equals(range.getBizId(), dto.getUserId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(noUserRangeTypeList)) {
                return;
            }

            waitUpdateShiftClassIds.add(oldRecord.getId());

            List<PunchClassConfigRangeDO> rangeList = PunchClassConfigRangeMapstruct.INSTANCE.toDOList(noUserRangeTypeList);
            rangeList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                range.setRemark("员工国家或部门变动");
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
            updateRunchClassConfigRangeList.addAll(rangeList);
        });

        bindNewClassRangeList(dto, newPunchClassConfigList, addRunchClassConfigRangeList);

        punchClassConfigManage.punchClassConfigRangeUpdate(updateRunchClassConfigRangeList, addRunchClassConfigRangeList);
        userShiftConfigDao.updateToHistory(dto.getUserId(), DateHelper.getDayId(currentDateAndTimeZoneDate.getTimeZoneDate()), new ArrayList<>(waitUpdateShiftClassIds));
    }

    /**
     * 班次编辑适用范围人数变化
     */
    protected abstract void buildChangeClassRangeCount(Boolean isClassInfoUpdate,
                                                       PunchClassConfigDTO oldDto,
                                                       PunchClassConfigAddDTO newDto,
                                                       PunchClassConfigUpdateConfirmVO result);

    /**
     * 班次性质切换绑定新班次
     */
    protected abstract void bindNewClassRange(UserInfoDO userInfoDO, DateAndTimeZoneDate currentDateAndTimeZoneDate);

    /**
     * 绑定新的班次适用范围
     */
    protected abstract void bindNewClassRangeList(CalendarAndPunchHandlerDTO classHandlerDto,
                                                  List<PunchClassConfigDTO> newPunchClassConfigList,
                                                  List<PunchClassConfigRangeDO> addRunchClassConfigRangeList);


    /**
     * 班次新增自定义校验
     */
    protected abstract PunchClassConfigAddVO customCheck(PunchClassConfigAddDTO dto);


    /**
     * 构建班次新增自动排班参数
     */
    protected abstract PunchClassConfigAddAutoShiftDTO buildPunchClassConfigAddAutoShiftDTO(PunchClassConfigAddDTO dto);


    /**
     * 班次新增后置处理
     */
    protected abstract void addPostProcessor(PunchClassConfigAddAutoShiftDTO classConfigAddAutoShiftDTO);


    /**
     * 班次编辑后置处理
     */
    protected void updatePostProcessor(PunchClassConfigUpdateAutoShiftDTO punchClassConfigUpdateAutoShiftDTO) {
        punchClassConfigEventPublisher.sendPunchClassConfigUpdateEvent(punchClassConfigUpdateAutoShiftDTO);
    }

    /**
     * 班次适用范围启用自定义校验
     */
    protected abstract void classRangeCustomCheck(PunchClassConfigDTO classConfigDTO, List<PunchClassConfigDTO> punchClassConfigDTOList);


    /**
     * 班次启停用后置处理
     */
    protected abstract void switchStatusPostProcessor(Long classId, Set<Long> userIds, String status);

    /**
     * 检查班次适用范围是否存在未结束的其他排班任务
     */
    protected abstract Boolean checkIsOccupiedOtherSchedulingTasks(Set<Long> userIds, OperationTypeEnum operationTypeEnum);


    /**
     * 班次性质切换后置处理
     */
    protected abstract void classNatureSwitchPostProcessor(Long userId);


    private void buildNewPunchClassConfig(PunchClassConfigAddDTO updateDto,
                                          PunchClassConfigDTO oldDto,
                                          List<PunchClassConfigDO> updatePunchClassConfigDOList) {
        PunchClassConfigDO updatePunchClassConfigDO = PunchClassConfigApiMapstruct.INSTANCE.toDO(oldDto);
        updatePunchClassConfigDO.setDeptIds(StringUtils.join(updateDto.getDeptIds(), BusinessConstant.DEFAULT_DELIMITER));
        updatePunchClassConfigDO.setItemNum(updateDto.getItemNum());
        updatePunchClassConfigDO.setIsCountryLevel(updateDto.countryLevel() ? BusinessConstant.Y : BusinessConstant.N);
        updatePunchClassConfigDO.setClassType(updateDto.getClassType());
        updatePunchClassConfigDO.setAttendanceHours(updateDto.getAttendanceHours());
        updatePunchClassConfigDO.setLegalWorkingHours(updateDto.getLegalWorkingHours());
        updatePunchClassConfigDO.setIsLatest(BusinessConstant.Y);
        updatePunchClassConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(updatePunchClassConfigDO);
        updatePunchClassConfigDOList.add(updatePunchClassConfigDO);
    }

    /**
     * 班次新增基础参数校验
     */
    private void saveOrUpdateBaseCheck(PunchClassConfigAddDTO addDTO) {
        //班次名称
        if (!addDTO.getClassName().matches(CLASS_NAME_REGEX)) {
            throw BusinessException.get(ErrorCodeEnum.CLASS_NAME_LENGTH_LIMIT_OF_100.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NAME_LENGTH_LIMIT_OF_100.getDesc()));
        }

        //班次名称重复
        List<PunchClassConfigDO> punchClassConfigList = punchClassConfigDao.selectLatestByClassName(addDTO.getClassName(), addDTO.getClassNature());
        if (Objects.isNull(addDTO.getId())) {
            if (CollectionUtils.isNotEmpty(punchClassConfigList)) {
                throw BusinessException.get(ErrorCodeEnum.CLASS_NAME_DUPLICATE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NAME_DUPLICATE.getDesc()));
            }
        } else {
            if (CollectionUtils.isNotEmpty(punchClassConfigList) && !punchClassConfigList.stream().map(PunchClassConfigDO::getId).collect(Collectors.toList()).contains(addDTO.getId())) {
                throw BusinessException.get(ErrorCodeEnum.CLASS_NAME_DUPLICATE.getCode(), I18nUtils.getMessage(ErrorCodeEnum.CLASS_NAME_DUPLICATE.getDesc()));
            }
            Optional<PunchClassConfigDO> classConfigDOOptional = punchClassConfigList.stream().filter(config -> Objects.equals(config.getId(), addDTO.getId())).findFirst();
            if (classConfigDOOptional.isPresent() && Objects.equals(StatusEnum.DISABLED.getCode(), classConfigDOOptional.get().getStatus())) {
                throw BusinessException.get(ErrorCodeEnum.DISABLE_SHIFT_PROHIBIT_EDIT.getCode(), I18nUtils.getMessage(ErrorCodeEnum.DISABLE_SHIFT_PROHIBIT_EDIT.getDesc()));
            }
        }

        //适用人员
        if (CollectionUtils.isNotEmpty(addDTO.getUserIdList())) {
            List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserIdsAndClassNature(addDTO.getUserIdList(), addDTO.getClassNature());
            if (CollectionUtils.isEmpty(userInfoDOList)) {
                throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
            }

            if (!Objects.equals(userInfoDOList.size(), addDTO.getUserIdList().size())) {
                log.info("param userId size:{},userInfoList size:{}", addDTO.getUserIdList().size(), userInfoDOList.size());
                throw BusinessException.get(ErrorCodeEnum.ACCOUNT_MISMATCH.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_MISMATCH.getDesc()));
            }
        }

        //出勤时长 = 工作时长 + 休息时长, 总工时时长、总出勤时长
        BigDecimal totalLegalWorkingHours = BigDecimal.ZERO;
        BigDecimal totalAttendanceHours = BigDecimal.ZERO;
        for (PunchClassItemConfigDTO itemConfigDTO : addDTO.getClassItemConfigList()) {
            BigDecimal legalWorkingHours = Objects.isNull(itemConfigDTO.getLegalWorkingHours()) ? BigDecimal.ZERO : itemConfigDTO.getLegalWorkingHours();
            BigDecimal attendanceHours = Objects.isNull(itemConfigDTO.getAttendanceHours()) ? BigDecimal.ZERO : itemConfigDTO.getAttendanceHours();
            if (Objects.nonNull(itemConfigDTO.getRestStartTime()) && Objects.nonNull(itemConfigDTO.getRestEndTime())) {
                BigDecimal restHours = calculateHourDifference(itemConfigDTO.getRestStartTime(), itemConfigDTO.getRestEndTime());
                if (attendanceHours.compareTo(legalWorkingHours.add(restHours)) != 0) {
                    throw BusinessException.get(ErrorCodeEnum.REST_HOURS_ABNORMAL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.REST_HOURS_ABNORMAL.getDesc()));
                }
            }

            totalLegalWorkingHours = totalLegalWorkingHours.add(legalWorkingHours);
            totalAttendanceHours = totalAttendanceHours.add(attendanceHours);
        }

        if (addDTO.getLegalWorkingHours().compareTo(totalLegalWorkingHours) != 0) {
            throw BusinessException.get(ErrorCodeEnum.LEGAL_WORKING_HOURS_ABNORMAL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.LEGAL_WORKING_HOURS_ABNORMAL.getDesc()));
        }
        if (addDTO.getAttendanceHours().compareTo(totalAttendanceHours) != 0) {
            throw BusinessException.get(ErrorCodeEnum.ATTENDANCE_HOURS_ABNORMAL.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ATTENDANCE_HOURS_ABNORMAL.getDesc()));
        }
    }


    /**
     * 计算时间差 单位（小时）
     *
     * @param restStartTime 休息开始时间
     * @param restEndTime   休息结束时间
     * @return 小时
     */
    private BigDecimal calculateHourDifference(Date restStartTime, Date restEndTime) {
        // 使用时区，根据实际情况调整
        ZoneId zone = ZoneId.systemDefault();

        LocalDateTime startDateTime = restStartTime.toInstant().atZone(zone).toLocalDateTime();
        LocalDateTime endDateTime = restEndTime.toInstant().atZone(zone).toLocalDateTime();

        // 如果结束时间早于开始时间，则加1天
        if (endDateTime.isBefore(startDateTime)) {
            endDateTime = endDateTime.plusDays(1);
        }

        Duration duration = Duration.between(startDateTime, endDateTime);

        // 转换为小时数（含小数）
        return Convert.toBigDecimal(duration.toMillis() / 3600000.0);
    }


    /**
     * 校验班次信时段信息重复
     */
    private void classRuleDuplicateCheck(Integer itemNum,
                                         Integer classType,
                                         BigDecimal legalWorkingHours,
                                         BigDecimal attendanceHours,
                                         List<PunchClassItemConfigDTO> classItemConfigList,
                                         List<PunchClassConfigDTO> punchClassConfigDTOList) {
        if (CollectionUtils.isEmpty(punchClassConfigDTOList)) {
            return;
        }
        for (PunchClassConfigDTO punchClassConfigDTO : punchClassConfigDTOList) {
            if (!checkPunchClassConfigDiffer(itemNum, classType, legalWorkingHours, attendanceHours, classItemConfigList, punchClassConfigDTO)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.CLASS_INFO_DUPLICATE, punchClassConfigDTO.getClassName());
            }
        }
    }


    /**
     * 校验班次信息是否不同
     */
    private Boolean checkPunchClassConfigDiffer(Integer itemNum,
                                                Integer classType,
                                                BigDecimal legalWorkingHours,
                                                BigDecimal attendanceHours,
                                                List<PunchClassItemConfigDTO> classItemConfigList,
                                                PunchClassConfigDTO old) {
        if (!Objects.equals(itemNum, old.getItemNum())
                || !Objects.equals(classType, old.getClassType())
                || legalWorkingHours.compareTo(old.getLegalWorkingHours()) != 0
                || attendanceHours.compareTo(old.getAttendanceHours()) != 0
        ) {
            return Boolean.TRUE;
        }

        if (classItemConfigList.size() != old.getClassItemConfigList().size()) {
            return Boolean.TRUE;
        }

        for (int item = 0; item < itemNum; item++) {
            PunchClassItemConfigDTO newRecord = classItemConfigList.get(item);
            PunchClassItemConfigDTO oldRecord = old.getClassItemConfigList().get(item);
            if (oldRecord.judgePunchClassItemConfigUpdate(newRecord)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    /**
     * 班次规则、适用范围信息新增构造
     */
    protected void classConfigAddDataBuild(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                           PunchClassConfigAddDTO addDTO,
                                           PunchClassConfigDO classConfigDO,
                                           List<PunchClassItemConfigDO> addPunchClassItemConfigDOList,
                                           List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                           List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        //班次规则信息构造
        classConfigDO.setId(defaultIdWorker.nextId());
        classConfigDO.setIsLatest(BusinessConstant.Y);
        classConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
        classConfigDO.setClassNature(addDTO.getClassNature());
        classConfigDO.setLegalWorkingHours(addDTO.getLegalWorkingHours());
        classConfigDO.setAttendanceHours(addDTO.getAttendanceHours());
        classConfigDO.setItemNum(addDTO.getItemNum());
        classConfigDO.setConfigNo(defaultIdWorker.nextPunchClassConfigNo());
        classConfigDO.setClassName(addDTO.getClassName());
        classConfigDO.setClassType(addDTO.getClassType());
        classConfigDO.setCountry(addDTO.getCountry());
        classConfigDO.setDeptIds(StringUtils.join(addDTO.getDeptIds(), BusinessConstant.DEFAULT_DELIMITER));
        classConfigDO.setIsCountryLevel(addDTO.countryLevel() ? BusinessConstant.Y : BusinessConstant.N);
        BaseDOUtil.fillDOInsert(classConfigDO);
        for (PunchClassItemConfigDTO item : addDTO.getClassItemConfigList()) {
            PunchClassItemConfigDO classItemConfigDO = new PunchClassItemConfigDO();
            classItemConfigDO.setId(defaultIdWorker.nextId());
            classItemConfigDO.setPunchClassId(classConfigDO.getId());
            classItemConfigDO.setSortNo(item.getSortNo());
            classItemConfigDO.setPunchInTime(item.getPunchInTime());
            classItemConfigDO.setPunchOutTime(item.getPunchOutTime());
            classItemConfigDO.setEarliestPunchInTime(item.getEarliestPunchInTime());
            classItemConfigDO.setLatestPunchInTime(item.getLatestPunchInTime());
            classItemConfigDO.setLatestPunchOutTime(item.getLatestPunchOutTime());
            // 判断是否跨天
            if (Objects.nonNull(classItemConfigDO.getPunchInTime()) && Objects.nonNull(classItemConfigDO.getPunchOutTime())) {
                classItemConfigDO.setIsAcross(DateHelper.judgeCrossDay(
                        classItemConfigDO.getPunchInTime(), classItemConfigDO.getPunchOutTime()));
            }
            classItemConfigDO.setIsLatest(BusinessConstant.Y);
            classItemConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
            classItemConfigDO.setElasticTime(item.getElasticTime());
            classItemConfigDO.setRestStartTime(item.getRestStartTime());
            classItemConfigDO.setRestEndTime(item.getRestEndTime());
            classItemConfigDO.setAttendanceHours(item.getAttendanceHours());
            classItemConfigDO.setLegalWorkingHours(item.getLegalWorkingHours());
            BaseDOUtil.fillDOInsert(classItemConfigDO);
            addPunchClassItemConfigDOList.add(classItemConfigDO);
        }

        //适用范围构造
        classConfigRangeAddBuild(addDTO, classConfigDO, addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList, currentDateAndTimeZoneDate);
    }

    /**
     * 班次适用范围新增构造
     */
    protected abstract void classConfigRangeAddBuild(PunchClassConfigAddDTO addDTO,
                                                     PunchClassConfigDO classConfigDO,
                                                     List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                                     List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList,
                                                     DateAndTimeZoneDate currentDateAndTimeZoneDate);

    /**
     * 班次适用范围编辑构造
     */
    protected void classConfigUpdateBuild(Boolean isClassInfoUpdate,
                                          DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                          PunchClassConfigDTO oldDto,
                                          PunchClassConfigAddDTO addDTO,
                                          Set<Long> removeUserIdList,
                                          PunchClassConfigDO classConfigDO,
                                          List<PunchClassItemConfigDO> addPunchClassItemConfigDOList,
                                          List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                          List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        if (isClassInfoUpdate) {
            //班次规则信息构造
            classConfigDO.setId(defaultIdWorker.nextId());
            classConfigDO.setIsLatest(BusinessConstant.Y);
            classConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
            classConfigDO.setClassNature(addDTO.getClassNature());
            classConfigDO.setLegalWorkingHours(addDTO.getLegalWorkingHours());
            classConfigDO.setAttendanceHours(addDTO.getAttendanceHours());
            classConfigDO.setItemNum(addDTO.getItemNum());
            classConfigDO.setConfigNo(oldDto.getConfigNo());
            classConfigDO.setClassName(addDTO.getClassName());
            classConfigDO.setClassType(addDTO.getClassType());
            classConfigDO.setCountry(addDTO.getCountry());
            classConfigDO.setDeptIds(StringUtils.join(addDTO.getDeptIds(), BusinessConstant.DEFAULT_DELIMITER));
            classConfigDO.setIsCountryLevel(addDTO.countryLevel() ? BusinessConstant.Y : BusinessConstant.N);
            BaseDOUtil.fillDOInsert(classConfigDO);
            for (PunchClassItemConfigDTO item : addDTO.getClassItemConfigList()) {
                PunchClassItemConfigDO classItemConfigDO = new PunchClassItemConfigDO();
                classItemConfigDO.setId(defaultIdWorker.nextId());
                classItemConfigDO.setPunchClassId(classConfigDO.getId());
                classItemConfigDO.setSortNo(item.getSortNo());
                classItemConfigDO.setPunchInTime(item.getPunchInTime());
                classItemConfigDO.setPunchOutTime(item.getPunchOutTime());
                classItemConfigDO.setEarliestPunchInTime(item.getEarliestPunchInTime());
                classItemConfigDO.setLatestPunchInTime(item.getLatestPunchInTime());
                classItemConfigDO.setLatestPunchOutTime(item.getLatestPunchOutTime());
                // 判断是否跨天
                if (Objects.nonNull(classItemConfigDO.getPunchInTime()) && Objects.nonNull(classItemConfigDO.getPunchOutTime())) {
                    classItemConfigDO.setIsAcross(DateHelper.judgeCrossDay(
                            classItemConfigDO.getPunchInTime(), classItemConfigDO.getPunchOutTime()));
                }
                classItemConfigDO.setIsLatest(BusinessConstant.Y);
                classItemConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
                classItemConfigDO.setElasticTime(item.getElasticTime());
                classItemConfigDO.setRestStartTime(item.getRestStartTime());
                classItemConfigDO.setRestEndTime(item.getRestEndTime());
                classItemConfigDO.setAttendanceHours(item.getAttendanceHours());
                classItemConfigDO.setLegalWorkingHours(item.getLegalWorkingHours());
                BaseDOUtil.fillDOInsert(classItemConfigDO);
                addPunchClassItemConfigDOList.add(classItemConfigDO);
            }
        }

        //适用范围构造
        classConfigRangeUpdateBuild(isClassInfoUpdate, currentDateAndTimeZoneDate, removeUserIdList, oldDto, addDTO, classConfigDO, addPunchClassConfigRangeDOList, updatePunchClassConfigRangeDOList);
    }

    /**
     * 班次适用范围变更构造
     */
    protected abstract void classConfigRangeUpdateBuild(Boolean isClassInfoUpdate,
                                                        DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                        Set<Long> removeUserIdList,
                                                        PunchClassConfigDTO oldDto,
                                                        PunchClassConfigAddDTO addDTO,
                                                        PunchClassConfigDO classConfigDO,
                                                        List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                                                        List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList);


    private void updateConfigToHistoryVersion(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                              PunchClassConfigDTO oldPunchClassConfigDTO,
                                              List<PunchClassConfigDO> updatePunchClassConfigDOList,
                                              List<PunchClassItemConfigDO> updatePunchClassItemConfigDOList,
                                              List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        PunchClassConfigDO updatePunchClassConfig = PunchClassConfigApiMapstruct.INSTANCE.toDO(oldPunchClassConfigDTO);
        updatePunchClassConfig.setIsLatest(BusinessConstant.N);
        BaseDOUtil.fillDOUpdate(updatePunchClassConfig);
        updatePunchClassConfigDOList.add(updatePunchClassConfig);

        List<PunchClassItemConfigDO> punchClassItemConfigDOList = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(oldPunchClassConfigDTO.getId()));
        if (CollectionUtils.isNotEmpty(punchClassItemConfigDOList)) {
            punchClassItemConfigDOList.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                BaseDOUtil.fillDOUpdate(item);
            });
            updatePunchClassItemConfigDOList.addAll(punchClassItemConfigDOList);
        }

        List<PunchClassConfigRangeDO> punchClassConfigRangeDOList = punchClassConfigRangeDao.selectByRuleConfigIds(Collections.singletonList(oldPunchClassConfigDTO.getId()))
                .stream()
                .filter(config -> Objects.equals(BusinessConstant.Y, config.getIsLatest())
                        && ((Objects.equals(StatusEnum.ACTIVE.getCode(), config.getStatus()))
                        || (Objects.equals(StatusEnum.DISABLED.getCode(), config.getStatus()) && Objects.equals(BusinessConstant.Y, config.getIsFromHrHistoryConfig())))
                ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(punchClassConfigRangeDOList)) {
            punchClassConfigRangeDOList.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                item.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                item.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                BaseDOUtil.fillDOUpdate(item);
            });
            updatePunchClassConfigRangeDOList.addAll(punchClassConfigRangeDOList);
        }
    }

    protected void removeClassRangeBuild(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                         Set<Long> removeUserIdList,
                                         PunchClassConfigDTO oldDto,
                                         List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        List<PunchClassConfigRangeDTO> classConfigRangeDTOList = oldDto.getClassConfigRangeList().stream().filter(range -> removeUserIdList.contains(range.getBizId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(classConfigRangeDTOList)) {
            List<PunchClassConfigRangeDO> updateRangeList = PunchClassConfigRangeMapstruct.INSTANCE.toDOList(classConfigRangeDTOList);
            updateRangeList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                range.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
            updatePunchClassConfigRangeDOList.addAll(updateRangeList);
        }
    }


    protected void build(List<Long> userIds,
                         DateAndTimeZoneDate currentDateAndTimeZoneDate,
                         String rangeType,
                         Long ruleConfigId,
                         String ruleConfigNo,
                         String remark,
                         List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        for (Long userId : userIds) {
            PunchClassConfigRangeDO classConfigRangeDO = new PunchClassConfigRangeDO();
            classConfigRangeDO.setId(defaultIdWorker.nextId());
            classConfigRangeDO.setBizId(userId);
            classConfigRangeDO.setRangeType(rangeType);
            classConfigRangeDO.setRuleConfigId(ruleConfigId);
            classConfigRangeDO.setRuleConfigNo(ruleConfigNo);
            classConfigRangeDO.setIsLatest(BusinessConstant.Y);
            classConfigRangeDO.setStatus(StatusEnum.ACTIVE.getCode());
            classConfigRangeDO.setEffectTime(currentDateAndTimeZoneDate.getTimeZoneDate());
            classConfigRangeDO.setExpireTime(BusinessConstant.DEFAULT_END_TIME);
            classConfigRangeDO.setEffectTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
            classConfigRangeDO.setExpireTimestamp(BusinessConstant.DEFAULT_END_TIMESTAMP);
            classConfigRangeDO.setRemark(remark);
            BaseDOUtil.fillDOInsertByUsrOrSystem(classConfigRangeDO);
            addPunchClassConfigRangeDOList.add(classConfigRangeDO);
        }
    }

    protected void build(List<Long> userIds,
                         Boolean isClassInfoUpdate,
                         DateAndTimeZoneDate currentDateAndTimeZoneDate,
                         String rangeType,
                         Long ruleConfigId,
                         String ruleConfigNo,
                         String remark,
                         Map<Long, PunchClassConfigRangeDTO> oldRangeDTOList,
                         List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList,
                         List<PunchClassConfigRangeDO> updatePunchClassConfigRangeDOList) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        if (isClassInfoUpdate) {
            build(userIds, currentDateAndTimeZoneDate, rangeType, ruleConfigId, ruleConfigNo, remark, addPunchClassConfigRangeDOList);
            return;
        }
        for (Long userId : userIds) {
            PunchClassConfigRangeDTO rangeDTO = oldRangeDTOList.get(userId);
            if (Objects.nonNull(rangeDTO)) {
                if (Objects.equals(rangeType, rangeDTO.getRangeType())) {
                    continue;
                }
                PunchClassConfigRangeDO oldClassConfigRangeDO = PunchClassConfigRangeMapstruct.INSTANCE.toDO(rangeDTO);
                oldClassConfigRangeDO.setIsLatest(BusinessConstant.N);
                oldClassConfigRangeDO.setExpireTime(currentDateAndTimeZoneDate.getTimeZoneDate());
                oldClassConfigRangeDO.setExpireTimestamp(currentDateAndTimeZoneDate.getDateTimeStamp());
                BaseDOUtil.fillDOUpdateByUserOrSystem(oldClassConfigRangeDO);
                updatePunchClassConfigRangeDOList.add(oldClassConfigRangeDO);
            }
            build(Collections.singletonList(userId), currentDateAndTimeZoneDate, rangeType, ruleConfigId, ruleConfigNo, remark, addPunchClassConfigRangeDOList);
        }
    }


    /**
     * 判断班次适用范围是否变更
     */
    protected abstract PunchClassConfigRangeDifferDTO judgePunchClassConfigRangeUpdate(PunchClassConfigAddDTO newDto, PunchClassConfigDTO oldDto, Boolean isClassInfoUpdate);


    public void setRangeChangeUserIds(String country,
                                      String classNature,
                                      List<Long> deptIdList,
                                      Set<Long> totalUserIdList) {
        List<Long> userIdList = userInfoManage.selectByDeptIdsAndClassNature(country, deptIdList, classNature)
                .stream().map(UserInfoDO::getId).collect(Collectors.toList());
        List<Long> userRangeBizIdList = punchClassConfigRangeDao.selectLatestByBizIdsAndRangeType(userIdList, RuleRangeTypeEnum.USER.getCode())
                .stream().map(PunchClassConfigRangeDO::getBizId).collect(Collectors.toList());
        List<Long> filterUserIds = userIdList.stream().filter(userId -> !userRangeBizIdList.contains(userId)).collect(Collectors.toList());
        totalUserIdList.addAll(filterUserIds);
    }


    /**
     * 获取适用范围用户集合
     */
    protected abstract Set<Long> getClassRangeUserList(String country, String classNature, List<Long> userIds, List<Long> deptIds);

    /**
     * 根据国家时区转换时间
     *
     * @param country 国家三字码
     * @return Date
     */
    public Date getDateByCountryTimeZone(String country) {
        CountryDTO countryDTO = countryService.queryCountry(country);
        return CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
    }

    /**
     * 根据国家时区转换时间DayId
     *
     * @param country 国家三字码
     * @return DayId
     */
    public Long getDayIdByCountryTimeZone(String country) {
        CountryDTO countryDTO = countryService.queryCountry(country);
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        return DateHelper.getDayId(date);
    }

    private Set<Long> getAllCheckUserIds(PunchClassConfigRangeDifferDTO punchClassConfigRangeDifferDTO) {
        Set<Long> userIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(punchClassConfigRangeDifferDTO.getAddRangeUserList())) {
            userIds.addAll(punchClassConfigRangeDifferDTO.getAddRangeUserList());
        }
        if (CollectionUtils.isNotEmpty(punchClassConfigRangeDifferDTO.getRemoveRangeUserList())) {
            userIds.addAll(punchClassConfigRangeDifferDTO.getRemoveRangeUserList());
        }
        if (CollectionUtils.isNotEmpty(punchClassConfigRangeDifferDTO.getNoChangeRangeUserList())) {
            userIds.addAll(punchClassConfigRangeDifferDTO.getNoChangeRangeUserList());
        }
        return userIds;
    }

    @NotNull
    private Map<String, Set<Long>> getClassRangeUserIds(PunchClassConfigDTO classConfigDTO) {
        Map<String, Set<Long>> userIdMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(classConfigDTO.getClassConfigRangeList())) {
            Map<String, Set<Long>> groupByRangeType = classConfigDTO.getClassConfigRangeList()
                    .stream().collect(Collectors.groupingBy(PunchClassConfigRangeDTO::getRangeType, Collectors.mapping(PunchClassConfigRangeDTO::getBizId, Collectors.toSet())));

            //国家级别
            Set<Long> countryRangeList = groupByRangeType.get(RuleRangeTypeEnum.COUNTRY.getCode());
            if (CollectionUtils.isNotEmpty(countryRangeList)) {
                userIdMap.put(RuleRangeTypeEnum.COUNTRY.getCode(), countryRangeList);
                return userIdMap;
            }

            //用户级别
            Set<Long> userRangeList = Optional.ofNullable(groupByRangeType.get(RuleRangeTypeEnum.USER.getCode())).orElse(new HashSet<>());
            if (CollectionUtils.isNotEmpty(userRangeList)) {
                userIdMap.put(RuleRangeTypeEnum.USER.getCode(), userRangeList);
            }

            //部门级别
            Set<Long> deptRangeList = groupByRangeType.get(RuleRangeTypeEnum.DEPT.getCode());
            if (CollectionUtils.isNotEmpty(deptRangeList)) {
                userIdMap.put(RuleRangeTypeEnum.DEPT.getCode(), deptRangeList);
            }
        }

        return userIdMap;
    }

    protected void downgradeBindingClassRangeList(String classNature,
                                                  Set<Long> downgradeUserIdList,
                                                  DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                  String remark,
                                                  List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList) {

        if (ClassNatureEnum.MULTIPLE_CLASS.name().equals(classNature)) {
            return;
        }
        Set<Long> hasBindUserIds = new HashSet<>();

        List<UserInfoDO> userInfoDOList = userInfoDao.getByUserIds(new ArrayList<>(downgradeUserIdList));
        List<Long> deptIds = userInfoDOList.stream().map(UserInfoDO::getDeptId).distinct().collect(Collectors.toList());

        Map<Long, PunchClassConfigDO> deptPunchClassConfigMap = new HashMap<>();
        for (Long deptId : deptIds) {
            List<PunchClassConfigDO> deptPunchClassConfigList = punchClassConfigDao.selectLatestAndActiveByDeptId(deptId, classNature);
            if (CollectionUtils.isEmpty(deptPunchClassConfigList)) {
                continue;
            }
            deptPunchClassConfigMap.put(deptId, deptPunchClassConfigList.get(0));
        }

        if (MapUtils.isNotEmpty(deptPunchClassConfigMap)) {
            Map<Long, List<Long>> groupDeptIdMap = userInfoDOList.stream().collect(Collectors.groupingBy(UserInfoDO::getDeptId, Collectors.mapping(UserInfoDO::getId, Collectors.toList())));
            for (Long deptId : groupDeptIdMap.keySet()) {
                PunchClassConfigDO punchClassConfigDO = deptPunchClassConfigMap.get(deptId);
                if (Objects.isNull(punchClassConfigDO)) {
                    continue;
                }
                List<Long> userIdList = groupDeptIdMap.get(deptId);
                if (CollectionUtils.isEmpty(userIdList)) {
                    continue;
                }
                build(userIdList, currentDateAndTimeZoneDate, RuleRangeTypeEnum.DEPT.getCode(), punchClassConfigDO.getId(), punchClassConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
                hasBindUserIds.addAll(userIdList);
            }
        }

        userInfoDOList = userInfoDOList.stream().filter(user -> !hasBindUserIds.contains(user.getId())).collect(Collectors.toList());
        List<String> locationCountryList = userInfoDOList.stream().map(UserInfoDO::getLocationCountry).distinct().collect(Collectors.toList());

        Map<String, PunchClassConfigDO> countryPunchClassConfigMap = punchClassConfigDao.selectLatestCountryRange(locationCountryList, classNature)
                .stream().collect(Collectors.toMap(PunchClassConfigDO::getCountry, Function.identity()));
        if (MapUtils.isEmpty(countryPunchClassConfigMap)) {
            return;
        }

        Map<String, List<Long>> groupCountryMap = userInfoDOList.stream().collect(Collectors.groupingBy(UserInfoDO::getLocationCountry, Collectors.mapping(UserInfoDO::getId, Collectors.toList())));
        for (String country : groupCountryMap.keySet()) {
            PunchClassConfigDO punchClassConfigDO = countryPunchClassConfigMap.get(country);
            if (Objects.isNull(punchClassConfigDO)) {
                continue;
            }
            List<Long> userIdList = groupCountryMap.get(country);
            if (CollectionUtils.isEmpty(userIdList)) {
                continue;
            }
            build(userIdList, currentDateAndTimeZoneDate, RuleRangeTypeEnum.COUNTRY.getCode(), punchClassConfigDO.getId(), punchClassConfigDO.getConfigNo(), remark, addPunchClassConfigRangeDOList);
        }
    }


    private String buildClassUpdateLogRecord(PunchClassConfigAddDTO newDto, PunchClassConfigDTO old) {
        StringBuilder sb = new StringBuilder()
                .append("编辑班次：").append("[").append(old.getCountry()).append("]")
                .append("的")
                .append("[").append(old.getClassName()).append("]").append("\n");
        if (!Objects.equals(newDto.getClassName(), old.getClassName())) {
            sb.append("[班次名称]：").append("[").append(old.getClassName()).append("]").append("更新为").append("[").append(newDto.getClassName()).append("]").append("\n");
        }
        if (!Objects.equals(newDto.getClassType(), old.getClassType())) {
            String oldClassType = StringUtils.isEmpty(ClassTypeEnum.getDesc(old.getClassType())) ? BusinessConstant.DEFAULT_HYPHEN : ClassTypeEnum.getDesc(old.getClassType());
            String newClassType = StringUtils.isEmpty(ClassTypeEnum.getDesc(newDto.getClassType())) ? BusinessConstant.DEFAULT_HYPHEN : ClassTypeEnum.getDesc(newDto.getClassType());
            sb.append("[班次类型]：").append("[").append(oldClassType).append("]").append("更新为").append("[").append(newClassType).append("]").append("\n");
        }
        if (!Objects.equals(newDto.getItemNum(), old.getItemNum())) {
            sb.append("[时段数]：").append("[").append(old.getItemNum()).append("]").append("更新为").append("[").append(newDto.getItemNum()).append("]").append("\n");
        }
        if (newDto.getLegalWorkingHours().compareTo(old.getLegalWorkingHours()) != 0) {
            sb.append("[总工作时长]：").append("[").append(old.getLegalWorkingHours()).append("]").append("更新为").append("[").append(newDto.getLegalWorkingHours()).append("]").append("\n");
        }

        if (!areListsEqual(newDto.getDeptIds(), old.convertDeptList())) {
            Set<Long> deptIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(newDto.getDeptIds())) {
                deptIds.addAll(newDto.getDeptIds());
            }
            deptIds.addAll(old.convertDeptList());
            Map<Long, AttendanceDept> attendanceDeptMap = attendanceDeptService.listByDeptIds(new ArrayList<>(deptIds))
                    .stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
            String oldApplyDept = StringUtils.isEmpty(convertToDeptNameStr(attendanceDeptMap, old.convertDeptList())) ? BusinessConstant.DEFAULT_HYPHEN : convertToDeptNameStr(attendanceDeptMap, old.convertDeptList());
            String newApplyDept = StringUtils.isEmpty(convertToDeptNameStr(attendanceDeptMap, newDto.getDeptIds())) ? BusinessConstant.DEFAULT_HYPHEN : convertToDeptNameStr(attendanceDeptMap, newDto.getDeptIds());
            sb.append("[适用部门]：").append("[").append(oldApplyDept).append("]").append("更新为").append("[").append(newApplyDept).append("]").append("\n");
        }


        List<Long> oldUserIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(old.getClassConfigRangeList())) {
            List<Long> userRangeIds = old.getClassConfigRangeList().stream()
                    .filter(range -> Objects.equals(RuleRangeTypeEnum.USER.getCode(), range.getRangeType())).map(PunchClassConfigRangeDTO::getBizId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userRangeIds)) {
                oldUserIds.addAll(userRangeIds);
            }
        }
        if (!areListsEqual(newDto.getUserIdList(), oldUserIds)) {
            Set<Long> userIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(newDto.getUserIdList())) {
                userIds.addAll(newDto.getUserIdList());
            }
            userIds.addAll(oldUserIds);
            Map<Long, UserInfoDO> userInfoMap = userInfoDao.getByUserIds(new ArrayList<>(userIds))
                    .stream().collect(Collectors.toMap(UserInfoDO::getId, Function.identity()));
            String oldApplyUser = StringUtils.isEmpty(convertToUserCodeStr(userInfoMap, oldUserIds)) ? BusinessConstant.DEFAULT_HYPHEN : convertToUserCodeStr(userInfoMap, oldUserIds);
            String newApplyUser = StringUtils.isEmpty(convertToUserCodeStr(userInfoMap, newDto.getUserIdList())) ? BusinessConstant.DEFAULT_HYPHEN : convertToUserCodeStr(userInfoMap, newDto.getUserIdList());
            sb.append("[适用员工]：").append("[").append(oldApplyUser).append("]").append("更新为").append("[").append(newApplyUser).append("]").append("\n");
        }


        List<PunchClassItemConfigDTO> addPunchClassItemConfigDTO = newDto.getClassItemConfigList().stream().filter(itemConfig -> Objects.isNull(itemConfig.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(addPunchClassItemConfigDTO)) {
            for (PunchClassItemConfigDTO itemConfigDTO : addPunchClassItemConfigDTO) {
                sb.append("新增时段 [").append(itemConfigDTO.getSortNo()).append("]: ")
                        .append("上班时间 [").append(itemConfigDTO.getPunchInTime()).append("]").append("、 ")
                        .append("下班时间 [").append(itemConfigDTO.getPunchOutTime()).append("]").append("、 ")
                        .append("休息开始时间 [").append(DateHelper.formatHHMMSS(itemConfigDTO.getRestStartTime())).append("]").append("、 ")
                        .append("休息结束时间 [").append(DateHelper.formatHHMMSS(itemConfigDTO.getRestEndTime())).append("]").append("、 ")
                        .append("弹性时间 [").append(itemConfigDTO.getElasticTime()).append("]").append("、 ")
                        .append("最早打卡时间 [").append(DateHelper.formatHHMMSS(itemConfigDTO.getEarliestPunchInTime())).append("]").append("\n");
            }
        }
        List<Long> oldItemConfigIds = old.getClassItemConfigList().stream().map(PunchClassItemConfigDTO::getId).collect(Collectors.toList());
        List<Long> newItemConfigIds = newDto.getClassItemConfigList().stream().map(PunchClassItemConfigDTO::getId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Long> removedItemConfigIdList = new ArrayList<>(oldItemConfigIds);
        removedItemConfigIdList.removeAll(newItemConfigIds);
        if (CollectionUtils.isNotEmpty(removedItemConfigIdList)) {
            old.getClassItemConfigList().stream().filter(itemConfig -> removedItemConfigIdList.contains(itemConfig.getId())).forEach(itemConfig -> {
                sb.append("删除时段 [").append(itemConfig.getSortNo()).append("]: ")
                        .append("上班时间 [").append(itemConfig.getPunchInTime()).append("]").append("、 ")
                        .append("下班时间 [").append(itemConfig.getPunchOutTime()).append("]").append("、 ")
                        .append("休息开始时间 [").append(DateHelper.formatHHMMSS(itemConfig.getRestStartTime())).append("]").append("、 ")
                        .append("休息结束时间 [").append(DateHelper.formatHHMMSS(itemConfig.getRestEndTime())).append("]").append("、 ")
                        .append("弹性时间 [").append(itemConfig.getElasticTime()).append("]").append("、 ")
                        .append("最早打卡时间 [").append(DateHelper.formatHHMMSS(itemConfig.getEarliestPunchInTime())).append("]").append("\n");
            });
        }

        Map<Long, PunchClassItemConfigDTO> punchClassItemConfigDTOMap = newDto.getClassItemConfigList().stream()
                .filter(itemConfig -> Objects.nonNull(itemConfig.getId())).collect(Collectors.toMap(PunchClassItemConfigDTO::getId, Function.identity()));
        old.getClassItemConfigList().forEach(itemConfig -> {
            PunchClassItemConfigDTO itemConfigDTO = punchClassItemConfigDTOMap.get(itemConfig.getId());
            if (Objects.isNull(itemConfigDTO)) {
                return;
            }
            if (itemConfig.getPunchInTime().compareTo(itemConfigDTO.getPunchInTime()) != 0) {
                sb.append("时段 [").append(itemConfig.getSortNo()).append("] - ").append("[上班时间]：")
                        .append("[").append(DateHelper.formatHHMMSS(itemConfig.getPunchInTime())).append("]").append("更新为")
                        .append("[").append(DateHelper.formatHHMMSS(itemConfigDTO.getPunchInTime())).append("]").append("\n");
            }
            if (itemConfig.getPunchOutTime().compareTo(itemConfigDTO.getPunchOutTime()) != 0) {
                sb.append("时段 [").append(itemConfig.getSortNo()).append("] - ").append("[下班时间]：")
                        .append("[").append(DateHelper.formatHHMMSS(itemConfig.getPunchOutTime())).append("]").append("更新为")
                        .append("[").append(DateHelper.formatHHMMSS(itemConfigDTO.getPunchOutTime())).append("]").append("\n");
            }

            if (!areDateEqual(itemConfig.getRestStartTime(), itemConfigDTO.getRestStartTime())) {
                String oldRestStartTime = Objects.isNull(itemConfig.getRestStartTime()) ? BusinessConstant.DEFAULT_HYPHEN : DateHelper.formatHHMMSS(itemConfig.getRestStartTime());
                String newRestStartTime = Objects.isNull(itemConfigDTO.getRestStartTime()) ? BusinessConstant.DEFAULT_HYPHEN : DateHelper.formatHHMMSS(itemConfigDTO.getRestStartTime());
                sb.append("时段 [").append(itemConfig.getSortNo()).append("] - ").append("[休息开始时间]：")
                        .append("[").append(oldRestStartTime).append("]").append("更新为")
                        .append("[").append(newRestStartTime).append("]").append("\n");
            }
            if (!areDateEqual(itemConfig.getRestEndTime(), itemConfigDTO.getRestEndTime())) {
                String oldRestEndTime = Objects.isNull(itemConfig.getRestEndTime()) ? BusinessConstant.DEFAULT_HYPHEN : DateHelper.formatHHMMSS(itemConfig.getRestEndTime());
                String newRestEndTime = Objects.isNull(itemConfigDTO.getRestEndTime()) ? BusinessConstant.DEFAULT_HYPHEN : DateHelper.formatHHMMSS(itemConfigDTO.getRestEndTime());
                sb.append("时段 [").append(itemConfig.getSortNo()).append("] - ").append("[休息结束时间]：")
                        .append("[").append(oldRestEndTime).append("]").append("更新为")
                        .append("[").append(newRestEndTime).append("]").append("\n");
            }
            if (itemConfig.getElasticTime().compareTo(itemConfigDTO.getElasticTime()) != 0) {
                sb.append("时段 [").append(itemConfig.getSortNo()).append("] - ").append("[弹性时间]：")
                        .append("[").append(itemConfig.getElasticTime()).append("]").append("更新为")
                        .append("[").append(itemConfigDTO.getElasticTime()).append("]").append("\n");
            }
            if (itemConfig.getEarliestPunchInTime().compareTo(itemConfigDTO.getEarliestPunchInTime()) != 0) {
                sb.append("时段 [").append(itemConfig.getSortNo()).append("] - ").append("[最早打卡时间]：")
                        .append("[").append(DateHelper.formatHHMMSS(itemConfig.getEarliestPunchInTime())).append("]").append("更新为")
                        .append("[").append(DateHelper.formatHHMMSS(itemConfigDTO.getEarliestPunchInTime())).append("]").append("\n");
            }
        });
        return sb.toString();
    }

    private boolean areListsEqual(List<Long> newList, List<Long> oldList) {
        if (CollectionUtils.isEmpty(newList)) {
            newList = Collections.emptyList();
        }
        Collections.sort(newList);
        Collections.sort(oldList);
        return newList.equals(oldList);
    }

    private boolean areDateEqual(Date oldTime, Date newTime) {
        if (Objects.isNull(oldTime) && Objects.isNull(newTime)) {
            return true;
        }
        if (Objects.nonNull(oldTime) && Objects.nonNull(newTime)) {
            return oldTime.compareTo(newTime) == 0;
        }
        return false;
    }

    private String convertToDeptNameStr(Map<Long, AttendanceDept> attendanceDeptMap, List<Long> deptIds) {
        if (CollectionUtils.isEmpty(deptIds) || MapUtils.isEmpty(attendanceDeptMap)) {
            return null;
        }
        List<String> deptNames = new ArrayList<>();
        for (Long deptId : deptIds) {
            AttendanceDept attendanceDept = attendanceDeptMap.get(deptId);
            if (Objects.isNull(attendanceDept)) {
                continue;
            }
            deptNames.add(attendanceDept.getDeptNameCn());
        }
        return String.join(BusinessConstant.DEFAULT_DELIMITER, deptNames);
    }

    private String convertToUserCodeStr(Map<Long, UserInfoDO> userInfoMap, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds) || MapUtils.isEmpty(userInfoMap)) {
            return null;
        }
        List<String> deptNames = new ArrayList<>();
        for (Long deptId : userIds) {
            UserInfoDO userInfoDO = userInfoMap.get(deptId);
            if (Objects.isNull(userInfoDO)) {
                continue;
            }
            deptNames.add(userInfoDO.getUserCode());
        }
        return String.join(BusinessConstant.DEFAULT_DELIMITER, deptNames);
    }


    protected abstract void buildClassEnableRangeAddData(DateAndTimeZoneDate currentDateAndTimeZoneDate,
                                                         PunchClassConfigDTO classConfigDTO,
                                                         List<PunchClassConfigRangeDO> addPunchClassConfigRangeDOList);
}
