package com.imile.attendance.rule.event.domain;

import com.imile.attendance.rule.dto.PunchClassConfigSwitchStatusDTO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
public class PunchClassConfigSwitchStatusEvent extends ApplicationEvent {
    private final PunchClassConfigSwitchStatusDTO configSwitchStatusDTO;

    public PunchClassConfigSwitchStatusDTO getData() {
        return configSwitchStatusDTO;
    }

    public PunchClassConfigSwitchStatusEvent(Object source, PunchClassConfigSwitchStatusDTO configSwitchStatusDTO) {
        super(source);
        this.configSwitchStatusDTO = configSwitchStatusDTO;
    }
}
