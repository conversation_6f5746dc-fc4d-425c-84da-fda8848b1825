package com.imile.attendance.rule.event.listener;


import com.imile.attendance.abnormal.UserAttendanceAbnormalTrigger;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.dto.PunchClassConfigUpdateAutoShiftDTO;
import com.imile.attendance.rule.event.domain.PunchClassConfigUpdateEvent;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class PunchClassConfigUpdateEventListener {
    private final PunchClassConfigManage punchClassConfigManage;
    private final AutoShiftConfigFactory autoShiftConfigFactory;
    private final CountryService countryService;
    private final UserAttendanceAbnormalTrigger userAttendanceAbnormalTrigger;

    @Async("bizTaskThreadPool")
    @EventListener
    public void onPunchClassConfigUpdateEvent(PunchClassConfigUpdateEvent punchClassConfigUpdateEvent) {
        if (Objects.isNull(punchClassConfigUpdateEvent.getData())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        PunchClassConfigUpdateAutoShiftDTO updateAutoShiftDTO = punchClassConfigUpdateEvent.getData();
        long startTime = System.currentTimeMillis();
        log.info("PunchClassConfigUpdateEvent | startTime:{},oldPunchClassConfigId:{},latestPunchClassConfigId:{}",
                startTime, updateAutoShiftDTO.getOldClassId(), updateAutoShiftDTO.getNewClassId());

        PunchClassConfigDTO latestPunchClassConfigDTO = punchClassConfigManage.selectById(updateAutoShiftDTO.getNewClassId());

        CountryDTO countryDTO = countryService.queryCountry(latestPunchClassConfigDTO.getCountry());
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        Long dayId = DateHelper.getDayId(date);

        UserAutoShiftParam userAutoShiftParam = new UserAutoShiftParam();
        userAutoShiftParam.setClassNatureEnum(ClassNatureEnum.getByCode(latestPunchClassConfigDTO.getClassNature()));

        //班次时段变化后 未变化的人群需要刷新升级后的班次ID 且不能按照日历重新排班
        if (CollectionUtils.isNotEmpty(updateAutoShiftDTO.getNoChangeRangeUserIdList()) && updateAutoShiftDTO.getClassRuleUpdate()) {
            UserAutoShiftParam.PunchClassNoChangeUserParam noChangeUserParam = new UserAutoShiftParam.PunchClassNoChangeUserParam();
            noChangeUserParam.setUserIdList(new ArrayList<>(updateAutoShiftDTO.getNoChangeRangeUserIdList()));
            noChangeUserParam.setTargetClassId(latestPunchClassConfigDTO.getId());
            noChangeUserParam.setShiftStartDayId(dayId);
            if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), latestPunchClassConfigDTO.getClassNature())) {
                Map<Long, List<Long>> userNeedClearClassMap = updateAutoShiftDTO.getNoChangeRangeUserIdList().stream()
                        .collect(Collectors.toMap(
                                userId -> userId,
                                userId -> Collections.singletonList(updateAutoShiftDTO.getOldClassId())
                        ));
                noChangeUserParam.setUserNeedClearClassMap(userNeedClearClassMap);
            }
            noChangeUserParam.setIsOnlyClearShift(updateAutoShiftDTO.getIsOnlyClearSchedule());
            userAutoShiftParam.setPunchClassNoChangeUserParam(noChangeUserParam);
        }

        if (CollectionUtils.isNotEmpty(updateAutoShiftDTO.getAddRangeUserIdList())) {
            Set<Long> newRangeUserIdList = updateAutoShiftDTO.getAddRangeUserIdList();
            if (CollectionUtils.isNotEmpty(updateAutoShiftDTO.getRemoveRangeUserIdList())) {
                newRangeUserIdList.removeAll(updateAutoShiftDTO.getRemoveRangeUserIdList());
            }
            UserAutoShiftParam.PunchClassAddUserParam punchClassAddUserParam = new UserAutoShiftParam.PunchClassAddUserParam();
            punchClassAddUserParam.setUserIdList(new ArrayList<>(newRangeUserIdList));
            punchClassAddUserParam.setTargetClassId(latestPunchClassConfigDTO.getId());
            punchClassAddUserParam.setShiftStartDayId(dayId);
            punchClassAddUserParam.setIsOnlyClearShift(updateAutoShiftDTO.getIsOnlyClearSchedule());
            if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), latestPunchClassConfigDTO.getClassNature())) {
                Map<Long, List<Long>> userNeedClearClassMap = newRangeUserIdList.stream()
                        .collect(Collectors.toMap(
                                userId -> userId,
                                userId -> Collections.singletonList(updateAutoShiftDTO.getOldClassId())
                        ));
                punchClassAddUserParam.setUserNeedClearClassMap(userNeedClearClassMap);
            }
            userAutoShiftParam.setPunchClassAddUserParam(punchClassAddUserParam);
        }

        //移除适用范围的用户集合需要降级匹配是否存在新的班次规则
        if (CollectionUtils.isNotEmpty(updateAutoShiftDTO.getRemoveRangeUserIdList())) {
            Map<Long, PunchClassConfigDO> punchClassConfigIdMap = punchClassConfigManage.selectTopPriorityByUserIds(updateAutoShiftDTO.getRemoveRangeUserIdList());

            UserAutoShiftParam.PunchClassRemoveUserParam punchClassRemoveUserParam = new UserAutoShiftParam.PunchClassRemoveUserParam();
            List<UserAutoShiftParam.PunchClassRemoveSingleUserParam> classRemoveSingleUserParams = new ArrayList<>();
            for (Long userId : updateAutoShiftDTO.getRemoveRangeUserIdList()) {
                UserAutoShiftParam.PunchClassRemoveSingleUserParam removeSingleUserParam = new UserAutoShiftParam.PunchClassRemoveSingleUserParam();
                removeSingleUserParam.setUserId(userId);
                PunchClassConfigDO punchClassConfigDO = punchClassConfigIdMap.get(userId);
                if (Objects.nonNull(punchClassConfigDO)) {
                    removeSingleUserParam.setTargetClassId(punchClassConfigDO.getId());
                    if (Objects.equals(latestPunchClassConfigDTO.getCountry(), punchClassConfigDO.getCountry())) {
                        removeSingleUserParam.setShiftStartDayId(dayId);
                    } else {
                        CountryDTO country = countryService.queryCountry(punchClassConfigDO.getCountry());
                        removeSingleUserParam.setShiftStartDayId(DateHelper.getDayId(CommonUtil.convertDateByTimeZonePlus(country.getTimeZone(), new Date())));
                    }
                } else {
                    removeSingleUserParam.setShiftStartDayId(dayId);
                }
                if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), latestPunchClassConfigDTO.getClassNature())) {
                    removeSingleUserParam.setClearClassIdList(Collections.singletonList(updateAutoShiftDTO.getOldClassId()));
                }
                classRemoveSingleUserParams.add(removeSingleUserParam);
            }
            punchClassRemoveUserParam.setIsOnlyClearShift(updateAutoShiftDTO.getIsOnlyClearSchedule());
            punchClassRemoveUserParam.setClassRemoveSingleUserParams(classRemoveSingleUserParams);
            userAutoShiftParam.setPunchClassRemoveUserParam(punchClassRemoveUserParam);
        }
        autoShiftConfigFactory.autoShift(userAutoShiftParam);

        log.info("PunchClassConfigUpdateEvent | 耗时:{}", System.currentTimeMillis() - startTime);

        Set<Long> userIds = new HashSet<>();
        if (Objects.nonNull(userAutoShiftParam.getPunchClassNoChangeUserParam())
                && CollectionUtils.isNotEmpty(userAutoShiftParam.getPunchClassNoChangeUserParam().getUserIdList())) {
            userIds.addAll(userAutoShiftParam.getPunchClassNoChangeUserParam().getUserIdList());
        }
        if (Objects.nonNull(userAutoShiftParam.getPunchClassAddUserParam())
                && CollectionUtils.isNotEmpty(userAutoShiftParam.getPunchClassAddUserParam().getUserIdList())) {
            userIds.addAll(userAutoShiftParam.getPunchClassAddUserParam().getUserIdList());
        }
        if (Objects.nonNull(userAutoShiftParam.getPunchClassRemoveUserParam())
                && CollectionUtils.isNotEmpty(userAutoShiftParam.getPunchClassRemoveUserParam().getClassRemoveSingleUserParams())) {
            List<UserAutoShiftParam.PunchClassRemoveSingleUserParam> classRemoveSingleUserParams = userAutoShiftParam.getPunchClassRemoveUserParam().getClassRemoveSingleUserParams();
            Set<Long> removeUserIds = classRemoveSingleUserParams.stream().map(UserAutoShiftParam.PunchClassRemoveSingleUserParam::getUserId).collect(Collectors.toSet());
            userIds.addAll(removeUserIds);
        }

        //排班触发当日异常计算
        userAttendanceAbnormalTrigger.userShiftAbnormalCalculateHandler(new ArrayList<>(userIds), dayId);
    }

}
