package com.imile.attendance.rule.event.domain;

import com.imile.attendance.rule.dto.PunchClassConfigUpdateAutoShiftDTO;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
public class PunchClassConfigUpdateEvent extends ApplicationEvent {
    private final PunchClassConfigUpdateAutoShiftDTO punchClassConfigUpdateAutoShiftDTO;

    public PunchClassConfigUpdateAutoShiftDTO getData() {
        return punchClassConfigUpdateAutoShiftDTO;
    }

    public PunchClassConfigUpdateEvent(Object source, PunchClassConfigUpdateAutoShiftDTO punchClassConfigUpdateAutoShiftDTO) {
        super(source);
        this.punchClassConfigUpdateAutoShiftDTO = punchClassConfigUpdateAutoShiftDTO;
    }
}
