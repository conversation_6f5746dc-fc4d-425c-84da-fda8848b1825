package com.imile.attendance.rule.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Data
public class PunchClassConfigDetailVO implements Serializable {

    /**
     * 班次ID
     */
    private Long id;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 班次类型
     */
    private String classType;

    /**
     * 时段数
     */
    private Integer itemNum;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 国家
     */
    private String country;

    /**
     * 适用部门
     */
    private List<Long> deptIds;

    /**
     * 适用人员
     */
    private List<Long> userIdList;

    /**
     * 适用部门
     */
    private List<PunchClassConfigApplyRangeVO> applyDeptList;

    /**
     * 适用人员
     */
    private List<PunchClassConfigApplyRangeVO> applyUserList;

    /**
     * 班次时段信息
     */
    private List<PunchClassItemConfigVO> classItemConfigList;


    @Data
    public static class PunchClassConfigApplyRangeVO {

        /**
         * 业务ID 部门id、用户ID
         */
        private Long bizId;

        /**
         * 业务名称
         */
        private String bizNameByLang;
    }
}
