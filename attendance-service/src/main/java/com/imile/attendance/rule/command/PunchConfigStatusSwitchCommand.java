package com.imile.attendance.rule.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/4/11 
 * @Description
 */
@Data
public class PunchConfigStatusSwitchCommand {

    /**
     * 打卡规则编码
     */
    @NotBlank(message = "punchConfigNo can not be null")
    private String punchConfigNo;

    /**
     * 状态(ACTIVE 生效,DISABLED)
     */
    @NotNull(message = "status cannot be empty")
    private String status;
}
