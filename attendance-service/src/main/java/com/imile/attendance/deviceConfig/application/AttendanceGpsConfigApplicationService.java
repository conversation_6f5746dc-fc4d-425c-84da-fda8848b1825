package com.imile.attendance.deviceConfig.application;

import com.imile.attendance.deviceConfig.AttendanceGpsConfigService;
import com.imile.attendance.deviceConfig.MobilePunchQueryService;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigDeleteCommand;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigImportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.factory.AttendanceGpsConfigFactory;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.common.page.PaginationResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Service
public class AttendanceGpsConfigApplicationService {

    @Resource
    private AttendanceGpsConfigFactory attendanceGpsConfigFactory;
    @Resource
    private AttendanceGpsConfigService attendanceGpsConfigService;
    @Resource
    private MobilePunchQueryService mobilePunchQueryService;


    public PaginationResult<AttendanceGpsConfigDTO> list(AttendanceGpsConfigQuery query) {
        return attendanceGpsConfigService.list(query);
    }

    public void add(AttendanceGpsConfigAddCommand addCommand) {
        attendanceGpsConfigFactory.add(addCommand);
    }

    public AttendanceGpsConfigDTO update(AttendanceGpsConfigUpdateCommand updateCommand) {
        return attendanceGpsConfigFactory.update(updateCommand);
    }

    public AttendanceGpsConfigDTO delete(AttendanceGpsConfigDeleteCommand deleteCommand) {
        return attendanceGpsConfigFactory.delete(deleteCommand);
    }

    public List<AttendanceGpsConfigImportDTO> importGpsConfig(List<AttendanceGpsConfigImportDTO> param) {
        return attendanceGpsConfigService.importGpsConfig(param);
    }

    public List<AttendanceGpsConfigDTO> selectList(AttendanceGpsConfigQuery query) {
        return attendanceGpsConfigService.selectList(query);
    }

    public List<String> selectFilterList(AttendanceConfigFilterQuery query) {
        return attendanceGpsConfigService.selectFilterList(query);
    }

    public PaginationResult<AttendanceGpsConfigExportDTO> export(AttendanceGpsConfigQuery query) {
        return attendanceGpsConfigService.export(query);
    }

    /**
     * 获取所有的gps配置
     */
    public List<AttendanceGpsConfigDTO> getAllGpsConfig(){
        return mobilePunchQueryService.getAllGpsConfig();
    }

}
