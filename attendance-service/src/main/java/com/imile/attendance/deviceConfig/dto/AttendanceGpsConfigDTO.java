package com.imile.attendance.deviceConfig.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Data
public class AttendanceGpsConfigDTO {

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * 地址名称
     */
    private String addressName;

    /**
     * 地址详情
     */
    private String addressDetail;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 有效范围
     */
    private Integer effectiveRange;

    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;
    /**
     * 最近修改日期
     */
    private Date lastUpdDate;
}
