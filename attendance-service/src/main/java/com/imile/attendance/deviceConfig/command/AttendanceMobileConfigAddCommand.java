package com.imile.attendance.deviceConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceMobileConfigAddCommand {


    /**
     * id
     */
    private Long id;

    /**
     * 用户编码
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String userCode;

    /**
     * 手机唯一标识
     */
    private String mobileUnicode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 手机品牌
     */
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    private String mobileVersion;
}
