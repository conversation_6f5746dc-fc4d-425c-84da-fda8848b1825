package com.imile.attendance.deviceConfig.command;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceMobileConfigDeleteCommand {

    private Long mobileConfigId;

    public static AttendanceMobileConfigDeleteCommand of(Long mobileConfigId) {
        return new AttendanceMobileConfigDeleteCommand(mobileConfigId);
    }
}
