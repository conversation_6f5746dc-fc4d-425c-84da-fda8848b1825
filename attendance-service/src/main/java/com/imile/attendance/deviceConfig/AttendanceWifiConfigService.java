package com.imile.attendance.deviceConfig;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigExportDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigImportDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceWifiConfigMapstruct;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.ZoneService;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceWifiConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceConfigFilterQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.IpepUtils;
import com.imile.attendance.util.PageUtil;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.business.dto.BusCityResultDTO;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.ucenter.api.context.RequestInfoHolder;
import com.imile.util.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * H5/移动 考勤打卡Wifi配置相关接口
 *
 * <AUTHOR>
 * @date 2025/4/16
 */
@Service
public class AttendanceWifiConfigService {
    @Resource
    private AttendanceWifiConfigDao attendanceWifiConfigDao;
    @Resource
    private CountryService countryService;
    @Resource
    private ZoneService zoneService;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private LogRecordService logRecordService;

    /**
     * wifi配置列表
     */
    public PaginationResult<AttendanceWifiConfigDTO> list(AttendanceWifiConfigQuery query) {
        // 1. 拼接数据权限条件（wifi只含国家）
        List<String> userCountryAuthList = attendancePermissionService.filterUserCountryAuth(query.getCountry(), query.getCountryList());
        if (CollectionUtils.isEmpty(userCountryAuthList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setCountryList(userCountryAuthList);
        // 2. 查询wifi配置
        PageInfo<AttendanceWifiConfigDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> attendanceWifiConfigDao.list(query));

        List<AttendanceWifiConfigDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<AttendanceWifiConfigDTO> wifiConfigDTOS = AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDTO(list);
        return PageUtil.getPageResult(wifiConfigDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * wifi配置下拉框
     */
    public List<AttendanceWifiConfigDTO> selectList(AttendanceWifiConfigQuery query) {
        List<AttendanceWifiConfigDO> list = attendanceWifiConfigDao.list(query);
        return AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDTO(list);
    }

    /**
     * wifi筛选条件查询(国家和城市)
     */
    public List<String> selectFilterList(AttendanceConfigFilterQuery query) {
        return attendanceWifiConfigDao.selectFilterList(query);
    }

    /**
     * 查询wifi配置详情
     */
    public AttendanceWifiConfigDTO detail(AttendanceWifiConfigQuery query) {
        AttendanceWifiConfigDO wifiConfigDO = attendanceWifiConfigDao.getById(query.getId());
        if (Objects.isNull(wifiConfigDO)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        return AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDTO(wifiConfigDO);
    }

    /**
     * 导入wifi配置
     */
    public List<AttendanceWifiConfigImportDTO> importWifiConfig(List<AttendanceWifiConfigImportDTO> param) {
        List<AttendanceWifiConfigImportDTO> failImportList = new ArrayList<>();
        // wi-fi import check
        List<AttendanceWifiConfigImportDTO> params = checkImportParam(param, failImportList);
        // save
        Object obj = null;
        for (AttendanceWifiConfigImportDTO item : params) {
            try {
                AttendanceWifiConfigDO wifiConfigDO = BeanUtils.convert(item, AttendanceWifiConfigDO.class);
                BaseDOUtil.fillDOInsert(wifiConfigDO);
                attendanceWifiConfigDao.save(wifiConfigDO);
                if (Objects.isNull(obj)) {
                    obj = wifiConfigDO;
                }
            } catch (Exception e) {
                IpepUtils.putFail(item, e.getMessage());
                failImportList.add(item);
            }
        }
        // operation log
        if (Objects.nonNull(obj)) {
            logRecordService.recordOperation(obj,
                    LogRecordOptions.builder()
                            .pageOperateType(PageOperateType.IMPORT)
                            .operationType(OperationTypeEnum.WIFI_CONFIG_IMPORT.getCode())
                            .remark("导入WIFI")
                            .build());
        }
        return failImportList;
    }

    /**
     * 导出GPS
     */
    public PaginationResult<AttendanceWifiConfigExportDTO> export(AttendanceWifiConfigQuery query) {
        PaginationResult<AttendanceWifiConfigDTO> pageList = this.list(query);
        PaginationResult<AttendanceWifiConfigExportDTO> pageResult = AttendanceWifiConfigMapstruct.INSTANCE.toPageDTO(pageList);
        if (CollectionUtils.isEmpty(pageList.getResults())) {
            return pageResult;
        }
        //operation log
        AttendanceWifiConfigDO wifiConfigDO = AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDO(pageList.getResults().get(0));
        logRecordService.recordOperation(wifiConfigDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.EXPORT)
                        .operationType(OperationTypeEnum.WIFI_CONFIG_EXPORT.getCode())
                        .remark("导出WIFI")
                        .build());
        return pageResult;
    }

    private List<AttendanceWifiConfigImportDTO> checkImportParam(List<AttendanceWifiConfigImportDTO> params,
                                                                 List<AttendanceWifiConfigImportDTO> failImportList) {
        List<AttendanceWifiConfigImportDTO> normal = new ArrayList<>();
        List<String> importCountryNames = new ArrayList<>();
        List<String> countryNamesParam = params.stream()
                .map(AttendanceWifiConfigImportDTO::getCountry)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(countryNamesParam)) {
            importCountryNames = countryNamesParam;
        }
        List<String> countryList = new ArrayList<>();
        // 获取系统国家
        if (CollectionUtils.isNotEmpty(importCountryNames)) {
            List<String> countryNames = importCountryNames.stream().distinct().collect(Collectors.toList());
            CountryApiQuery countryQuery = new CountryApiQuery();
            countryQuery.setCountryNames(countryNames);
            countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
            List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
            countryList = Optional.ofNullable(countryConfigList.stream().map(CountryConfigDTO::getCountryName).collect(Collectors.toList())).orElse(Collections.emptyList());
        }

        out:
        for (AttendanceWifiConfigImportDTO param : params) {
            if (StringUtils.isBlank(param.getCountry()) || StringUtils.isBlank(param.getLocationCity())
                    || StringUtils.isBlank(param.getMacAddress()) || StringUtils.isBlank(param.getWifiName())) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ? "必填项不能为空" : "The required fields cannot be blank");
                failImportList.add(param);
                continue;
            }

            if ((param.getWifiName().length() > 15)) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ? "wifi名称长度不能超过15" : "The length of the wifi name cannot exceed 15");
                failImportList.add(param);
                continue;
            }

            if (!countryList.contains(param.getCountry())) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ? "当前国家不存在" : "The country does not exist");
                failImportList.add(param);
                continue;
            }

            List<BusCityResultDTO> cityListByCountry = zoneService.getCityListByCountry(param.getCountry());
            if (CollectionUtils.isEmpty(cityListByCountry)) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ? "国家下不存在城市" : "The city does not exist");
                failImportList.add(param);
                continue;
            }

            List<String> cityNameList = cityListByCountry.stream().map(BusCityResultDTO::getCityName).collect(Collectors.toList());
            if (!cityNameList.contains(param.getLocationCity())) {
                IpepUtils.putFail(param, RequestInfoHolder.isChinese() ? "当前城市不存在" : "The city does not exist");
                failImportList.add(param);
                continue;
            }

            AttendanceWifiConfigQuery query = AttendanceWifiConfigQuery.builder()
                    .macAddress(param.getMacAddress())
                    .build();
            List<AttendanceWifiConfigDO> list = attendanceWifiConfigDao.list(query);
            if (CollectionUtils.isNotEmpty(list)) {
                IpepUtils.putFail(param, ErrorCodeEnum.WIFI_MAC_ADDRESS_DUPLICATE_ERROR.getMessage());
                failImportList.add(param);
                continue;
            }
            // 校验mac地址格式
            String macAddress = param.getMacAddress();
            String regex = "^([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}$|^([0-9a-fA-F]{2}-){5}[0-9a-fA-F]{2}$";
            if (StringUtils.isEmpty(macAddress) || !macAddress.matches(regex)) {
                IpepUtils.putFail(param, ErrorCodeEnum.WIFI_MAC_ADDRESS_FORMAT_ERROR.getMessage());
                failImportList.add(param);
                continue;
            }
            normal.add(param);
        }
        return normal;
    }

//    /**
//     * 根据查询条件获取当前用户数据权限
//     *
//     * @param query
//     * @return
//     */
//    private List<String> getUserCountryAuthList(AttendanceWifiConfigQuery query) {
//        // 传递国家不为空
//        List<String> countryList = Lists.newArrayList();
//        if (StringUtils.isNotBlank(query.getCountry())) {
//            countryList.add(query.getCountry());
//        }
//        if (CollectionUtils.isNotEmpty(query.getCountryList())) {
//            countryList.addAll(query.getCountryList());
//        }
//        return attendancePermissionService.getUserCountryPermissionByParam(countryList);
//    }

}
