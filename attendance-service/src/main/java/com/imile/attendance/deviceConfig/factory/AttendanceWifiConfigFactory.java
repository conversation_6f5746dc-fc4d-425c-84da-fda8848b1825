package com.imile.attendance.deviceConfig.factory;

import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigDeleteCommand;
import com.imile.attendance.deviceConfig.command.AttendanceWifiConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceWifiConfigDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceWifiConfigMapstruct;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceWifiConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceWifiConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Slf4j
@Service
public class AttendanceWifiConfigFactory {

    @Resource
    private AttendanceWifiConfigDao attendanceWifiConfigDao;
    @Resource
    private LogRecordService logRecordService;


    /**
     * 新增wifi配置
     */
    @Transactional
    public void add(AttendanceWifiConfigAddCommand addCommand) {
        // wi-fi add check
        checkAddParam(addCommand);
        // save
        AttendanceWifiConfigDO wifiConfigDO = AttendanceWifiConfigMapstruct.INSTANCE.toAddAttendanceWifiConfigDO(addCommand);
        attendanceWifiConfigDao.save(wifiConfigDO);
        // operation log
        logRecordService.recordOperation(wifiConfigDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.WIFI_CONFIG_ADD.getCode())
                        .country(wifiConfigDO.getCountry())
                        .bizName(wifiConfigDO.getWifiName())
                        .build());
    }

    /**
     * 更新wifi配置
     */
    @Transactional
    public AttendanceWifiConfigDTO update(AttendanceWifiConfigUpdateCommand updateCommand) {
        // wi-fi update check
        checkUpdateParam(updateCommand);
        AttendanceWifiConfigDO localWifiConfig = attendanceWifiConfigDao.getById(updateCommand.getId());
        if (Objects.isNull(localWifiConfig) || Objects.equals(localWifiConfig.getIsDelete(), IsDeleteEnum.YES.getCode())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // update
        AttendanceWifiConfigDO wifiUpdateConfigDO = buildUpdateDO(updateCommand, localWifiConfig);
        attendanceWifiConfigDao.updateById(wifiUpdateConfigDO);
        // operation log
        logRecordService.recordObjectChange(
                wifiUpdateConfigDO,
                localWifiConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.UPDATE)
                        .operationType(OperationTypeEnum.WIFI_CONFIG_UPDATE.getCode())
                        .country(localWifiConfig.getCountry())
                        .bizName(localWifiConfig.getWifiName())
                        .build()
        );
        return AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDTO(wifiUpdateConfigDO);
    }

    /**
     * 删除wifi配置
     */
    @Transactional
    public AttendanceWifiConfigDTO delete(AttendanceWifiConfigDeleteCommand deleteCommand) {
        // wi-fi delete check
        Long wifiConfigId = deleteCommand.getWifiConfigId();
        AttendanceWifiConfigDO localWifiConfig = attendanceWifiConfigDao.getById(wifiConfigId);
        if (Objects.isNull(localWifiConfig)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // delete
        AttendanceWifiConfigDO deleteDO = new AttendanceWifiConfigDO();
        deleteDO.setId(localWifiConfig.getId());
        deleteDO.setIsDelete(IsDeleteEnum.YES.getCode());
        BaseDOUtil.fillDOUpdate(deleteDO);
        attendanceWifiConfigDao.updateById(deleteDO);
        // operation log
        logRecordService.recordOperation(deleteDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DELETE)
                        .operationType(OperationTypeEnum.WIFI_CONFIG_DELETE.getCode())
                        .country(localWifiConfig.getCountry())
                        .bizName(localWifiConfig.getWifiName())
                        .build());
        return AttendanceWifiConfigMapstruct.INSTANCE.toAttendanceWifiConfigDTO(localWifiConfig);
    }

    private void checkAddParam(AttendanceWifiConfigAddCommand addCommand) {
        AttendanceWifiConfigQuery query = AttendanceWifiConfigQuery.builder()
                .macAddress(addCommand.getMacAddress())
                .build();
        List<AttendanceWifiConfigDO> list = attendanceWifiConfigDao.list(query);
        if (CollectionUtils.isNotEmpty(list)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.WIFI_MAC_ADDRESS_DUPLICATE_ERROR);
        }
        // 校验mac地址格式
        checkMacAddress(addCommand);
    }

    private void checkUpdateParam(AttendanceWifiConfigUpdateCommand updateCommand) {
        AttendanceWifiConfigQuery query = AttendanceWifiConfigQuery.builder()
                .macAddress(updateCommand.getMacAddress())
                .build();
        List<AttendanceWifiConfigDO> list = attendanceWifiConfigDao.list(query);
        List<AttendanceWifiConfigDO> mac_list = list.stream()
                .filter(item -> !item.getId().equals(updateCommand.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mac_list)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.WIFI_MAC_ADDRESS_DUPLICATE_ERROR);
        }
        // 校验mac地址格式
        checkMacAddress(updateCommand);
    }

    private void checkMacAddress(AttendanceWifiConfigAddCommand addCommand) {
        // 校验mac地址格式
        String macAddress = addCommand.getMacAddress();
        String regex = "^([0-9a-fA-F]{2}:){5}[0-9a-fA-F]{2}$|^([0-9a-fA-F]{2}-){5}[0-9a-fA-F]{2}$";
        if (StringUtils.isEmpty(macAddress) || !macAddress.matches(regex)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.WIFI_MAC_ADDRESS_FORMAT_ERROR);
        }
    }

    private AttendanceWifiConfigDO buildUpdateDO(AttendanceWifiConfigUpdateCommand updateCommand,
                                                 AttendanceWifiConfigDO wifiConfigDO) {
        AttendanceWifiConfigDO attendanceWifiConfigDO = AttendanceWifiConfigMapstruct.INSTANCE.deepCopy(wifiConfigDO);
        if (StringUtils.isNotBlank(updateCommand.getCountry())) {
            attendanceWifiConfigDO.setCountry(updateCommand.getCountry());
        }
        if (StringUtils.isNotBlank(updateCommand.getWifiName())) {
            attendanceWifiConfigDO.setWifiName(updateCommand.getWifiName());
        }
        if (StringUtils.isNotBlank(updateCommand.getLocationCity())) {
            attendanceWifiConfigDO.setLocationCity(updateCommand.getLocationCity());
        }
        if (StringUtils.isNotBlank(updateCommand.getMacAddress())) {
            attendanceWifiConfigDO.setMacAddress(updateCommand.getMacAddress());
        }
        BaseDOUtil.fillDOUpdate(attendanceWifiConfigDO);
        return attendanceWifiConfigDO;
    }

}
