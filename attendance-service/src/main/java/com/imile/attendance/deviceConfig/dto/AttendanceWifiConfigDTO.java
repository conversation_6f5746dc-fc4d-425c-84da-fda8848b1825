package com.imile.attendance.deviceConfig.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Data
public class AttendanceWifiConfigDTO {

    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 城市
     */
    private String locationCity;

    /**
     * wifi名称
     */
    private String wifiName;

    /**
     * mac地址
     */
    private String macAddress;

    /**
     * 创建人
     */
    private String createUserName;
    /**
     * 创建日期
     */
    private Date createDate;

    /**
     * 最后修改人
     */
    private String lastUpdUserName;
    /**
     * 最近修改日期
     */
    private Date lastUpdDate;
}
