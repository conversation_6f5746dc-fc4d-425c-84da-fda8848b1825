package com.imile.attendance.deviceConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/4/16
 * @Description
 */
@Data
public class AttendanceWifiConfigAddCommand {

    /**
     * 国家
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String country;

    /**
     * 城市
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String locationCity;

    /**
     * wifi名称
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String wifiName;

    /**
     * mac地址
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class, Groups.Add.class})
    private String macAddress;
}
