package com.imile.attendance.deviceConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Data
public class AttendanceGpsConfigAddCommand {

    /**
     * 国家
     */
    @ApiModelProperty(value = "国家")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String country;

    /**
     * 城市
     */
    @ApiModelProperty(value = "城市")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String locationCity;

    /**
     * 地址名称
     */
    @ApiModelProperty(value = "地址名称")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String addressName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String addressDetail;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private BigDecimal latitude;

    /**
     * 有效范围
     */
    @ApiModelProperty(value = "有效范围")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer effectiveRange;
}
