package com.imile.attendance.deviceConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttendanceGpsConfigUpdateCommand extends AttendanceGpsConfigAddCommand {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 有效范围
     */
    @ApiModelProperty(value = "有效范围")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer effectiveRange;
}
