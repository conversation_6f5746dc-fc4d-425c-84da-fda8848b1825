package com.imile.attendance.deviceConfig.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Data
public class AttendanceMobileConfigDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 手机唯一标识
     */
    private String mobileUnicode;

    /**
     * 手机型号
     */
    private String mobileModel;

    /**
     * 手机品牌
     */
    private String mobileBranch;

    /**
     * 手机系统版本
     */
    private String mobileVersion;

    /**
     * 最近打卡时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date punchTime;

    /**
     * 绑定时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    /**
     * 解绑时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
