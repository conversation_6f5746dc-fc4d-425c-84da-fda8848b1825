package com.imile.attendance.deviceConfig;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileHistoryConfigDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceMobileConfigMapstruct;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceMobileConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.dto.AttendanceMobileConfigListDTO;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceWifiConfigQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.util.BusinessFieldUtils;
import com.imile.attendance.util.PageUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.page.Pagination;
import com.imile.common.page.PaginationResult;
import com.imile.ucenter.api.context.RequestInfoHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考勤手机配置业务接口
 *
 * <AUTHOR>
 * @date 2025/4/18
 */
@Slf4j
@Service
public class AttendanceMobileConfigService {
    @Resource
    private AttendanceMobileConfigDao attendanceMobileConfigDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private EmployeePunchRecordService punchRecordService;
    @Resource
    private AttendancePermissionService attendancePermissionService;

    /**
     * 考勤手机列表查询
     *
     * @param query 查询参数
     * @return 考勤手机列表
     */
    public PaginationResult<AttendanceMobileConfigListDTO> list(AttendanceMobileConfigListQuery query) {
        boolean chinese = RequestInfoHolder.isChinese();
        log.info("查询考勤手机列表，查询参数：{}", query);
        List<String> userCountryAuthList = attendancePermissionService.filterUserCountryAuth(query.getCountry(), query.getCountryList());
        if (CollectionUtils.isEmpty(userCountryAuthList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setCountryList(userCountryAuthList);
        PageInfo<AttendanceMobileConfigListDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> attendanceMobileConfigDao.queryAttendanceMobileConfig(query));
        List<AttendanceMobileConfigListDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return getEmptyPageResult(query);
        }
        // 获取部门id集合
        List<Long> deptIds = list.stream().map(AttendanceMobileConfigListDTO::getDeptId).collect(Collectors.toList());
        List<AttendanceDept> deptList = deptService.selectDeptByIds(deptIds);
        // 转换为部门id转为map
        Map<Long, AttendanceDept> deptMap = deptList.stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
        for (AttendanceMobileConfigListDTO attendanceMobileConfig : list) {
            // 设置部门
            if (ObjectUtil.isNotNull(attendanceMobileConfig.getDeptId())) {
                AttendanceDept attendanceDept = deptMap.get(attendanceMobileConfig.getDeptId());
                if (ObjectUtil.isNotNull(attendanceDept)) {
                    attendanceMobileConfig.setDeptName(chinese ? attendanceDept.getDeptNameCn() : attendanceDept.getDeptNameEn());
                }
            }
            // 设置用户名称
            attendanceMobileConfig.setUserNameDesc(BusinessFieldUtils.getUnifiedUserName(attendanceMobileConfig.getUserName()
                    , attendanceMobileConfig.getUserNameEn()));
        }

        PaginationResult<AttendanceMobileConfigListDTO> pageResult = PageUtil.getPageResult(list, query, (int) pageInfo.getTotal(), pageInfo.getPages());
        return pageResult;
    }

    /**
     * 用户考勤手机查询
     */
    public List<AttendanceMobileConfigDTO> selectByUserCode(String userCode) {
        AttendanceMobileConfigQuery query = AttendanceMobileConfigQuery
                .builder()
                .userCode(userCode)
                .isDelete(IsDeleteEnum.NO.getCode())
                .build();
        List<AttendanceMobileConfigDO> list = attendanceMobileConfigDao.list(query);
        return AttendanceMobileConfigMapstruct.INSTANCE.toAttendanceMobileConfigDTO(list);
    }

    /**
     * 用户考勤手机查询(含打卡时间)
     */
    public List<AttendanceMobileConfigDTO> selectByUserCodeForDetail(String userCode) {
        List<AttendanceMobileConfigDTO> listDetail = this.selectByUserCode(userCode);
        if (CollectionUtils.isEmpty(listDetail)) {
            return Collections.emptyList();
        }
        List<Long> mobileConfigIds = listDetail
                .stream()
                .map(AttendanceMobileConfigDTO::getId)
                .collect(Collectors.toList());
        Map<Long, List<EmployeePunchRecordDO>> punchMap = new HashedMap();
        if (CollectionUtils.isNotEmpty(mobileConfigIds)) {
            List<EmployeePunchRecordDO> punchListByMobileConfigId = punchRecordService.getPunchListByMobileConfigId(mobileConfigIds);
            if (CollectionUtils.isNotEmpty(punchListByMobileConfigId)) {
                punchMap = punchListByMobileConfigId
                        .stream()
                        .collect(Collectors.groupingBy(EmployeePunchRecordDO::getMobileConfigId));
            }
        }
        for (AttendanceMobileConfigDTO dto : listDetail) {
            List<EmployeePunchRecordDO> punchRecordDOS = punchMap.get(dto.getId());
            if (CollectionUtils.isNotEmpty(punchRecordDOS)) {
                dto.setPunchTime(punchRecordDOS.get(0).getPunchTime());
            }
        }
        return listDetail;
    }

    /**
     * 用户考勤手机历史记录查询
     */
    public List<AttendanceMobileHistoryConfigDTO> selectHistoryByUserCode(String userCode) {
        AttendanceMobileConfigQuery query = AttendanceMobileConfigQuery
                .builder()
                .userCode(userCode)
                .build();
        List<AttendanceMobileConfigDO> list = attendanceMobileConfigDao.list(query);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        return AttendanceMobileConfigMapstruct.INSTANCE.toAttendanceMobileConfigHistoryDTO(list);
    }

    /**
     * 返回记录为空的分页参数
     *
     * @param pagination 分页信息
     * @return
     */
    public PaginationResult getEmptyPageResult(Pagination pagination) {
        PaginationResult result = new PaginationResult<>();
        result.setResults(Collections.emptyList());
        result.setPagination(pagination);
        return result;
    }
}
