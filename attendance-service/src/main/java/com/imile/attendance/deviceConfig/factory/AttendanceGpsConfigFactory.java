package com.imile.attendance.deviceConfig.factory;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigDeleteCommand;
import com.imile.attendance.deviceConfig.command.AttendanceGpsConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceGpsConfigDTO;
import com.imile.attendance.deviceConfig.mapstruct.AttendanceGpsConfigMapstruct;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.deviceConfig.dao.AttendanceGpsConfigDao;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceGpsConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceGpsConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Slf4j
@Service
public class AttendanceGpsConfigFactory {

    @Resource
    private AttendanceGpsConfigDao attendanceGpsConfigDao;
    @Resource
    private CountryService countryService;
    @Resource
    private LogRecordService logRecordService;


    @Transactional
    public void add(AttendanceGpsConfigAddCommand addCommand) {
        // gps add check
        checkAddParam(addCommand);
        // save
        AttendanceGpsConfigDO gpsConfigDO = AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDO(addCommand);
        attendanceGpsConfigDao.save(gpsConfigDO);
        // operation log
        logRecordService.recordOperation(gpsConfigDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.GPS_CONFIG_ADD.getCode())
                        .country(gpsConfigDO.getCountry())
                        .bizName(gpsConfigDO.getAddressName())
                        .build());
    }

    @Transactional
    public AttendanceGpsConfigDTO update(AttendanceGpsConfigUpdateCommand updateCommand) {
        // gps update check
        AttendanceGpsConfigDO gpsConfigDO = attendanceGpsConfigDao.getById(updateCommand.getId());
        if (Objects.isNull(gpsConfigDO)
                || Objects.equals(gpsConfigDO.getIsDelete(), IsDeleteEnum.YES.getCode())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        checkUpdateParam(updateCommand);
        // update
        AttendanceGpsConfigDO gpsUpdateConfigDO = buildUpdateDO(updateCommand, gpsConfigDO);
        attendanceGpsConfigDao.updateById(gpsUpdateConfigDO);
        // operation log
        logRecordService.recordObjectChange(
                gpsUpdateConfigDO,
                gpsConfigDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.UPDATE)
                        .operationType(OperationTypeEnum.GPS_CONFIG_UPDATE.getCode())
                        .country(gpsConfigDO.getCountry())
                        .bizName(gpsConfigDO.getAddressName())
                        .build()
        );
        return AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(gpsConfigDO);
    }

    @Transactional
    public AttendanceGpsConfigDTO delete(AttendanceGpsConfigDeleteCommand deleteCommand) {
        // gps delete check
        Long gpsConfigId = deleteCommand.getGpsConfigId();
        AttendanceGpsConfigDO localGpsConfig = attendanceGpsConfigDao.getById(gpsConfigId);
        if (Objects.isNull(localGpsConfig)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // delete
        AttendanceGpsConfigDO deleteDO = new AttendanceGpsConfigDO();
        deleteDO.setId(localGpsConfig.getId());
        deleteDO.setIsDelete(IsDeleteEnum.YES.getCode());
        BaseDOUtil.fillDOUpdate(deleteDO);
        attendanceGpsConfigDao.updateById(deleteDO);
        // operation log
        logRecordService.recordOperation(deleteDO,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.DELETE)
                        .operationType(OperationTypeEnum.GPS_CONFIG_DELETE.getCode())
                        .country(localGpsConfig.getCountry())
                        .bizName(localGpsConfig.getAddressName())
                        .build());
        return AttendanceGpsConfigMapstruct.INSTANCE.toPunchGpsConfigDTO(localGpsConfig);
    }


    private void checkAddParam(AttendanceGpsConfigAddCommand addCommand) {
        // 获取系统国家,校验国家是否存在
        String countryName = addCommand.getCountry();
        if (StringUtils.isNotBlank(countryName)) {
            CountryApiQuery countryQuery = new CountryApiQuery();
            countryQuery.setCountryName(countryName);
            countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
            CountryConfigDTO countryConfigDTO = countryService.queryCountryConfig(countryQuery);
            if (Objects.isNull(countryConfigDTO) || !countryName.equals(countryConfigDTO.getCountryName())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.COUNTRY_CODE_NOT_EXISTS);
            }
        }

        // 校验经纬度是否存在
        AttendanceGpsConfigQuery query = AttendanceGpsConfigQuery.builder()
                .country(addCommand.getCountry())
                .longitude(addCommand.getLongitude())
                .latitude(addCommand.getLatitude()).build();
        List<AttendanceGpsConfigDO> list = attendanceGpsConfigDao.list(query);
        if (CollectionUtils.isNotEmpty(list)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.GPS_ADDRESS_DUPLICATE_ERROR);
        }

        // 校验名称是否重复
        AttendanceGpsConfigQuery queryName = AttendanceGpsConfigQuery.builder()
                .country(addCommand.getCountry())
                .addressName(addCommand.getAddressName())
                .build();
        List<AttendanceGpsConfigDO> listByName = attendanceGpsConfigDao.list(queryName);
        if (CollectionUtils.isNotEmpty(listByName)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.GPS_NAME_DUPLICATE_ERROR);
        }
    }

    private void checkUpdateParam(AttendanceGpsConfigUpdateCommand updateCommand) {
        // 校验经纬度是否存在
        AttendanceGpsConfigQuery query = AttendanceGpsConfigQuery.builder()
                .country(updateCommand.getCountry())
                .longitude(updateCommand.getLongitude())
                .latitude(updateCommand.getLatitude())
                .build();
        List<AttendanceGpsConfigDO> list = attendanceGpsConfigDao.list(query)
                .stream()
                .filter(item -> !item.getId().equals(updateCommand.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.GPS_ADDRESS_DUPLICATE_ERROR);
        }

        // 校验名称是否重复
        AttendanceGpsConfigQuery queryName = AttendanceGpsConfigQuery.builder()
                .country(updateCommand.getCountry())
                .addressName(updateCommand.getAddressName())
                .build();
        List<AttendanceGpsConfigDO> listByName = attendanceGpsConfigDao.list(queryName)
                .stream()
                .filter(item -> !item.getId().equals(updateCommand.getId()))
                .collect(Collectors.toList());;
        if (CollectionUtils.isNotEmpty(listByName)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.GPS_NAME_DUPLICATE_ERROR);
        }
    }

    private AttendanceGpsConfigDO buildUpdateDO(AttendanceGpsConfigUpdateCommand updateCommand,
                                                AttendanceGpsConfigDO gpsConfigDO) {
        AttendanceGpsConfigDO attendanceGpsConfigDO = AttendanceGpsConfigMapstruct.INSTANCE.deepCopy(gpsConfigDO);
        if (StringUtils.isNotBlank(updateCommand.getCountry())) {
            attendanceGpsConfigDO.setCountry(updateCommand.getCountry());
        }
        if (StringUtils.isNotBlank(updateCommand.getLocationCity())) {
            attendanceGpsConfigDO.setLocationCity(updateCommand.getLocationCity());
        }
        if (StringUtils.isNotBlank(updateCommand.getAddressName())) {
            attendanceGpsConfigDO.setAddressName(updateCommand.getAddressName());
        }
        if (StringUtils.isNotBlank(updateCommand.getAddressDetail())) {
            attendanceGpsConfigDO.setAddressDetail(updateCommand.getAddressDetail());
        }
        if (Objects.nonNull(updateCommand.getLongitude())) {
            attendanceGpsConfigDO.setLongitude(updateCommand.getLongitude());
        }
        if (Objects.nonNull(updateCommand.getLatitude())) {
            attendanceGpsConfigDO.setLatitude(updateCommand.getLatitude());
        }
        if (Objects.nonNull(updateCommand.getEffectiveRange())) {
            attendanceGpsConfigDO.setEffectiveRange(updateCommand.getEffectiveRange());
        }
        BaseDOUtil.fillDOUpdate(attendanceGpsConfigDO);
        return attendanceGpsConfigDO;
    }

}
