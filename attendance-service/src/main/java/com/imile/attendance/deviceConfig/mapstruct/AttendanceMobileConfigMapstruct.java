package com.imile.attendance.deviceConfig.mapstruct;


import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigAddCommand;
import com.imile.attendance.deviceConfig.command.AttendanceMobileConfigUpdateCommand;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileConfigDTO;
import com.imile.attendance.deviceConfig.dto.AttendanceMobileHistoryConfigDTO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.deviceConfig.model.AttendanceMobileConfigDO;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigListQuery;
import com.imile.attendance.infrastructure.repository.deviceConfig.query.AttendanceMobileConfigQuery;
import com.imile.attendance.util.BaseDOUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/18
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceMobileConfigMapstruct {

    AttendanceMobileConfigMapstruct INSTANCE = Mappers.getMapper(AttendanceMobileConfigMapstruct.class);

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    AttendanceMobileConfigDO toAttendanceMobileConfigDO(AttendanceMobileConfigAddCommand addCommand);


    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "id", source = "id")
    AttendanceMobileConfigDO toAttendanceMobileConfigDO(AttendanceMobileConfigUpdateCommand updateCommand);

    default AttendanceMobileConfigDO toAddAttendanceMobileConfigDO(AttendanceMobileConfigAddCommand addCommand) {
        AttendanceMobileConfigDO attendanceMobileConfigDO = toAttendanceMobileConfigDO(addCommand);
        BaseDOUtil.fillDOInsert(attendanceMobileConfigDO);
        return attendanceMobileConfigDO;
    }

    default AttendanceMobileConfigDO toAddAttendanceMobileConfigDO(AttendanceMobileConfigUpdateCommand updateCommand) {
        AttendanceMobileConfigDO attendanceMobileConfigDO = toAttendanceMobileConfigDO(updateCommand);
        BaseDOUtil.fillDOUpdate(attendanceMobileConfigDO);
        return attendanceMobileConfigDO;
    }

    @Mapping(target = "punchTime", ignore = true)
    AttendanceMobileConfigDTO toAttendanceMobileConfigDTO(AttendanceMobileConfigDO attendanceMobileConfigDO);

    List<AttendanceMobileConfigDTO> toAttendanceMobileConfigDTO(List<AttendanceMobileConfigDO> attendanceMobileConfigDOList);

    @Mapping(target = "lastUpdDate", expression = "java(attendanceMobileConfigDO.getIsDelete()==0?null:attendanceMobileConfigDO.getLastUpdDate())")
    AttendanceMobileHistoryConfigDTO toAttendanceMobileConfigHistoryDTO(AttendanceMobileConfigDO attendanceMobileConfigDO);

    List<AttendanceMobileHistoryConfigDTO> toAttendanceMobileConfigHistoryDTO(List<AttendanceMobileConfigDO> attendanceMobileConfigDOList);

}
