package com.imile.attendance.employee.repository.impl;

import com.imile.attendance.employee.Employee;
import com.imile.attendance.employee.converter.EmployeeConverter;
import com.imile.attendance.employee.repository.EmployeeRepository;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/1/16 
 * @Description
 */
@Component
@RequiredArgsConstructor
public class EmployeeRepositoryImpl implements EmployeeRepository {

    private final UserInfoDao userInfoDao;


    @Override
    public Employee getEmployeeByCode(String userCode) {
        if (StringUtils.isBlank(userCode)){
            return null;
        }
        UserDTO userDTO = userInfoDao.getUserByCode(userCode);
        return EmployeeConverter.INSTANCE.mapToEmployee(userDTO);
    }
}
