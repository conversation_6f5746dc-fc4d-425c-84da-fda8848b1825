package com.imile.attendance.attendance.converter;

import com.imile.attendance.attendance.dto.AttendanceClassCalendarDTO;
import com.imile.attendance.attendance.dto.AttendanceClassDetailDTO;
import com.imile.attendance.attendance.vo.AttendanceClassCalendarVO;
import com.imile.attendance.attendance.vo.AttendanceClassDetailVO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceClassDetailConverter {

    AttendanceClassDetailConverter INSTANCE = Mappers.getMapper(AttendanceClassDetailConverter.class);


    List<AttendanceClassDetailVO> mapToAttendanceClassDetailVOList(List<AttendanceClassDetailDTO> attendanceClassDetailDTOList);

    List<AttendanceClassCalendarVO> mapToAttendanceClassCalendarVOList(List<AttendanceClassCalendarDTO> attendanceClassCalendarDTOList);
}
