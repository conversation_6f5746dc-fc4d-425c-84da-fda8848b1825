package com.imile.attendance.attendance;

import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 员工出勤详情查询
 *
 * <AUTHOR>
 * @menu 考勤日月报
 * @date 2025/6/9
 */
@Service
@Slf4j
public class EmployeeDetailQueryService {
    @Resource
    private AttendanceEmployeeDetailDao employeeDetailDao;

    /**
     * 查询用户指定时间段内的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectAttendanceByDayId(Long userId,
                                                                    Long startDayId, Long endDayId) {
        return employeeDetailDao.selectAttendanceByDayId(userId, startDayId, endDayId);
    }

    /**
     * 查询用户指定时间段内的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectAttendanceByCycleDay(List<Long> userIdList,
                                                                       Long startDayId, Long endDayId) {
        return employeeDetailDao.selectAttendanceByCycleDay(userIdList, startDayId, endDayId);
    }

    /**
     * 查询用户指定时间的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectAttendanceByDayIdList(Long userId, List<Long> dayIdList) {
        return employeeDetailDao.selectAttendanceByDayIdList(userId, dayIdList);
    }

    /**
     * 查询员工该年/月的所有出勤明细
     */
    public List<AttendanceEmployeeDetailDO> selectByYearAndMonth(Long userId, Long year, Long month) {
        return employeeDetailDao.selectByYearAndMonth(userId, year, month);
    }

    /**
     * 查询员工某天的考勤记录
     */
    public List<AttendanceEmployeeDetailDO> selectByDayId(Long userId, Long dayId) {
        return employeeDetailDao.selectByDayId(userId, dayId);
    }

    public List<AttendanceEmployeeDetailDO> selectByUserId(List<Long> userIds, Long dayId) {
        List<AttendanceEmployeeDetailDO> employeeDetailDOList = employeeDetailDao.selectByUserId(userIds, dayId);
        if (CollectionUtils.isEmpty(employeeDetailDOList)) {
            return Collections.emptyList();
        }
        return employeeDetailDOList;
    }

}
