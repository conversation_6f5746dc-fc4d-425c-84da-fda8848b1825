package com.imile.attendance.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 排班日历视图
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceClassCalendarDTO {
    /**
     * dayId
     */
    private Long dayId;

    /**
     * 类型（未排班 NONE、休息日 OFF、节假日 PH）
     */
    private String type;
}
