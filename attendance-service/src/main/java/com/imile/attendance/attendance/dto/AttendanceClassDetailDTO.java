package com.imile.attendance.attendance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 排班明细视图
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceClassDetailDTO {
    /**
     * dayId
     */
    private Long dayId;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 当天班次上班时间（自由排班显示最早上班时间，多班次显示当天最早时段的开始时间）
     */
    private Date startTime;

    /**
     * 当天班次下班时间（自由排班显示最晚下班时间，多班次显示当天最晚时段的结束时间）
     */
    private Date endTime;

    /**
     * 是否排班
     */
    private Boolean isWork;
}
