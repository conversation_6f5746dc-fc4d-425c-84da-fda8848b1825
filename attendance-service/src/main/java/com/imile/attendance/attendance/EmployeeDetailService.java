package com.imile.attendance.attendance;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.attendance.dto.AttendanceClassCalendarDTO;
import com.imile.attendance.attendance.dto.AttendanceClassDetailDTO;
import com.imile.attendance.attendance.query.AttendanceEmployeeDetailClassQuery;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.punch.dao.EmployeePunchRecordDao;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassItemConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.DateHelper;
import com.imile.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/6
 */
@Service
@Slf4j
public class EmployeeDetailService {
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private PunchClassItemConfigDao punchClassItemConfigDao;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private EmployeePunchRecordDao employeePunchRecordDao;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;


    /**
     * 员工日历排班明细
     */
    public List<AttendanceClassDetailDTO> detailUserClassDTO(AttendanceEmployeeDetailClassQuery query) {
        Long userId = Objects.isNull(query.getUserId()) ? RequestInfoHolder.getUserId() : query.getUserId();
        UserInfoDO userInfo = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfo)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        //根据日期查询
        Long dayId = query.getDate();
        Long preDayId = DateHelper.getPreviousDayId(dayId);
        Long afterDayId = DateHelper.getNextDayId(dayId);
        //查询用户两天排班
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.selectRecordByDayList(userId, Arrays.asList(dayId, preDayId, afterDayId));
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return Lists.newArrayList(AttendanceClassDetailDTO.builder().isWork(false).build());
        }
        Map<Long, List<UserShiftConfigDO>> dayMap = userShiftConfigDOList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));
        List<UserShiftConfigDO> userShiftConfigPreDay = dayMap.get(preDayId);
        List<UserShiftConfigDO> userShiftConfigDay = dayMap.get(dayId);
        if (CollectionUtils.isEmpty(userShiftConfigDay)) {
            return Lists.newArrayList(AttendanceClassDetailDTO.builder().isWork(false).build());
        }
        List<AttendanceClassDetailDTO> attendanceClassDetailDTOList = new ArrayList<>();
        //查询当天排班详情
        AttendanceClassDetailDTO attendanceClassDetailDTO = buildEmployeeClassDTO(userShiftConfigDay.get(0), dayId, userInfo.getUserCode());
        attendanceClassDetailDTOList.add(attendanceClassDetailDTO);
        //前端传递选择为开始时间
        if (Objects.isNull(query.getIsEndTime()) || !query.getIsEndTime()) {
            if (CollectionUtils.isEmpty(attendanceClassDetailDTOList)) {
                return attendanceClassDetailDTOList;
            }
            //判断当天班次的结束时间是否跨天，如果跨天，则返回前后两天时间让用户选择
            List<UserShiftConfigDO> userShiftConfigAfterDay = dayMap.get(afterDayId);
            if (CollectionUtils.isEmpty(userShiftConfigAfterDay)
                    || Objects.isNull(userShiftConfigAfterDay.get(0).getPunchClassConfigId())) {
                return attendanceClassDetailDTOList;
            }
            //当天班次结束时间
            Date endTime = attendanceClassDetailDTOList.get(0).getEndTime();
            if (Objects.nonNull(endTime)
                    && afterDayId.equals(DateHelper.getDayId(endTime))) {
                //跨天则需要查询后一天的排班看是否和结束时间为同一天
                List<PunchClassItemConfigDO> afterDayItemConfigList = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(userShiftConfigAfterDay.get(0).getPunchClassConfigId()));
                if (CollectionUtils.isNotEmpty(afterDayItemConfigList)) {
                    PunchClassItemConfigDO afterDayItemConfig = afterDayItemConfigList.get(afterDayItemConfigList.size() - 1);
                    if (Objects.nonNull(afterDayItemConfig.getIsAcross()) && afterDayItemConfig.getIsAcross() == 0) {
                        AttendanceClassDetailDTO afterDayClassDetailDTO = buildEmployeeClassDTO(userShiftConfigAfterDay.get(0), afterDayId, userInfo.getUserCode());
                        attendanceClassDetailDTOList.add(afterDayClassDetailDTO);
                    }
                }
            }
            return attendanceClassDetailDTOList;
        }
        //如果前一天没有排班或者前一天排班班次和当天班次一样，则返回当天排班详情即可
        if (CollectionUtils.isNotEmpty(userShiftConfigPreDay)
                && Objects.nonNull(userShiftConfigPreDay.get(0).getPunchClassConfigId())) {
            //前后两天班次不一样并且前一天的班次跨天，则返回前后两天时间天让用户选择
            List<PunchClassItemConfigDO> preDayItemConfigList = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(userShiftConfigPreDay.get(0).getPunchClassConfigId()));
            if (CollectionUtils.isNotEmpty(preDayItemConfigList)) {
                PunchClassItemConfigDO preDayItemConfig = preDayItemConfigList.get(preDayItemConfigList.size() - 1);
                if (Objects.nonNull(preDayItemConfig.getIsAcross()) && preDayItemConfig.getIsAcross() == 1) {
                    AttendanceClassDetailDTO preDayClassDetailDTO = buildEmployeeClassDTO(userShiftConfigPreDay.get(0), preDayId, userInfo.getUserCode());
                    attendanceClassDetailDTOList.add(preDayClassDetailDTO);
                }
            }
        }
        //过滤结束时间不为当天的排班
        attendanceClassDetailDTOList.removeIf(item -> Objects.nonNull(item.getEndTime())
                && !dayId.equals(DateHelper.getDayId(item.getEndTime())));
        return attendanceClassDetailDTOList;
    }


    /**
     * 员工未排班日历查询
     */
    public List<AttendanceClassCalendarDTO> detailUserClassCalendar(Integer year, Long userIdParam) {
        Long userId = Objects.isNull(userIdParam) ? RequestInfoHolder.getUserId() : userIdParam;
        UserInfoDO userInfo = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfo)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        if (Objects.isNull(year)) {
            return Collections.emptyList();
        }
        //按年查找当前用户排班
        Long firstDayId = DateHelper.getDayId(DateUtils.yearBegin(year));
        Long lastDayId = DateHelper.getNextDayId(DateHelper.getDayId(DateUtils.yearEnd(year)));
        List<UserShiftConfigDO> userShiftConfigList = userShiftConfigDao.selectRecordByDateRange(userId, firstDayId, lastDayId);
        if (CollectionUtils.isEmpty(userShiftConfigList)) {
            return Lists.newArrayList();
        }
        Map<Long, List<UserShiftConfigDO>> dayMap = userShiftConfigList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getDayId));
        //过滤未排班数据dayId
        List<Long> noClassDayId = userShiftConfigList.stream()
                .filter(item -> Objects.isNull(item.getPunchClassConfigId()))
                .map(UserShiftConfigDO::getDayId)
                .collect(Collectors.toList());
        //过滤已排班数据dayId
        List<Long> classDayId = userShiftConfigList.stream()
                .filter(item -> Objects.nonNull(item.getPunchClassConfigId()))
                .map(UserShiftConfigDO::getDayId)
                .collect(Collectors.toList());
        //遍历日期
        List<AttendanceClassCalendarDTO> dateList = Lists.newArrayList();
        while (firstDayId <= lastDayId) {
            AttendanceClassCalendarDTO dto = AttendanceClassCalendarDTO.builder().dayId(firstDayId).build();
            if (noClassDayId.contains(firstDayId)) {
                //节假日
                List<UserShiftConfigDO> currentDayList = dayMap.get(firstDayId);
                if (CollectionUtils.isNotEmpty(currentDayList)) {
                    dto.setType(currentDayList.get(0).getDayShiftRule());
                }
                dateList.add(dto);
                firstDayId = DateHelper.getNextDayId(firstDayId);
                continue;
            }

            if (classDayId.contains(firstDayId)) {
                firstDayId = DateHelper.getNextDayId(firstDayId);
                continue;
            }
            //未排班
            dto.setType("NONE");
            dateList.add(dto);
            firstDayId = DateHelper.getNextDayId(firstDayId);
        }
        return dateList;
    }

    /**
     * 生成排班明细
     */
    private AttendanceClassDetailDTO buildEmployeeClassDTO(UserShiftConfigDO userShiftConfigDO, Long dayId, String userCode) {
        if (Objects.isNull(userShiftConfigDO.getPunchClassConfigId()) || userShiftConfigDO.getPunchClassConfigId() <= 0) {
            return AttendanceClassDetailDTO.builder().isWork(false).build();
        }
        //获取打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.mapByUserIds(Collections.singletonList(userShiftConfigDO.getUserId()), DateHelper.endOfDay(DateHelper.transferDayIdToDate(dayId)));
        if (MapUtils.isEmpty(punchConfigMap)) {
            return AttendanceClassDetailDTO.builder().isWork(false).build();
        }
        PunchConfigDO punchConfigDO = punchConfigMap.get(userShiftConfigDO.getUserId());
        //获取当前时间的打卡班次及详情
        List<PunchClassItemConfigDO> attendancePunchClassItemConfig = punchClassItemConfigDao.selectByClassIds(Collections.singletonList(userShiftConfigDO.getPunchClassConfigId()));
        punchClassConfigQueryService.transferItemConfigTimeFormat(attendancePunchClassItemConfig, dayId);
        //排序
        List<PunchClassItemConfigDO> itemConfigList = attendancePunchClassItemConfig
                .stream()
                .sorted(Comparator.comparing(PunchClassItemConfigDO::getSortNo))
                .collect(Collectors.toList());
        //自由打卡返回最早最晚时间即可
        PunchClassItemConfigDO itemConfigDO = itemConfigList.get(0);
        if (PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode().equals(punchConfigDO.getConfigType())) {
            return AttendanceClassDetailDTO.builder()
                    .dayId(dayId)
                    .classId(userShiftConfigDO.getPunchClassConfigId())
                    .isWork(true)
                    .startTime(itemConfigDO.getEarliestPunchInTime())
                    .endTime(itemConfigDO.getLatestPunchOutTime())
                    .build();
        }
        //获取当前用户当天打卡信息
        EmployeePunchCardRecordQuery punchCardRecordQuery = new EmployeePunchCardRecordQuery();
        punchCardRecordQuery.setDayId(String.valueOf(dayId));
        punchCardRecordQuery.setUserCode(userCode);
        List<EmployeePunchRecordDO> userPunchRecords = employeePunchRecordDao.listRecords(punchCardRecordQuery).stream()
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime)).collect(Collectors.toList());
        AttendanceClassDetailDTO userClassDetailDTO = AttendanceClassDetailDTO.builder()
                .dayId(dayId)
                .classId(userShiftConfigDO.getPunchClassConfigId())
                .isWork(true)
                .startTime(itemConfigDO.getPunchInTime())
                .endTime(itemConfigList.size() > 1 ? itemConfigList.get(itemConfigList.size() - 1).getPunchOutTime()
                        : itemConfigDO.getPunchOutTime())
                .build();
        //没有打卡记录
        if (CollectionUtils.isEmpty(userPunchRecords)) {
            return userClassDetailDTO;
        }
        //存在多班次则判断最后班次的打卡时间
        if (itemConfigList.size() > 1) {
            itemConfigDO = itemConfigList.get(itemConfigList.size() - 1);
        }
        for (EmployeePunchRecordDO userPunchRecord : userPunchRecords) {
            //忽略秒
            Date punchTime = userPunchRecord.getPunchTime();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(punchTime);
            calendar.set(Calendar.SECOND, 0);
            punchTime = calendar.getTime();
            Date elasticPunchInTime = DateUtil.offsetMinute(itemConfigDO.getPunchInTime()
                    , itemConfigDO.getElasticTime().multiply(BusinessConstant.MINUTES).intValue());
            if (punchTime.compareTo(itemConfigDO.getPunchInTime()) > 0
                    && punchTime.compareTo(elasticPunchInTime) <= 0) {
                //存在弹性范围内打卡，默认下班时间加上弹性时间
                DateTime elasticPunchOutTime = DateUtil.offsetMinute(itemConfigDO.getPunchOutTime()
                        , itemConfigDO.getElasticTime().multiply(BusinessConstant.MINUTES).intValue());
                userClassDetailDTO.setEndTime(elasticPunchOutTime);
                break;
            }
        }
        return userClassDetailDTO;
    }
}
