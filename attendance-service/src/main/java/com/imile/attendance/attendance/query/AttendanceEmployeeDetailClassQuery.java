package com.imile.attendance.attendance.query;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class AttendanceEmployeeDetailClassQuery {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 日期(yyyyMMdd)
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long date;

    /**
     * 是否结束时间(true/false)
     */
    private Boolean isEndTime;

}
