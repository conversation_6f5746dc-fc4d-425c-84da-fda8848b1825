package com.imile.attendance.archive.query;

import com.imile.attendance.query.ResourceQuery;
import com.imile.attendance.rule.dto.RuleConfigSelectDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttendanceArchiveListQuery extends ResourceQuery {

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 部门
     */
    private List<Long> deptIdList;

    /**
     * 岗位
     */
    private List<Long> postIdList;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 是否派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 班次类型
     */
    private String classNature;

    /**
     * 日历ID
     */
    private Long calendarId;

    /**
     * 日历下拉选择
     */
    private RuleConfigSelectDTO calendarSelect;

    /**
     * 打卡规则下拉选择
     */
    private RuleConfigSelectDTO punchConfigSelect;

    /**
     * 补卡规则下拉选择
     */
    private RuleConfigSelectDTO reissueCardConfigSelect;

    /**
     * 加班规则下拉选择
     */
    private RuleConfigSelectDTO overTimeConfigSelect;

    /**
     * 打卡方式
     */
    private String punchCardType;

    /**
     * 打卡方式集合
     */
    private List<String> punchCardTypeList;

    /**
     * 用户ID列表
     */
    private List<Long> userIdList;

    /**
     * 当用户ID超过1000条的处理列表
     */
    private List<List<Long>> batchUserIdList;

    /**
     * 权限常驻国
     */
    private List<String> authLocationCountryList;

    /**
     * 权限部门
     */
    private List<Long> authDeptIdList;

    /**
     * 是否需要查询特殊的国家，影响用工类型
     */
    private Boolean isNeedQuerySpecialCountry = false;

    /**
     * 特殊国家集合
     */
    private List<String> specialCountryList;

    /**
     * 常规国家集合
     */
    private List<String> normalCountryList;

    /**
     *  过滤国家集合
     */
    private List<String> filterCountryList;

    /**
     * 特殊的用工类型集合
     */
    private List<String> specialEmployeeTypeList;

    /**
     * 常规用工类型集合
     */
    private List<String> normalEmployeeTypeList;

    private List<Long> specialDeptList;

    private List<Long> normalDeptList;

    /**
     * 是否导出
     */
    private Boolean arePageExport = Boolean.FALSE;
}
