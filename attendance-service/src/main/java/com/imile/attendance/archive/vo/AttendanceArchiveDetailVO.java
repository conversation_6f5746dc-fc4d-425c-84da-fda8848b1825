package com.imile.attendance.archive.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.user.vo.UserBaseInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Data
public class AttendanceArchiveDetailVO implements Serializable {

    /**
     * 员工基本信息
     */
    private UserBaseInfoVO employeeBaseInfo;

    /**
     * 考勤配置信息
     */
    private AttendanceRuleConfigVO attendanceRuleConfig;

    @Data
    public static class AttendanceRuleConfigVO {
        /**
         * 班次性质
         */
        @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.CLASS_NATURE, ref = "classNatureDesc")
        private String classNature;

        /**
         * 班次性质描述
         */
        private String classNatureDesc;

        /**
         * 班次信息
         */
        private List<ClassVO> classVOList;

        /**
         * 日历ID
         */
        private Long calendarId;

        /**
         * 日历名称
         */
        private String calendarName;

        /**
         * 日历所属国
         */
        private String calendarCountry;

        /**
         * 打卡规则ID
         */
        private Long punchConfigId;

        /**
         * 打卡规则编码
         */
        private String punchConfigNo;

        /**
         * 打卡规则名称
         */
        private String punchConfigName;

        /**
         * 打卡规则所属国
         */
        private String punchConfigCountry;

        /**
         * 补卡规则ID
         */
        private Long reissueCardConfigId;

        /**
         * 补卡规则编码
         */
        private String reissueCardConfigNo;

        /**
         * 补卡规则名称
         */
        private String reissueCardConfigName;

        /**
         * 补卡规则所属国
         */
        private String reissueCardConfigCountry;

        /**
         * 加班规则ID
         */
        private Long overTimeConfigId;

        /**
         * 加班规则编码
         */
        private String overTimeConfigNo;

        /**
         * 加班规则名称
         */
        private String overTimeConfigName;

        /**
         * 加班规则所属国
         */
        private String overTimeConfigCountry;
    }

    /**
     * 班次信息
     */
    @Data
    public static class ClassVO {
        /**
         * 班次ID
         */
        private Long classId;
        /**
         * 班次名称
         */
        private String className;
    }

}
