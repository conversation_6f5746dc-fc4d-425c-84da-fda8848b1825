package com.imile.attendance.shift.dto;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/27 
 * @Description
 */
@Data
public class UserShiftCheckResultDTO {

    private Boolean success = Boolean.TRUE;

    private BatchUserShiftCheckDTO batchUserShiftCheckDTO;

    public static UserShiftCheckResultDTO buildSuccess(){
        UserShiftCheckResultDTO resultDTO = new UserShiftCheckResultDTO();
        resultDTO.setSuccess(Boolean.TRUE);
        resultDTO.setBatchUserShiftCheckDTO(BatchUserShiftCheckDTO.initEmpty());
        return resultDTO;
    }

    public static UserShiftCheckResultDTO buildFail(BatchUserShiftCheckDTO batchUserShiftCheckDTO){
        UserShiftCheckResultDTO resultDTO = new UserShiftCheckResultDTO();
        resultDTO.setSuccess(Boolean.FALSE);
        resultDTO.setBatchUserShiftCheckDTO(batchUserShiftCheckDTO);
        return resultDTO;
    }
}
