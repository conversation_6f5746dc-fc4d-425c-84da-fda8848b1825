package com.imile.attendance.shift.impl;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRecordDO;
import com.imile.attendance.infrastructure.repository.shift.model.ShiftTaskRelateUserDO;
import com.imile.attendance.shift.ShiftTaskManage;
import com.imile.attendance.shift.ShiftTaskService;
import com.imile.attendance.shift.bo.ShiftTask;
import com.imile.attendance.shift.bo.ShiftTaskBO;
import com.imile.attendance.shift.bo.ShiftTaskEnum;
import com.imile.attendance.shift.dto.UserOccupiedByTaskResult;
import com.imile.attendance.util.BaseDOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19
 * @Description
 */
@Slf4j
@Service
public class ShiftTaskServiceImpl implements ShiftTaskService {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private ShiftTaskManage shiftTaskManage;


    private static final String USER_IN_RUNNING_SHIFT_TASK = "SHIFT:TASK:USERID:";

    private static final String RUNNING_SHIFT_TASK = "SHIFT:TASK:RUNNING";

    private static final Integer MAX_SHIFT_TASK_SIZE = 5;


    @Override
    public ShiftTaskBO init(ShiftTask shiftTask) {
        ShiftTaskRecordDO shiftTaskRecordDO = new ShiftTaskRecordDO();
        shiftTaskRecordDO.setId(defaultIdWorker.nextId());
        shiftTaskRecordDO.setTaskFlag(shiftTask.getTaskFlag());
        shiftTaskRecordDO.setShiftType(shiftTask.getShiftSourceEnum().getCategory().getCode());
        shiftTaskRecordDO.setShiftSource(shiftTask.getShiftSourceEnum().getCode());
        shiftTaskRecordDO.setStartDate(new Date());
        shiftTaskRecordDO.setStatus(ShiftTaskEnum.EXECUTING.getCode());
        shiftTaskRecordDO.setTotalCount(shiftTask.getUserIds().size());
        BaseDOUtil.fillDOInsertByUsrOrSystem(shiftTaskRecordDO);

        List<Long> userIds = shiftTask.getUserIds();
        List<ShiftTaskRelateUserDO> shiftTaskRelateUserDOList = userIds.stream()
                .map(userId -> {
                    ShiftTaskRelateUserDO shiftTaskRelateUserDO = new ShiftTaskRelateUserDO();
                    shiftTaskRelateUserDO.setId(defaultIdWorker.nextId());
                    shiftTaskRelateUserDO.setShiftTaskRecordId(shiftTaskRecordDO.getId());
                    shiftTaskRelateUserDO.setUserId(userId);
                    BaseDOUtil.fillDOInsertByUsrOrSystem(shiftTaskRelateUserDO);
                    return shiftTaskRelateUserDO;
                }).collect(Collectors.toList());
        return ShiftTaskBO.of(shiftTaskRecordDO, shiftTaskRelateUserDOList);
    }

    @Override
    public void updateCompleted(ShiftTaskBO shiftTaskBO) {
        ShiftTaskRecordDO shiftTaskRecordDO = shiftTaskBO.getShiftTaskRecordDO();
        shiftTaskRecordDO.setEndDate(new Date());
        shiftTaskRecordDO.setStatus(ShiftTaskEnum.COMPLETED.getCode());
        shiftTaskRecordDO.setExecutedCount(shiftTaskRecordDO.getTotalCount());
        BaseDOUtil.fillDOUpdateByUserOrSystem(shiftTaskRecordDO);
        shiftTaskManage.updateBO(shiftTaskBO);
    }

    @Override
    public void updateFailed(ShiftTaskBO shiftTaskBO) {
        ShiftTaskRecordDO shiftTaskRecordDO = shiftTaskBO.getShiftTaskRecordDO();
        shiftTaskRecordDO.setEndDate(new Date());
        shiftTaskRecordDO.setStatus(ShiftTaskEnum.FAILED.getCode());
        shiftTaskRecordDO.setExecutedCount(shiftTaskRecordDO.getTotalCount());
        BaseDOUtil.fillDOUpdateByUserOrSystem(shiftTaskRecordDO);
        shiftTaskManage.updateBO(shiftTaskBO);
    }

    @Override
    public void batchSetUserOccupiedByTask(List<Long> userIdList, String shiftTaskFlag) {
        for (Long userId : userIdList) {
            RBucket<String> bucket = redissonClient.getBucket(USER_IN_RUNNING_SHIFT_TASK + userId);
            bucket.set(shiftTaskFlag, 10, TimeUnit.HOURS);
        }
    }

    @Override
    public void batchSetUserReleaseByTask(List<Long> userIdList, String shiftTaskFlag) {
        for (Long userId : userIdList) {
            RBucket<String> bucket = redissonClient.getBucket(USER_IN_RUNNING_SHIFT_TASK + userId);
            if (bucket.isExists()) {
                bucket.deleteAsync();
            }
        }
    }

    @Override
    public Boolean checkUsersIsOccupiedByTask(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return false;
        }
        return userIdList.stream()
                .anyMatch(userId -> {
                            RBucket<String> bucket = redissonClient.getBucket(USER_IN_RUNNING_SHIFT_TASK + userId);
                            return bucket.isExists();
                        }
                );
    }

    @Override
    public List<UserOccupiedByTaskResult> getUserOccupiedByTaskResult(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return Collections.emptyList();
        }
        return userIdList.stream()
                .map(userId -> {
                    UserOccupiedByTaskResult userOccupiedByTaskResult = new UserOccupiedByTaskResult();
                    userOccupiedByTaskResult.setUserId(userId);
                    RBucket<String> bucket = redissonClient.getBucket(USER_IN_RUNNING_SHIFT_TASK + userId);
                    String shiftTaskFlag = bucket.get();
                    if (StringUtils.isNotBlank(shiftTaskFlag)) {
                        userOccupiedByTaskResult.setIsOccupied(true);
                        userOccupiedByTaskResult.setOccupiedShiftTaskFlag(shiftTaskFlag);
                    } else {
                        userOccupiedByTaskResult.setIsOccupied(false);
                    }
                    return userOccupiedByTaskResult;
                }).collect(Collectors.toList());
    }

    @Override
    public Integer getRunningTaskSize() {
        RMap<String, Integer> shiftTaskMap = getShiftTaskMap();
        if (shiftTaskMap.isEmpty()) {
            return 0;
        }
        return shiftTaskMap.size();
    }

    @Override
    @Transactional
    public Boolean runTask(ShiftTask shiftTask) {
        List<Long> userIds = shiftTask.getUserIds();
        String taskFlag = shiftTask.getTaskFlag();
        RMap<String, Integer> shiftTaskMap = getShiftTaskMap();
        int currentRunningTaskSize = shiftTaskMap.size();
        log.info("current running task size:{},taskFlags:{}", currentRunningTaskSize, String.join(",", shiftTaskMap.keySet()));
        if (currentRunningTaskSize > MAX_SHIFT_TASK_SIZE) {
            log.warn("current running task size exceeds MAX_SHIFT_TASK_SIZE,taskFlags:{}",
                    String.join(",", shiftTaskMap.keySet()));
        }
        shiftTaskMap.fastPut(taskFlag, userIds.size());
        //执行任务
        try {
            batchSetUserOccupiedByTask(userIds, taskFlag);
            shiftTask.run();
        } catch (Exception e) {
            log.error("execute shift task:{} fail", taskFlag, e);
            batchSetUserReleaseByTask(userIds, taskFlag);
            shiftTaskMap.remove(taskFlag);
            //记录错误信息
            if (e instanceof NullPointerException) {
                shiftTask.setErrorMsg(StringUtils.left(ExceptionUtils.getRootCauseMessage(e), 500));
            } else {
                shiftTask.setErrorMsg(StringUtils.left(e.getMessage(), 500));
            }
            shiftTask.setTaskIsSuccess(false);
            return false;
        }
        batchSetUserReleaseByTask(userIds, taskFlag);
        shiftTaskMap.remove(taskFlag);
        log.info("auto shift task:{} success", taskFlag);
        shiftTask.setTaskIsSuccess(true);
        return true;
    }

    private RMap<String, Integer> getShiftTaskMap() {
        RMap<String, Integer> rMap = redissonClient.getMap(RUNNING_SHIFT_TASK);
        rMap.expire(24, TimeUnit.HOURS);
        return rMap;
    }
}
