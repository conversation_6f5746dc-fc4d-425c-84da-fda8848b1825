package com.imile.attendance.shift.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2025/5/7
 */
@Data
public class UserArchiveShiftQuery {

    @NotNull(message = "userCode cannot be empty")
    private String userCode;

    /**
     * 查询起始时间
     */
    @NotNull(message = "startTime cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @NotNull(message = "endTime cannot be empty")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}
