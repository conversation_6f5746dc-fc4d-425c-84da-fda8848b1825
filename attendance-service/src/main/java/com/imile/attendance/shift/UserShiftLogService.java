package com.imile.attendance.shift;

import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.shift.command.BatchShiftDayCommand;
import com.imile.attendance.shift.command.CycleShiftDayCommand;
import com.imile.attendance.util.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户排班日志服务
 * 负责生成与排班操作相关的日志记录内容，这些内容会被用于记录系统操作日志
 *
 * <AUTHOR> chen
 * @Date 2025/5/15
 */
@Service
public class UserShiftLogService {


    /**
     * 格式化用户信息为"用户名(用户编码)"格式
     *
     * @param attendanceUser 考勤用户对象
     * @return 格式化后的用户信息字符串，如果用户为null则返回null
     */
    private String formatUserInfo(AttendanceUser attendanceUser) {
        if (attendanceUser == null) {
            return null;
        }
        return attendanceUser.getUserName() + "(" + attendanceUser.getUserCode() + ")";
    }

    /**
     * 生成添加单个用户排班的日志记录内容
     * 格式：
     * 设置人员排班：
     * 员工：[人员姓名(人员账号)]
     * 日期：[年-月-日]
     * 班次：[班次名称]
     *
     * @param attendanceUser 考勤用户对象
     * @param addUserShiftConfigList 用户排班配置列表
     * @return 格式化的日志内容
     */
    public String addShiftLogRecord(AttendanceUser attendanceUser,
                                    List<UserShiftConfigDO> addUserShiftConfigList) {
        UserShiftConfigDO userShiftConfigDO = addUserShiftConfigList.get(0);
        return new StringBuilder()
                .append("设置人员排班：").append("\n")
                .append("员工：[").append(formatUserInfo(attendanceUser)).append("]").append("\n")
                .append("日期：[").append(DateHelper.formatYYYYMMDD(userShiftConfigDO.getClassTime())).append("]").append("\n")
                .append("班次：[").append(userShiftConfigDO.getDayShiftRule()).append("]")
                .toString();
    }


    /**
     * 生成批量排班的日志记录内容
     * 格式：
     * 设置批量排班：
     * 员工：[人员姓名(人员账号)]、[人员姓名(人员账号)]...
     * 排班：
     * [日期]:[班次名称]
     * [日期]:[班次名称]
     * ...
     *
     * @param attendanceUsers 考勤用户列表
     * @param batchShiftDayCommandList 批量排班日期命令列表
     * @return 格式化的日志内容
     */
    public String batchShiftLogRecord(List<AttendanceUser> attendanceUsers,
                                      List<BatchShiftDayCommand> batchShiftDayCommandList) {
        StringBuilder sb = new StringBuilder()
                .append("设置批量排班：").append("\n")
                .append("员工：");
        for (int i = 0; i < attendanceUsers.size(); i++) {
            AttendanceUser attendanceUser = attendanceUsers.get(i);
            sb.append("[").append(formatUserInfo(attendanceUser)).append("]");
            if (i < attendanceUsers.size() - 1) {
                sb.append("、");
            }
        }
        sb.append("\n");
        sb.append("排班：").append("\n");
        for (BatchShiftDayCommand batchShiftDayCommand : batchShiftDayCommandList) {
            sb.append("[")
                    .append(DateHelper.formatYYYYMMDD(DateHelper.transferDayIdToDate(batchShiftDayCommand.getDayId())))
                    .append("]")
                    .append(":")
                    .append("[")
                    .append(batchShiftDayCommand.getDayShiftRule())
                    .append("]")
                    .append("\n");
        }
        return sb.toString();
    }


    /**
     * 生成循环排班的日志记录内容
     * 格式：
     * 设置循环排班：
     * 员工：[人员姓名(人员账号)]、[人员姓名(人员账号)]...
     * 周期：[天数]天
     * 排班：
     * 第一天：[班次名称]
     * 第二天：[班次名称]
     * 第三天：[班次名称]
     * ...
     *
     * @param attendanceUsers 考勤用户列表
     * @param cycleShiftDayParamList 循环排班日期参数列表
     * @return 格式化的日志内容
     */
    public String cycleShiftLogRecord(List<AttendanceUser> attendanceUsers,
                                      List<CycleShiftDayCommand> cycleShiftDayParamList) {
        StringBuilder sb = new StringBuilder()
                .append("设置循环排班：").append("\n")
                .append("员工：");
        for (int i = 0; i < attendanceUsers.size(); i++) {
            AttendanceUser attendanceUser = attendanceUsers.get(i);
            sb.append("[").append(formatUserInfo(attendanceUser)).append("]");
            if (i < attendanceUsers.size() - 1) {
                sb.append("、");
            }
        }
        sb.append("\n");
        sb.append("周期：").append(cycleShiftDayParamList.size()).append("天").append("\n");
        sb.append("排班：").append("\n");
        for (int i = 0; i < cycleShiftDayParamList.size(); i++) {
            CycleShiftDayCommand cycleShiftDayCommand = cycleShiftDayParamList.get(i);
            sb.append("第").append(i + 1).append("天：").append("[")
                    .append(StringUtils.defaultIfEmpty(cycleShiftDayCommand.getDayShiftRule(), "-"))
                    .append("]").append("\n");
        }
        return sb.toString();
    }

    /**
     * 生成取消循环排班的日志记录内容
     * 格式：
     * 终止循环排班：
     * 员工：[人员姓名(人员账号)]、[人员姓名(人员账号)]...
     *
     * @param attendanceUsers 考勤用户列表
     * @return 格式化的日志内容
     */
    public String cancelCycleShiftLogRecord(List<AttendanceUser> attendanceUsers) {
        StringBuilder sb = new StringBuilder()
                .append("终止循环排班：").append("\n")
                .append("员工：");
        for (int i = 0; i < attendanceUsers.size(); i++) {
            AttendanceUser attendanceUser = attendanceUsers.get(i);
            sb.append("[").append(formatUserInfo(attendanceUser)).append("]");
            if (i < attendanceUsers.size() - 1) {
                sb.append("、");
            }
        }
        return sb.toString();
    }

    /**
     * 生成导入排班的日志记录内容
     *
     * @return 格式化的日志内容
     */
    public String importShiftLogRecord() {
        return "导入排班";
    }


}
