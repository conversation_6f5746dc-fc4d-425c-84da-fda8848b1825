package com.imile.attendance.shift.application;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.shift.ShiftQueryService;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.shift.command.BatchShiftCommand;
import com.imile.attendance.shift.command.CancelCycleShiftCommand;
import com.imile.attendance.shift.command.CycleShiftCommand;
import com.imile.attendance.shift.command.UserShiftConfigAddCommand;
import com.imile.attendance.infrastructure.repository.shift.dto.UserDayShiftRuleDTO;
import com.imile.attendance.shift.dto.BatchUserShiftCheckDTO;
import com.imile.attendance.shift.dto.UserShiftCheckResultDTO;
import com.imile.attendance.shift.dto.UserShiftImportDTO;
import com.imile.attendance.shift.param.BatchShiftUserShiftRuleParam;
import com.imile.attendance.shift.param.CheckCustomShiftParam;
import com.imile.attendance.shift.param.CheckCycleShiftParam;
import com.imile.attendance.shift.param.CycleShiftUserShiftRuleParam;
import com.imile.attendance.shift.param.UserDayShiftRuleParam;
import com.imile.attendance.shift.query.UserArchiveShiftQuery;
import com.imile.attendance.shift.vo.UserArchiveShiftVO;
import com.imile.common.excel.ExcelCallBackParam;
import com.imile.common.page.PaginationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 排班应用服务
 * 提供排班相关的业务操作，包括：
 * - 排班查询和分页
 * - 班次规则管理
 * - 批量排班处理
 * - 循环排班处理
 * - 排班导入导出
 *
 * <AUTHOR> chen
 * @since 2025/4/21
 */
@Slf4j
@Service
public class ShiftApplicationService {

    @Resource
    private ShiftQueryService shiftQueryService;
    @Resource
    private UserShiftService userShiftService;

    /**
     * 分页查询排班配置信息
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public PaginationResult<UserShiftConfigDTO> page(UserShiftConfigQuery query) {
        return userShiftService.page(query);
    }

    /**
     * 查询班次选项列表
     *
     * @param classNature 班次性质
     * @return 班次选项列表
     */
    public List<PunchClassConfigSelectDTO> queryClassSelects(String classNature) {
        return shiftQueryService.queryClassSelects(classNature);
    }

    /**
     * 批量查询用户排班规则
     *
     * @param shiftRuleParam 包含用户ID列表的查询参数
     * @return 用户排班规则列表
     */
    public List<UserDayShiftRuleDTO> queryBatchShiftUserShiftRule(BatchShiftUserShiftRuleParam shiftRuleParam) {
        return shiftQueryService.queryBatchShiftUserShiftRule(shiftRuleParam.getUserIdList());
    }

    /**
     * 查询循环排班用户规则
     *
     * @param shiftRuleParam 包含用户ID列表和班次ID列表的查询参数
     * @return 用户排班规则列表
     */
    public List<UserDayShiftRuleDTO> queryCycleShiftUserShiftRuleParam(CycleShiftUserShiftRuleParam shiftRuleParam) {
        return shiftQueryService.queryCycleShiftUserShiftRuleParam(shiftRuleParam.getUserIdList(), shiftRuleParam.getClassIdList());
    }

    /**
     * 查询单个用户某天的排班规则
     *
     * @param userDayShiftRuleParam 包含用户ID和日期的查询参数
     * @return 用户排班规则列表
     */
    public List<UserDayShiftRuleDTO> queryUserDayShiftRule(UserDayShiftRuleParam userDayShiftRuleParam) {
        return shiftQueryService.queryUserDayShiftRule(userDayShiftRuleParam.getUserId(), userDayShiftRuleParam.getDayId());
    }

    /**
     * 添加排班配置
     *
     * @param addCommand 添加排班命令
     */
    public void addShift(UserShiftConfigAddCommand addCommand) {
        userShiftService.addShift(addCommand);
    }

    /**
     * 检查批量排班参数
     *
     * @param checkCustomShiftParam 自定义排班检查参数
     * @return 排班检查结果
     */
    public UserShiftCheckResultDTO checkBatchShift(CheckCustomShiftParam checkCustomShiftParam) {
        return userShiftService.checkBatchShift(checkCustomShiftParam);
    }

    /**
     * 执行批量排班
     *
     * @param batchShiftCommand 批量排班命令
     */
    public void batchShift(BatchShiftCommand batchShiftCommand) {
        userShiftService.batchShift(batchShiftCommand);
    }

    /**
     * 检查循环排班参数
     *
     * @param param 循环排班检查参数
     * @return 排班检查结果
     */
    public UserShiftCheckResultDTO checkCycleShift(CheckCycleShiftParam param) {
        return userShiftService.checkCycleShift(param);
    }

    /**
     * 执行循环排班
     *
     * @param cycleShiftCommand 循环排班命令
     */
    public void cycleShift(CycleShiftCommand cycleShiftCommand) {
        userShiftService.cycleShift(cycleShiftCommand);
    }

    /**
     * 取消循环排班
     *
     * @param cancelCycleShiftCommand 取消循环排班命令
     */
    public void cancelCycleShift(CancelCycleShiftCommand cancelCycleShiftCommand) {
        userShiftService.cancelCycleShift(cancelCycleShiftCommand);
    }

    /**
     * 导入排班数据
     * 支持Excel文件导入，返回导入失败的记录列表
     *
     * @param request HTTP请求，包含Excel文件
     * @return 导入失败的记录列表
     */
    public List<UserShiftImportDTO> shiftImport(HttpServletRequest request) {
        ExcelCallBackParam callBackParam = new ExcelCallBackParam(request);

        log.info("------->import shift jobId:{}", callBackParam.getJobId());

        long startTime = System.currentTimeMillis();

        List<UserShiftImportDTO> importList = JSON.parseArray(callBackParam.getPageData(), UserShiftImportDTO.class);
        if (CollectionUtils.isEmpty(importList)) {
            return Collections.emptyList();
        }
        List<UserShiftImportDTO> failImportList = userShiftService.shiftImport(importList);
        log.info(" ------------ import shift,spendTime:{}ms --------------", System.currentTimeMillis() - startTime);

        return failImportList;
    }

    /**
     * 获取排班导出的Excel表头
     *
     * @param query 查询条件
     * @return Excel表头配置列表
     */
    public List<ExcelTitleExportDTO> titleExport(UserShiftConfigQuery query) {
        return userShiftService.titleExport(query);
    }

    /**
     * 导出排班数据
     * 支持分页导出，返回可直接写入Excel的数据
     *
     * @param query 查询条件
     * @return 分页的排班数据
     */
    public PaginationResult<Map<String, String>> shiftExport(UserShiftConfigQuery query) {
        return userShiftService.shiftExport(query);
    }

    /**
     * 员工考勤档案排班情况
     */
    public UserArchiveShiftVO shiftAttendanceArchive(UserArchiveShiftQuery query) {
        return userShiftService.shiftAttendanceArchive(query);
    }

}
