package com.imile.attendance.shift.job;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.param.FixedClassAutoRenewalParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 固定班次自动排班续期任务
 *
 * <AUTHOR>
 * @since 2025/4/23
 */
@Slf4j
@Component
public class FixedClassAutoSchedulingHandler {
    @Resource
    private AutoShiftConfigFactory autoShiftConfigFactory;

    @XxlJob(BusinessConstant.JobHandler.FIXED_CLASS_AUTO_SCHEDULING_HANDLER)
    public ReturnT<String> employeeFixedSchedulingHandler(String param) {
        XxlJobLogger.log("固定班次自动排班续期任务开始,参数为:{}", param);

        // 解析任务参数
        FixedClassAutoRenewalParam classAutoRenewalParam = StringUtils.isNotBlank(param) ?
                JSON.parseObject(param, FixedClassAutoRenewalParam.class) :
                new FixedClassAutoRenewalParam();

        autoShiftConfigFactory.fixedClassAutoRenewal(classAutoRenewalParam);

        XxlJobLogger.log("固定班次自动排班续期任务结束");
        return ReturnT.SUCCESS;
    }


}
