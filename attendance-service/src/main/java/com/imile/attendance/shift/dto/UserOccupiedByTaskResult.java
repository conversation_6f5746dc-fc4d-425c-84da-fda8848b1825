package com.imile.attendance.shift.dto;

import lombok.Data;

/**
 * 用户排班任务占用状态结果
 * 用于查询用户是否被某个排班任务占用，以及被哪个任务占用
 *
 * <AUTHOR> chen
 * @Date 2025/4/19
 */
@Data
public class UserOccupiedByTaskResult {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 是否被任务占用
     * true: 用户当前被某个排班任务占用
     * false: 用户当前未被任何排班任务占用
     */
    private Boolean isOccupied;

    /**
     * 占用该用户的排班任务标识
     * 当isOccupied为true时，表示占用该用户的具体任务标识
     * 当isOccupied为false时，该字段为null
     */
    private String occupiedShiftTaskFlag;
}