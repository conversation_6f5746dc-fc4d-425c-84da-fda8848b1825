package com.imile.attendance.shift.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserEntryRecordManage;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.shift.UserCycleShiftConfigManage;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.shift.command.CycleShiftDayCommand;
import com.imile.attendance.shift.param.FixedClassAutoRenewalParam;
import com.imile.attendance.shift.param.UserAutoShiftParam;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18
 * @Description
 */
@Slf4j
@Component
public class AutoShiftConfigFactory extends UserShiftConfigAbstractFactory {

    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private UserCycleShiftConfigManage userCycleShiftConfigManage;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private CountryService countryService;
    @Resource
    private UserEntryRecordManage entryRecordManage;


    @Override
    public boolean isMatch(String shiftType) {
        return ShiftTypeEnum.AUTO_SHIFT.getCode().equals(shiftType);
    }


    public void autoShift(UserAutoShiftParam userAutoShiftParam) {
        ClassNatureEnum classNatureEnum = userAutoShiftParam.getClassNatureEnum();
        log.info("user classNature:{},Adding auto shift config:{}", classNatureEnum, userAutoShiftParam);
        List<Long> allUserIdList = userAutoShiftParam.getAllUserId(userAutoShiftParam);
        if (CollectionUtils.isEmpty(allUserIdList)) {
            log.info("no user need auto shift, userAutoShiftParam:{}", userAutoShiftParam);
            return;
        }
        doShift(
                allUserIdList,
                ShiftSourceEnum.SYSTEM_SHIFT,
                (taskFlag) -> {
                    Map<Long, CalendarConfigDO> calendarConfigMap = userCalendarService.getCalendarConfigs(allUserIdList);
                    userAutoShiftParam.setUserCalendarConfigMap(calendarConfigMap);
                    userAutoShiftParam.setTaskFlag(taskFlag);
                    doAutoShiftClass(userAutoShiftParam);
                }
        );
    }

    private void doAutoShiftClass(UserAutoShiftParam userAutoShiftParam) {
        // 1. 处理班次新增用户
        List<UserShiftConfigDO> oldUserShiftConfigList = new ArrayList<>();
        List<UserShiftConfigDO> newUserShiftConfigList = new ArrayList<>();
        List<UserCycleShiftConfigDO> oldUserCycleShiftConfigList = new ArrayList<>();
        List<UserCycleShiftConfigDO> newUserCycleShiftConfigList = new ArrayList<>();

        //处理新增用户
        processAddUsers(userAutoShiftParam, oldUserShiftConfigList, newUserShiftConfigList, oldUserCycleShiftConfigList, newUserCycleShiftConfigList);

        // 2. 处理班次移除用户
        processRemoveUsers(userAutoShiftParam, oldUserShiftConfigList, newUserShiftConfigList, oldUserCycleShiftConfigList);

        //处理未变化的用户班次升级
        processNoChangeUsers(userAutoShiftParam, oldUserShiftConfigList, newUserShiftConfigList, oldUserCycleShiftConfigList, newUserCycleShiftConfigList);

        // 3. 批量更新
        userShiftConfigManage.cycleShiftSave(oldUserShiftConfigList, newUserShiftConfigList, oldUserCycleShiftConfigList, newUserCycleShiftConfigList);
    }


    /**
     * 处理班次新增用户
     */
    private void processAddUsers(UserAutoShiftParam userAutoShiftParam,
                                 List<UserShiftConfigDO> oldUserShiftConfigList,
                                 List<UserShiftConfigDO> newUserShiftConfigList,
                                 List<UserCycleShiftConfigDO> oldUserCycleShiftConfigList,
                                 List<UserCycleShiftConfigDO> newUserCycleShiftConfigList) {
        UserAutoShiftParam.PunchClassAddUserParam classAddUserParam = userAutoShiftParam.getPunchClassAddUserParam();
        if (classAddUserParam == null) {
            return;
        }

        // 参数校验
        Long shiftStartDayId = classAddUserParam.getShiftStartDayId();
        Long targetClassId = classAddUserParam.getTargetClassId();
        List<Long> userIdList = classAddUserParam.getUserIdList();
        Boolean isOnlyClearShift = classAddUserParam.getIsOnlyClearShift();

        if (shiftStartDayId == null || targetClassId == null || isOnlyClearShift == null ||
                CollectionUtils.isEmpty(userIdList)) {
            log.info("Invalid parameters for new users: shiftStartDayId={}, targetClassId={}, isOnlyClearShift={}, userIdList={}",
                    shiftStartDayId, targetClassId, isOnlyClearShift, userIdList);
            return;
        }

        Date shiftStartDate = DateHelper.transferDayIdToDate(shiftStartDayId);
        Date shiftEndDate = calculateShiftEndDate(shiftStartDate);
        Map<Long, CalendarConfigDO> userCalendarConfigMap = userAutoShiftParam.getUserCalendarConfigMap();

        // 获取并处理旧的排班配置
        ClassNatureEnum classNatureEnum = userAutoShiftParam.getClassNatureEnum();
        if (classNatureEnum == ClassNatureEnum.FIXED_CLASS) {
            Map<Long, List<UserShiftConfigDO>> oldAutoShiftConfigMap =
                    userShiftConfigManage.getConfigByUserIdsAndDayIdIncludeAfter(userIdList, shiftStartDayId);

            // 将所有旧配置标记为删除
            oldAutoShiftConfigMap.values().stream()
                    .flatMap(List::stream)
                    .peek(item -> {
                        item.setIsLatest(BusinessConstant.N);
                        item.setIsDelete(IsDeleteEnum.YES.getCode());
                        BaseDOUtil.fillDOUpdateBySystem(item);
                    })
                    .forEach(oldUserShiftConfigList::add);
        } else {
            //取消该员工的循环排班配置
            List<UserCycleShiftConfigDO> cycleShiftConifgList = userCycleShiftConfigManage.selectByUserIdList(userIdList);
            cycleShiftConifgList.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                BaseDOUtil.fillDOUpdateBySystem(item);
            });
            oldUserCycleShiftConfigList.addAll(cycleShiftConifgList);

            //取消员工开始日期之后的指定班次的排班
            Map<Long, List<Long>> userNeedClearClassMap = classAddUserParam.getUserNeedClearClassMap();
            List<Long> allUserClearClassIdList = userNeedClearClassMap.values()
                    .stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(allUserClearClassIdList)) {
                log.info("多班次人员的清理排班参数有误，未指定用户需要清理的排班:{}", userAutoShiftParam);
                return;
            }

            Map<Long, List<UserShiftConfigDO>> oldAllShiftConfigMap =
                    userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(userIdList, allUserClearClassIdList, shiftStartDayId);

            // 将所有旧配置标记为删除
            oldAllShiftConfigMap.values().stream()
                    .flatMap(List::stream)
                    .peek(item -> {
                        item.setIsLatest(BusinessConstant.N);
                        item.setIsDelete(IsDeleteEnum.YES.getCode());
                        BaseDOUtil.fillDOUpdateBySystem(item);
                    })
                    .forEach(oldUserShiftConfigList::add);
        }

        // 如果只清理排班，不需要添加新排班
        if (isOnlyClearShift) {
            return;
        }

        //多班次选择自动更新
        if (classNatureEnum == ClassNatureEnum.MULTIPLE_CLASS) {
            multipleClassAutoUpdateShift(oldUserShiftConfigList, newUserShiftConfigList, oldUserCycleShiftConfigList, newUserCycleShiftConfigList, targetClassId, classAddUserParam.getUserNeedClearClassMap());
            return;
        }

        //固定班次自动排班
        for (Long userId : userIdList) {
            userSchedulingHandler(
                    userId,
                    targetClassId,
                    shiftStartDate,
                    shiftEndDate,
                    userCalendarConfigMap.get(userId),
                    userAutoShiftParam.getTaskFlag(),
                    newUserShiftConfigList);
        }
    }


    /**
     * 处理班次移除用户
     */
    private void processRemoveUsers(UserAutoShiftParam userAutoShiftParam,
                                    List<UserShiftConfigDO> oldUserShiftConfigList,
                                    List<UserShiftConfigDO> newUserShiftConfigList,
                                    List<UserCycleShiftConfigDO> oldUserCycleShiftConfigList) {
        UserAutoShiftParam.PunchClassRemoveUserParam removeUserParam = userAutoShiftParam.getPunchClassRemoveUserParam();
        if (removeUserParam == null) {
            return;
        }

        List<UserAutoShiftParam.PunchClassRemoveSingleUserParam> removeUserParams =
                removeUserParam.getClassRemoveSingleUserParams();
        if (CollectionUtils.isEmpty(removeUserParams)) {
            return;
        }

        // 获取日历配置
        Map<Long, CalendarConfigDO> userCalendarConfigMap = userAutoShiftParam.getUserCalendarConfigMap();
        boolean isOnlyClearShift = removeUserParam.getIsOnlyClearShift();

        // 批量获取所有需要删除的用户ID和开始日期，多班次需要清除的排班
        List<Long> allRemoveUserIds = new ArrayList<>();
        Map<Long, Long> userStartDayMap = new HashMap<>();
        List<Long> allClearClassIds = new ArrayList<>();

        for (UserAutoShiftParam.PunchClassRemoveSingleUserParam param : removeUserParams) {
            Long userId = param.getUserId();
            Long startDayId = param.getShiftStartDayId();
            if (userId != null && startDayId != null) {
                allRemoveUserIds.add(userId);
                userStartDayMap.put(userId, startDayId);
            }
            if (CollectionUtils.isEmpty(param.getClearClassIdList())) {
                continue;
            }
            allClearClassIds.addAll(param.getClearClassIdList());
        }

        if (allRemoveUserIds.isEmpty()) {
            return;
        }

        ClassNatureEnum classNatureEnum = userAutoShiftParam.getClassNatureEnum();
        //获取需要清理的循环排班配置，固定排班不需要处理，只有多班次的人员需要清理循环排班
        Map<Long, List<UserCycleShiftConfigDO>> allUserCycleShiftConfigList = classNatureEnum == ClassNatureEnum.FIXED_CLASS ?
                Collections.emptyMap() :
                userCycleShiftConfigManage.selectByUserIdList(allRemoveUserIds)
                        .stream()
                        .collect(Collectors.groupingBy(UserCycleShiftConfigDO::getUserId));

        // 批量获取所有移除用户的排班配置（固定班次的清除员工今日和之后的排班数据，多班次人员清除员工的指定班次今日和之后的排班数据）
        Map<Long, List<UserShiftConfigDO>> allUserShiftConfigMap = classNatureEnum == ClassNatureEnum.FIXED_CLASS ?
                userShiftConfigManage.getConfigByUserIdsAndDayIdIncludeAfter(allRemoveUserIds, Collections.min(userStartDayMap.values())) :
                userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(
                        allRemoveUserIds,
                        allClearClassIds.stream().distinct().collect(Collectors.toList()),
                        Collections.min(userStartDayMap.values())
                );

        // 处理每个用户
        for (UserAutoShiftParam.PunchClassRemoveSingleUserParam param : removeUserParams) {
            Long userId = param.getUserId();
            Long startDayId = param.getShiftStartDayId();

            if (userId == null || startDayId == null) {
                log.info("Invalid parameters for remove user: userId={}, startDayId={}", userId, startDayId);
                continue;
            }

            // 获取该用户的循环排班配置
            List<UserCycleShiftConfigDO> userCycleShiftConfigs = allUserCycleShiftConfigList.getOrDefault(userId, Collections.emptyList());
            userCycleShiftConfigs.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                item.setExpireDate(new Date());
                BaseDOUtil.fillDOUpdateBySystem(item);
            });
            oldUserCycleShiftConfigList.addAll(userCycleShiftConfigs);

            // 获取该用户的历史排班配置
            List<UserShiftConfigDO> userShiftConfigs = allUserShiftConfigMap.getOrDefault(userId, Collections.emptyList())
                    .stream()
                    .filter(config -> config.getDayId() >= startDayId)
                    .collect(Collectors.toList());

            // 标记为删除
            userShiftConfigs.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                item.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateBySystem(item);
            });
            oldUserShiftConfigList.addAll(userShiftConfigs);


            if (isOnlyClearShift) {
                continue;
            }

            Long targetClassId = param.getTargetClassId();
            if (targetClassId == null) {
                log.info("Invalid parameters for remove user: userId={}, targetClassId is null, can not shift", userId);
                continue;
            }

            // 如果不是只清理排班，则添加新排班
            Date removeUserShiftStartDate = DateHelper.transferDayIdToDate(startDayId);
            Date removeUserShiftEndDate = calculateShiftEndDate(removeUserShiftStartDate);

            userSchedulingHandler(
                    userId,
                    targetClassId,
                    removeUserShiftStartDate,
                    removeUserShiftEndDate,
                    userCalendarConfigMap.get(userId),
                    userAutoShiftParam.getTaskFlag(),
                    newUserShiftConfigList
            );
        }
    }


    /**
     * 处理班次未变化用户升级
     */
    private void processNoChangeUsers(UserAutoShiftParam userAutoShiftParam,
                                      List<UserShiftConfigDO> oldUserShiftConfigList,
                                      List<UserShiftConfigDO> newUserShiftConfigList,
                                      List<UserCycleShiftConfigDO> oldUserCycleShiftConfigList,
                                      List<UserCycleShiftConfigDO> newUserCycleShiftConfigList) {
        UserAutoShiftParam.PunchClassNoChangeUserParam noChangeUserParam = userAutoShiftParam.getPunchClassNoChangeUserParam();
        if (noChangeUserParam == null) {
            return;
        }

        // 参数校验
        Long shiftStartDayId = noChangeUserParam.getShiftStartDayId();
        Long targetClassId = noChangeUserParam.getTargetClassId();
        List<Long> userIdList = noChangeUserParam.getUserIdList();
        Boolean isOnlyClearShift = noChangeUserParam.getIsOnlyClearShift();

        if (shiftStartDayId == null || targetClassId == null || isOnlyClearShift == null ||
                CollectionUtils.isEmpty(userIdList)) {
            log.info("Invalid parameters for no change users: shiftStartDayId={}, targetClassId={}, isOnlyClearShift={}, userIdList={}",
                    shiftStartDayId, targetClassId, isOnlyClearShift, userIdList);
            return;
        }

        // 获取并处理旧的排班配置
        ClassNatureEnum classNatureEnum = userAutoShiftParam.getClassNatureEnum();
        if (classNatureEnum == ClassNatureEnum.FIXED_CLASS) {
            Map<Long, List<UserShiftConfigDO>> oldAutoShiftConfigMap =
                    userShiftConfigManage.getConfigByUserIdsAndDayIdIncludeAfter(userIdList, shiftStartDayId);

            // 将所有旧配置标记为删除
            oldAutoShiftConfigMap.values().stream()
                    .flatMap(List::stream)
                    .peek(item -> {
                        item.setIsLatest(BusinessConstant.N);
                        item.setIsDelete(IsDeleteEnum.YES.getCode());
                        BaseDOUtil.fillDOUpdateBySystem(item);
                    })
                    .forEach(oldUserShiftConfigList::add);
        } else {
            //取消该员工的循环排班配置
            List<UserCycleShiftConfigDO> cycleShiftConifgList = userCycleShiftConfigManage.selectByUserIdList(userIdList);
            cycleShiftConifgList.forEach(item -> {
                item.setIsLatest(BusinessConstant.N);
                BaseDOUtil.fillDOUpdateBySystem(item);
            });
            oldUserCycleShiftConfigList.addAll(cycleShiftConifgList);

            //取消员工开始日期之后的指定班次的排班
            Map<Long, List<Long>> userNeedClearClassMap = noChangeUserParam.getUserNeedClearClassMap();
            List<Long> allUserClearClassIdList = userNeedClearClassMap.values()
                    .stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(allUserClearClassIdList)) {
                log.info("多班次人员的清理排班参数有误，未指定用户需要清理的排班:{}", userAutoShiftParam);
                return;
            }

            Map<Long, List<UserShiftConfigDO>> oldAllShiftConfigMap =
                    userShiftConfigManage.getShiftConfigByUserIdsAndClassIdsAndDayIdIncludeAfter(userIdList, allUserClearClassIdList, shiftStartDayId);

            // 将所有旧配置标记为删除
            oldAllShiftConfigMap.values().stream()
                    .flatMap(List::stream)
                    .peek(item -> {
                        item.setIsLatest(BusinessConstant.N);
                        item.setIsDelete(IsDeleteEnum.YES.getCode());
                        BaseDOUtil.fillDOUpdateBySystem(item);
                    })
                    .forEach(oldUserShiftConfigList::add);
        }

        // 如果只清理排班，不需要添加新排班
        if (isOnlyClearShift) {
            return;
        }

        //多班次选择自动更新
        if (classNatureEnum == ClassNatureEnum.MULTIPLE_CLASS) {
            multipleClassAutoUpdateShift(oldUserShiftConfigList, newUserShiftConfigList, oldUserCycleShiftConfigList, newUserCycleShiftConfigList, targetClassId, noChangeUserParam.getUserNeedClearClassMap());
            return;
        }

        //固定班次自动排班 不按照日历排班，根据原先的排班升级班次ID
        fixedClassAutoUpdateShift(oldUserShiftConfigList, newUserShiftConfigList, targetClassId);
    }


    public void userSchedulingHandler(Long userId,
                                      Long classId,
                                      Date startDate,
                                      Date endDate,
                                      CalendarConfigDO calendarConfigDO,
                                      String taskFlag,
                                      List<UserShiftConfigDO> addUserShiftConfigDOList) {
        //无考勤日历，一律不排班
        if (null == calendarConfigDO) {
            return;
        }
        PunchClassConfigDO punchClassConfig = getPunchClassConfig(classId);
        if (null == punchClassConfig) {
            log.info("user:{}'s punchClass config rule is null, not auto shift", userId);
            return;
        }
        //只有为固定班次的用户才需要排班（工作日：人关联的固定班次； 休息日：OFF； 节假日：H）

        //查询日历详情
        CalendarConfigDetailQuery configDetailQuery = new CalendarConfigDetailQuery();
        configDetailQuery.setCalendarConfigId(calendarConfigDO.getId());
        configDetailQuery.setStartTime(DateUtil.beginOfDay(startDate));
        configDetailQuery.setEndTime(DateUtil.endOfDay(endDate));

        List<CalendarConfigDetailDO> calendarConfigDetailDOList = calendarConfigDetailDao.listRecords(configDetailQuery);

        Map<Long, CalendarConfigDetailDO> calendarConfigDetailDOMap = calendarConfigDetailDOList.stream()
                .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, i -> i, (i1, i2) -> i1));

        int dateDiff = DateHelper.getDateDiff(startDate, endDate) + 1;
        Long startDayId = DateHelper.getDayId(startDate);
        for (int i = 0; i < dateDiff; i++) {
            Long dayId = DateHelper.getOffsetDayId(startDayId, i);

            CalendarConfigDetailDO calendarConfigDetailDO = calendarConfigDetailDOMap.get(dayId);
            //当天为工作日
            if (calendarConfigDetailDO == null) {
                shiftDayInfoBuild(
                        userId,
                        dayId,
                        classId,
                        punchClassConfig.getClassName(),
                        calendarConfigDO.getId(),
                        ShiftSourceEnum.SYSTEM_SHIFT,
                        taskFlag,
                        addUserShiftConfigDOList
                );
                continue;
            }
            if (calendarConfigDetailDO.areWeekend()) {
                shiftDayInfoBuild(
                        userId,
                        dayId,
                        null,
                        DayShiftRuleEnum.OFF.getCode(),
                        calendarConfigDO.getId(),
                        ShiftSourceEnum.SYSTEM_SHIFT,
                        taskFlag,
                        addUserShiftConfigDOList
                );
            }
            if (calendarConfigDetailDO.areHOLIDAY()) {
                shiftDayInfoBuild(
                        userId,
                        dayId,
                        null,
                        DayShiftRuleEnum.H.getCode(),
                        calendarConfigDO.getId(),
                        ShiftSourceEnum.SYSTEM_SHIFT,
                        taskFlag,
                        addUserShiftConfigDOList
                );
            }
        }
    }

    private void multipleClassAutoUpdateShift(List<UserShiftConfigDO> oldUserShiftConfigList,
                                              List<UserShiftConfigDO> newUserShiftConfigList,
                                              List<UserCycleShiftConfigDO> oldUserCycleShiftConfigList,
                                              List<UserCycleShiftConfigDO> newUserCycleShiftConfigList,
                                              Long targetClassId,
                                              Map<Long, List<Long>> userNeedClearClassMap) {

        if (CollectionUtils.isNotEmpty(oldUserShiftConfigList)) {
            List<UserShiftConfigDO> addUserShiftList = oldUserShiftConfigList.stream().map(shiftConfig -> {
                UserShiftConfigDO userShiftConfigDO = BeanUtils.convert(shiftConfig, UserShiftConfigDO.class);
                userShiftConfigDO.setId(defaultIdWorker.nextId());
                userShiftConfigDO.setPunchClassConfigId(targetClassId);
                userShiftConfigDO.setIsLatest(BusinessConstant.Y);
                userShiftConfigDO.setIsDelete(IsDeleteEnum.NO.getCode());
                BaseDOUtil.fillDOInsertByUsrOrSystem(userShiftConfigDO);
                return userShiftConfigDO;
            }).collect(Collectors.toList());
            newUserShiftConfigList.addAll(addUserShiftList);
        }

        if (CollectionUtils.isEmpty(oldUserCycleShiftConfigList)) {
            return;
        }

        List<UserCycleShiftConfigDO> userCycleShiftConfigList = oldUserCycleShiftConfigList.stream().map(cycleShiftConfig -> {
            UserCycleShiftConfigDO configDO = BeanUtils.convert(cycleShiftConfig, UserCycleShiftConfigDO.class);
            configDO.setId(defaultIdWorker.nextId());
            configDO.setIsLatest(BusinessConstant.Y);
            configDO.setIsDelete(IsDeleteEnum.NO.getCode());
            BaseDOUtil.fillDOInsertBySystem(configDO);
            if (StringUtils.isNotEmpty(cycleShiftConfig.getDayShiftInfo())) {
                List<Long> oldClassIdList = userNeedClearClassMap.get(configDO.getUserId());
                List<CycleShiftDayCommand> cycleShiftDayCommandList = JSON.parseArray(cycleShiftConfig.getDayShiftInfo(), CycleShiftDayCommand.class);
                for (CycleShiftDayCommand command : cycleShiftDayCommandList) {
                    if (Objects.isNull(command.getClassId())) {
                        continue;
                    }
                    if (oldClassIdList.contains(command.getClassId())) {
                        command.setClassId(targetClassId);
                    }
                }
                configDO.setDayShiftInfo(JSON.toJSONString(cycleShiftDayCommandList));
            }
            return configDO;
        }).collect(Collectors.toList());
        newUserCycleShiftConfigList.addAll(userCycleShiftConfigList);
    }

    private void fixedClassAutoUpdateShift(List<UserShiftConfigDO> oldUserShiftConfigList,
                                           List<UserShiftConfigDO> newUserShiftConfigList,
                                           Long targetClassId) {
        if (CollectionUtils.isNotEmpty(oldUserShiftConfigList)) {
            List<UserShiftConfigDO> addUserShiftList = oldUserShiftConfigList.stream().map(shiftConfig -> {
                UserShiftConfigDO userShiftConfigDO = BeanUtils.convert(shiftConfig, UserShiftConfigDO.class);
                userShiftConfigDO.setId(defaultIdWorker.nextId());
                userShiftConfigDO.setPunchClassConfigId(DayShiftRuleEnum.isRestDay(userShiftConfigDO.getDayShiftRule())
                        ? null : targetClassId);
                userShiftConfigDO.setIsLatest(BusinessConstant.Y);
                userShiftConfigDO.setIsDelete(IsDeleteEnum.NO.getCode());
                BaseDOUtil.fillDOInsertByUsrOrSystem(userShiftConfigDO);
                return userShiftConfigDO;
            }).collect(Collectors.toList());
            newUserShiftConfigList.addAll(addUserShiftList);
        }
    }

    private PunchClassConfigDO getPunchClassConfig(Long classId) {
        return punchClassConfigDao.selectById(classId);
    }

    /**
     * 固定班次自动排班续期
     * <p>
     * 该方法用于为固定班次类型的员工自动生成未来一段时间的排班数据。
     * 它支持按国家或特定用户进行排班
     * </p>
     *
     * @param param 排班参数，包含国家列表、用户编码、开始时间和结束时间等
     */
    public void fixedClassAutoRenewal(FixedClassAutoRenewalParam param) {
        Date nowDate = new Date();
        Long currentDayId = DateHelper.getDayId(nowDate);
        log.info("固定班次自动排班续期开始,dayId:{}", currentDayId);
        XxlJobLogger.log("固定班次自动排班续期开始,dayId:{}", currentDayId);

        // 如果指定了用户编码，则只处理这些用户
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            List<String> userCodes = Arrays.asList(param.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER));
            List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserCodes(userCodes)
                    .stream()
                    .filter(user -> Objects.equals(ClassNatureEnum.FIXED_CLASS.name(), user.getClassNature()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userInfoDOList)) {
                List<Long> userIdList = userInfoDOList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
                doFixedClassAutoRenewal(userIdList, userInfoDOList, param.getStartTime(), param.getEndTime());
            }
            log.info("处理指定用户:{}固定班次自动排班续期结束,dayId:{}", param.getUserCodes(), currentDayId);
            XxlJobLogger.log("处理指定用户:{}固定班次自动排班续期结束,dayId:{}", param.getUserCodes(), currentDayId);
            return;
        }

        // 处理所有国家的固定班次员工
        List<String> countryList = getCountryList(param.getCountryList());
        for (String country : countryList) {
            processCountryUsers(country, param);
        }
        log.info("处理国家的固定班次自动排班续期结束,dayId:{}", currentDayId);
        XxlJobLogger.log("处理国家的固定班次自动排班续期结束,dayId:{}", currentDayId);
    }

    /**
     * 处理指定国家的固定班次用户
     *
     * @param country 国家代码
     * @param param   排班参数
     */
    private void processCountryUsers(String country, FixedClassAutoRenewalParam param) {
        int pageSize = param.getPageSize() != null ? param.getPageSize() : 500;
        int currentPage = 1;

        // 构建查询条件
        UserDaoQuery query = UserDaoQuery.builder()
                .locationCountry(country)
                .status(StatusEnum.ACTIVE.getCode())
                .workStatus(WorkStatusEnum.ON_JOB.getCode())
                .isDriver(BusinessConstant.N)
                .classNature(ClassNatureEnum.FIXED_CLASS.name())
                .build();

        // 第一页查询
        Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
        PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(query));

        // 处理第一页数据
        List<UserInfoDO> userInfoList = pageInfo.getList();
        if (CollUtil.isNotEmpty(userInfoList)) {
            List<Long> userIdList = userInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
            doFixedClassAutoRenewal(userIdList, userInfoList, param.getStartTime(), param.getEndTime());
        }

        log.info("国家：{},固定班次自动排班续期 | currentPage:{},pageSize:{},total:{}",
                country, currentPage, pageSize, pageInfo.getTotal());
        XxlJobLogger.log("国家：{},固定班次自动排班续期 | currentPage:{},pageSize:{},total:{}",
                country, currentPage, pageSize, pageInfo.getTotal());

        // 循环处理剩余页
        while (currentPage < pageInfo.getPages()) {
            currentPage++;
            log.info("国家：{},currentPage：{}，pages：{}", country, currentPage, pageInfo.getPages());
            XxlJobLogger.log("国家：{},currentPage：{}，pages：{}", country, currentPage, pageInfo.getPages());

            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(query));
            userInfoList = pageInfo.getList();

            if (CollectionUtils.isNotEmpty(userInfoList)) {
                List<Long> userIdList = userInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
                doFixedClassAutoRenewal(userIdList, userInfoList, param.getStartTime(), param.getEndTime());
            }

            log.info("国家：{},currentPage {}，处理完成", country, currentPage);
            XxlJobLogger.log("国家：{},currentPage {}，处理完成", country, currentPage);
        }
    }

    /**
     * 执行固定班次自动排班续期
     *
     * @param userIdList   用户ID列表
     * @param userInfoList 用户信息列表
     * @param startDate    开始日期
     * @param endDate      结束日期
     */
    private void doFixedClassAutoRenewal(List<Long> userIdList, List<UserInfoDO> userInfoList, Date startDate, Date endDate) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }

        //查询员工入职记录map <userId, UserEntryRecordDO>
        Map<Long, UserEntryRecordDO> userEntryRecordMap = entryRecordManage.mapByUserIds(userIdList);

        // 使用doShift方法执行排班任务
        doShift(
                userIdList,
                ShiftSourceEnum.SYSTEM_SHIFT_RENEWAL,
                (taskFlag) -> {
                    // 查询用户的考勤日历
                    List<CalendarConfigRangeDO> calendarConfigRangeList = calendarManage.selectCalendarConfigRange(userIdList);
                    if (CollectionUtils.isEmpty(calendarConfigRangeList)) {
                        log.info("固定班次自动排班续期 - 日历查询为空");
                        XxlJobLogger.log("固定班次自动排班续期 - 日历查询为空");
                        return;
                    }

                    Map<Long, List<CalendarConfigRangeDO>> calendarConfigRangeMap = calendarConfigRangeList.stream()
                            .collect(Collectors.groupingBy(CalendarConfigRangeDO::getBizId));

                    // 查询固定班次用户关联的最新班次
                    Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigManage.selectTopPriorityByUserIds(userIdList);
                    if (MapUtils.isEmpty(punchClassConfigMap)) {
                        log.info("固定班次自动排班续期 - 班次查询为空");
                        XxlJobLogger.log("固定班次自动排班续期 - 班次查询为空");
                        return;
                    }

                    // 设置排班时间范围
                    Date actualStartDate = startDate;
                    Date actualEndDate = endDate;
                    if (actualStartDate == null || actualEndDate == null) {
                        CountryDTO countryDTO = countryService.queryCountry(userInfoList.get(0).getLocationCountry());
                        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
                        actualStartDate = DateUtils.dayBegin(date);
                        actualEndDate = calculateShiftEndDate(date);
                    }

                    // 查询用户已存在的排班记录
                    List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(
                            userIdList,
                            DateHelper.getDayId(actualStartDate),
                            DateHelper.getDayId(actualEndDate)
                    );
                    Map<Long, List<UserShiftConfigDO>> shiftConfigGroupByUserId = userShiftConfigList.stream()
                            .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

                    // 新增排班数据列表
                    List<UserShiftConfigDO> addUserShiftConfigList = new ArrayList<>();

                    Long actualStartDayId = DateHelper.getDayId(actualStartDate);
                    // 逐个处理用户排班
                    for (UserInfoDO userInfo : userInfoList) {
                        // 查询用户入职记录,有入职记录才处理,如果员工实际入职日期小于排班日期则不处理
                        UserEntryRecordDO userEntryRecord = userEntryRecordMap.get(userInfo.getId());
                        if (Objects.nonNull(userEntryRecord)) {
                            Date confirmDate = userEntryRecord.getConfirmDate();
                            if (Objects.nonNull(confirmDate)) {
                                if (isConfirmDateAfterNowDay(confirmDate, actualStartDayId)) {
                                    log.info("固定班次员工{}排班自动刷新,入职日期{}晚于开始排班日期{}，不处理", userInfo.getUserCode(), confirmDate, actualStartDate);
                                    XxlJobLogger.log("固定班次员工{}排班自动刷新,入职日期{}晚于开始排班日期{}，不处理", userInfo.getUserCode(), confirmDate, actualStartDate);
                                    continue;
                                }
                            }
                        }

                        // 查询日历范围
                        List<CalendarConfigRangeDO> userCalendarList = calendarConfigRangeMap.get(userInfo.getId());
                        if (CollectionUtils.isEmpty(userCalendarList)) {
                            log.info("用户：{} 日历范围匹配异常", userInfo.getUserCode());
                            XxlJobLogger.log("用户：{} 日历范围匹配异常", userInfo.getUserCode());
                            continue;
                        }

                        PunchClassConfigDO punchClassConfig = punchClassConfigMap.get(userInfo.getId());
                        if (Objects.isNull(punchClassConfig)) {
                            log.info("用户：{} 没有班次", userInfo.getUserCode());
                            XxlJobLogger.log("用户：{} 没有班次", userInfo.getUserCode());
                            continue;
                        }

                        List<UserShiftConfigDO> userShiftList = shiftConfigGroupByUserId.getOrDefault(userInfo.getId(), new ArrayList<>());

                        // 处理单个用户排班
                        processUserScheduling(
                                userInfo.getId(),
                                actualStartDate,
                                actualEndDate,
                                userCalendarList.get(0),
                                punchClassConfig,
                                userShiftList,
                                taskFlag,
                                addUserShiftConfigList
                        );
                    }

                    // 批量保存排班记录
                    if (!addUserShiftConfigList.isEmpty()) {
                        userShiftConfigManage.batchShiftUpdateOrAdd(null, addUserShiftConfigList);
                    }
                }
        );
    }

    /**
     * 处理单个用户的排班
     *
     * @param userId                 用户ID
     * @param startDate              开始日期
     * @param endDate                结束日期
     * @param calendarConfigRange    日历配置范围
     * @param punchClassConfig       班次配置
     * @param existingShiftList      已存在的排班记录
     * @param taskFlag               任务标识
     * @param addUserShiftConfigList 新增排班记录列表
     */
    private void processUserScheduling(Long userId,
                                       Date startDate,
                                       Date endDate,
                                       CalendarConfigRangeDO calendarConfigRange,
                                       PunchClassConfigDO punchClassConfig,
                                       List<UserShiftConfigDO> existingShiftList,
                                       String taskFlag,
                                       List<UserShiftConfigDO> addUserShiftConfigList) {
        // 查询日历详情
        CalendarConfigDetailQuery configDetailQuery = new CalendarConfigDetailQuery();
        configDetailQuery.setCalendarConfigId(calendarConfigRange.getAttendanceConfigId());
        configDetailQuery.setStartTime(DateUtil.beginOfDay(startDate));
        configDetailQuery.setEndTime(DateUtil.endOfDay(endDate));

        List<CalendarConfigDetailDO> calendarDetailList = calendarConfigDetailDao.listRecords(configDetailQuery);
        Map<Long, CalendarConfigDetailDO> calendarDetailMap = calendarDetailList.stream()
                .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, i -> i, (i1, i2) -> i1));

        // 计算日期差
        int dateDiff = DateHelper.getDateDiff(startDate, endDate) + 1;

        // 排班记录处理为map
        Map<Long, UserShiftConfigDO> existingShiftMap = existingShiftList.stream()
                .collect(Collectors.toMap(UserShiftConfigDO::getDayId, Function.identity(), (oldVal, newVal) -> oldVal));

        // 逐天处理排班
        for (int i = 0; i < dateDiff; i++) {
            Date date = DateUtil.offsetDay(startDate, i);
            Long dayId = DateHelper.getDayId(date);

            // 检查该天是否已有排班记录
            UserShiftConfigDO userDayShiftConfig = existingShiftMap.get(dayId);
            if (userDayShiftConfig != null) {
                log.info("用户：{} 在dayId: {} 已存在排班记录:{}，跳过", userId, dayId, userDayShiftConfig.getDayShiftRule());
                // 已有排班记录，跳过
                continue;
            }

            // 获取日历详情
            CalendarConfigDetailDO calendarDetail = calendarDetailMap.get(dayId);

            // 根据日历类型生成不同的排班记录
            if (calendarDetail == null) {
                // 工作日：使用固定班次
                shiftDayInfoBuild(
                        userId,
                        dayId,
                        punchClassConfig.getId(),
                        punchClassConfig.getClassName(),
                        calendarConfigRange.getId(),
                        ShiftSourceEnum.SYSTEM_SHIFT,
                        taskFlag,
                        addUserShiftConfigList
                );
            } else if (calendarDetail.areWeekend()) {
                // 周末：安排休息(OFF)
                shiftDayInfoBuild(
                        userId,
                        dayId,
                        null,
                        DayShiftRuleEnum.OFF.getCode(),
                        calendarConfigRange.getId(),
                        ShiftSourceEnum.SYSTEM_SHIFT,
                        taskFlag,
                        addUserShiftConfigList
                );
            } else if (calendarDetail.areHOLIDAY()) {
                // 节假日：安排假期(H)
                shiftDayInfoBuild(
                        userId,
                        dayId,
                        null,
                        DayShiftRuleEnum.H.getCode(),
                        calendarConfigRange.getId(),
                        ShiftSourceEnum.SYSTEM_SHIFT,
                        taskFlag,
                        addUserShiftConfigList
                );
            }
        }
    }

    /**
     * 获取国家列表
     *
     * @param countryListStr 国家列表字符串，逗号分隔
     * @return 国家代码列表
     */
    private List<String> getCountryList(String countryListStr) {
        if (StringUtils.isNotBlank(countryListStr)) {
            return Arrays.asList(countryListStr.split(","));
        }
        return Arrays.stream(CountryCodeEnum.values())
                .map(CountryCodeEnum::getCode)
                .collect(Collectors.toList());
    }

    /**
     * 计算排班结束日期
     * <p>
     * 根据开始日期和天数计算排班结束日期，确保结束日期是当天的最后一刻
     * </p>
     *
     * @param startDate 开始日期
     * @return 计算后的结束日期
     */
    private Date calculateShiftEndDate(Date startDate) {
        return DateUtils.dayEnd(DateUtil.offsetDay(startDate, BusinessConstant.MAX_AUTO_SHIFT_DAYS));
    }


    /**
     * 员工确认入职时间是否大于当前指定日期，不排班
     */
    private boolean isConfirmDateAfterNowDay(Date confirmDate, Long dayId) {
        if (null == confirmDate) {
            return true;
        }
        Long confirmDayId = DateHelper.getDayId(confirmDate);
        if (null == confirmDayId) {
            return true;
        }
        return confirmDayId.compareTo(dayId) > 0;
    }
}
