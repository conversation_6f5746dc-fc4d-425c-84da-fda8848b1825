package com.imile.attendance.shift.mapstruct;

import com.imile.attendance.infrastructure.config.MapperConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> chen
 * @Date 2025/4/19 
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface ShiftConfigMapstruct {

    ShiftConfigMapstruct INSTANCE = Mappers.getMapper(ShiftConfigMapstruct.class);
}
