package com.imile.attendance.shift.command;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Data
public class BatchShiftCommand {

    /**
     * 循环排班用户
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<Long> userIdList;

    /**
     * 循环排班周期班次信息
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<BatchShiftDayCommand> batchShiftDayParamList;

    /**
     * 来自页面排班
     */
    private Boolean fromPage = Boolean.FALSE;
}
