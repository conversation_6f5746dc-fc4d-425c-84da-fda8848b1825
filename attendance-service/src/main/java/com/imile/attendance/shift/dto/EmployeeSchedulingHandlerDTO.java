package com.imile.attendance.shift.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EmployeeSchedulingHandlerDTO implements Serializable {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 考勤日历id
     */
    private Long calendarConfigId;

    /**
     * 班次ID
     */
    private Long punchClassConfigId;

    /**
     * 排班开始时间
     */
    private Date startDate;

}
