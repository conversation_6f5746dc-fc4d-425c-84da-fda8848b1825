package com.imile.attendance.shift.factory;

import com.imile.attendance.abnormal.UserAttendanceAbnormalTrigger;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.env.EnvHelper;
import com.imile.attendance.infrastructure.excel.ExcelFailData;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.shift.UserShiftLogService;
import com.imile.attendance.shift.dto.UserShiftImportDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21
 * @Description
 */
@Slf4j
@Component
public class ImportShiftConfigFactory extends UserShiftConfigAbstractFactory {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private UserShiftLogService userShiftLogService;
    @Resource
    private EnvHelper envHelper;
    @Resource
    private UserAttendanceAbnormalTrigger userAttendanceAbnormalTrigger;

    @Override
    public boolean isMatch(String shiftType) {
        return ShiftTypeEnum.CUSTOM_SHIFT.getCode().equals(shiftType);
    }

    /**
     * 导入排班
     *
     * @param importList 导入数据列表
     * @return 导入失败的数据列表
     */
    public List<UserShiftImportDTO> importShift(List<UserShiftImportDTO> importList) {
        //设置行号，错误数据顺序返回
        Integer dataStartSortNum = 2;
        for (UserShiftImportDTO importDTO : importList) {
            if (Objects.isNull(importDTO.getRowNum())) {
                importDTO.setRowNum(dataStartSortNum);
            }
            dataStartSortNum++;
        }
        ExcelFailData<UserShiftImportDTO> excelFailData = new ExcelFailData<>();
        List<UserShiftImportDTO> filterImportDTOList = shiftImportCheck(importList, excelFailData);
        // failImportList不为空返回错误数据
        if (excelFailData.isNotEmpty()) {
            return excelFailData.getFailData();
        }
        Map<Long, List<UserShiftImportDTO>> userShiftImportMapByUserId = filterImportDTOList.stream()
                .collect(Collectors.groupingBy(UserShiftImportDTO::getUserId));
        doShift(
                new ArrayList<>(userShiftImportMapByUserId.keySet()),
                ShiftSourceEnum.IMPORT_SHIFT,
                (taskFlag) -> {
                    //待删除的历史旧排班
                    List<UserShiftConfigDO> oldUserShiftConfigDOList = new ArrayList<>();
                    //新增排班
                    List<UserShiftConfigDO> addUserShiftConfigDOList = new ArrayList<>();

                    for (Map.Entry<Long, List<UserShiftImportDTO>> entry : userShiftImportMapByUserId.entrySet()) {
                        Long userId = entry.getKey();
                        List<UserShiftImportDTO> shiftImportDTOList = entry.getValue();
                        //以导入的天数为准，导入几天改几天，没有导入的数据无需修改
                        if (CollectionUtils.isEmpty(shiftImportDTOList)) {
                            continue;
                        }
                        //将历史排班的数据修改为非最新
                        List<Long> dayIdList = shiftImportDTOList.stream()
                                .map(UserShiftImportDTO::getClassDayId)
                                .collect(Collectors.toList());
                        List<UserShiftConfigDO> oldUserShiftConfigList = userShiftConfigDao.selectRecordByDayList(userId, dayIdList);
                        for (UserShiftConfigDO item : oldUserShiftConfigList) {
                            item.setIsLatest(BusinessConstant.N);
                            BaseDOUtil.fillDOUpdate(item);
                        }
                        oldUserShiftConfigDOList.addAll(oldUserShiftConfigList);

                        // 新增数据
                        List<UserShiftConfigDO> addUserShiftConfigList = new ArrayList<>();
                        for (UserShiftImportDTO importDTO : shiftImportDTOList) {
                            if (StringUtils.isBlank(importDTO.getDayShiftRule())) {
                                continue;
                            }
                            shiftDayInfoBuild(
                                    userId,
                                    DateHelper.getDayId(importDTO.getDate()),
                                    importDTO.getClassId(),
                                    importDTO.getDayShiftRule(),
                                    importDTO.getAttendanceConfigId(),
                                    ShiftSourceEnum.IMPORT_SHIFT,
                                    taskFlag,
                                    addUserShiftConfigList
                            );
                        }
                        addUserShiftConfigDOList.addAll(addUserShiftConfigList);
                    }
                    userShiftConfigManage.batchShiftUpdateOrAdd(oldUserShiftConfigDOList, addUserShiftConfigDOList);
                    //触发最早时间的用户异常计算
                    if (CollectionUtils.isNotEmpty(addUserShiftConfigDOList)) {
                        List<Long> dayIdList = addUserShiftConfigDOList.stream()
                                .sorted(Comparator.comparing(UserShiftConfigDO::getDayId))
                                .map(UserShiftConfigDO::getDayId)
                                .collect(Collectors.toList());
                        Long dayId = dayIdList.get(0);
                        List<Long> userIds = addUserShiftConfigDOList
                                .stream()
                                .filter(userShift -> Objects.equals(dayId, userShift.getDayId()))
                                .map(UserShiftConfigDO::getUserId)
                                .distinct()
                                .collect(Collectors.toList());
                        userAttendanceAbnormalTrigger.userShiftAbnormalCalculateHandler(userIds, dayIdList.get(0));
                    }
                    logRecordService.recordOperation(
                            addUserShiftConfigDOList.get(0),
                            LogRecordOptions.buildWithRemark(OperationTypeEnum.IMPORT_SHIFT.getCode(),
                                    userShiftLogService.importShiftLogRecord()
                            )
                    );
                }
        );
        return excelFailData.getFailData();
    }

    private List<UserShiftImportDTO> shiftImportCheck(List<UserShiftImportDTO> importList,
                                                      ExcelFailData<UserShiftImportDTO> excelFailData) {
        List<UserShiftImportDTO> firstFilterList = importDataCheckBasicInfo(importList, excelFailData);
        List<UserShiftImportDTO> sencondFilterList = importDataCheckBizInfo(firstFilterList, excelFailData);
        return importDataCheckShiftDay(sencondFilterList, excelFailData);
    }

    private List<UserShiftImportDTO> importDataCheckShiftDay(List<UserShiftImportDTO> secondFilterList,
                                                             ExcelFailData<UserShiftImportDTO> excelFailData) {
        //用户导入的天数判断，最多只能导入180天
        List<UserShiftImportDTO> thirdFilterList = new ArrayList<>();
        Map<String, List<UserShiftImportDTO>> userShiftImportMapByUserCode = secondFilterList.stream()
                .collect(Collectors.groupingBy(UserShiftImportDTO::getUserCode));
        for (Map.Entry<String, List<UserShiftImportDTO>> entry : userShiftImportMapByUserCode.entrySet()) {
            List<UserShiftImportDTO> userShiftImportDTOList = entry.getValue().stream()
                    .sorted(Comparator.comparing(UserShiftImportDTO::getClassDayId))
                    .collect(Collectors.toList());

            userShiftImportDTOList.forEach(item -> {
                if (item.getDate() != null) {
                    Date date = item.getDate();
                    item.setYear((long) DateHelper.year(date));
                    item.setMonth(DateHelper.formatMonth(date));
                    item.setDay((long) DateHelper.dayOfMonth(date));
                    item.setYearMonth(item.getYear().toString() + item.getMonth());
                }
            });

            if (userShiftImportDTOList.size() > BusinessConstant.MAX_AUTO_SHIFT_DAYS) {
                userShiftImportDTOList.forEach(item -> {
                    excelFailData.putData(item, RequestInfoHolder.isChinese() ?
                            "导入天数最多为" + BusinessConstant.MAX_AUTO_SHIFT_DAYS + "天" :
                            "The import period is a maximum of " + BusinessConstant.MAX_AUTO_SHIFT_DAYS + " days");
                });
                continue;
            }

            //不允许导入之前的排班,即本次排班的起始时间必须是当天及其之后(非正式环境不校验历史日期)
            if (envHelper.isProd()) {
                long nowDayId = DateHelper.getDayId(new Date());
                for (UserShiftImportDTO importDTO : userShiftImportDTOList) {
                    if (nowDayId > importDTO.getClassDayId()) {
                        excelFailData.putData(importDTO, RequestInfoHolder.isChinese() ? "排班时间必须在当天之后" :
                                "The schedule time must be later than that day");
                    }
                }
            }
            for (UserShiftImportDTO importDTO : userShiftImportDTOList) {
                if (importDTO.getSuccess()) {
                    thirdFilterList.add(importDTO);
                }
            }
        }
        return thirdFilterList;
    }

    private List<UserShiftImportDTO> importDataCheckBizInfo(List<UserShiftImportDTO> firstFilterList,
                                                            ExcelFailData<UserShiftImportDTO> excelFailData) {
        List<UserShiftImportDTO> secondFilterList = new ArrayList<>();
        //查询员工，排班规则，班次这些信息，用于后续判断是否存在
        List<String> userCodeList = firstFilterList.stream()
                .map(UserShiftImportDTO::getUserCode)
                .distinct()
                .collect(Collectors.toList());
        List<AttendanceUser> userList = userService.listUsersByUserCodes(userCodeList);

        //限制排班的情况(目前是墨西哥仓内劳务)
        List<String> shiftLimitUserCodeList = userList.stream()
                .filter(this::judgeSchedulingLimit)
                .map(AttendanceUser::getUserCode)
                .collect(Collectors.toList());

        firstFilterList.stream()
                .filter(item -> shiftLimitUserCodeList.contains(item.getUserCode()))
                .forEach(item -> {
                    excelFailData.putData(item, RequestInfoHolder.isChinese() ? "墨西哥仓内劳务派遣员工限制排班" :
                            "Mexico warehouse labor dispatch employees are restricted from scheduling");
                });

        firstFilterList = firstFilterList.stream()
                .filter(item -> !shiftLimitUserCodeList.contains(item.getUserCode()))
                .collect(Collectors.toList());

        userList = userList.stream()
                .filter(user -> !shiftLimitUserCodeList.contains(user.getUserCode()))
                .collect(Collectors.toList());

        Map<String, AttendanceUser> userMapByUserCode = userList.stream()
                .collect(Collectors.toMap(AttendanceUser::getUserCode, i -> i, (oldVal, newVal) -> oldVal));

        //查询用户的排班
        List<Long> userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        Map<Long, List<PunchClassConfigSelectDTO>> userClassConfigMap = punchClassConfigQueryService.selectUserClassConfigList(userIdList)
                .stream()
                .collect(Collectors.toMap(UserClassConfigDTO::getUserId, UserClassConfigDTO::getClassConfigSelectList, (oldVal, newVal) -> oldVal));

        //查询用户的日历
        Map<Long, CalendarConfigDO> userCalendarConfigMap = userCalendarService.getCalendarConfigs(userIdList);
        //查询用户的排班日期
        List<Integer> yearList = firstFilterList.stream()
                .map(UserShiftImportDTO::getClassDate)
                .filter(Objects::nonNull)
                .distinct()
                .map(DateHelper::parseYYYYMMDD)
                .map(DateHelper::year)
                .distinct()
                .collect(Collectors.toList());
        //获取用户日历详情
        Map<Long, Map<Long, CalendarConfigDetailDO>> userCalendarDetailMap =
                userCalendarService.getUserCalendarDetailMap(userCalendarConfigMap, yearList);


        //根据用户分组，分别处理
        Map<String, List<UserShiftImportDTO>> userShiftImportDTOListMap = firstFilterList.stream()
                .collect(Collectors.groupingBy(UserShiftImportDTO::getUserCode));

        for (Map.Entry<String, List<UserShiftImportDTO>> entry : userShiftImportDTOListMap.entrySet()) {
            String userCode = entry.getKey();
            List<UserShiftImportDTO> userShiftImportDTOList = entry.getValue();
            if (CollectionUtils.isEmpty(userShiftImportDTOList)) {
                continue;
            }
            //看用户是否存在
            AttendanceUser attendanceUser = userMapByUserCode.get(userCode);
            if (null == attendanceUser) {
                userShiftImportDTOList.forEach(item -> {
                    excelFailData.putData(item, RequestInfoHolder.isChinese() ? "该用户不存在" : "user not exist");
                });
                continue;
            }
            userShiftImportDTOList.forEach(item ->
                    item.setUserId(attendanceUser.getId())
            );

            //一个用户一天多个排班日期校验
            Map<Long, List<UserShiftImportDTO>> userShiftImportDTOListMapByDayId = userShiftImportDTOList.stream()
                    .collect(Collectors.groupingBy(UserShiftImportDTO::getClassDayId));

            for (Map.Entry<Long, List<UserShiftImportDTO>> userDayShiftEntry : userShiftImportDTOListMapByDayId.entrySet()) {
                List<UserShiftImportDTO> userDayShiftImportDTOList = userDayShiftEntry.getValue();
                //将一天多排班的标记为重复
                if (userDayShiftImportDTOList.size() > 1) {
                    userDayShiftImportDTOList.forEach(item -> {
                        excelFailData.putData(item, RequestInfoHolder.isChinese() ? "该日期" + item.getClassDate() + "存在多条排班记录" :
                                "There are multiple shift scheduling records on this date :" + item.getClassDate());
                    });
                }
            }

            //获取用户的日历详情
            Map<Long, CalendarConfigDetailDO> calendarDetailMap =
                    userCalendarDetailMap.getOrDefault(attendanceUser.getId(), Collections.emptyMap());

            for (UserShiftImportDTO userShiftImportDTO : userShiftImportDTOList) {
                Long userId = userShiftImportDTO.getUserId();
                //查询该用户的考勤日历
                CalendarConfigDO calendarConfigDO = userCalendarConfigMap.get(userId);
                if (null == calendarConfigDO) {
                    excelFailData.putData(userShiftImportDTO, RequestInfoHolder.isChinese() ?
                            "该用户没有日历方案，请先创建日历方案" :
                            "The user does not have a calendar scheme. Create a calendar scheme first");
                    continue;
                }
                userShiftImportDTO.setAttendanceConfigId(calendarConfigDO.getId());

                //班次名称处理，去除空格并转成大写
                String className = userShiftImportDTO.getClassName().trim().toUpperCase();
                //处理H,OFF
                if (StringUtils.equals(DayShiftRuleEnum.H.getCode(), className)) {
                    //可以导入节假日，需要判断用户在当天的日历是否为节假日
                    CalendarConfigDetailDO calendarConfigDetailDO = calendarDetailMap.get(userShiftImportDTO.getClassDayId());
                    if (null == calendarConfigDetailDO) {
                        excelFailData.putData(userShiftImportDTO, RequestInfoHolder.isChinese() ?
                                "假期跟日历规则不符，节假日排班不能安排在工作日" : "holiday can not scheduling at working day");
                    } else if (calendarConfigDetailDO.areWeekend()) {
                        excelFailData.putData(userShiftImportDTO, RequestInfoHolder.isChinese() ?
                                "假期跟日历规则不符，节假日排班不能安排在休息日" : "holiday can not scheduling at weekend day");
                    } else if (calendarConfigDetailDO.areHOLIDAY()) {
                        //可以导入节假日
                        userShiftImportDTO.setDayShiftRule(DayShiftRuleEnum.H.getCode());
                        if (userShiftImportDTO.getSuccess()) {
                            secondFilterList.add(userShiftImportDTO);
                        }
                    }
                } else if (StringUtils.equals(DayShiftRuleEnum.OFF.getCode(), className)) {
                    userShiftImportDTO.setDayShiftRule(DayShiftRuleEnum.OFF.getCode());
                    if (userShiftImportDTO.getSuccess()) {
                        secondFilterList.add(userShiftImportDTO);
                    }
                } else {
                    //处理班次情况
                    List<PunchClassConfigSelectDTO> userClassConfigSelectList = userClassConfigMap.get(userId);
                    if (CollectionUtils.isEmpty(userClassConfigSelectList)) {
                        excelFailData.putData(userShiftImportDTO, RequestInfoHolder.isChinese() ?
                                "该用户没有关联班次" : "this user not relate punch class rule");
                        continue;
                    }
                    List<PunchClassConfigSelectDTO> userClassConfigsByClassName = userClassConfigSelectList.stream()
                            //班次名称对比忽略大小写
                            .filter(classConfigSelect -> StringUtils.equalsIgnoreCase(classConfigSelect.getClassName(), className))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(userClassConfigsByClassName)) {
                        excelFailData.putData(userShiftImportDTO, RequestInfoHolder.isChinese() ?
                                "该用户的班次不匹配" : "this user class not match");
                        continue;
                    }
                    PunchClassConfigSelectDTO punchClassConfigSelectDTO = userClassConfigsByClassName.get(0);
                    userShiftImportDTO.setDayShiftRule(punchClassConfigSelectDTO.getClassName());
                    userShiftImportDTO.setClassId(punchClassConfigSelectDTO.getId());
                    if (userShiftImportDTO.getSuccess()) {
                        secondFilterList.add(userShiftImportDTO);
                    }
                }
            }
        }
        return secondFilterList;
    }

    private List<UserShiftImportDTO> importDataCheckBasicInfo(List<UserShiftImportDTO> importList,
                                                              ExcelFailData<UserShiftImportDTO> excelFailData) {
        List<UserShiftImportDTO> filterList = new ArrayList<>();
        for (UserShiftImportDTO importDTO : importList) {
            if (StringUtils.isBlank(importDTO.getUserCode())) {
                excelFailData.putData(importDTO, RequestInfoHolder.isChinese() ? "账号不能为空" : "user account can not be empty");
                continue;
            }
            if (StringUtils.isBlank(importDTO.getUserName())) {
                excelFailData.putData(importDTO, RequestInfoHolder.isChinese() ? "姓名不能为空" : "user name can not be empty");
                continue;
            }
            if (StringUtils.isBlank(importDTO.getClassDate())) {
                excelFailData.putData(importDTO, RequestInfoHolder.isChinese() ? "日期不能为空" : "shift date can not be empty");
                continue;
            }
            //yyyy-MM-dd，校验时间格式
            try {
                Date classDate = DateHelper.parseYYYYMMDD(importDTO.getClassDate());
                //设置classDayId,date
                importDTO.setClassDayId(DateHelper.getDayId(classDate));
                importDTO.setDate(classDate);
            } catch (Exception e) {
                excelFailData.putData(importDTO, RequestInfoHolder.isChinese() ?
                        "排班日期格式有问题" : "There is a problem with the scheduling date format");
                continue;
            }
            if (StringUtils.isBlank(importDTO.getClassName())) {
                excelFailData.putData(importDTO, RequestInfoHolder.isChinese() ?
                        "班次不能为空" : "shift class can not be empty");
                continue;
            }
            filterList.add(importDTO);
        }
        return filterList;
    }
}
