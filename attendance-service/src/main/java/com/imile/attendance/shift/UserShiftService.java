package com.imile.attendance.shift;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.UserCalendarService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.enums.CalendarDayTypeEnum;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftStatusEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.excel.header.ExcelHeaderUtil;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dao.PunchClassConfigDao;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserCycleShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.dto.ShiftDayConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserDayShiftRuleDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserDayShiftRuleDetailDTO;
import com.imile.attendance.infrastructure.repository.shift.dto.UserShiftConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.model.UserCycleShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.query.UserShiftConfigQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.command.BatchShiftCommand;
import com.imile.attendance.shift.command.CancelCycleShiftCommand;
import com.imile.attendance.shift.command.CycleShiftCommand;
import com.imile.attendance.shift.command.UserShiftConfigAddCommand;
import com.imile.attendance.shift.dto.BatchUserShiftCheckDTO;
import com.imile.attendance.shift.dto.EmployeeSchedulingHandlerDTO;
import com.imile.attendance.shift.dto.UserShiftCheckResultDTO;
import com.imile.attendance.shift.dto.UserShiftImportDTO;
import com.imile.attendance.shift.excel.ShiftExportHeaderEnum;
import com.imile.attendance.shift.factory.AutoShiftConfigFactory;
import com.imile.attendance.shift.factory.CustomShiftConfigFactory;
import com.imile.attendance.shift.factory.CycleShiftConfigFactory;
import com.imile.attendance.shift.factory.ImportShiftConfigFactory;
import com.imile.attendance.shift.param.CheckCustomShiftParam;
import com.imile.attendance.shift.param.CheckCycleShiftParam;
import com.imile.attendance.shift.query.UserArchiveShiftQuery;
import com.imile.attendance.shift.vo.UserArchiveShiftVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.Pagination;
import com.imile.common.page.PaginationResult;
import com.imile.util.date.DateUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21
 * @Description 用户排班服务
 */
@Slf4j
@Service
public class UserShiftService {

    @Resource
    private CustomShiftConfigFactory customShiftConfigFactory;
    @Resource
    private CycleShiftConfigFactory cycleShiftConfigFactory;
    @Resource
    private ImportShiftConfigFactory importShiftConfigFactory;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private UserCycleShiftConfigDao userCycleShiftConfigDao;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private UserCycleShiftConfigManage userCycleShiftConfigManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;
    @Resource
    private UserCalendarService userCalendarService;
    @Resource
    private CountryService countryService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private PunchClassConfigDao punchClassConfigDao;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;
    @Resource
    private ShiftQueryService shiftQueryService;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private AutoShiftConfigFactory autoShiftConfigFactory;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private MigrationService migrationService;


    /**
     * 添加排班配置
     *
     * @param addCommand 添加排班命令
     */
    public void addShift(UserShiftConfigAddCommand addCommand) {
        customShiftConfigFactory.addShift(addCommand);
    }

    /**
     * 检查批量排班参数
     *
     * @param checkCustomShiftParam 自定义排班检查参数
     * @return 排班检查结果
     */
    public UserShiftCheckResultDTO checkBatchShift(CheckCustomShiftParam checkCustomShiftParam) {
        BatchUserShiftCheckDTO batchUserShiftCheckDTO = customShiftConfigFactory.checkCustomShift(true,
                checkCustomShiftParam.getUserIdList());
        if (batchUserShiftCheckDTO.isAnyNotMatch()) {
            return UserShiftCheckResultDTO.buildFail(batchUserShiftCheckDTO);
        }
        return UserShiftCheckResultDTO.buildSuccess();
    }

    /**
     * 批量排班
     */
    public void batchShift(BatchShiftCommand batchShiftCommand) {
        customShiftConfigFactory.batchShift(batchShiftCommand);
    }

    /**
     * 检查循环排班参数
     *
     * @param param 循环排班检查参数
     * @return 排班检查结果
     */
    public UserShiftCheckResultDTO checkCycleShift(CheckCycleShiftParam param) {
        BatchUserShiftCheckDTO batchUserShiftCheckDTO =
                cycleShiftConfigFactory.checkCycleShiftUser(true, param.getClassIdList(), param.getUserIdList());
        if (batchUserShiftCheckDTO.isAnyNotMatch()) {
            return UserShiftCheckResultDTO.buildFail(batchUserShiftCheckDTO);
        }
        return UserShiftCheckResultDTO.buildSuccess();
    }

    /**
     * 循环排班
     */
    public void cycleShift(CycleShiftCommand cycleShiftCommand) {
        cycleShiftConfigFactory.cycleShift(cycleShiftCommand);
    }

    /**
     * 取消循环排班
     */
    public void cancelCycleShift(CancelCycleShiftCommand cancelCycleShiftCommand) {
        cycleShiftConfigFactory.cancelCycleShift(cancelCycleShiftCommand);
    }

    /**
     * 导入排班数据
     */
    public List<UserShiftImportDTO> shiftImport(List<UserShiftImportDTO> importList) {
        return importShiftConfigFactory.importShift(importList);
    }


    /**
     * 分页查询
     */
    public PaginationResult<UserShiftConfigDTO> page(UserShiftConfigQuery query) {
        validatePageQuery(query);
        PaginationResult<UserShiftConfigDTO> paginationResult = buildUserShiftConfigQuery(query);
        if (paginationResult != null) {
            return paginationResult;
        }
        PageInfo<UserShiftConfigDTO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount()).doSelectPageInfo(
                () -> userShiftConfigDao.page(query)
        );
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<UserShiftConfigDTO> resultList = pageInfo.getList();
        buildUserShiftConfigResult(query, resultList);
        return PageUtil.getPageResult(resultList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 校验分页查询参数
     */
    private void validatePageQuery(UserShiftConfigQuery query) {
        String classNature = query.getClassNature();
        List<Long> classIdList = query.getClassIdList();
        if (StringUtils.isBlank(classNature)) {
            if (CollectionUtils.isNotEmpty(classIdList)) {
                throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                        "classNature is null, but classIdList is not empty");
            }
        } else {
            //查询的班次分类不为空，固定班次要求classIdList要么为空，要么只有一个，多班次没有要求
            if (StringUtils.equals(classNature, ClassNatureEnum.FIXED_CLASS.name())
                    && CollectionUtils.isNotEmpty(classIdList) && classIdList.size() > 1) {
                throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                        "classNature is FIXED_CLASS, but classIdList size is more than 1");
            }


        }

    }

    /**
     * 排班导出excel的表头
     */
    public List<ExcelTitleExportDTO> titleExport(UserShiftConfigQuery query) {
        Date startTime = query.getStartTime();
        Date endTime = query.getEndTime();
        if (null == startTime || null == endTime) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "startTime or endTime is null");
        }
        // 获取固定表头
        List<ExcelTitleExportDTO> result =
                ExcelHeaderUtil.convertToExportDTOs(ShiftExportHeaderEnum.class, RequestInfoHolder.isChinese());

        // 添加日期表头
        long startDayId = DateHelper.getDayId(startTime);
        long endDayId = DateHelper.getDayId(endTime);

        while (startDayId <= endDayId) {
            String dayFormat = DateHelper.dayIdFormat(startDayId);
            result.add(ExcelTitleExportDTO.of(dayFormat, dayFormat));
            startDayId = DateHelper.getNextDayId(startDayId);
        }
        return result;
    }


    /**
     * 排班导出
     */
    public PaginationResult<Map<String, String>> shiftExport(UserShiftConfigQuery query) {
        PaginationResult<UserShiftConfigDTO> paginationResult = page(query);
        List<UserShiftConfigDTO> results = paginationResult.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        Pagination pagination = paginationResult.getPagination();

        PaginationResult<Map<String, String>> resPaginationResult = new PaginationResult<>();
        resPaginationResult.setPagination(pagination);

        List<Map<String, String>> resList = new ArrayList<>();
        for (UserShiftConfigDTO userShiftConfigDTO : results) {
            Map<String, String> map = new LinkedHashMap<>();
            map.put(ShiftExportHeaderEnum.EMPLOYEE_ID.getEnglishTitle(), userShiftConfigDTO.getUserCode());
            map.put(ShiftExportHeaderEnum.EMPLOYEE_NAME.getEnglishTitle(), userShiftConfigDTO.getUserName());
            map.put(ShiftExportHeaderEnum.COUNTRY.getEnglishTitle(), userShiftConfigDTO.getCountry());
            map.put(ShiftExportHeaderEnum.DEPARTMENT_NAME.getEnglishTitle(), userShiftConfigDTO.getDeptName());
            map.put(ShiftExportHeaderEnum.DESIGNATION.getEnglishTitle(), userShiftConfigDTO.getPostName());
            map.put(ShiftExportHeaderEnum.EMPLOYEE_TYPE.getEnglishTitle(), userShiftConfigDTO.getEmployeeTypeDesc());
            map.put(ShiftExportHeaderEnum.PUNCH_CLASS_TYPE.getEnglishTitle(), ClassNatureEnum.getLocalizedDesc(userShiftConfigDTO.getClassNature()));
            map.put(ShiftExportHeaderEnum.MATCHED_PUNCH_CLASS.getEnglishTitle(), String.valueOf(userShiftConfigDTO.getRelateClassCount()));
            map.put(ShiftExportHeaderEnum.CALENDAR.getEnglishTitle(), userShiftConfigDTO.getCalendarConfigName());

            List<ShiftDayConfigDTO> classDetailList = userShiftConfigDTO.getClassDetail();
            if (CollectionUtils.isNotEmpty(classDetailList)) {
                classDetailList = classDetailList.stream()
                        .sorted(Comparator.comparing(ShiftDayConfigDTO::getDayId))
                        .collect(Collectors.toList());
                classDetailList.forEach(item -> {
                    map.put(item.getDateStrForExport(), item.getDayShiftRule());
                });
            }
            resList.add(map);
        }
        resPaginationResult.setResults(resList);
        logRecordService.recordOperation(
                new UserShiftConfigDO(),
                LogRecordOptions.buildWithRemark(OperationTypeEnum.EXPORT_SHIFT.getCode(),
                        "导出排班")
        );
        return resPaginationResult;
    }

    /**
     * 员工国家&部门变动自动排班处理
     */
    public boolean employeeSchedulingHandler(List<EmployeeSchedulingHandlerDTO> handlerDTOList) {
        if (CollectionUtils.isEmpty(handlerDTOList)) {
            return false;
        }
        List<Long> userIds = handlerDTOList.stream().map(EmployeeSchedulingHandlerDTO::getUserId).distinct().collect(Collectors.toList());
        List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserIdsAndClassNature(userIds, null);
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("员工排班处理查询有效用户为空,userIds:{}", userIds);
            return false;
        }

        //获取日历具体数据
        Date startDate = handlerDTOList.get(0).getStartDate();
        if (Objects.isNull(startDate)) {
            CountryDTO countryDTO = countryService.queryCountry(userInfoDOList.get(0).getLocationCountry());
            Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
            startDate = DateUtils.dayBegin(date);
        }
        Date endDate = DateUtils.dayEnd(DateHelper.pushDate(startDate, 60));
        //这里查一百天的考勤，防止其他排班超过60天，导致删除不干净
        Date attendanceEndDate = DateUtils.dayEnd(DateHelper.pushDate(startDate, 100));

        Map<Long, UserInfoDO> userMap = userInfoDOList.stream().collect(Collectors.toMap(UserInfoDO::getId, o -> o, (v1, v2) -> v1));
        List<Long> attendanceIds = handlerDTOList.stream().map(EmployeeSchedulingHandlerDTO::getCalendarConfigId).distinct().collect(Collectors.toList());
        List<CalendarConfigDO> calendarConfigDOList = calendarManage.getByCalendarConfigIds(attendanceIds);
        Map<Long, CalendarConfigDO> calendarConfigMap = calendarConfigDOList.stream().collect(Collectors.toMap(CalendarConfigDO::getId, o -> o, (v1, v2) -> v1));

        List<Long> classIds = handlerDTOList.stream().map(EmployeeSchedulingHandlerDTO::getPunchClassConfigId).distinct().collect(Collectors.toList());
        List<PunchClassConfigDO> punchClassConfigDOList = punchClassConfigDao.selectByIds(classIds);
        Map<Long, PunchClassConfigDO> punchClassConfigMap = punchClassConfigDOList.stream().collect(Collectors.toMap(PunchClassConfigDO::getId, o -> o, (v1, v2) -> v1));

        //查询用户存在的排班记录
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.selectRecordByUserIdList(userIds, DateHelper.getDayId(startDate), DateHelper.getDayId(attendanceEndDate));
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigDOList.stream().collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        for (EmployeeSchedulingHandlerDTO handlerDTO : handlerDTOList) {
            UserInfoDO userInfoDO = userMap.get(handlerDTO.getUserId());
            if (userInfoDO == null) {
                continue;
            }
            CalendarConfigDO calendarConfigDO = calendarConfigMap.get(handlerDTO.getCalendarConfigId());
            if (calendarConfigDO == null) {
                continue;
            }
            PunchClassConfigDO punchConfigDO = punchClassConfigMap.get(handlerDTO.getPunchClassConfigId());
            if (punchConfigDO == null) {
                continue;
            }

            // 特殊判断：这里拦截一下：如果是mex、BRA国家的仓内人员 并且是多班次的情况，直接返回，不重新排班（班次上下班）：因为仓内人员是班次上线班，触发重新排班会删除之前排好的班次
            if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfoDO.getLocationCountry())
                    && ObjectUtil.equal(userInfoDO.getIsWarehouseStaff(), BusinessConstant.Y)
                    && Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), userInfoDO.getClassNature())) {
                log.info("employeeSchedulingHandler | not need to update shift|userId:{}", userInfoDO.getId());
                continue;
            }

            List<UserShiftConfigDO> userShiftConfigList = userShiftConfigMap.getOrDefault(userInfoDO.getId(), Collections.emptyList());
            userSchedulingHandler(userInfoDO, startDate, endDate, calendarConfigDO, punchConfigDO, userShiftConfigList);
        }
        return true;
    }

    public void userSchedulingHandler(UserInfoDO userInfoDO,
                                      Date startDate,
                                      Date endDate,
                                      CalendarConfigDO calendarConfigDO,
                                      PunchClassConfigDO punchClassConfigDO,
                                      List<UserShiftConfigDO> userShiftConfigDOList) {
        //取消该员工的循环排班
        List<UserCycleShiftConfigDO> cycleShiftConfigDOList = userCycleShiftConfigDao.selectByUserIdList(Collections.singletonList(userInfoDO.getId()));
        cycleShiftConfigDOList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            item.setExpireDate(startDate);
            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
        });
        userCycleShiftConfigDao.updateBatchById(cycleShiftConfigDOList);

        if (CollectionUtils.isNotEmpty(userShiftConfigDOList)) {
            userShiftConfigDOList.forEach(shiftConfig -> {
                shiftConfig.setIsLatest(BusinessConstant.N);
                BaseDOUtil.fillDOUpdateByUserOrSystem(shiftConfig);
            });
            userShiftConfigDao.updateBatchById(userShiftConfigDOList);
        }

        if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), userInfoDO.getClassNature())) {
            return;
        }

        //查询日历详情
        CalendarConfigDetailQuery configDetailQuery = new CalendarConfigDetailQuery();
        configDetailQuery.setCalendarConfigId(calendarConfigDO.getId());
        configDetailQuery.setStartTime(DateUtil.beginOfDay(startDate));
        configDetailQuery.setEndTime(DateUtil.endOfDay(endDate));
        List<CalendarConfigDetailDO> calendarConfigDetailDOList = calendarConfigDetailDao.listRecords(configDetailQuery);
        Map<Long, CalendarConfigDetailDO> calendarConfigDetailMap = calendarConfigDetailDOList.stream()
                .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, i -> i, (i1, i2) -> i1));


        // 新增数据
        List<UserShiftConfigDO> addUserShiftConfigDOList = new ArrayList<>();
        int dateDiff = DateHelper.getDateDiff(startDate, endDate) + 1;
        for (int i = 0; i < dateDiff; i++) {
            Date date = DateHelper.pushDate(startDate, i);
            Long dayId = DateHelper.getDayId(date);

            CalendarConfigDetailDO calendarConfigDetailDO = calendarConfigDetailMap.get(dayId);
            //当天是工作日
            if (calendarConfigDetailDO == null) {
                autoShiftConfigFactory.shiftDayInfoBuild(userInfoDO.getId(), dayId, punchClassConfigDO.getId(), punchClassConfigDO.getClassName(),
                        calendarConfigDO.getId(), ShiftSourceEnum.SYSTEM_SHIFT, "员工变动自动排班", addUserShiftConfigDOList);
                continue;
            }
            //当天是节假日/工作日
            String dayPunchType = StringUtils.equalsIgnoreCase(calendarConfigDetailDO.getDayType(), "WEEKEND") ? "OFF" : "PH";
            autoShiftConfigFactory.shiftDayInfoBuild(userInfoDO.getId(), dayId, null, dayPunchType,
                    calendarConfigDO.getId(), ShiftSourceEnum.SYSTEM_SHIFT, "员工变动自动排班", addUserShiftConfigDOList);
        }
        if (CollectionUtils.isNotEmpty(addUserShiftConfigDOList)) {
            userShiftConfigDao.saveBatch(addUserShiftConfigDOList);
        }
        log.info("initEmployeeScheduling | userCode:{},addEmployeeClassList:{}", userInfoDO.getUserCode(), addUserShiftConfigDOList.size());
    }


    /**
     * 员工确认离职清理排班
     */
    @Transactional(rollbackFor = Exception.class)
    public void userDimissionCleanShift(Long userId, Date actualDimissionDate) {
        if (!migrationService.verifyUserIsEnableAttendanceForCountry(userId)) {
            log.info("userDimissionCleanShift | userInfo is not in new attendance country, userId:{}", userId);
            return;
        }
        userCycleShiftConfigManage.deleteByUserId(userId);
        userShiftConfigManage.updateToOld(userId, actualDimissionDate);
    }

    public UserArchiveShiftVO shiftAttendanceArchive(UserArchiveShiftQuery query) {
        UserArchiveShiftVO result = new UserArchiveShiftVO();
        UserInfoDO userInfoDO = Optional.ofNullable(userInfoDao.getByUserCode(query.getUserCode()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(), I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));

        if (Objects.equals(BusinessConstant.Y, userInfoDO.getIsDriver())) {
            return result;
        }
        //查询日历信息
        Map<Long, CalendarConfigDO> userCalendarMap = userCalendarService.getCalendarConfigs(Collections.singletonList(userInfoDO.getId()));
        if (MapUtils.isEmpty(userCalendarMap)) {
            return result;
        }

        //查询日历配置明细表
        CalendarConfigDetailQuery configDetailQuery = new CalendarConfigDetailQuery();
        configDetailQuery.setCalendarConfigId(userCalendarMap.get(userInfoDO.getId()).getId());
        configDetailQuery.setStartTime(query.getStartTime());
        configDetailQuery.setEndTime(query.getEndTime());
        List<CalendarConfigDetailDO> calendarConfigDetailDOList = calendarConfigDetailDao.listRecords(configDetailQuery);
        Map<Long, CalendarConfigDetailDO> calendarDetailMap = calendarConfigDetailDOList.stream().collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, Function.identity()));

        //查询排班信息
        List<UserShiftConfigDO> userShiftList = userShiftConfigManage.selectRecordByUserIdList(
                        Collections.singletonList(userInfoDO.getId()),
                        DateHelper.getDayId(query.getStartTime()),
                        DateHelper.getDayId(query.getEndTime()))
                .stream().sorted(Comparator.comparing(UserShiftConfigDO::getDayId)).collect(Collectors.toList());

        //排班信息封装
        List<UserArchiveShiftVO.ShiftDayConfigVO> shiftDayConfigInfo = new ArrayList<>();
        result.setShiftDayConfigInfo(shiftDayConfigInfo);

        long startDayId = DateHelper.getDayId(query.getStartTime());
        Long endDayId = DateHelper.getDayId(query.getEndTime());
        //遍历查询的开始和结束时间,构建当前用户的排班信息
        Long temDayId = startDayId;
        while (temDayId.compareTo(endDayId) < 1) {
            //构建当前用户的排班信息
            UserArchiveShiftVO.ShiftDayConfigVO shiftDayConfigVO = new UserArchiveShiftVO.ShiftDayConfigVO();
            shiftDayConfigVO.setDayId(temDayId);
            shiftDayConfigVO.setDate(DateHelper.transferDayIdToDate(temDayId));

            CalendarConfigDetailDO calendarConfigDetailDO = calendarDetailMap.get(temDayId);
            if (Objects.nonNull(calendarConfigDetailDO)) {
                //不为空，则表示当天为节假日或休息日
                if (calendarConfigDetailDO.areHOLIDAY()) {
                    shiftDayConfigVO.setCalendarDayType(CalendarDayTypeEnum.HOLIDAY.getShortCode());
                    //设置法假名称
                    CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO = calendarManage.getLegalLeaveByCalendarConfigIdAndDayId(
                            calendarConfigDetailDO.getAttendanceConfigId(),
                            temDayId
                    );
                    if (calendarLegalLeaveConfigDO != null) {
                        //设置节假日名称
                        shiftDayConfigVO.setHolidayName(calendarLegalLeaveConfigDO.getLegalLeaveName());
                    }
                } else if (calendarConfigDetailDO.areWeekend()) {
                    shiftDayConfigVO.setCalendarDayType(CalendarDayTypeEnum.WEEKEND.getShortCode());
                }
            } else {
                shiftDayConfigVO.setCalendarDayType(CalendarDayTypeEnum.PRESENT.getShortCode());
            }

            //添加当前用户的该天的排班信息
            shiftDayConfigInfo.add(shiftDayConfigVO);
            //后一天
            temDayId = DateHelper.getNextDayId(temDayId);
        }

        //将结果根据天分组
        Map<Long, UserArchiveShiftVO.ShiftDayConfigVO> doFilterConfigMaps = shiftDayConfigInfo.stream()
                .collect(Collectors.toMap(UserArchiveShiftVO.ShiftDayConfigVO::getDayId, Function.identity()));

        //获取已经编辑的排班信息，需要展示出来
        for (UserShiftConfigDO userShiftConfigDO : userShiftList) {
            //获取当前天的排班信息
            UserArchiveShiftVO.ShiftDayConfigVO shiftDayConfigVO = doFilterConfigMaps.get(userShiftConfigDO.getDayId());
            if (Objects.isNull(shiftDayConfigVO)) {
                continue;
            }
            //设置班次时间
            shiftDayConfigVO.setClassTime(userShiftConfigDO.getClassTime());
            //设置班次ID
            shiftDayConfigVO.setClassId(userShiftConfigDO.getPunchClassConfigId());
            //设置排班规则
            shiftDayConfigVO.setDayShiftRule(userShiftConfigDO.getDayShiftRule());
        }
        return result;
    }

    /**
     * 构建用户排班查询条件
     * <p>
     * 该方法处理用户排班查询的前置条件，包括：
     * 1. 验证查询的时间范围参数
     * 2. 处理用户的国家和部门权限
     * 3. 处理特殊国家和普通国家的用工类型
     * 4. 处理排班状态和班次条件关联的人员
     * </p>
     *
     * @param query 用户排班查询对象，包含查询条件（如时间范围、国家、部门、排班状态、班次等）
     * @return 如果查询条件不满足要求（如无权限、无匹配数据等），返回空结果的分页对象；
     * 如果查询条件有效，返回null，表示需要继续执行后续查询
     * @throws BusinessLogicException 当查询的开始时间或结束时间为空时抛出参数验证错误
     * @see UserShiftConfigQuery
     * @see PaginationResult
     */
    private PaginationResult<UserShiftConfigDTO> buildUserShiftConfigQuery(UserShiftConfigQuery query) {
        Date startTime = query.getStartTime();
        Date endTime = query.getEndTime();
        if (null == startTime || null == endTime) {
            throw BusinessLogicException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                    "startTime or endTime is null");
        }
        List<Long> authDeptIdList = attendancePermissionService.getUserDeptPermission();
        if (CollectionUtils.isNotEmpty(query.getDeptIds())) {
            authDeptIdList = authDeptIdList.stream()
                    .filter(deptId -> query.getDeptIds().contains(deptId))
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<String> authLocationCountryList = attendancePermissionService.getUserLocationCountryPermission();
        if (StringUtils.isNotBlank(query.getCountry())) {
            authLocationCountryList = authLocationCountryList.stream()
                    .filter(country -> Objects.equals(country, query.getCountry()))
                    .distinct()
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(authDeptIdList)
                && CollectionUtils.isEmpty(authLocationCountryList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        boolean queryFlag = dealWithQueryCountryAndEmployeeType(query, authLocationCountryList, authDeptIdList);

        if (!queryFlag) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        // 处理排班和班次条件关联的人员
        Set<Long> conditionUserIds = null;
        //排班条件关联的员工
        if (StringUtils.isNotBlank(query.getShiftStatus())) {
            List<Long> shiftConditionUserIds = null;
            if (StringUtils.equals(query.getShiftStatus(), ShiftStatusEnum.SHIFTED.getCode())) {
                shiftConditionUserIds = userShiftConfigManage.getAllHasShiftedUsersInDayRange(
                        DateHelper.getDayId(startTime), DateHelper.getDayId(endTime));
            }
            if (StringUtils.equals(query.getShiftStatus(), ShiftStatusEnum.UN_SHIFTED.getCode())) {
                //未排班是先将已排班查询出来，然后取反
                List<Long> allHasShiftedUsers = userShiftConfigManage.getAllHasShiftedUsersInDayRange(
                        DateHelper.getDayId(startTime), DateHelper.getDayId(endTime));

                List<String> allQueryCountries = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(query.getNormalCountryList())) {
                    allQueryCountries.addAll(query.getNormalCountryList());
                }
                if (CollectionUtils.isNotEmpty(query.getSpecialCountryList())) {
                    allQueryCountries.addAll(query.getSpecialCountryList());
                }
                shiftConditionUserIds = userService.listOnJobNonDriverUserByCountries(allQueryCountries, authDeptIdList)
                        .stream()
                        .map(AttendanceUser::getId)
                        .filter(i -> !allHasShiftedUsers.contains(i))
                        .collect(Collectors.toList());
            }
            if (CollectionUtils.isEmpty(shiftConditionUserIds)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            conditionUserIds = new HashSet<>(shiftConditionUserIds);
        }
        //班次条件关联的员工
        if (CollectionUtils.isNotEmpty(query.getClassIdList())) {
            //根据不同的班次类型分别获取对应的员工
            Set<Long> classConditionUserIds = null;
            ClassNatureEnum classNatureEnum = ClassNatureEnum.getByCode(query.getClassNature());
            switch (classNatureEnum) {
                case FIXED_CLASS:
                    classConditionUserIds = punchClassConfigQueryService.selectAllUserIds(query.getClassNature(), query.getClassIdList());
                    break;
                case MULTIPLE_CLASS:
                    //多班次的特殊查询
                    classConditionUserIds = punchClassConfigQueryService.selectMatchAllClassIdsUserIds(query.getClassNature(), query.getClassIdList());
                    break;
                default:
                    break;
            }
            if (CollectionUtils.isEmpty(classConditionUserIds)) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            // 如果已经有排班条件的用户ID，则取交集；否则直接使用班次条件的用户ID
            if (conditionUserIds != null) {
                conditionUserIds = Sets.intersection(conditionUserIds, classConditionUserIds);
            } else {
                conditionUserIds = classConditionUserIds;
            }
        }

        if (conditionUserIds != null) {
            // 说明进行筛选后符合条件的用户为0，直接返回
            if (conditionUserIds.isEmpty()) {
                return PaginationResult.get(Collections.emptyList(), query);
            }
            // 进行筛选后符合条件的用户加入查询条件
            query.setConditionUserIds(new ArrayList<>(conditionUserIds));
            if (query.getConditionUserIds() != null && query.getConditionUserIds().size() > 1000) {
                // 将ID列表分批，每批1000个
                List<List<Long>> batchedIds = Lists.partition(query.getConditionUserIds(), 1000);
                query.setBatchedConditionUserIds(batchedIds);
            }
        }
        log.info("userShiftList query:{}", JSON.toJSONString(query));
        return null;
    }

    /**
     * 处理查询中的国家和用工类型参数
     * <p>
     * 根据用户权限和查询条件，设置查询对象中的国家和用工类型参数。
     * 系统管理员和普通用户的处理逻辑不同：
     * - 系统管理员：可查询所有国家和对应的用工类型
     * - 普通用户：只能查询其权限范围内的国家和对应的用工类型
     * </p>
     *
     * @param query                   用户排班查询对象，包含查询条件和权限信息
     * @param authLocationCountryList 用户的国家权限信息
     * @throws NullPointerException 如果query或permissionDept为null
     */
    private boolean dealWithQueryCountryAndEmployeeType(UserShiftConfigQuery query,
                                                     List<String> authLocationCountryList,
                                                     List<Long> authDeptList) {
        if (StringUtils.isNotBlank(query.getCountry())) {
            if (!CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(query.getCountry())
                    && CollectionUtils.isNotEmpty(query.getEmployeeTypeList())
                    && query.getEmployeeTypeList().contains(EmploymentTypeEnum.OS_FIXED_SALARY.getCode())) {
                return false;
            }
        }

        if (CollectionUtils.isNotEmpty(authLocationCountryList)) {
            // 检查是否包含特殊国家
            boolean hasSpecialCountry = authLocationCountryList.stream()
                    .anyMatch(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains);

            if (hasSpecialCountry) {
                // 特殊国家和普通国家分组
                Map<Boolean, List<String>> countryMap = authLocationCountryList.stream()
                        .collect(Collectors.groupingBy(CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT::contains));

                query.setIsNeedQuerySpecialCountry(true);
                query.setSpecialCountryList(countryMap.get(true));
                query.setNormalCountryList(countryMap.get(false));

                // 处理用工类型
                if (CollectionUtils.isEmpty(query.getEmployeeTypeList())) {
                    // 未指定用工类型时使用默认值
                    query.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                    query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                } else {
                    // 指定用工类型时取交集
                    query.setSpecialEmployeeTypeList(
                            new ArrayList<>(Sets.intersection(
                                    new HashSet<>(query.getEmployeeTypeList()),
                                    new HashSet<>(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                            ))
                    );
                    query.setNormalEmployeeTypeList(
                            new ArrayList<>(Sets.intersection(
                                    new HashSet<>(query.getEmployeeTypeList()),
                                    new HashSet<>(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                            ))
                    );
                }
            } else {
                query.setNormalCountryList(authLocationCountryList);
                // 只有普通国家
                if (CollectionUtils.isEmpty(query.getEmployeeTypeList())) {
                    query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                } else {
                    query.setNormalEmployeeTypeList(
                            new ArrayList<>(Sets.intersection(
                                    new HashSet<>(query.getEmployeeTypeList()),
                                    new HashSet<>(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                            ))
                    );
                }
            }
        }

        if (CollectionUtils.isNotEmpty(authDeptList)) {
            Map<String, List<AttendanceDept>> countryGroup = deptService.listByDeptIds(authDeptList)
                    .stream()
                    .filter(dept -> StringUtils.isNotBlank(dept.getCountry())
                            && (StringUtils.isBlank(query.getCountry()) || Objects.equals(query.getCountry(), dept.getCountry())))
                    .collect(Collectors.groupingBy(AttendanceDept::getCountry));
            Set<Long> normalDeptList = new HashSet<>();
            Set<Long> specialDeptList = new HashSet<>();
            for (String country : countryGroup.keySet()) {
                List<AttendanceDept> attendanceDeptList = countryGroup.get(country);
                if (CollectionUtils.isEmpty(attendanceDeptList)) {
                    continue;
                }
                List<Long> deptIds = attendanceDeptList.stream().map(AttendanceDept::getId).distinct().collect(Collectors.toList());
                if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(country)) {
                    specialDeptList.addAll(deptIds);
                } else {
                    normalDeptList.addAll(deptIds);
                }
            }
            if (CollectionUtils.isNotEmpty(normalDeptList)) {
                query.setNormalDeptList(new ArrayList<>(normalDeptList));

                // 处理用工类型
                if (CollectionUtils.isEmpty(query.getEmployeeTypeList())) {
                    query.setNormalEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                } else {
                    query.setNormalEmployeeTypeList(
                            new ArrayList<>(Sets.intersection(
                                    new HashSet<>(query.getEmployeeTypeList()),
                                    new HashSet<>(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                            ))
                    );
                }
            }

            if (CollectionUtils.isNotEmpty(specialDeptList)) {
                query.setIsNeedQuerySpecialCountry(true);
                query.setSpecialDeptList(new ArrayList<>(specialDeptList));

                // 处理用工类型
                if (CollectionUtils.isEmpty(query.getEmployeeTypeList())) {
                    query.setSpecialEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
                } else {
                    // 指定用工类型时取交集
                    query.setSpecialEmployeeTypeList(
                            new ArrayList<>(Sets.intersection(
                                    new HashSet<>(query.getEmployeeTypeList()),
                                    new HashSet<>(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                            ))
                    );
                }
            }
        }

        if (CollectionUtils.isEmpty(query.getNormalEmployeeTypeList()) && CollectionUtils.isEmpty(query.getSpecialEmployeeTypeList())) {
            return false;
        }

        //如果正常国家或正常国家的用工类型为空，则将特殊国家和其用工类型调换（去除union的条件）
        if (CollectionUtils.isEmpty(query.getNormalEmployeeTypeList())){
            query.setIsNeedQuerySpecialCountry(false);
            query.setNormalCountryList(query.getSpecialCountryList());
            query.setNormalDeptList(query.getSpecialDeptList());
            query.setNormalEmployeeTypeList(query.getSpecialEmployeeTypeList());
            return true;
        }

        if (CollectionUtils.isEmpty(query.getSpecialEmployeeTypeList())){
            query.setIsNeedQuerySpecialCountry(false);
        }

        return true;
    }

    /**
     * 构建用户排班查询结果
     */
    private void buildUserShiftConfigResult(UserShiftConfigQuery query, List<UserShiftConfigDTO> resultList) {
        // 获取用户ID列表
        List<Long> userIdList = resultList.stream()
                .map(UserShiftConfigDTO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        // 获取用户信息map <userId, AttendanceUser>
        Map<Long, AttendanceUser> userMap = userService.listUsersByIds(userIdList)
                .stream()
                .collect(Collectors.toMap(AttendanceUser::getId, Function.identity()));

        // 获取部门ID列表
        List<Long> deptIdList = resultList.stream()
                .map(UserShiftConfigDTO::getDeptId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 获取部门信息map <deptId, AttendanceDept>
        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIdList).stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));

        // 获取岗位ID列表
        List<Long> postIdList = resultList.stream()
                .map(UserShiftConfigDTO::getPostId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 获取岗位信息map <postId, AttendancePost>
        Map<Long, AttendancePost> postMap = postService.listByPostList(postIdList).stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

        //查询员工入职记录map <userId, UserEntryRecordDO>
        Map<Long, UserEntryRecordDO> userEntryRecordMap = userEntryRecordDao.listByUserIds(userIdList).stream()
                .filter(item -> Objects.equals(EntryStatusEnum.ENTRY.getCode(), item.getEntryStatus()))
                .collect(Collectors.toMap(UserEntryRecordDO::getUserId, Function.identity()));

        //查询员工离职记录map <userId, UserDimissionRecordDO>
        Map<Long, UserDimissionRecordDO> userDimissionRecordDOMap = userDimissionRecordDao.listByUserIds(userIdList).stream()
                .filter(item -> Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), item.getDimissionStatus()))
                .collect(Collectors.toMap(UserDimissionRecordDO::getUserId, Function.identity()));

        //查询用户指定范围内的排班 map <userId, List<UserShiftConfigDO>>
        Map<Long, List<UserShiftConfigDO>> userShiftMap = userShiftConfigManage.selectRecordByUserIdList(
                        userIdList,
                        DateHelper.getDayId(query.getStartTime()),
                        DateHelper.getDayId(query.getEndTime())
                ).stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        //查询用户循环排班规则 map <userId, List<UserCycleShiftConfigDO>>
        Map<Long, List<UserCycleShiftConfigDO>> userCycleShiftMap = userCycleShiftConfigManage.selectByUserIdList(userIdList)
                .stream()
                .collect(Collectors.groupingBy(UserCycleShiftConfigDO::getUserId));

        //获取日历信息 map <userId, CalendarConfigDO>
        Map<Long, CalendarConfigDO> userCalendarMap = userCalendarService.getCalendarConfigs(userIdList);

        //获取查询的年份列表
        List<Integer> queryYears = DateHelper.judgeCrossYear(query.getStartTime(), query.getEndTime()) ?
                Arrays.asList(DateHelper.year(query.getStartTime()), DateHelper.year(query.getEndTime())) :
                Collections.singletonList(DateHelper.year(query.getStartTime()));
        //获取日历详情<userId,<dayId,CalendarConfigDetailDO>>
        Map<Long, Map<Long, CalendarConfigDetailDO>> userCalendarDetailMap = userCalendarService.getUserCalendarDetailMap(userCalendarMap, queryYears);
        //获取法定节假日 map <userId, List<CalendarLegalLeaveConfigDO>>
        Map<Long, List<CalendarLegalLeaveConfigDO>> userLegalLeaveMap = userCalendarService.getUserLegalLeaveMap(userCalendarMap, queryYears);


        //获取用户可以排的班次 map <userId, UserClassConfigDTO>
        List<UserClassConfigDTO> userClassConfigDTOS = punchClassConfigQueryService.selectUserClassConfigList(userIdList);
        Map<Long, UserClassConfigDTO> userClassConfigDTOMap = userClassConfigDTOS.stream()
                .collect(Collectors.toMap(UserClassConfigDTO::getUserId, Function.identity(), (oldVal, newVal) -> oldVal));


        //获取可以编辑的日期
        Date offsetDate = new Date();
        if (StringUtils.isNotBlank(query.getCountry())) {
            CountryDTO countryDTO = countryService.queryCountry(query.getCountry());
            offsetDate = countryDTO.getCountryTimeZoneDate(new Date());
        }
        Long editDayId = DateHelper.getDayId(offsetDate);

        //获取查询的开始和结束时间
        Long startDayId = DateHelper.getDayId(query.getStartTime());
        Long endDayId = DateHelper.getDayId(query.getEndTime());

        //对每个用户进行遍历
        for (UserShiftConfigDTO configDTO : resultList) {
            Long userId = configDTO.getUserId();

            //获取用户信息
            AttendanceUser attendanceUser = userMap.get(userId);
            if (attendanceUser != null) {
                //设置用户用工类型
                EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(attendanceUser.getEmployeeType());
                if (null != employmentTypeEnum) {
                    configDTO.setEmployeeType(employmentTypeEnum.getCode());
                    configDTO.setEmployeeTypeDesc(RequestInfoHolder.isChinese() ?
                            employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
                }
                //设置用户排班限制
                configDTO.setSchedulingLimit(shiftQueryService.judgeSchedulingLimit(attendanceUser) ? BusinessConstant.Y : BusinessConstant.N);
            }
            //获取用户部门信息
            AttendanceDept attendanceDept = deptMap.get(configDTO.getDeptId());
            if (attendanceDept != null) {
                //设置用户部门名称
                configDTO.setDeptName(attendanceDept.getLocalizeName());
                configDTO.setDeptNameEn(attendanceDept.getDeptNameEn());
                configDTO.setDeptNameCn(attendanceDept.getDeptNameCn());
            }
            //获取用户岗位信息
            AttendancePost attendancePost = postMap.get(configDTO.getPostId());
            if (attendancePost != null) {
                //设置用户岗位名称
                configDTO.setPostName(attendancePost.getLocalizeName());
                configDTO.setPostNameEn(attendancePost.getPostNameEn());
                configDTO.setPostNameCn(attendancePost.getPostNameCn());
            }

            //获取员工的实际入职日期
            UserEntryRecordDO userEntryRecordDO = userEntryRecordMap.get(configDTO.getUserId());
            Long confirmDayId = null;
            if (Objects.nonNull(userEntryRecordDO) && Objects.nonNull(userEntryRecordDO.getConfirmDate())) {
                //设置用户实际入职日期
                confirmDayId = DateHelper.getDayId(userEntryRecordDO.getConfirmDate());
                configDTO.setEntryDate(DateHelper.formatDateWithSlash(userEntryRecordDO.getConfirmDate()));
            }

            //获取员工的实际离职日期
            UserDimissionRecordDO userDimissionRecordDO = userDimissionRecordDOMap.get(configDTO.getUserId());
            if (Objects.nonNull(userDimissionRecordDO) && Objects.nonNull(userDimissionRecordDO.getActualDimissionDate())) {
                configDTO.setDimissionDate(DateHelper.formatDateWithSlash(userDimissionRecordDO.getActualDimissionDate()));
            }

            //获取用户循环排班规则
            List<UserCycleShiftConfigDO> cycleShiftConfigDOList = userCycleShiftMap.getOrDefault(userId, Collections.emptyList());
            //设置用户是否循环排班
            configDTO.setIsCycleShift(
                    CollectionUtils.isEmpty(cycleShiftConfigDOList) ? BusinessConstant.N : BusinessConstant.Y);

            //获取用户日历配置
            CalendarConfigDO calendarConfigDO = userCalendarMap.get(userId);
            if (null == calendarConfigDO) {
                // 设置用户无日历导致的排班限制
                configDTO.setNoCalendarSchedulingLimit(BusinessConstant.Y);
            } else {
                //设置用户日历配置名称
                configDTO.setCalendarConfigName(calendarConfigDO.getAttendanceConfigName());
                if (AttendanceTypeEnum.DEFAULT.name().equals(calendarConfigDO.getType()) && !RequestInfoHolder.isChinese()) {
                    configDTO.setCalendarConfigName("Default calendar");
                }
            }

            //获取用户的日历详情
            Map<Long, CalendarConfigDetailDO> calendarDetailMap = userCalendarDetailMap.getOrDefault(userId, Collections.emptyMap());
            //获取用户的法假
            List<CalendarLegalLeaveConfigDO> legalLeaveConfigList = userLegalLeaveMap.getOrDefault(userId, Collections.emptyList());

            //获取用户可用的班次
            List<UserDayShiftRuleDTO> userDayShiftClassDTOList = new ArrayList<>();
            UserClassConfigDTO userClassConfigDTO = userClassConfigDTOMap.get(userId);
            if (userClassConfigDTO != null) {
                //设置用户可用的班次
                List<PunchClassConfigSelectDTO> classConfigSelectList = userClassConfigDTO.getClassConfigSelectList();
                classConfigSelectList.forEach(classConfigSelectDTO -> {
                            userDayShiftClassDTOList.add(UserDayShiftRuleDTO.buildClass(
                                    classConfigSelectDTO.getId(), classConfigSelectDTO.getClassName()
                            ));
                        }
                );
                //设置用户关联的班次数量
                configDTO.setRelateClassCount(classConfigSelectList.size());
                //设置用户可用的班次
                configDTO.setClassConfigSelectDTOList(classConfigSelectList);
            } else {
                configDTO.setRelateClassCount(0);
                configDTO.setClassConfigSelectDTOList(Collections.emptyList());
            }

            //排班信息封装
            List<ShiftDayConfigDTO> shiftDayConfigDTOList = new ArrayList<>();
            configDTO.setClassDetail(shiftDayConfigDTOList);

            //遍历查询的开始和结束时间,构建当前用户的排班信息
            Long temDayId = startDayId;
            while (temDayId.compareTo(endDayId) < 1) {
                //构建当前用户的排班信息
                ShiftDayConfigDTO shiftDayConfigDTO = new ShiftDayConfigDTO();
                //添加当前用户的该天的排班信息
                shiftDayConfigDTOList.add(shiftDayConfigDTO);

                shiftDayConfigDTO.setUserId(userId);
                shiftDayConfigDTO.setDate(DateHelper.transferDayIdToDate(temDayId));
                shiftDayConfigDTO.setDateStrForExport(DateHelper.dayIdFormat(temDayId));
                shiftDayConfigDTO.setDayId(temDayId);
                //在当前时间及之前都无法编辑
                shiftDayConfigDTO.setIsEdit(BusinessConstant.N);
                //（当前日期在可编辑日期范围内，且当前日期在入职日期之后），则设置可编辑
                if (temDayId.compareTo(editDayId) > -1 && (Objects.isNull(confirmDayId) || temDayId.compareTo(confirmDayId) > -1)) {
                    shiftDayConfigDTO.setIsEdit(BusinessConstant.Y);
                }

                //该用户可以选择的排班规则
                List<UserDayShiftRuleDTO> dayShiftRuleDTOList = new ArrayList<>();
                //该用户在当天实际的排班规则类型
                UserDayShiftRuleDetailDTO dayShiftRuleDetailDTO = new UserDayShiftRuleDetailDTO();
                shiftDayConfigDTO.setUserDayShiftRuleList(dayShiftRuleDTOList);
                shiftDayConfigDTO.setUserDayShiftRuleDetailDTO(dayShiftRuleDetailDTO);

                if (MapUtils.isEmpty(calendarDetailMap)) {
                    //后一天
                    temDayId = DateHelper.getNextDayId(temDayId);
                    continue;
                }

                //拼接班次
                if (CollectionUtils.isEmpty(userDayShiftClassDTOList)) {
                    //如果可选的班次为空，无论哪一天都不可编辑
                    shiftDayConfigDTO.setIsEdit(BusinessConstant.N);
                }
                dayShiftRuleDTOList.addAll(userDayShiftClassDTOList);

                //拼接OFF
                dayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildOff());

                //法定节假日的日期才有H的选项，其他日期不可配置H
                CalendarConfigDetailDO calendarConfigDetailDO = calendarDetailMap.get(temDayId);
                if (calendarConfigDetailDO != null) {
                    //如果calendarConfigDetailDO不为空，则表示当天为节假日或休息日
                    if (calendarConfigDetailDO.areHOLIDAY()) {
                        dayShiftRuleDTOList.add(UserDayShiftRuleDTO.buildHoliday());
                        dayShiftRuleDetailDTO.setCalendarDayType(CalendarDayTypeEnum.HOLIDAY.getShortCode());
                        //设置法假名称
                        CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO = userCalendarService.getLegalLeaveConfigByDayId(legalLeaveConfigList, temDayId);
                        if (calendarLegalLeaveConfigDO != null) {
                            //设置节假日名称
                            dayShiftRuleDetailDTO.setHolidayName(calendarLegalLeaveConfigDO.getLegalLeaveName());
                        }
                    } else if (calendarConfigDetailDO.areWeekend()) {
                        dayShiftRuleDetailDTO.setCalendarDayType(CalendarDayTypeEnum.WEEKEND.getShortCode());
                    }
                } else {
                    //calendarConfigDetailDO为空，则表示当天为应出勤日
                    dayShiftRuleDetailDTO.setCalendarDayType(CalendarDayTypeEnum.PRESENT.getShortCode());
                }

                //后一天
                temDayId = DateHelper.getNextDayId(temDayId);
            }

            //将结果根据天分组
            Map<Long, List<ShiftDayConfigDTO>> doFilterConfigMaps = shiftDayConfigDTOList.stream()
                    .collect(Collectors.groupingBy(ShiftDayConfigDTO::getDayId));

            //获取用户的所有已经生成的排班记录
            List<UserShiftConfigDO> userShiftConfigDOList = userShiftMap.getOrDefault(configDTO.getUserId(), Collections.emptyList());
            //根据dayId排序
            userShiftConfigDOList = userShiftConfigDOList.stream()
                    .sorted(Comparator.comparing(UserShiftConfigDO::getDayId))
                    .collect(Collectors.toList());

            //获取用户所有已经生成的排班记录的班次ID列表
            List<Long> userClassIdList = userShiftConfigDOList.stream()
                    .map(UserShiftConfigDO::getPunchClassConfigId)
                    .collect(Collectors.toList());

            //获取班次信息 map <punchClassId, List<PunchClassConfigDO>>
            List<PunchClassConfigDO> punchClassConfigDOS = punchClassConfigDao.selectByIds(userClassIdList);
            Map<Long, List<PunchClassConfigDO>> punchClassMap = punchClassConfigDOS.stream()
                    .collect(Collectors.groupingBy(PunchClassConfigDO::getId));

            //获取已经编辑的排班信息，需要展示出来
            for (UserShiftConfigDO userShiftConfigDO : userShiftConfigDOList) {
                //获取当前天的排班信息
                List<ShiftDayConfigDTO> dayConfigDTOList = doFilterConfigMaps.get(userShiftConfigDO.getDayId());
                if (CollectionUtils.isEmpty(dayConfigDTOList)) {
                    continue;
                }
                ShiftDayConfigDTO shiftDayConfigDTO = dayConfigDTOList.get(0);
                //设置排班信息ID
                shiftDayConfigDTO.setUserShiftConfigId(userShiftConfigDO.getId());
                //设置班次时间
                shiftDayConfigDTO.setClassTime(userShiftConfigDO.getClassTime());
                //设置班次ID
                shiftDayConfigDTO.setClassId(userShiftConfigDO.getPunchClassConfigId());
                //设置排班规则
                shiftDayConfigDTO.setDayShiftRule(userShiftConfigDO.getDayShiftRule());
                //设置是否来自hr的历史排班
                shiftDayConfigDTO.setIsFromHrShift(userShiftConfigDO.areFromHrShift() ?
                        BusinessConstant.ONE : BusinessConstant.ZERO);
                //获取班次信息
                if (MapUtils.isNotEmpty(punchClassMap)) {
                    List<PunchClassConfigDO> recordList = punchClassMap.getOrDefault(userShiftConfigDO.getPunchClassConfigId(), Collections.emptyList());
                    if (CollectionUtils.isNotEmpty(recordList)) {
                        //设置班次名称
                        String className = recordList.get(0).getClassName();
                        shiftDayConfigDTO.setClassName(className);
                        //该用户在当天实际的排班规则类型的名称
                        UserDayShiftRuleDetailDTO userDayShiftRuleDetailDTO = shiftDayConfigDTO.getUserDayShiftRuleDetailDTO();
                        userDayShiftRuleDetailDTO.setRealShiftRuleName(className);
                    }
                }

            }

        }
    }


    public static void main(String[] args) {
        List<Long> conditionUserIds = new ArrayList<>(Sets.intersection(new HashSet<>(), new HashSet<>()));
        System.out.println(conditionUserIds);

        List<Integer> conditionUserIds2 = new ArrayList<>(Sets.intersection(Sets.newHashSet(1, 2), new HashSet<>()));
        System.out.println(conditionUserIds2);

        List<Integer> conditionUserIds3 = new ArrayList<>(Sets.intersection(Sets.newHashSet(1, 2), Sets.newHashSet(2, 3, 4)));
        System.out.println(conditionUserIds3);
    }

}
