package com.imile.attendance.shift.factory;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.UserAttendanceAbnormalTrigger;
import com.imile.attendance.calendar.notify.EmployeeSchedulingParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.enums.shift.ShiftSourceEnum;
import com.imile.attendance.enums.shift.ShiftTypeEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigSelectDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserClassConfigDTO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import com.imile.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/15
 * @Description
 */
@Slf4j
@Service
public class CalendarChangeShiftConfigFactory extends UserShiftConfigAbstractFactory {

    @Resource
    private CountryService countryService;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;
    @Resource
    private UserAttendanceAbnormalTrigger userAttendanceAbnormalTrigger;

    @Override
    public boolean isMatch(String shiftType) {
        return ShiftTypeEnum.AUTO_SHIFT.getCode().equals(shiftType);
    }

    public void handlerEmployeeScheduling(EmployeeSchedulingParam param) {
        log.info("日历变动排班处理: {}", JSON.toJSONString(param));
        // 添加时间过滤(小于当前日期不处理，大于未来180天也不处理)
        if (!checkDayTime(param)) {
            log.info("handlerEmployeeScheduling event checkDayTime is false. | country:{},startDayId:{},endDayId:{}",
                    param.getCountry(), param.getStartDayId(), param.getEndDayId());
            return;
        }

        // 参数包含人员
        if (CollectionUtils.isNotEmpty(param.getUserIds())) {
            // 超过500进行分组
            List<List<Long>> partitionList = Lists.partition(param.getUserIds(), 500);
            for (List<Long> userIdList : partitionList) {
                log.info("handlerEmployeeScheduling event | country:{},userIds:{}", param.getCountry(), userIdList);
                List<UserInfoDO> userInfoDOList = userInfoManage.selectByUserIdsAndClassNature(userIdList, null);
                if (CollectionUtils.isEmpty(param.getDayIds())) {
                    handlerEmployeeDetail(userInfoDOList, param.getStartDayId(), param.getEndDayId(), param.getReSchedule(), param.getDelAllSchedule());
                } else {
                    handlerEmployeeDetail(userInfoDOList, param.getDayIds(), param.getReSchedule());
                }
            }
            return;
        }
        // 参数不包含人员, 根据国家查询人员并且分页
        List<String> countryList = getCountryList(param.getCountry());
        for (String country : countryList) {
            UserDaoQuery query = UserDaoQuery.builder()
                    .locationCountry(country)
                    .status(StatusEnum.ACTIVE.getCode())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .isDriver(BusinessConstant.N)
                    .build();
            pageUserInfoHandle(country, query, param);
        }
    }

    private Boolean checkDayTime(EmployeeSchedulingParam param) {
        if (Objects.isNull(param.getCountry())) {
            return false;
        }
        CountryDTO countryDTO = countryService.queryCountry(param.getCountry());
        Date date = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), new Date());
        Date endDate = DateUtils.dayEnd(DateHelper.pushDate(date, BusinessConstant.MAX_AUTO_SHIFT_DAYS));
        // 最小是当前日期，最大是180天之后
        Long minDayId = DateHelper.getDayId(date);
        Long maxDayId = DateHelper.getDayId(endDate);
        // 校验考勤日
        if (CollectionUtils.isNotEmpty(param.getDayIds())) {
            List<Long> dayIds = param.getDayIds();
            //小于最小日期不处理，大于最大日期不处理
            dayIds.removeIf(item -> (item.compareTo(minDayId) < 0 || item.compareTo(maxDayId) > 0));
            if (CollectionUtils.isEmpty(dayIds)) {
                return false;
            }
            param.setDayIds(dayIds);
            return true;
        }
        // 校验时间段
        Long startDayId = param.getStartDayId();
        Long endDayId = param.getEndDayId();
        if (Objects.isNull(startDayId) || Objects.isNull(endDayId)) {
            return false;
        }

        // 开始日期在180天之后，超过180天不处理
        if (startDayId > maxDayId) {
            return false;
        }
        // 结束日期在当前日期之前，历史日期不处理
        if (endDayId < minDayId) {
            return false;
        }
        // 存在交集关系
        if (startDayId <= maxDayId && endDayId >= minDayId) {
            //最大最小日期完全包含开始结束日期
            if (minDayId <= startDayId && maxDayId >= endDayId) {
                return true;
            }
            //开始结束日期完全包含最大最小日期
            if (startDayId <= minDayId && endDayId >= maxDayId) {
                param.setStartDayId(minDayId);
                param.setEndDayId(maxDayId);
                return true;
            }
            //部分重叠
            if (startDayId <= minDayId && endDayId >= minDayId) {
                param.setStartDayId(minDayId);
                return true;
            }
            if (startDayId >= minDayId && endDayId >= maxDayId) {
                param.setEndDayId(maxDayId);
                return true;
            }
        }
        // 当前日期在结束日期之后
        return false;
    }

    public List<String> getCountryList(String param) {
        if (StringUtils.isNotBlank(param)) {
            String[] arr = (String[]) ConvertUtils.convert(param.split(BusinessConstant.DEFAULT_DELIMITER), String.class);
            return Arrays.asList(arr);
        }
        return Arrays.stream(CountryCodeEnum.values()).map(CountryCodeEnum::getCode).collect(Collectors.toList());
    }

    private void pageUserInfoHandle(String country, UserDaoQuery query, EmployeeSchedulingParam param) {
        int currentPage = 1, pageSize = 500;
        Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
        PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(query));

        // 总记录数
        List<UserInfoDO> pageUserInfoList = pageInfo.getList();
        if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
            log.info("handlerEmployeeScheduling event | country：{},pageUserInfoList size:{}，pageUserInfoList：{}",
                    country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
            handlerEmployeeDetail(pageUserInfoList, param.getStartDayId(), param.getEndDayId()
                    , param.getReSchedule(), param.getDelAllSchedule());
        }
        log.info("handlerEmployeeScheduling event | country：{},pageUserInfoHandle | currentPage:{},pageSize:{},total:{},pages：{}",
                country, currentPage, pageSize, pageInfo.getTotal(), pageInfo.getPages());
        while (currentPage < pageInfo.getPages()) {
            currentPage++;
            log.info("handlerEmployeeScheduling event | country：{},currentPage：{}，pages：{}", country, currentPage, pageInfo.getPages());

            page = PageHelper.startPage(currentPage, pageSize, true);
            pageInfo = page.doSelectPageInfo(() -> userInfoDao.userList(query));
            pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                log.info("handlerEmployeeScheduling event | country：{},,while循环：pageUserInfoList size:{}，pageUserInfoList：{}",
                        country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                handlerEmployeeDetail(pageUserInfoList, param.getStartDayId(), param.getEndDayId()
                        , param.getReSchedule(), param.getDelAllSchedule());
            }
        }
        log.info("handlerEmployeeScheduling event | country：{},currentPage {}，while循环结束", country, currentPage);
    }

    private void handlerEmployeeDetail(List<UserInfoDO> userInfoDOList, Long startDayId, Long endDayId
            , Boolean reSchedule, Boolean delAllSchedule) {
        // 参数校验
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("handlerEmployeeDetail event | userInfoDOList is empty.");
            return;
        }

        if (Objects.isNull(startDayId) || Objects.isNull(endDayId)) {
            log.info("handlerEmployeeDetail event | startDayId/endDayId is empty.");
            return;
        }

        if (endDayId.compareTo(startDayId) < 0) {
            log.info("handlerEmployeeDetail event | endDayId is less than startDayId.");
            return;
        }
        // 通过时间段处理人员排班
        handlerClassEmployeeList(userInfoDOList, null, startDayId, endDayId, reSchedule, delAllSchedule);
    }

    private void handlerEmployeeDetail(List<UserInfoDO> userInfoDOList, List<Long> dayIds, Boolean reSchedule) {
        // 参数校验
        if (CollectionUtils.isEmpty(userInfoDOList)) {
            log.info("handlerEmployeeDetail event | userInfoDOList is empty.");
            return;
        }

        if (CollectionUtils.isEmpty(dayIds)) {
            log.info("handlerEmployeeDetail event | dayIds is empty.");
            return;
        }
        // 通过考勤日处理人员排班
        handlerClassEmployeeList(userInfoDOList, dayIds, null, null, reSchedule, false);
    }

    private void handlerClassEmployeeList(List<UserInfoDO> userInfoDOList,
                                          List<Long> dayIds,
                                          Long startDayId,
                                          Long endDayId,
                                          Boolean reSchedule,
                                          Boolean delAllSchedule) {
        List<Long> userIds = userInfoDOList.stream().map(UserInfoDO::getId).distinct().collect(Collectors.toList());

        // 先查询当前用户配置的考勤日历
        List<CalendarConfigRangeDO> calendarConfigRangeList = calendarConfigRangeDao.selectConfigRange(userIds);
        if (CollectionUtils.isEmpty(calendarConfigRangeList)) {
            log.info("handlerEmployeeDetail event | calendarConfigRangeList is empty.");
            return;
        }
        Map<Long, CalendarConfigRangeDO> calendarConfigRangeMap = calendarConfigRangeList.stream()
                .collect(Collectors.toMap(CalendarConfigRangeDO::getBizId, o -> o, (v1, v2) -> v1));

        List<UserClassConfigDTO> userClassConfigDTOList = punchClassConfigQueryService.selectUserClassConfigList(userIds);
        if (CollectionUtils.isEmpty(userClassConfigDTOList)) {
            log.info("handlerEmployeeDetail event | userClassConfigDTOList is empty.");
            return;
        }
        Map<Long, UserClassConfigDTO> userClassConfigDTOMap = userClassConfigDTOList.stream()
                .collect(Collectors.toMap(UserClassConfigDTO::getUserId, Function.identity(), (v1, v2) -> v1));

        // 查询用户存在的排班记录
        List<UserShiftConfigDO> userShiftConfigDOList;
        // 判断根据考勤日还是时间段查询
        if (CollectionUtils.isEmpty(dayIds)) {
            userShiftConfigDOList = userShiftConfigDao.selectRecordByUserIdList(userIds, startDayId, delAllSchedule ? null : endDayId);
        } else {
            userShiftConfigDOList = userShiftConfigDao.selectBatchUserRecord(userIds, dayIds);
        }
        Map<Long, List<UserShiftConfigDO>> userShiftConfigDOMap = userShiftConfigDOList.stream()
                .collect(Collectors.groupingBy(UserShiftConfigDO::getUserId));

        for (UserInfoDO userInfoDO : userInfoDOList) {
            CalendarConfigRangeDO calendarConfigRangeDO = calendarConfigRangeMap.get(userInfoDO.getId());
            log.info("handlerEmployeeDetail event | userCode:{},calendarConfigRangeDO:{}",
                    userInfoDO.getUserCode(), JSON.toJSONString(calendarConfigRangeDO));
            if (calendarConfigRangeDO == null) {
                continue;
            }

            UserClassConfigDTO userClassConfigDTO = userClassConfigDTOMap.get(userInfoDO.getId());
            if (userClassConfigDTO == null) {
                log.info("handlerEmployeeDetail event userClassConfigDTO is null");
                continue;
            }

            log.info("handlerEmployeeDetail event | userCode:{},classConfigSelectList:{}",
                    userInfoDO.getUserCode(), JSON.toJSONString(userClassConfigDTO.getClassConfigSelectList()));

            // 特殊判断：这里拦截一下：如果仓内推广国家人员 并且是多班次的情况，不重新排班直接返回
            if (Lists.newArrayList(CountryCodeEnum.MEX.getCode(), CountryCodeEnum.BRA.getCode()).contains(userInfoDO.getLocationCountry())
                    && ObjectUtil.equal(userInfoDO.getIsWarehouseStaff(), BusinessConstant.Y)
                    && Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), userInfoDO.getClassNature())) {
                log.info("EmployeeSchedulingService |not need to update shift|userId:{}", userInfoDO.getId());
                continue;
            }

            List<UserShiftConfigDO> userShiftConfigList = userShiftConfigDOMap.get(userInfoDO.getId());
            if (CollectionUtils.isEmpty(userShiftConfigList)) {
                userShiftConfigList = new ArrayList<>();
            }
            Long dayId;
            if (CollectionUtils.isEmpty(dayIds)) {
                // 根据时间段重新排班
                Date startDate = DateHelper.transferDayIdToDate(startDayId);
                Date endDate = DateHelper.transferDayIdToDate(endDayId);
                ((CalendarChangeShiftConfigFactory) AopContext.currentProxy()).userSchedulingHandler(userInfoDO, null, startDate, endDate,
                        calendarConfigRangeDO.getAttendanceConfigId(), userClassConfigDTO, userShiftConfigList, reSchedule);
                dayId = startDayId;
            } else {
                // 根据考勤日重新排班
                ((CalendarChangeShiftConfigFactory) AopContext.currentProxy()).userSchedulingHandler(userInfoDO, dayIds, null, null,
                        calendarConfigRangeDO.getAttendanceConfigId(), userClassConfigDTO, userShiftConfigList, reSchedule);
                dayId = dayIds.get(0);
            }
            //触发第一天的异常计算
            userAttendanceAbnormalTrigger.userShiftAbnormalCalculateHandler(Collections.singletonList(userInfoDO.getId()), dayId);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void userSchedulingHandler(UserInfoDO userInfoDO,
                                      List<Long> dayIds,
                                      Date startDate,
                                      Date endDate,
                                      Long calendarConfigId,
                                      UserClassConfigDTO userClassConfigDTO,
                                      List<UserShiftConfigDO> userShiftConfigDOList,
                                      Boolean reSchedule) {
        //更新历史排班记录
        updateClassEmployeeConfig(userShiftConfigDOList, reSchedule);

        //多班次不自动排班
        if (Objects.equals(ClassNatureEnum.MULTIPLE_CLASS.name(), userInfoDO.getClassNature())) {
            return;
        }

        // 新增数据
        List<UserShiftConfigDO> addUserShiftConfigDOList = new ArrayList<>();
        //查询日历详情
        CalendarConfigDetailQuery configDetailQuery = new CalendarConfigDetailQuery();
        configDetailQuery.setCalendarConfigId(calendarConfigId);
        if (CollectionUtils.isNotEmpty(dayIds)) {
            configDetailQuery.setDayIds(dayIds);
        } else {
            configDetailQuery.setStartTime(DateUtil.beginOfDay(startDate));
            configDetailQuery.setEndTime(DateUtil.endOfDay(endDate));
        }
        List<CalendarConfigDetailDO> calendarConfigDetailDOList = calendarConfigDetailDao.listRecords(configDetailQuery);
        Map<Long, CalendarConfigDetailDO> calendarConfigDetailDOMap = calendarConfigDetailDOList.stream()
                .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, i -> i, (i1, i2) -> i1));


        if (CollectionUtils.isNotEmpty(dayIds)) {
            //获取打卡配置数据
            for (Long dayId : dayIds) {
                // 构建排班数据
                buildClassEmployeeConfig(userShiftConfigDOList, dayId, calendarConfigDetailDOMap, userClassConfigDTO, userInfoDO, calendarConfigId, addUserShiftConfigDOList, reSchedule);
            }
        } else {
            int dateDiff = DateHelper.getDateDiff(startDate, endDate) + 1;
            for (int i = 0; i < dateDiff; i++) {
                Date date = DateHelper.pushDate(startDate, i);
                Long dayId = DateHelper.getDayId(date);
                // 构建排班数据
                buildClassEmployeeConfig(userShiftConfigDOList, dayId, calendarConfigDetailDOMap, userClassConfigDTO, userInfoDO, calendarConfigId, addUserShiftConfigDOList, reSchedule);
            }
        }
        // 批量新增排班
        if (CollectionUtils.isNotEmpty(addUserShiftConfigDOList)) {
            userShiftConfigDao.saveBatch(addUserShiftConfigDOList);
        }
    }

    private void updateClassEmployeeConfig(List<UserShiftConfigDO> userShiftConfigDOList, Boolean reSchedule) {
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return;
        }
        List<UserShiftConfigDO> updateList = Lists.newArrayList();
        //是和否循环排班都不修改
        userShiftConfigDOList.stream()
                .filter(item -> !ShiftTypeEnum.CYCLE_SHIFT.getCode().equals(item.getShiftType()))
                .forEach(item -> {
                    if (reSchedule) {
                        //选择是，手工/自动排班都需要修改
                        item.setIsLatest(BusinessConstant.N);
                        BaseDOUtil.fillDOUpdateByUserOrSystem(item);
                        updateList.add(item);
                    } else {
                        //选择否，手工排班不修改，自动排班修改
                        if (ShiftTypeEnum.listNoCustomShift().contains(item.getShiftType())) {
                            item.setIsLatest(BusinessConstant.N);
                            BaseDOUtil.fillDOUpdateByUserOrSystem(item);
                            updateList.add(item);
                        }
                    }
                });
        userShiftConfigDao.updateBatchById(updateList);
    }

    private void buildClassEmployeeConfig(List<UserShiftConfigDO> userShiftConfigDOList,
                                          Long dayId,
                                          Map<Long, CalendarConfigDetailDO> configDetailMap,
                                          UserClassConfigDTO userClassConfigDTO,
                                          UserInfoDO userInfoDO,
                                          Long calendarConfigId,
                                          List<UserShiftConfigDO> addUserShiftConfigDOList,
                                          Boolean reSchedule) {
        // 如果有排班，则判断当前排班类型是手动排班还是系统排班，系统排班则需要更新排班信息(删除重新排)
        Optional<UserShiftConfigDO> userShiftConfigOptional = userShiftConfigDOList.stream()
                .filter(item -> item.getDayId().equals(dayId))
                .findFirst();
        if (userShiftConfigOptional.isPresent()) {
            UserShiftConfigDO userShiftConfigDO = userShiftConfigOptional.get();
            if (ShiftTypeEnum.CYCLE_SHIFT.getCode().equals(userShiftConfigDO.getShiftType())) {
                //是和否循环排班都不新增排班
                return;
            }
            if (!reSchedule && ShiftTypeEnum.CUSTOM_SHIFT.getCode().equals(userShiftConfigDO.getShiftType())) {
                //reSchedule = false, 则表示手工排班不用处理,不用新增排班，自动排班都需要删除后重排
                return;
            }
        }

        CalendarConfigDetailDO calendarConfigDetailDO = configDetailMap.get(dayId);
        //当天是工作日
        if (calendarConfigDetailDO == null) {
            PunchClassConfigSelectDTO punchClassConfigSelectDTO = userClassConfigDTO.getClassConfigSelectList().get(0);
            // 工作日：使用固定班次
            shiftDayInfoBuild(
                    userInfoDO.getId(),
                    dayId,
                    punchClassConfigSelectDTO.getId(),
                    punchClassConfigSelectDTO.getClassName(),
                    calendarConfigId,
                    ShiftSourceEnum.SYSTEM_SHIFT_CALENDAR_TRIGGER,
                    "日历变动自动排班",
                    addUserShiftConfigDOList
            );
        } else if (calendarConfigDetailDO.areWeekend()) {
            // 周末：安排休息(OFF)
            shiftDayInfoBuild(
                    userInfoDO.getId(),
                    dayId,
                    null,
                    DayShiftRuleEnum.OFF.getCode(),
                    calendarConfigId,
                    ShiftSourceEnum.SYSTEM_SHIFT_CALENDAR_TRIGGER,
                    "日历变动自动排班",
                    addUserShiftConfigDOList
            );
        } else if (calendarConfigDetailDO.areHOLIDAY()) {
            // 节假日：安排假期(H)
            shiftDayInfoBuild(
                    userInfoDO.getId(),
                    dayId,
                    null,
                    DayShiftRuleEnum.H.getCode(),
                    calendarConfigId,
                    ShiftSourceEnum.SYSTEM_SHIFT_CALENDAR_TRIGGER,
                    "日历变动自动排班",
                    addUserShiftConfigDOList
            );
        }
    }


}
