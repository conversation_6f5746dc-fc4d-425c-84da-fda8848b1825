package com.imile.attendance.common;

import com.imile.attendance.common.param.CountryDateParam;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface AttendanceCountryService {
    /**
     * 获取对应国家的当前时间
     *
     * @param country
     * @return
     */
    Date getCountryCurrentDate(String country);

    /**
     * 获取对应国家的当前时间
     *
     * @param countryList
     * @return
     */
    Map<String, Date> getCountryCurrentDate(List<String> countryList);

    /**
     * 获取对应国家的当前时间（适配测试自定义参数）
     *
     * @param countryList
     * @return
     */
    Map<String, Date> getCountryDateMap(List<String> countryList,
                                               CountryDateParam param);
}
