package com.imile.attendance.common.impl;

import com.imile.attendance.common.AttendanceBaseService;
import com.imile.attendance.cycleConfig.application.AttendanceCycleConfigApplicationService;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Service
public class AttendanceBaseServiceImpl implements AttendanceBaseService {

    @Resource
    private AttendanceCycleConfigApplicationService cycleConfigApplicationService;

    @Override
    public AttendanceCycleConfigDO getUserAttendanceCycleConfig(Long userId) {
        return cycleConfigApplicationService.getUserAttendanceCycleConfig(userId);
    }

    @Override
    public AttendanceDayCycleDTO getUserAttendanceCycleConfigDay(Long nowDayId, AttendanceCycleConfigDO attendanceCycleConfigDO) {
        return cycleConfigApplicationService.getUserAttendanceCycleConfigDay(nowDayId, attendanceCycleConfigDO);
    }

    @Override
    public AttendanceCycleConfigDO getUserAttendanceCycleConfigUserCard(Long userId) {
        return cycleConfigApplicationService.getUserAttendanceCycleConfigUserCard(userId);
    }
}
