package com.imile.attendance.common.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025/6/18
 * @Description 自定义时间参数
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CountryDateParam {
    /**
     * 定义当地时间年月日(YYYY-MM-dd)
     */
    private String localDate;

    /**
     * 是否启用自定义当前时间：true使用自定义时间。false使用当前时间
     */
    private Boolean isUseCustomLocalTime = false;
}