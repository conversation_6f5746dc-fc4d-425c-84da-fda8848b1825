package com.imile.attendance.common.impl;

import cn.hutool.core.util.ObjectUtil;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.common.param.CountryDateParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateFormatUtils;
import com.imile.attendance.vacation.param.UserLeaveReissueParam;
import com.imile.hermes.business.dto.CountryConfigDTO;
import com.imile.hermes.business.query.CountryApiQuery;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 考勤国家通用查询业务类
 * @author: han.wang
 * @createDate: 2024-10-17
 * @version: 1.0
 */
@Slf4j
@Service
public class AttendanceCountryServiceImpl implements AttendanceCountryService {

    @Resource
    private CountryService countryService;

    @Override
    public Map<String, Date> getCountryCurrentDate(List<String> countryList) {
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        if (CollectionUtils.isEmpty(countryConfigList)) {
            return Collections.emptyMap();
        }
        Map<String, String> countryConfigMap = countryConfigList.stream()
                .collect(Collectors.toMap(CountryConfigDTO::getCountryName, CountryConfigDTO::getTimeZone));
        Map<String, Date> dateCountryMap = new HashMap<>();
        // 获取国家对应时区
        for (String countryName : countryConfigMap.keySet()) {
            String timeZone = countryConfigMap.getOrDefault(countryName, "");
            if (ObjectUtil.equal(timeZone, "")) {
                continue;
            }
            Date now = new Date();
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            XxlJobLogger.log("getCountryCurrentDate: 北京时间:{},当前国家:{},当地时间:{}", now, countryName, dateTime);
            dateCountryMap.put(countryName, dateTime);
        }
        return dateCountryMap;
    }

    @Override
    public Date getCountryCurrentDate(String country) {
        // 通过假期分组获取国家
        Date now = new Date();
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryName(country);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        if (CollectionUtils.isEmpty(countryConfigList)) {
            XxlJobLogger.log("countryConfigList is Empty");
            return now;
        }
        for (CountryConfigDTO countryConfigDTO : countryConfigList) {
            // 获取国家对应时区
            String timeZone = countryConfigDTO.getTimeZone();
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("该国家:{},不存在国家时区", countryConfigDTO.getCountryName());
                continue;
            }
            XxlJobLogger.log("当前国家:{},时区:{}", countryConfigDTO.getCountryName(), timeZone);
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            now = dateTime;
        }
        return now;
    }

    /**
     * 获取对应国家时间
     *
     * @param countryList
     * @param param
     * @return
     */
    public Map<String, Date> getCountryDateMap(List<String> countryList,
                                               CountryDateParam param) {
        Map<String, Date> countryConfigMap = new HashMap<>();
        if (CollectionUtils.isEmpty(countryList)) {
            XxlJobLogger.log("getCountryDateMap | countryList is Empty");
            return countryConfigMap;
        }
        // 通过假期分组获取国家
        CountryApiQuery countryQuery = new CountryApiQuery();
        countryQuery.setCountryNames(countryList);
        countryQuery.setOrgId(BusinessConstant.DEFAULT_ORG_ID);
        List<CountryConfigDTO> countryConfigList = countryService.selectCountryConfigList(countryQuery);
        if (CollectionUtils.isEmpty(countryConfigList)) {
            XxlJobLogger.log("getCountryDateMap | countryConfigList is Empty");
            return countryConfigMap;
        }
        Date now = new Date();
        // 获取当前时间，明天
        if (param.getIsUseCustomLocalTime()) {
            // 根据当前时间年份月日时生成日期
            String localDate = param.getLocalDate();
            now = DateUtils.str2Date(localDate, DateFormatUtils.DATE);
        }
        for (CountryConfigDTO countryConfigDTO : countryConfigList) {
            // 获取国家对应时区
            String timeZone = countryConfigDTO.getTimeZone();
            if (ObjectUtil.equal(timeZone, "")) {
                XxlJobLogger.log("该国家:{},不存在国家时区", countryConfigDTO.getCountryName());
                continue;
            }
            XxlJobLogger.log("当前国家:{},时区:{}", countryConfigDTO.getCountryName(), timeZone);
            // 获取国家当地时间
            Date dateTime = CommonUtil.convertDateByTimeZonePlus(timeZone, now);
            if (param.getIsUseCustomLocalTime()) {
                // 自定义时间，不根据国家时区来计算
                dateTime = now;
            }
            countryConfigMap.put(countryConfigDTO.getCountryName(), dateTime);
        }
        return countryConfigMap;
    }
}
