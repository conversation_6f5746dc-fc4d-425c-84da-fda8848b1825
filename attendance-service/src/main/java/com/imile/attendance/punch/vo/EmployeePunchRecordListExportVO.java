package com.imile.attendance.punch.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
@Data
public class EmployeePunchRecordListExportVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 日期
     */
    private String dayId;
    /**
     * 组织编码
     */
    private String organizationCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 用户编码
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 数据来源
     */
    private String sourceType;
    /**
     * 打卡区域
     */
    private String punchArea;
    /**
     * 打卡方式
     */
    private String punchCardType;
    /**
     * 打卡时间
     */
    private String punchTime;
}