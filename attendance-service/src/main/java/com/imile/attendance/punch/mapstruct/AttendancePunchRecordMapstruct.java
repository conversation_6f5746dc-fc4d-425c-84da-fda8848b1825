package com.imile.attendance.punch.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.punch.dto.PunchCardRecordDTO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.punch.vo.EmployeePunchRecordListExportVO;
import com.imile.attendance.util.DateHelper;
import com.imile.common.page.PaginationResult;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/5
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendancePunchRecordMapstruct {

    AttendancePunchRecordMapstruct INSTANCE = Mappers.getMapper(AttendancePunchRecordMapstruct.class);

    EmployeePunchRecordListExportVO toPageExportVO(PunchCardRecordDTO punchCardRecordDTO);

    default PaginationResult<EmployeePunchRecordListExportVO> toPageExportVO(PaginationResult<PunchCardRecordDTO> punchCardRecordDTOList) {
        PaginationResult<EmployeePunchRecordListExportVO> result = new PaginationResult<>();
        result.setPagination(punchCardRecordDTOList.getPagination());
        List<EmployeePunchRecordListExportVO> results = new ArrayList<>();
        for (PunchCardRecordDTO dto : punchCardRecordDTOList.getResults()) {
            EmployeePunchRecordListExportVO exportVO = toPageExportVO(dto);
            exportVO.setPunchTime(DateHelper.formatYYYYMMDDHHMMSS(dto.getPunchTime()));
            results.add(exportVO);
        }
        result.setResults(results);
        return result;
    }
    List<UserPunchRecordBO> toPunchRecordBo(List<UserPunchRecordDTO> dtoList);
}
