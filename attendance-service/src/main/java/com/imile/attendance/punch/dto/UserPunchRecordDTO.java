package com.imile.attendance.punch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023/10/20 11:46
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPunchRecordDTO {

    /**
     * 打卡记录ID
     */
    private Long id;

    /**
     * 用户账号
     */
    private String userCode;

    /**
     * 审批单ID
     */
    private Long formId;

    /**
     * 转换后的打卡时间，无视秒
     */
    private Date formatPunchTime;

    /**
     * 打卡区域
     */
    private String punchArea;

    /**
     * 班次ID
     */
    private Long classId;
}
