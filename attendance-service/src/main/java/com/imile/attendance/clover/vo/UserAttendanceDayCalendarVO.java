package com.imile.attendance.clover.vo;

import com.imile.attendance.clover.dto.UserDayAttendanceInfoDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25 
 * @Description
 */
@Data
public class UserAttendanceDayCalendarVO {

    /**
     * 天
     */
    private Integer day;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * 星期几
     */
    private String dayOfWeek;

    /**
     * 出勤类型 PRESENT 应出勤日，WEEKEND 休息日 ，HOLIDAY 节假日
     */
    private String attendanceType;

    /**
     * 当天的异常个数(未处理)
     */
    private Integer abnormalUntreatedCount;

    /**
     * 当天的异常个数(审批中)
     */
    private Integer abnormalInReviewCount;


    /**
     * 用户当天具体出勤信息
     */
    private List<UserDayAttendanceInfoDTO> userDayAttendanceInfoDTOList;
}
