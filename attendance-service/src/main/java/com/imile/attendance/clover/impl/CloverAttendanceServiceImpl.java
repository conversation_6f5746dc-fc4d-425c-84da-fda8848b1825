package com.imile.attendance.clover.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.clover.bo.DayDetailData;
import com.imile.attendance.clover.bo.DayDetailTimeRange;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.clover.CloverAttendanceService;
import com.imile.attendance.clover.bo.AbnormalAttendanceData;
import com.imile.attendance.clover.dto.DayAttendanceAbnormalDTO;
import com.imile.attendance.clover.dto.DayAttendanceItemDTO;
import com.imile.attendance.clover.dto.DayAttendancePassFormDTO;
import com.imile.attendance.clover.dto.DayPunchClassItemInfoDTO;
import com.imile.attendance.clover.vo.UserAttendanceCalendarVO;
import com.imile.attendance.clover.dto.UserAttendanceCycleAbnormalDTO;
import com.imile.attendance.clover.dto.UserAttendanceCycleDetailDTO;
import com.imile.attendance.clover.vo.UserAttendanceDayCalendarVO;
import com.imile.attendance.clover.dto.UserAttendanceDayDetailDTO;
import com.imile.attendance.clover.dto.UserAttendanceGenerateCheckDTO;
import com.imile.attendance.clover.vo.UserAttendanceMonthCalendarVO;
import com.imile.attendance.clover.mapstruct.CloverAttendanceMapstruct;
import com.imile.attendance.clover.query.UserAttendanceCalendarQuery;
import com.imile.attendance.clover.query.UserAttendanceCycleDetailQuery;
import com.imile.attendance.clover.query.UserAttendanceDayDetailQuery;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.dto.AttendanceDayCycleDTO;
import com.imile.attendance.enums.AttendanceDayTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.punch.SourceTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.infrastructure.form.FormAttrUtils;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.calendar.dao.BaseDayInfoDao;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.query.BaseDayQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDateQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PunchTimeCalculator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/5/25
 * @Description
 */
@Slf4j
@Service
public class CloverAttendanceServiceImpl implements CloverAttendanceService {

    @Resource
    private AttendanceCycleConfigService cycleConfigService;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private EmployeeAbnormalAttendanceService abnormalAttendanceService;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private BaseDayInfoDao baseDayInfoDao;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private PunchConfigManage punchConfigManage;


    @Override
    public UserAttendanceCycleDetailDTO getUserAttendanceCycleDetail(UserAttendanceCycleDetailQuery cycleDetailQuery) {
        UserAttendanceCycleDetailDTO cycleDetailDTO = new UserAttendanceCycleDetailDTO();
        Long userId = cycleDetailQuery.getUserId();
        AttendanceCycleConfigDO userCycleConfig = cycleConfigService.getUserAttendanceCycleConfig(userId);
        if (userCycleConfig == null) {
            return cycleDetailDTO;
        }
        Date date = Objects.isNull(cycleDetailQuery.getDateTime()) ? new Date() : cycleDetailQuery.getDateTime();
        Long dayId = DateHelper.getDayId(date);
        AttendanceDayCycleDTO userDayCycleConfig = cycleConfigService.getUserAttendanceCycleConfigDay(dayId, userCycleConfig);
        if (userDayCycleConfig == null) {
            return cycleDetailDTO;
        }
        cycleDetailDTO.setAttendanceStartDate(userDayCycleConfig.getAttendanceStartDate());
        cycleDetailDTO.setAttendanceEndDate(userDayCycleConfig.getAttendanceEndDate());

        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceManage.selectAbnormalByUserId(
                userId,
                DateHelper.getDayId(cycleDetailDTO.getAttendanceStartDate()),
                dayId
        );
        //过滤异常考勤（除了当天也需要过滤前一天）
        Long filterDay = DateHelper.getPreviousDayId(dayId);
        while (filterDay <= dayId) {
            Long finalFilterDay = filterDay;
            List<EmployeeAbnormalAttendanceDO> dayAbnormalAttendanceDOList = abnormalAttendanceDOList.stream()
                    .filter(item -> finalFilterDay.equals(item.getDayId()))
                    .collect(Collectors.toList());
            List<Long> punchClassIdList = dayAbnormalAttendanceDOList.stream()
                    .map(EmployeeAbnormalAttendanceDO::getPunchClassConfigId)
                    .collect(Collectors.toList());
            List<PunchClassItemConfigDO> classItemConfigDOList = punchClassConfigManage.selectClassItemByClassIds(punchClassIdList);

            punchClassConfigQueryService.transferItemConfigTimeFormat(classItemConfigDOList, finalFilterDay);
            abnormalAttendanceService.filterAbnormalAttendance(dayAbnormalAttendanceDOList, classItemConfigDOList, date);

            Map<Long, List<EmployeeAbnormalAttendanceDO>> dayAbnormalListMap = dayAbnormalAttendanceDOList.stream()
                    .collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getId));
            abnormalAttendanceDOList.removeIf(item -> !dayAbnormalListMap.containsKey(item.getId())
                    && (finalFilterDay.equals(item.getDayId())));
            filterDay = DateHelper.getNextDayId(filterDay);
        }
        //封装异常实体
        List<EmployeeAbnormalAttendanceDO> unProcessedAbnormalList = abnormalAttendanceDOList.stream()
                .filter(item ->
                        !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.PASS.getCode())
                                && !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.EXPIRED.getCode())
                                && !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode()))
                .collect(Collectors.toList());

        cycleDetailDTO.setAbnormalCount(unProcessedAbnormalList.size());

        List<UserAttendanceCycleAbnormalDTO> userAttendanceCycleAbnormalDTOList = new ArrayList<>();
        cycleDetailDTO.setUserAttendanceCycleAbnormalDTOList(userAttendanceCycleAbnormalDTOList);

        for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
            UserAttendanceCycleAbnormalDTO abnormalDTO = new UserAttendanceCycleAbnormalDTO();
            abnormalDTO.setAbnormalId(abnormalAttendanceDO.getId());
            abnormalDTO.setAbnormalStatus(abnormalAttendanceDO.getStatus());
            if (StringUtils.isNotBlank(abnormalAttendanceDO.getExtend())) {
                //替换
                AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                abnormalDTO.setCorrectPunchTime(abnormalExtendDTO.getCorrectPunchTime());
                abnormalDTO.setActualPunchTime(abnormalExtendDTO.getActualPunchTime());
            }
            abnormalDTO.setReissueCardType(abnormalAttendanceDO.getAbnormalType());
            abnormalDTO.setReissueCardTypeDesc(abnormalAttendanceDO.getAbnormalType());
            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
            if (abnormalTypeEnum != null) {
                abnormalDTO.setReissueCardTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getCode());
            }
            userAttendanceCycleAbnormalDTOList.add(abnormalDTO);
        }

        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigManage.selectBatchUserRecord(
                Collections.singletonList(userId), Collections.singletonList(dayId));
        if (CollectionUtils.isEmpty(userShiftConfigDOList)) {
            return cycleDetailDTO;
        }
        UserShiftConfigDO userDayShiftConfigDO = userShiftConfigDOList.get(0);
        cycleDetailDTO.setPunchConfigClassName(userDayShiftConfigDO.getDayShiftRule());
        if (userDayShiftConfigDO.havePunchClassId()) {
            List<PunchClassItemConfigDO> classItemConfigDOList =
                    punchClassConfigManage.selectClassItemByClassIds(Collections.singletonList(userDayShiftConfigDO.getPunchClassConfigId()));
            StringBuilder stringBuilder = new StringBuilder();
            if (CollectionUtils.isNotEmpty(classItemConfigDOList)) {
                for (PunchClassItemConfigDO classItemConfigDO : classItemConfigDOList) {
                    //这里要注意，自由打卡规则和班次/固定的有出入，自由只有最早和最晚
                    if (classItemConfigDO.getPunchInTime() != null) {
                        stringBuilder
                                .append(DateUtil.format(classItemConfigDO.getPunchInTime(), "HH:mm"))
                                .append("-")
                                .append(DateUtil.format(classItemConfigDO.getPunchOutTime(), "HH:mm"))
                                .append("&");
                        continue;
                    }
                    stringBuilder
                            .append(DateUtil.format(classItemConfigDO.getEarliestPunchInTime(), "HH:mm"))
                            .append("-")
                            .append(DateUtil.format(classItemConfigDO.getLatestPunchOutTime(), "HH:mm"))
                            .append("&");
                }
            }
            if (!stringBuilder.toString().isEmpty()) {
                cycleDetailDTO.setPunchConfigClassItemInfo(stringBuilder.substring(0, stringBuilder.toString().length() - 1));
            }
        }
        return cycleDetailDTO;
    }


    /**
     * 获取用户考勤日历数据
     *
     * - 获取指定用户指定年份的完整考勤日历信息
     * - 按月份和日期组织数据，构建年度考勤日历视图
     *
     * @param calendarQuery 日历查询参数，包含用户ID、年份、查询时间等
     * @return 用户考勤日历数据，包含12个月的详细考勤信息
     */
    @Override
    public UserAttendanceCalendarVO getUserAttendanceCalendar(UserAttendanceCalendarQuery calendarQuery) {
        // 验证用户是否存在，确保查询的用户账户有效
        Long userId = calendarQuery.getUserId();
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        if (null == attendanceUser) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }

        // 获取查询基础参数：年份和当前日期信息
        Long year = calendarQuery.getYear();
        Date currentDate = Objects.isNull(calendarQuery.getDateTime()) ? new Date() : calendarQuery.getDateTime();
        Long currentDayId = DateHelper.getDayId(currentDate);
        Long previousDayId = DateHelper.getPreviousDayId(currentDayId);

        // 获取员工考勤相关的核心数据
        // 按日期分组的考勤详情记录（包含打卡时间、工作时长等）
        Map<Long, List<AttendanceEmployeeDetailDO>> attendanceDetailsByDay = fetchAttendanceDetailsByDay(userId, year);
        // 用户的日历配置范围（定义哪些时间段适用哪种考勤规则）
        List<CalendarConfigRangeDO> userCalendarRanges = fetchUserCalendarRanges(userId, year);
        // 日历配置的详细信息（具体每天的考勤类型：工作日/休息日/节假日）
        List<CalendarConfigDetailDO> userCalendarDetails = fetchUserCalendarDetails(userCalendarRanges);
        // 按月份分组的基础日期信息（年份中每一天的基本信息）
        Map<Integer, List<BaseDayInfoDO>> baseDayInfoByMonth = fetchBaseDayInfo(year);

        // 获取异常考勤数据（包含异常记录、关联表单、审核状态等）
        AbnormalAttendanceData abnormalData = fetchAbnormalAttendanceData(userId, year);

        // 构建完整的考勤日历数据结构
        return buildCalendarDTO(
                calendarQuery,
                currentDate,
                currentDayId,
                previousDayId,
                attendanceDetailsByDay,
                userCalendarRanges,
                userCalendarDetails,
                baseDayInfoByMonth,
                abnormalData
        );
    }

    /**
     * 用户每日日历详情
     *
     * - 获取指定用户指定日期的详细考勤信息
     * - 构建完整的日考勤详情视图，包含时段级别的考勤信息
     *
     * @param dayDetailQuery 日详情查询参数，包含用户ID、日期ID、查询时间等
     * @return 用户考勤日详情数据，包含完整的考勤信息和异常处理状态
     */
    @Override
    public UserAttendanceDayDetailDTO getUserAttendanceDayDetail(UserAttendanceDayDetailQuery dayDetailQuery) {
        long startTime = System.currentTimeMillis();

        Long userId = dayDetailQuery.getUserId();
        AttendanceUser attendanceUser = userService.getByUserId(userId);
        if (null == attendanceUser) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }

        // 预处理时间相关数据
        DayDetailTimeRange timeRange = calculateTimeRange(dayDetailQuery.getDayId());

        // 获取日详情所需的所有数据
        DayDetailData dayDetailData = fetchDayDetailData(userId, attendanceUser.getUserCode(),
                dayDetailQuery.getDayId(), timeRange, dayDetailQuery.getDateTime());

        // 没有排班数据，直接返回
        if (dayDetailData.getUserDayShiftConfig() == null) {
            log.info("getUserAttendanceDayDetail: 用户{}在{}没有排班配置", userId, dayDetailQuery.getDayId());
            return new UserAttendanceDayDetailDTO();
        }

        // 构建返回结果
        UserAttendanceDayDetailDTO dayDetailDTO = buildDayDetailDTO(dayDetailData, timeRange);

        log.info("getUserAttendanceDayDetail: 用户{}日期{}查询耗时{}ms",
                userId, dayDetailQuery.getDayId(), System.currentTimeMillis() - startTime);

        return dayDetailDTO;
    }

    /**
     * 计算日期相关的时间范围
     *
     * 业务含义：预处理时间相关的计算，避免在后续逻辑中重复进行日期解析和计算，
     * 提高性能并减少代码重复
     */
    private DayDetailTimeRange calculateTimeRange(Long dayId) {
        Date dayDate = DateHelper.transferDayIdToDate(dayId);
        Long beforeDayId = DateHelper.getPreviousDayId(dayId);
        Long afterDayId = DateHelper.getNextDayId(dayId);

        Date startDate = DateUtil.beginOfDay(DateUtil.offsetDay(dayDate, -1));
        Date endDate = DateUtil.endOfDay(DateUtil.offsetDay(dayDate, 1));

        return new DayDetailTimeRange(dayDate, beforeDayId, afterDayId, startDate, endDate);
    }

    /**
     * 批量获取日详情所需的所有数据
     *
     * 业务含义：采用批量查询策略，一次性获取构建日详情所需的所有数据，
     * 减少数据库往返次数，提高查询性能
     */
    private DayDetailData fetchDayDetailData(Long userId, String userCode, Long dayId,
                                             DayDetailTimeRange timeRange, Date queryDateTime) {
        // 获取用户在3天的打卡记录，过滤掉补卡记录，并按照打卡时间排序
        List<EmployeePunchRecordDO> punchRecords = employeePunchRecordManage.getUserPunchRecordInTimeRange(
                        userCode, timeRange.getStartDate(), timeRange.getEndDate())
                .stream()
                .filter(item -> !SourceTypeEnum.REISSUE_CARD.name().equals(item.getSourceType()))
                .sorted(Comparator.comparing(EmployeePunchRecordDO::getPunchTime))
                .collect(Collectors.toList());

        // 批量获取用户排班配置（3天范围）
        List<UserShiftConfigDO> userShiftConfigs = fetchUserShiftConfigs(userId, dayId, timeRange);

        // 获取当天的排班配置
        UserShiftConfigDO userDayShiftConfig = userShiftConfigs.stream()
                .filter(item -> item.getDayId().equals(dayId))
                .findAny()
                .orElse(null);

        // 获取打卡规则配置
        PunchConfigDO punchConfig = fetchPunchConfig(userId, timeRange.getDayDate());

        // 获取班次配置
        List<PunchClassItemConfigDO> classItemConfigs = fetchClassItemConfigs(userDayShiftConfig);

        // 获取当天的异常考勤数据
        List<EmployeeAbnormalAttendanceDO> abnormalAttendances = fetchAbnormalAttendances(userId, dayId, classItemConfigs, queryDateTime);

        // 获取当天的考勤详情数据
        List<AttendanceEmployeeDetailDO> attendanceDetails = attendanceEmployeeDetailManage.selectAttendanceByDayId(userId, dayId, dayId);

        // 获取用户所有审批通过的请假/外勤单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserId(userId);
        applicationFormQuery.setFromTypeList(Arrays.asList(FormTypeEnum.LEAVE.getCode(), FormTypeEnum.OUT_OF_OFFICE.getCode()));
        applicationFormQuery.setStatusList(Collections.singletonList(FormStatusEnum.PASS.getCode()));
        List<AttendanceFormDO> passedForms = attendanceFormManage.selectForm(applicationFormQuery);

        return new DayDetailData(punchRecords, userShiftConfigs, userDayShiftConfig,
                punchConfig, classItemConfigs, abnormalAttendances, attendanceDetails, passedForms);
    }

    /**
     * 获取用户排班配置数据
     *
     * 业务含义：获取用户在指定日期范围内的排班配置，包括当天及前后一天的排班信息，
     * 用于判断跨天班次和考勤时间计算
     */
    private List<UserShiftConfigDO> fetchUserShiftConfigs(Long userId, Long dayId, DayDetailTimeRange timeRange) {
        return userShiftConfigManage.selectBatchUserRecord(
                Collections.singletonList(userId),
                Arrays.asList(dayId, timeRange.getBeforeDayId(), timeRange.getAfterDayId()));
    }

    /**
     * 获取打卡规则配置
     */
    private PunchConfigDO fetchPunchConfig(Long userId, Date dayDate) {
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.mapByUserIds(
                Collections.singletonList(userId), DateUtil.endOfDay(dayDate));
        PunchConfigDO punchConfig = punchConfigMap.get(userId);
        if (punchConfig == null) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_PUNCH_IS_EMPTY);
        }
        return punchConfig;
    }

    /**
     * 根据用户的排班配置获取对应的班次详细配置信息
     */
    private List<PunchClassItemConfigDO> fetchClassItemConfigs(UserShiftConfigDO userDayShiftConfig) {
        if (userDayShiftConfig == null || !userDayShiftConfig.havePunchClassId()) {
            return Collections.emptyList();
        }
        return punchClassConfigManage.selectClassItemByClassIds(
                Collections.singletonList(userDayShiftConfig.getPunchClassConfigId()));
    }

    /**
     * 获取用户当天的异常考勤记录，并进行过滤处理
     */
    private List<EmployeeAbnormalAttendanceDO> fetchAbnormalAttendances(Long userId, Long dayId,
                                                                        List<PunchClassItemConfigDO> classItemConfigs,
                                                                        Date queryDateTime) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendances = abnormalAttendanceManage.selectAbnormalByUserId(
                userId, dayId, dayId);

        // 过滤异常考勤
        punchClassConfigQueryService.transferItemConfigTimeFormat(classItemConfigs, dayId);
        abnormalAttendanceService.filterAbnormalAttendance(abnormalAttendances, classItemConfigs,
                Objects.isNull(queryDateTime) ? new Date() : queryDateTime);

        return abnormalAttendances;
    }

    /**
     * 组装完整的当天的考勤详情
     */
    private UserAttendanceDayDetailDTO buildDayDetailDTO(DayDetailData dayDetailData, DayDetailTimeRange timeRange) {
        UserAttendanceDayDetailDTO dayDetailDTO = new UserAttendanceDayDetailDTO();

        // 设置基础信息
        dayDetailDTO.setPunchConfigType(dayDetailData.getPunchConfig().getConfigType());
        dayDetailDTO.setPunchConfigClassName(dayDetailData.getUserDayShiftConfig().getDayShiftRule());

        // 设置班次配置信息
        if (CollectionUtils.isEmpty(dayDetailData.getClassItemConfigs())) {
            dayDetailDTO.setCorrectDayClassItemInfoDTOList(Collections.emptyList());
        } else {
            dayDetailDTO.setCorrectDayClassItemInfoDTOList(
                    CloverAttendanceMapstruct.INSTANCE.toDayPunchClassItemInfoDTO(dayDetailData.getClassItemConfigs()));
        }

        // 根据排班配置和打卡规则类型计算当天的考勤时间范围
        calculateAttendanceTime(dayDetailDTO, timeRange.getDayId(), dayDetailData);

        // 处理当天的异常考勤信息，设置未处理异常数量
        processAbnormalAttendancesForDay(dayDetailDTO, dayDetailData);

        // 根据考勤详情和异常考勤情况设置是否有考勤记录
        setAttendanceStatus(dayDetailDTO, dayDetailData);

        // 为每个班次时段构建详细信息，包括异常考勤和打卡时间
        buildDayAttendanceItems(dayDetailDTO, dayDetailData, timeRange);

        // 处理审批通过的表单
        processPassedFormsForDay(dayDetailDTO, dayDetailData, timeRange);

        return dayDetailDTO;
    }

    /**
     * 根据排班配置和打卡规则类型计算当天的考勤时间范围
     */
    private void calculateAttendanceTime(UserAttendanceDayDetailDTO dayDetailDTO, Long dayId, DayDetailData dayDetailData) {
        UserShiftConfigDO userDayShiftConfig = dayDetailData.getUserDayShiftConfig();
        PunchConfigDO punchConfig = dayDetailData.getPunchConfig();
        List<PunchClassItemConfigDO> classItemConfigs = dayDetailData.getClassItemConfigs();

        // 当天是整个排班，有班次的
        if (userDayShiftConfig.havePunchClassId()) {
            // 特殊逻辑，比如是自由打卡，他的正常上下班时间都是空的，需要注意空报错
            if (PunchConfigTypeEnum.isNoNeedPunchWork(punchConfig.getConfigType())) {
                Date earliestPunchInTime = classItemConfigs.get(0).getEarliestPunchInTime();
                String earliestPunchInTimeString = DateHelper.formatHHMMSS(earliestPunchInTime);
                String earliestPunchInTimeDayString = DateHelper.formatYYYYMMDD(DateHelper.transferDayIdToDate(dayId));
                dayDetailDTO.setDayAttendanceStartDate(PunchTimeCalculator.getDateFromDateAndTimeStr(earliestPunchInTimeDayString, earliestPunchInTimeString));
                dayDetailDTO.setDayAttendanceEndDate(DateUtil.offsetDay(dayDetailDTO.getDayAttendanceStartDate(), 1));
                return;
            }

            // 灵活两次
            if (PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfig.getConfigType())){
                DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserFreeWorkPunchClassItemDayTime(dayId, classItemConfigs.get(0));
                if (dayPunchTimeDTO != null) {
                    dayDetailDTO.setDayAttendanceStartDate(dayPunchTimeDTO.getDayPunchStartTime());
                    dayDetailDTO.setDayAttendanceEndDate(dayPunchTimeDTO.getDayPunchEndTime());
                }
                return;
            }

            // 班次/固定打卡规则
            DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchDayTime(dayId, classItemConfigs);
            if (dayPunchTimeDTO != null) {
                dayDetailDTO.setDayAttendanceStartDate(dayPunchTimeDTO.getDayPunchStartTime());
                dayDetailDTO.setDayAttendanceEndDate(dayPunchTimeDTO.getDayPunchEndTime());
            }
            return;
        }
        // 当天是H/OFF
        dayDetailDTO.setDayAttendanceStartDate(DateUtil.beginOfDay(DateHelper.transferDayIdToDate(dayId)));
        dayDetailDTO.setDayAttendanceEndDate(DateUtil.endOfDay(dayDetailDTO.getDayAttendanceStartDate()));
    }

    /**
     * 处理当天的异常考勤信息，设置未处理异常数量
     */
    private void processAbnormalAttendancesForDay(UserAttendanceDayDetailDTO dayDetailDTO, DayDetailData dayDetailData) {
        List<EmployeeAbnormalAttendanceDO> abnormalAttendances = dayDetailData.getAbnormalAttendances();

        List<EmployeeAbnormalAttendanceDO> pendingAbnormalAttendances = abnormalAttendances.stream()
                .filter(item ->
                        !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.PASS.getCode()) &&
                                !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.EXPIRED.getCode()))
                .collect(Collectors.toList());

        dayDetailDTO.setAbnormalUntreatedCount(pendingAbnormalAttendances.size());
    }

    /**
     * 根据考勤详情和异常考勤情况设置是否有考勤记录
     */
    private void setAttendanceStatus(UserAttendanceDayDetailDTO dayDetailDTO, DayDetailData dayDetailData) {
        List<AttendanceEmployeeDetailDO> attendanceDetails = dayDetailData.getAttendanceDetails();
        List<EmployeeAbnormalAttendanceDO> abnormalAttendances = dayDetailData.getAbnormalAttendances();

        if (CollectionUtils.isEmpty(attendanceDetails) && CollectionUtils.isEmpty(abnormalAttendances)) {
            dayDetailDTO.setHasAttendance(BusinessConstant.N);
        }
    }

    /**
     * 为每个班次时段构建详细信息，包括异常考勤和打卡时间
     */
    private void buildDayAttendanceItems(UserAttendanceDayDetailDTO dayDetailDTO, DayDetailData dayDetailData, DayDetailTimeRange timeRange) {
        List<DayAttendanceItemDTO> dayAttendanceItemDTOList = new ArrayList<>();
        dayDetailDTO.setDayAttendanceItemDTOList(dayAttendanceItemDTOList);

        // 将异常考勤DO转换为DTO,填充实际打卡时间和补卡后的时间
        List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList = convertAbnormalAttendances(dayDetailData.getAbnormalAttendances());

        // 为每个班次时段构建考勤项目
        for (DayPunchClassItemInfoDTO dayClassItemInfoDTO : dayDetailDTO.getCorrectDayClassItemInfoDTOList()) {
            DayAttendanceItemDTO dayAttendanceItemDTO = buildSingleDayAttendanceItem(
                    dayClassItemInfoDTO, dayAttendanceAbnormalDTOList, dayDetailData, dayDetailDTO);
            dayAttendanceItemDTOList.add(dayAttendanceItemDTO);
        }
    }

    /**
     * 将异常考勤DO转换为DTO,填充实际打卡时间和补卡后的时间
     */
    private List<DayAttendanceAbnormalDTO> convertAbnormalAttendances(List<EmployeeAbnormalAttendanceDO> abnormalAttendances) {
        List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList = new ArrayList<>();

        for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendances) {
            DayAttendanceAbnormalDTO dayAttendanceAbnormalDTO = new DayAttendanceAbnormalDTO();
            dayAttendanceAbnormalDTO.setAbnormalId(abnormalAttendanceDO.getId());
            dayAttendanceAbnormalDTO.setAbnormalType(abnormalAttendanceDO.getAbnormalType());

            AttendanceAbnormalTypeEnum abnormalTypeEnum = AttendanceAbnormalTypeEnum.getInstanceByCode(abnormalAttendanceDO.getAbnormalType());
            if (abnormalTypeEnum != null) {
                dayAttendanceAbnormalDTO.setAbnormalTypeDesc(RequestInfoHolder.isChinese() ? abnormalTypeEnum.getDesc() : abnormalTypeEnum.getCode());
            }

            dayAttendanceAbnormalDTO.setAbnormalStatus(abnormalAttendanceDO.getStatus());
            dayAttendanceAbnormalDTO.setPunchClassItemId(abnormalAttendanceDO.getPunchClassItemConfigId());

            if (StringUtils.isNotEmpty(abnormalAttendanceDO.getExtend())) {
                AbnormalExtendDTO abnormalExtendDTO = JSON.parseObject(abnormalAttendanceDO.getExtend(), AbnormalExtendDTO.class);
                dayAttendanceAbnormalDTO.setActualPunchTime(abnormalExtendDTO.getActualPunchTime());
                dayAttendanceAbnormalDTO.setCorrectPunchTime(abnormalExtendDTO.getCorrectPunchTime());
            }
            dayAttendanceAbnormalDTOList.add(dayAttendanceAbnormalDTO);
        }
        return dayAttendanceAbnormalDTOList;
    }

    /**
     * 为单个班次时段构建考勤信息，包括异常考勤和打卡时间信息
     */
    private DayAttendanceItemDTO buildSingleDayAttendanceItem(DayPunchClassItemInfoDTO dayClassItemInfoDTO,
                                                              List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList,
                                                              DayDetailData dayDetailData,
                                                              UserAttendanceDayDetailDTO dayDetailDTO) {
        DayAttendanceItemDTO dayAttendanceItemDTO = new DayAttendanceItemDTO();
        dayAttendanceItemDTO.setSortNo(dayClassItemInfoDTO.getSortNo());

        // 设置该时段的异常考勤（注意未排班的异常，itemId是空的）
        List<DayAttendanceAbnormalDTO> dayItemList = dayAttendanceAbnormalDTOList.stream()
                .filter(item -> item.getPunchClassItemId() != null &&
                        item.getPunchClassItemId().equals(dayClassItemInfoDTO.getId()))
                .collect(Collectors.toList());
        dayAttendanceItemDTO.setDayAttendanceAbnormalDTOList(dayItemList);

        // 设置该时段的打卡时间
        setPunchTimesForItem(dayAttendanceItemDTO, dayClassItemInfoDTO, dayDetailData, dayDetailDTO, dayAttendanceAbnormalDTOList);

        return dayAttendanceItemDTO;
    }

    /**
     * 根据打卡规则和班次配置，设置最早和最晚打卡时间
     */
    private void setPunchTimesForItem(DayAttendanceItemDTO dayAttendanceItemDTO,
                                      DayPunchClassItemInfoDTO dayClassItemInfoDTO,
                                      DayDetailData dayDetailData,
                                      UserAttendanceDayDetailDTO dayDetailDTO,
                                      List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList) {

        PunchConfigDO punchConfig = dayDetailData.getPunchConfig();
        List<EmployeePunchRecordDO> punchRecords = dayDetailData.getPunchRecords();
        List<PunchClassItemConfigDO> classItemConfigs = dayDetailData.getClassItemConfigs();

        // 自由打卡规则处理
        if (StringUtils.equalsIgnoreCase(punchConfig.getConfigType(), PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode())) {
            handleFreePunchWork(dayAttendanceItemDTO, punchRecords, dayDetailDTO);
            return;
        }

        // 班次打卡规则处理
        handleClassPunchWork(dayAttendanceItemDTO, dayClassItemInfoDTO, punchRecords, classItemConfigs,
                dayDetailData.getAbnormalAttendances(), dayAttendanceAbnormalDTOList);
    }

    /**
     * 在免打卡下，获取考勤时间范围内的最早和最晚打卡时间
     */
    private void handleFreePunchWork(DayAttendanceItemDTO dayAttendanceItemDTO,
                                     List<EmployeePunchRecordDO> punchRecords,
                                     UserAttendanceDayDetailDTO dayDetailDTO) {
        List<EmployeePunchRecordDO> itemPunchRecordList = punchRecords.stream()
                .filter(item -> item.getPunchTime().compareTo(dayDetailDTO.getDayAttendanceStartDate()) > -1 &&
                        item.getPunchTime().compareTo(dayDetailDTO.getDayAttendanceEndDate()) < 1)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(itemPunchRecordList)) {
            dayAttendanceItemDTO.setItemBeginPunchTime(itemPunchRecordList.get(0).getPunchTime());
            dayAttendanceItemDTO.setItemEndPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getPunchTime());
        }
    }

    /**
     * 在固定班次下，根据班次时间范围获取打卡记录，并处理异常打卡
     */
    private void handleClassPunchWork(DayAttendanceItemDTO dayAttendanceItemDTO,
                                      DayPunchClassItemInfoDTO dayClassItemInfoDTO,
                                      List<EmployeePunchRecordDO> punchRecords,
                                      List<PunchClassItemConfigDO> classItemConfigs,
                                      List<EmployeeAbnormalAttendanceDO> abnormalAttendances,
                                      List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList) {

        // 获取该时段的班次配置
        List<PunchClassItemConfigDO> itemConfigs = classItemConfigs.stream()
                .filter(item -> item.getId().equals(dayClassItemInfoDTO.getId()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemConfigs)) {
            return;
        }

        PunchClassItemConfigDO itemConfig = itemConfigs.get(0);

        // 获取该时段内的打卡记录
        List<EmployeePunchRecordDO> itemPunchRecordList = punchRecords.stream()
                .filter(item -> item.getPunchTime().compareTo(itemConfig.getEarliestPunchInTime()) > -1 &&
                        item.getPunchTime().compareTo(itemConfig.getLatestPunchOutTime()) < 1)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(itemPunchRecordList)) {
            return;
        }

        // 移除异常打卡记录
        removeAbnormalPunchRecords(itemPunchRecordList, dayAttendanceAbnormalDTOList);

        if (CollectionUtils.isNotEmpty(itemPunchRecordList)) {
            setPunchTimesBasedOnAbnormalType(dayAttendanceItemDTO, itemPunchRecordList, abnormalAttendances);
        }
    }

    /**
     * 从打卡记录中移除已标记为异常的打卡记录，避免重复显示
     */
    private void removeAbnormalPunchRecords(List<EmployeePunchRecordDO> itemPunchRecordList,
                                            List<DayAttendanceAbnormalDTO> dayAttendanceAbnormalDTOList) {
        itemPunchRecordList.removeIf(item -> dayAttendanceAbnormalDTOList.stream().anyMatch(abnormal ->
                Objects.nonNull(abnormal.getActualPunchTime())
                        && Objects.nonNull(item.getPunchTime())
                        && DateUtil.format(abnormal.getActualPunchTime(), "yyyy-MM-dd HH:mm")
                        .equals(DateUtil.format(item.getPunchTime(), "yyyy-MM-dd HH:mm"))));
    }

    /**
     * 根据异常考勤类型（迟到、早退等）设置显示的打卡时间
     */
    private void setPunchTimesBasedOnAbnormalType(DayAttendanceItemDTO dayAttendanceItemDTO,
                                                  List<EmployeePunchRecordDO> itemPunchRecordList,
                                                  List<EmployeeAbnormalAttendanceDO> abnormalAttendances) {

        List<String> abnormalTypes = abnormalAttendances.stream()
                .map(EmployeeAbnormalAttendanceDO::getAbnormalType)
                .collect(Collectors.toList());

        if (itemPunchRecordList.size() == 1) {
            // 单次打卡的处理逻辑
            if (abnormalTypes.contains(AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode())
                    || abnormalTypes.contains(AttendanceAbnormalTypeEnum.LATE.getCode())) {
                dayAttendanceItemDTO.setItemEndPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getPunchTime());
            } else {
                dayAttendanceItemDTO.setItemBeginPunchTime(itemPunchRecordList.get(0).getPunchTime());
            }
        } else {
            // 多次打卡的处理逻辑
            dayAttendanceItemDTO.setItemBeginPunchTime(itemPunchRecordList.get(0).getPunchTime());
            dayAttendanceItemDTO.setItemEndPunchTime(itemPunchRecordList.get(itemPunchRecordList.size() - 1).getPunchTime());
        }
    }

    /**
     * 处理与当天考勤时间有交集的审批通过的请假/外勤表单
     */
    private void processPassedFormsForDay(UserAttendanceDayDetailDTO dayDetailDTO, DayDetailData dayDetailData, DayDetailTimeRange timeRange) {
        List<AttendanceFormDO> passedForms = dayDetailData.getPassedForms();

        // 获取表单属性
        List<Long> formIdList = passedForms.stream()
                .map(AttendanceFormDO::getId)
                .collect(Collectors.toList());

        List<AttendanceFormAttrDO> allFormAttrDOList = attendanceFormManage.selectFormAttrByFormIdList(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> formAttrMap = allFormAttrDOList.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));

        List<DayAttendancePassFormDTO> dayAttendancePassFormDTOList = new ArrayList<>();
        dayDetailDTO.setDayAttendancePassFormDTOList(dayAttendancePassFormDTOList);

        // 处理每个表单
        for (AttendanceFormDO formDO : passedForms) {
            DayAttendancePassFormDTO passFormDTO = processPassedForm(formDO, formAttrMap, dayDetailDTO);
            if (passFormDTO != null) {
                dayAttendancePassFormDTOList.add(passFormDTO);
            }
        }
    }

    /**
     * 处理单个审批通过的请假/外勤表单，判断是否与当天考勤时间有交集
     */
    private DayAttendancePassFormDTO processPassedForm(AttendanceFormDO formDO,
                                                       Map<Long, List<AttendanceFormAttrDO>> formAttrMap,
                                                       UserAttendanceDayDetailDTO dayDetailDTO) {

        List<AttendanceFormAttrDO> formAttrDOList = formAttrMap.getOrDefault(formDO.getId(), Collections.emptyList());
        if (CollectionUtils.isEmpty(formAttrDOList)) {
            return null;
        }

        // 检查表单是否被撤销
        AttendanceFormAttrDO isRevokeAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.isRevoke);
        if (Objects.nonNull(isRevokeAttr) && StringUtils.isNotBlank(isRevokeAttr.getAttrValue()) &&
                isRevokeAttr.getAttrValue().equals(BusinessConstant.Y.toString())) {
            return null;
        }

        // 获取表单时间范围
        Date startDate = null;
        Date endDate = null;

        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
            AttendanceFormAttrDO leaveStartDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveStartDate);
            AttendanceFormAttrDO leaveEndDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.leaveEndDate);
            if (Objects.isNull(leaveStartDateAttr) || Objects.isNull(leaveEndDateAttr)) {
                return null;
            }
            startDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateAttr.getAttrValue());
            endDate = DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateAttr.getAttrValue());
        }

        if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
            AttendanceFormAttrDO outOfOfficeStartDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeStartDate);
            AttendanceFormAttrDO outOfOfficeEndDateAttr = FormAttrUtils.getFormAttr(formAttrDOList, ApplicationFormAttrKeyEnum.outOfOfficeEndDate);
            if (Objects.isNull(outOfOfficeStartDateAttr) || Objects.isNull(outOfOfficeEndDateAttr)) {
                return null;
            }
            startDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateAttr.getAttrValue());
            endDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateAttr.getAttrValue());
        }

        // 检查时间交集
        if (startDate == null || endDate == null ||
                dayDetailDTO.getDayAttendanceEndDate().compareTo(startDate) < 1 ||
                dayDetailDTO.getDayAttendanceStartDate().compareTo(endDate) > -1) {
            return null;
        }

        return CloverAttendanceMapstruct.INSTANCE.toDayAttendancePassFormDTO(formDO, startDate, endDate);
    }

    @Override
    public UserAttendanceGenerateCheckDTO getUserAttendanceGenerateCheck() {
        UserAttendanceGenerateCheckDTO checkDTO = new UserAttendanceGenerateCheckDTO();
        Date date = new Date();
        Long dayId = DateHelper.getDayId(date);
        checkDTO.setDayId(dayId);
        List<Long> dayIdList = new ArrayList<>();
        dayIdList.add(dayId);
        dayIdList.add(DateHelper.getDayId(DateUtil.offsetDay(date, -1)));
        dayIdList.add(DateHelper.getDayId(DateUtil.offsetDay(date, -2)));
        dayIdList.add(DateHelper.getDayId(DateUtil.offsetDay(date, -3)));
        dayIdList.add(DateHelper.getDayId(DateUtil.offsetDay(date, -4)));

        List<AttendanceEmployeeDetailDO> employeeDetailDOList = attendanceEmployeeDetailManage.selectAttendanceByDayIdList(
                        RequestInfoHolder.getUserId(), dayIdList)
                .stream()
                .sorted(Comparator.comparing(AttendanceEmployeeDetailDO::getDayId).reversed())
                .collect(Collectors.toList());

        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceManage.selectAbnormalAttendanceByDayIdList(Collections.singletonList(RequestInfoHolder.getUserId()), dayIdList)
                .stream()
                .sorted(Comparator.comparing(EmployeeAbnormalAttendanceDO::getDayId).reversed())
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(employeeDetailDOList)) {
            checkDTO.setDayId(employeeDetailDOList.get(0).getDayId());
            return checkDTO;
        }
        if (CollectionUtils.isNotEmpty(abnormalAttendanceDOList)) {
            checkDTO.setDayId(abnormalAttendanceDOList.get(0).getDayId());
            return checkDTO;
        }
        return checkDTO;
    }

    /**
     * 获取用户指定年份的考勤详情数据，按日期分组
     *
     * 业务含义：获取用户在指定年份内每天的具体考勤记录，包括打卡时间、工作时长、
     * 出勤状态等详细信息，用于在日历中展示每天的实际考勤情况
     */
    private Map<Long, List<AttendanceEmployeeDetailDO>> fetchAttendanceDetailsByDay(Long userId, Long year) {
        return attendanceEmployeeDetailManage.selectByYear(userId, year)
                .stream()
                .collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getDayId));
    }

    /**
     * 获取用户的日历配置范围数据
     *
     * 业务含义：获取用户适用的考勤日历配置时间范围，这些配置定义了在不同时间段内
     * 用户应该遵循哪种考勤规则（如标准工作制、弹性工作制等）
     */
    private List<CalendarConfigRangeDO> fetchUserCalendarRanges(Long userId, Long year) {
        CalendarConfigDateQuery calendarConfigDateQuery = new CalendarConfigDateQuery();
        calendarConfigDateQuery.setUserIds(Collections.singletonList(userId));
        return calendarManage.selectCalendarConfigByDate(calendarConfigDateQuery);
    }

    /**
     * 获取日历配置的详细信息
     *
     * 业务含义：根据日历配置范围，获取具体每天的考勤类型定义（工作日、休息日、节假日等），
     * 这些信息决定了日历中每天显示的考勤类型和相应的考勤要求
     */
    private List<CalendarConfigDetailDO> fetchUserCalendarDetails(List<CalendarConfigRangeDO> userCalendarRanges) {
        List<Long> calendarConfigIdList = userCalendarRanges.stream()
                .map(CalendarConfigRangeDO::getAttendanceConfigId)
                .distinct()
                .collect(Collectors.toList());
        return calendarManage.selectCalendarDetailsByConfigIds(calendarConfigIdList);
    }

    /**
     * 获取指定年份的基础日期信息，按月份分组
     *
     * 业务含义：获取年份中每一天的基础信息（如日期、星期几等），
     * 为构建日历结构提供基础的日期框架
     */
    private Map<Integer, List<BaseDayInfoDO>> fetchBaseDayInfo(Long year) {
        BaseDayQuery baseDayQuery = BaseDayQuery.builder()
                .year(year)
                .build();
        return baseDayInfoDao.getBaseDay(baseDayQuery)
                .stream()
                .collect(Collectors.groupingBy(BaseDayInfoDO::getMonth));
    }

    /**
     * 获取用户指定年份的异常考勤数据
     *
     * 业务含义：获取用户在指定年份内的所有异常考勤记录及其处理状态，包括：
     * - 异常考勤记录（迟到、早退、缺卡等）
     * - 关联的申请表单（补卡申请、请假申请等）
     * - 表单的审核状态（用于区分未处理和审核中的异常）
     *
     * 业务规则：只获取未通过和未过期的异常记录，已通过或已过期的异常不影响日历显示
     */
    private AbnormalAttendanceData fetchAbnormalAttendanceData(Long userId, Long year) {
        // 计算指定年份的时间范围
        DateTime yearDate = DateUtil.parse(String.valueOf(year), "yyyy");
        Date startYearDate = DateUtil.beginOfYear(yearDate);
        Date endYearDate = DateUtil.endOfYear(yearDate);
        Long startDayId = DateHelper.getDayId(startYearDate);
        Long endDayId = DateHelper.getDayId(endYearDate);

        // 获取需要在日历中显示的异常考勤记录（排除已通过和已过期的异常）
        List<EmployeeAbnormalAttendanceDO> abnormalAttendances = abnormalAttendanceManage.selectAbnormalByUserId(userId, startDayId, endDayId)
                .stream()
                .filter(item -> !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.PASS.getCode()) &&
                        !StringUtils.equalsIgnoreCase(item.getStatus(), AbnormalAttendanceStatusEnum.EXPIRED.getCode()))
                .collect(Collectors.toList());

        // 获取异常考勤关联的申请表单（用于判断异常是否有对应的处理申请）
        List<Long> abnormalIds = abnormalAttendances.stream()
                .map(EmployeeAbnormalAttendanceDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormRelationDO> formRelations = attendanceFormManage.selectRelationByRelationIdList(abnormalIds);

        // 获取审核中的表单（用于区分未处理异常和审核中异常）
        List<Long> formIds = formRelations.stream()
                .map(AttendanceFormRelationDO::getFormId)
                .collect(Collectors.toList());
        List<AttendanceFormDO> inReviewForms = attendanceFormManage.selectByIdList(formIds)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode()))
                .collect(Collectors.toList());

        return new AbnormalAttendanceData(abnormalAttendances, formRelations, inReviewForms);
    }

    /**
     * 构建完整的考勤日历数据结构
     *
     * 业务功能：将获取到的各种考勤数据整合成完整的年度日历结构，
     * 按月份和日期组织数据，为前端展示提供结构化的考勤日历信息
     *
     * 数据组织结构：年份 -> 月份 -> 日期 -> 具体考勤信息
     */
    private UserAttendanceCalendarVO buildCalendarDTO(
            UserAttendanceCalendarQuery calendarQuery,
            Date currentDate,
            Long currentDayId,
            Long previousDayId,
            Map<Long, List<AttendanceEmployeeDetailDO>> attendanceDetailsByDay,
            List<CalendarConfigRangeDO> userCalendarRanges,
            List<CalendarConfigDetailDO> userCalendarDetails,
            Map<Integer, List<BaseDayInfoDO>> baseDayInfoByMonth,
            AbnormalAttendanceData abnormalData) {

        UserAttendanceCalendarVO calendarDTO = new UserAttendanceCalendarVO();
        calendarDTO.setYear(calendarQuery.getYear());
        List<UserAttendanceMonthCalendarVO> monthCalendarList = new ArrayList<>();
        calendarDTO.setMonthCalendarVOList(monthCalendarList);

        // 按月份构建日历数据（1月到12月）
        for (int month = Constants.JANUARY; month <= Constants.DECEMBER; month++) {
            UserAttendanceMonthCalendarVO monthCalendar = new UserAttendanceMonthCalendarVO();
            monthCalendarList.add(monthCalendar);
            monthCalendar.setMonth((long) month);

            List<UserAttendanceDayCalendarVO> dayCalendarList = new ArrayList<>();
            monthCalendar.setDayCalendarVOList(dayCalendarList);

            // 为当月的每一天构建考勤日历信息
            List<BaseDayInfoDO> daysInMonth = baseDayInfoByMonth.getOrDefault(month, Collections.emptyList());
            for (BaseDayInfoDO dayInfo : daysInMonth) {
                UserAttendanceDayCalendarVO dayCalendar = createDayCalendar(
                        dayInfo,
                        currentDate,
                        currentDayId,
                        previousDayId,
                        attendanceDetailsByDay,
                        userCalendarRanges,
                        userCalendarDetails,
                        abnormalData
                );

                // 只添加有效的日历数据（有考勤配置的日期）
                if (dayCalendar != null) {
                    dayCalendarList.add(dayCalendar);
                }
            }
        }

        return calendarDTO;
    }

    /**
     * 创建单日考勤日历数据
     *
     * 业务功能：为指定日期创建完整的考勤日历信息，包括：
     * - 基础日期信息（日期、星期几）
     * - 考勤类型（工作日、休息日、节假日、请假）
     * - 异常考勤统计（未处理异常数量、审核中异常数量）
     * - 具体考勤详情（打卡记录、工作时长等）
     *
     * 业务规则：
     * - 只有配置了考勤规则的日期才会返回日历数据
     * - 只对当天及之前的日期处理异常考勤信息
     * - 考勤类型默认为应出勤日P，根据日历配置进行覆盖为H或OFF
     */
    private UserAttendanceDayCalendarVO createDayCalendar(BaseDayInfoDO dayInfo,
                                                          Date currentDate,
                                                          Long currentDayId,
                                                          Long previousDayId,
                                                          Map<Long, List<AttendanceEmployeeDetailDO>> attendanceDetailsByDay,
                                                          List<CalendarConfigRangeDO> userCalendarRanges,
                                                          List<CalendarConfigDetailDO> userCalendarDetails,
                                                          AbnormalAttendanceData abnormalData) {

        Long dayId = dayInfo.getId();
        UserAttendanceDayCalendarVO dayCalendar = new UserAttendanceDayCalendarVO();
        dayCalendar.setDay(dayInfo.getDay());
        dayCalendar.setDayId(dayId);
        dayCalendar.setDayOfWeek(dayInfo.getDayOfWeek());

        // 查找当天适用的日历配置（判断该日期是否在配置的时间范围内）
        DateTime dayEndTime = DateUtil.endOfDay(DateHelper.transferDayIdToDate(dayId));
        List<CalendarConfigRangeDO> dayCalendarConfigs = userCalendarRanges.stream()
                .filter(item -> item.getStartDate().compareTo(dayEndTime) <= 0 &&
                        item.getEndDate().compareTo(dayEndTime) >= 0)
                .collect(Collectors.toList());

        // 如果没有找到适用的日历配置，则该日期不显示在日历中
        if (CollectionUtils.isEmpty(dayCalendarConfigs)) {
            return null;
        }

        // 设置当天的考勤类型（默认为应出勤日）
        CalendarConfigRangeDO calendarRange = dayCalendarConfigs.get(0);
        dayCalendar.setAttendanceType(AttendanceDayTypeEnum.PRESENT.name());

        // 根据日历配置详情覆盖考勤类型（如休息日、节假日等）
        userCalendarDetails.stream()
                .filter(item -> item.getAttendanceConfigId().equals(calendarRange.getAttendanceConfigId()))
                .filter(item -> item.getDayId().equals(dayId))
                .findAny()
                .ifPresent(detail -> dayCalendar.setAttendanceType(detail.getDayType()));

        // 只对当天及之前的日期处理异常考勤（未来日期不会有异常考勤）
        if (dayId <= currentDayId) {
            processAbnormalAttendance(dayCalendar, dayId, currentDayId, previousDayId, currentDate, abnormalData);
        }

        // 设置用户当天的具体考勤详情（打卡记录、工作时长等）
        List<AttendanceEmployeeDetailDO> dayAttendanceDetails = attendanceDetailsByDay.getOrDefault(dayId, Collections.emptyList());
        if (CollectionUtils.isEmpty(dayAttendanceDetails)) {
            return dayCalendar;
        }

        dayCalendar.setUserDayAttendanceInfoDTOList(
                CloverAttendanceMapstruct.INSTANCE.toUserDayAttendanceInfoDTO(dayAttendanceDetails));

        return dayCalendar;
    }


    private void setAttendanceType(UserAttendanceDayCalendarVO dayCalendar,
                                   Long dayId,
                                   CalendarConfigRangeDO calendarRange,
                                   List<CalendarConfigDetailDO> userCalendarDetails) {

        dayCalendar.setAttendanceType(AttendanceDayTypeEnum.PRESENT.name());

        userCalendarDetails.stream()
                .filter(item -> item.getAttendanceConfigId().equals(calendarRange.getAttendanceConfigId()))
                .filter(item -> item.getDayId().equals(dayId))
                .findAny()
                .ifPresent(detail -> dayCalendar.setAttendanceType(detail.getDayType()));
    }


    /**
     * 处理指定日期的异常考勤信息
     *
     * 业务功能：计算并设置日历中每天的异常考勤统计信息，包括：
     * - 未处理异常数量：需要员工或管理员处理的异常考勤
     * - 审核中异常数量：已提交申请正在审核流程中的异常考勤
     *
     * 特殊业务规则：
     * - 对于当天和前一天的异常考勤，需要进行实时过滤处理
     * - 这是因为当天和前一天的考勤数据可能还在变化，需要根据最新的打卡规则重新判断异常状态
     */
    private void processAbnormalAttendance(UserAttendanceDayCalendarVO dayCalendar,
                                           Long dayId,
                                           Long currentDayId,
                                           Long previousDayId,
                                           Date currentDate,
                                           AbnormalAttendanceData abnormalData) {

        // 筛选出当天的异常考勤记录
        List<EmployeeAbnormalAttendanceDO> dayAbnormalList = abnormalData.getAbnormalAttendances().stream()
                .filter(item -> item.getDayId().equals(dayId))
                .collect(Collectors.toList());

        // 对当天和前一天的异常考勤进行实时过滤（因为这些日期的考勤状态可能还在变化）
        if (dayId.equals(currentDayId) || dayId.equals(previousDayId)) {
            List<Long> punchClassIdList = dayAbnormalList.stream()
                    .map(EmployeeAbnormalAttendanceDO::getPunchClassConfigId)
                    .collect(Collectors.toList());
            List<PunchClassItemConfigDO> classItemConfigs = punchClassConfigManage.selectClassItemByClassIds(punchClassIdList);
            punchClassConfigQueryService.transferItemConfigTimeFormat(classItemConfigs, dayId);
            abnormalAttendanceService.filterAbnormalAttendance(dayAbnormalList, classItemConfigs, currentDate);
        }

        // 计算异常考勤的统计数量
        int totalAbnormalCount = dayAbnormalList.size();
        int abnormalInReviewCount = countAbnormalInReview(dayAbnormalList, abnormalData);

        // 设置日历中显示的异常统计信息
        dayCalendar.setAbnormalUntreatedCount(totalAbnormalCount - abnormalInReviewCount);
        dayCalendar.setAbnormalInReviewCount(abnormalInReviewCount);
    }

    /**
     * 统计审核中的异常考勤数量
     *
     * 业务逻辑：通过异常考勤与申请表单的关联关系，判断哪些异常考勤已经提交了申请
     * 并且申请表单正在审核流程中，这些异常考勤在日历中会单独统计显示
     */
    private int countAbnormalInReview(List<EmployeeAbnormalAttendanceDO> dayAbnormalList,
                                      AbnormalAttendanceData abnormalData) {

        int count = 0;
        for (EmployeeAbnormalAttendanceDO abnormal : dayAbnormalList) {
            // 查找异常考勤关联的申请表单
            Optional<AttendanceFormRelationDO> relation = abnormalData.getFormRelations().stream()
                    .filter(item -> item.getRelationId().equals(abnormal.getId()))
                    .findAny();

            if (!relation.isPresent()) {
                continue;
            }

            // 判断关联的表单是否处于审核中状态
            boolean hasInReviewForm = abnormalData.getInReviewForms().stream()
                    .anyMatch(form -> form.getId().equals(relation.get().getFormId()));

            if (hasInReviewForm) {
                count++;
            }
        }

        return count;
    }


}
