package com.imile.attendance.third.param;

import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description
 */
@Data
public class ZktecoAreaRelationUpdateParam {

    /**
     * 关联表ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;

    /**
     * 部门
     */
    private List<Long> deptIdList;

    /**
     * 员工
     */
    private List<Long> userIdList;

    /**
     * 国家
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private String country;
}
