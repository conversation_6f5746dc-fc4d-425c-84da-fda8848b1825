package com.imile.attendance.third.utils;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.attendance.infrastructure.repository.zkteco.DO.ZktecoEmployeeUpdateDO;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.attendance.util.DateFormatUtils;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.util.http.HttpResult;
import com.imile.util.http.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Component
@Slf4j
public class ZKTecoUtils {

    @Resource
    private ImileRedisClient imileRedisClient;
    @Autowired
    private AttendanceProperties attendanceProperties;

    public static final String LOCK_KEY = "ATTENDANCE:ZKTECO:LOCK:";

    public static final String LOCK_KEY8 = "ATTENDANCE:ZKTECO8:LOCK:";
    /**
     * 1个小时
     */
    public static final Long EXPIRE_TIME = 1 * 60 * 60L;


    /**
     * 获取token
     */
    public String getToken(String userName, String password, String serverUrl) {
        String tokenStr = null;
        if (StringUtils.equalsIgnoreCase(attendanceProperties.getZkteco().getSERVER_URL_VERSION_8(), serverUrl)) {
            tokenStr = imileRedisClient.get(LOCK_KEY8, String.class);
        } else {
            tokenStr = imileRedisClient.get(LOCK_KEY, String.class);
        }
        if (StringUtils.isNotBlank(tokenStr)) {
            return tokenStr;
        }
        String url = serverUrl + "/api-token-auth/";
        String param = "{\"username\": \"" + userName + "\"," +
                " \"password\": \"" + password + "\"}";

        String tokenJson = doPost(url, null, param, "POST");
        JSONObject jsonObject = JSONObject.parseObject(tokenJson);
        tokenStr = jsonObject.get("token").toString();
        tokenStr = "Token " + tokenStr;
        if (StringUtils.equalsIgnoreCase(attendanceProperties.getZkteco().getSERVER_URL_VERSION_8(), serverUrl)) {
            imileRedisClient.set(LOCK_KEY8, tokenStr);
            //tokenStr = imileRedisClient.set(LOCK_KEY8, String.class);
        } else {
            imileRedisClient.set(LOCK_KEY, tokenStr);
            // tokenStr = imileRedisClient.set(LOCK_KEY, String.class);
        }
        log.info("getToken | tokenJson:{}, tokenStr:{}", tokenJson, tokenStr);
        return tokenStr;
    }


    /**
     * 获取JWT token
     */
    public String getJWTToken(String token, String userName, String password, String serverUrl) {
        String tokenStr = imileRedisClient.get(LOCK_KEY, String.class);
        if (StringUtils.isNotBlank(tokenStr)) {
            return tokenStr;
        }
        String url = serverUrl + "/JWT-api-token-auth/";
        String param = "{\"username\": \"" + userName + "\"," +
                " \"password\": \"" + password + "\"}";

        String tokenJson = doPost(url, token, param, "POST");
        JSONObject jsonObject = JSONObject.parseObject(tokenJson);
        tokenStr = jsonObject.get("token").toString();
        tokenStr = "JWT " + tokenStr;
        imileRedisClient.set(LOCK_KEY, tokenStr, EXPIRE_TIME);
        return tokenStr;
    }

    /**
     * 创建区域信息
     */
    public String createArea(String token, String areaCode, String areaName, String serverUrl) {
        String url = serverUrl + "/personnel/api/areas/";
        String param = "{\"area_code\": \"" + areaCode + "\"," +
                " \"area_name\": \"" + areaName + "\"}";

        String result = doPost(url, token, param, "POST");
        return result;
    }

    /**
     * 创建部门信息
     */
    public String createDept(String token, String deptCode, String deptName, String serverUrl) {
        String url = serverUrl + "/personnel/api/departments/";
        String param = "{\"dept_code\": \"" + deptCode + "\"," +
                " \"dept_name\": \"" + deptName + "\"}";

        String result = doPost(url, token, param, "POST");
        return result;
    }

    /**
     * 创建职位信息
     */
    public String createPost(String token, String postCode, String postName, String serverUrl) {
        String url = serverUrl + "/personnel/api/positions/";
        String param = "{\"post_code\": \"" + postCode + "\"," +
                " \"post_name\": \"" + postName + "\"}";

        String result = doPost(url, token, param, "POST");
        return result;
    }

    /**
     * 创建员工信息
     */
    public String createEmployee(String token, String userCode, String firstName, String lastName, Integer deptId, List<Integer> arr, String serverUrl) {
        String url = serverUrl + "/personnel/api/employees/";
        String param = "{\"emp_code\": \"" + userCode + "\"," +
                " \"department\": " + deptId + "," +
                " \"area\": " + arr;
        if (StringUtils.isNotBlank(firstName)) {
            String newFirstName = firstName;
            if (newFirstName.length() > 24) {
                newFirstName = newFirstName.substring(0, 24);
            }
            // 去掉空格
            newFirstName = newFirstName.trim();
            param = param + "," + " \"first_name\": \"" + newFirstName + "\"";
        }
        if (StringUtils.isNotBlank(lastName)) {
            // 去掉空格
            lastName = lastName.trim();
            param = param + "," + " \"last_name\": " + lastName + "\"";
        }
        param = param + "}";
        log.info("createEmployee | url :{}, token :{}, param :{}", url, token, param);
        String result = doPost(url, token, param, "POST");
        log.info("createEmployee | result:{}", result);
        return result;
    }

    /**
     * 修改员工信息
     */
    public String updateEmployee(String token, ZktecoEmployeeUpdateDO updateDO, String serverUrl) {
        String url = serverUrl + "/personnel/api/employees/" + updateDO.getId() + "/";
        String param = "{\"emp_code\": \"" + updateDO.getUserCode() + "\"," +
                " \"department\": " + updateDO.getDeptId() + "," +
                " \"area\": " + updateDO.getAreaIds();
        if (StringUtils.isNotBlank(updateDO.getFirstName())) {
            String newFirstName = updateDO.getFirstName();
            if (newFirstName.length() > 24) {
                newFirstName = newFirstName.substring(0, 24);
            }
            // 去掉空格
            newFirstName = newFirstName.trim();
            param = param + "," + " \"first_name\": \"" + newFirstName + "\"";
        }
        if (StringUtils.isNotBlank(updateDO.getLastName())) {
            String lastName = updateDO.getLastName();
            // 去掉空格
            lastName = lastName.trim();
            param = param + "," + " \"last_name\": \"" + lastName + "\"";
        }
        if (updateDO.getStatus() != null) {
            param = param + "," + " \"status\": " + updateDO.getStatus();
        }
        param = param + "}";
        log.info("updateEmployee | url :{}, token :{}, param :{}", url, token, param);
        String result = doPut(url, token, param, "PUT");
        log.info("updateEmployee | result:{}", result);
        return result;
    }

    /**
     * 删除员工信息
     */
    public String deleteEmployee(String token, Integer id, String serverUrl) {
        String url = serverUrl + "/personnel/api/employees/" + id + "/";
        String result = doDelete(url, token);
        log.info("deleteEmployee | id :{}, result:{}", id, result);
        return result;
    }

    /**
     * 获取区域信息
     */
    public String listArea(String token, Integer page, Integer pageSize, String serverUrl) {
        String url = serverUrl + "/personnel/api/areas/";

        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 100;
        }
        url = url + "?" + "page=" + page + "&page_size=" + pageSize;
        String result = doGet(url, token);
        return result;
    }

    /**
     * 获取区域信息
     */
    public String listDept(String token, Integer page, Integer pageSize, String serverUrl) {
        String url = serverUrl + "/personnel/api/departments/";

        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 100;
        }
        url = url + "?" + "page=" + page + "&page_size=" + pageSize;
        String result = doGet(url, token);
        return result;
    }

    /**
     * 获取人员列表
     */
    public String listEmployee(String token, Integer page, Integer pageSize, String serverUrl) {
        String url = serverUrl + "/personnel/api/employees/";
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 100;
        }
        url = url + "?" + "page=" + page + "&page_size=" + pageSize;
        String result = doGet(url, token);
        return result;
    }

    /**
     * 获取设备信息
     */
    public String listDevices(String token, Integer page, Integer pageSize, String serverUrl) {
        String url = serverUrl + "/iclock/api/terminals/";
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 100;
        }
        url = url + "?" + "page=" + page + "&page_size=" + pageSize;
        String result = doGet(url, token);
        return result;
    }

    /**
     * 获取考勤列表
     */
    public String listAttendances(String token, Date startDate, Date endDate, Integer page, Integer pageSize, String sn, String userCode, String serverUrl) {
        String url = serverUrl + "/iclock/api/transactions/";
        if (page == null) {
            page = 1;
        }
        if (pageSize == null) {
            pageSize = 100;
        }
        if (startDate == null || endDate == null) {
            startDate = DateUtil.offsetDay(new Date(), -1);
            endDate = startDate;
        }
        try {
            String endTime = DateFormatUtils.format(DateConvertUtils.getMaxTime(endDate));
            String startTime = DateFormatUtils.format(DateConvertUtils.getMinTime(startDate));
            url = url + "?" + "page=" + page + "&page_size=" + pageSize + "&start_time=" + startTime + "&end_time=" + endTime;
        } catch (Exception e) {
            log.info("listAttendances | error:{}", e);
        }

        if (StringUtils.isNotBlank(sn)) {
            url = url + "&" + "terminal_sn=" + sn;
        }
        if (StringUtils.isNotBlank(userCode)) {
            url = url + "&" + "emp_code=" + userCode;
        }
        url = url.replaceAll(" ", "%20");
        String attendances = doGet(url, token);
        log.info("listAttendances | url :{}, attendances:{}", url, attendances);
        return attendances;
    }




    private String doGet(String url, String token) {
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("RequestMethod", "GET");
        headers.put("Authorization", token);
        return HttpUtils.getString(url, headers);
    }



    private String doPost(String url, String token, String param, String method) {
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("RequestMethod", method);
        if (StringUtils.isNotBlank(token)) {
            headers.put("Authorization", token);
        }

        return HttpUtils.postString(url, param, headers);
    }

    private static String doPut(String url, String token, String param, String method) {
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("RequestMethod", method);
        if (StringUtils.isNotBlank(token)) {
            headers.put("Authorization", token);
        }

        HttpResult httpResult = put(url, param, headers);
        return httpResult != null ? httpResult.getStringContent() : null;
    }

    private String doDelete(String url, String token) {
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-type", "application/json");
        headers.put("Accept", "application/json");
        headers.put("RequestMethod", "DELETE");
        headers.put("Authorization", token);
        HttpResult httpResult = delete(url, headers);
        return httpResult != null ? httpResult.getStringContent() : null;
    }

    public static HttpResult delete(String postUrl, Map<String, String> headers) {
        log.info("new delete:{url=" + postUrl + "}");
        HttpClient httpClient = HttpUtils.getClient(postUrl);
        HttpDelete delete = new HttpDelete(postUrl);
        RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(60000).setConnectTimeout(60000).setSocketTimeout(60000).build();
        if (headers != null) {
            Iterator var5 = headers.keySet().iterator();

            while (var5.hasNext()) {
                String headerName = (String) var5.next();
                delete.setHeader(headerName, (String) headers.get(headerName));
            }
        }

        try {
            delete.setConfig(config);
            HttpResponse response = httpClient.execute(delete);
            HttpResult result = new HttpResult();
            result.setStatus(response.getStatusLine().getStatusCode());
            HttpEntity entity = response.getEntity();
            result.setContent(entity.getContent());
            Map<String, String> responseHeaders = getResponseHeadersMap(response);
            result.setHeaders(responseHeaders);
            HttpResult var9 = result;
            return var9;
        } catch (IOException var13) {
            log.error("get:" + postUrl + "请求失败，发生IOException", var13);
        } finally {
            delete.releaseConnection();
        }

        return null;
    }

    public static HttpResult put(String postUrl, String postData, Map<String, String> headers) {
        HttpClient httpClient = HttpUtils.getClient(postUrl);
        HttpPut put = new HttpPut(postUrl);
        RequestConfig config = RequestConfig.custom().setConnectionRequestTimeout(60000).setConnectTimeout(60000).setSocketTimeout(60000).build();
        Iterator var6;
        if (headers != null) {
            var6 = headers.keySet().iterator();

            while (var6.hasNext()) {
                String headerName = (String) var6.next();
                put.setHeader(headerName, (String) headers.get(headerName));
            }
        }
        var6 = null;
        try {
            if (StringUtils.isNotBlank(postUrl)) {
                put.setEntity(new StringEntity(postData, "UTF-8"));
            }

            put.setConfig(config);
            HttpResponse response = httpClient.execute(put);
            HttpResult result = new HttpResult();
            result.setStatus(response.getStatusLine().getStatusCode());
            HttpEntity entity = response.getEntity();
            result.setContent(entity.getContent());
            HttpResult var9 = result;
            return var9;
        } catch (ClientProtocolException var14) {
            log.error("带请求头自定义的post:" + postUrl + "请求失败，发生ClientProtocolException", var14);
        } catch (IOException var15) {
            log.error("带请求头自定义的post:" + postUrl + "请求失败，发生IOException", var15);
        } finally {
            put.releaseConnection();
        }

        return null;
    }

    private static Map<String, String> getResponseHeadersMap(HttpResponse httpResponse) {
        Header[] allHeaders = httpResponse.getAllHeaders();
        HashMap<String, String> result = new HashMap();

        for (int i = 0; i < allHeaders.length; ++i) {
            Header header = allHeaders[i];
            result.put(header.getName(), header.getValue());
        }
        return result;
    }
}
