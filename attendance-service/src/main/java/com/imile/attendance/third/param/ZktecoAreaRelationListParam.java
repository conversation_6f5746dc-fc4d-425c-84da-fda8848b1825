package com.imile.attendance.third.param;

import com.imile.common.query.BaseQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ZktecoAreaRelationListParam extends BaseQuery implements Serializable {

    /**
     * 中控区域名称
     */
    private String zktecoAreaName;

    /**
     * 考勤机编号
     */
    private String terminalSn;

    /**
     * 部门
     */
    private Long deptId;

    /**
     * 国家
     */
    private String country;
}
