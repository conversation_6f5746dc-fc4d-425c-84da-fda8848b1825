package com.imile.attendance.third;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.third.model.ZktecoAreaSnRelationDO;
import com.imile.attendance.infrastructure.repository.third.query.ZktecoAreaRelationQueryDTO;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.third.dto.ZktecoAreaRelationDTO;
import com.imile.attendance.third.dto.ZktecoAreaRelationDeptDTO;
import com.imile.attendance.third.dto.ZktecoUserDTO;
import com.imile.attendance.third.mapstruct.ZktecoMapstruct;
import com.imile.attendance.third.dto.ZktecoAreasDTO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.PageUtil;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5 
 * @Description
 */
@Service
public class ZktecoAreaSnRelationService {

    @Resource
    private ZktecoAreaSnRelationManage zktecoAreaSnRelationManage;
    @Resource
    private AttendanceDeptService attendanceDeptService;
    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private ThirdZktecoService thirdZktecoService;
    @Resource
    private ThirdZktecoQueryService thirdZktecoQueryService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendancePermissionService attendancePermissionService;


    /**
     * 考勤机关联信息查询
     */
    public PaginationResult<ZktecoAreaRelationDTO> zktecoAreaRelationList(ZktecoAreaRelationQueryDTO queryDTO) {
        Page<ZktecoAreaSnRelationDO> page = PageHelper.startPage(
                queryDTO.getCurrentPage(),
                queryDTO.getShowCount(),
                queryDTO.getShowCount() > 0
        );
        // 添加常驻国权限
        List<String> countryList = attendancePermissionService.filterUserCountryAuth(queryDTO.getCountry(), queryDTO.getCountryList());
        if (CollectionUtils.isEmpty(countryList)) {
            return PaginationResult.get(Collections.emptyList(), queryDTO);
        }
        queryDTO.setCountryList(countryList);
        PageInfo<ZktecoAreaSnRelationDO> pageInfo = page.doSelectPageInfo(
                () -> zktecoAreaSnRelationManage.zktecoAreaRelationList(queryDTO));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), queryDTO);
        }

        List<Long> deptIdList = pageInfo.getList()
                .stream()
                .flatMap(relation -> relation.listDeptIdList().stream())
                .collect(Collectors.toList());

        Map<Long, List<AttendanceDept>> deptMap = attendanceDeptService.listByDeptIds(deptIdList)
                .stream()
                .collect(Collectors.groupingBy(AttendanceDept::getId));

        List<ZktecoAreaSnRelationDO> relationDOList = pageInfo.getList();
        List<ZktecoAreaRelationDTO> relationDTOList = new ArrayList<>();
        for (ZktecoAreaSnRelationDO relationDO : relationDOList) {
            ZktecoAreaRelationDTO zktecoAreaRelationDTO = new ZktecoAreaRelationDTO();
            zktecoAreaRelationDTO.setCountry(relationDO.getCountry());
            zktecoAreaRelationDTO.setId(relationDO.getId());
            zktecoAreaRelationDTO.setZktecoAreaId(relationDO.getZktecoAreaId());
            zktecoAreaRelationDTO.setZktecoAreaName(relationDO.getZktecoAreaName());
            zktecoAreaRelationDTO.setZktecoAreaCode(relationDO.getZktecoAreaCode());
            zktecoAreaRelationDTO.setTerminalSnList(relationDO.listTerminalSnList());

            List<ZktecoAreaRelationDeptDTO> zktecoAreaRelationDeptDTOS = new ArrayList<>();
            if (StringUtils.isNotBlank(relationDO.getDeptIds())) {
                List<Long> existDeptIdList = relationDO.listDeptIdList();
                existDeptIdList.forEach(item -> {
                    List<AttendanceDept> deptList = deptMap.get(item);
                    if (CollectionUtils.isNotEmpty(deptList)) {
                        zktecoAreaRelationDeptDTOS.add(ZktecoMapstruct.INSTANCE.toZktecoDeptDTO(deptList.get(0)));
                    }
                });
            }
            zktecoAreaRelationDTO.setZktecoAreaRelationDeptDTOS(zktecoAreaRelationDeptDTOS);

            if (StringUtils.isNotBlank(relationDO.getUserIds())) {
                List<Long> userIdList = relationDO.listUserIdList();
                List<AttendanceUser> userInfoDOList = attendanceUserService.listUsersByIds(userIdList);
                List<ZktecoUserDTO> zktecoUserDTO = ZktecoMapstruct.INSTANCE.toZktecoUserDTO(userInfoDOList);
                zktecoAreaRelationDTO.setUserList(zktecoUserDTO);
            }

            zktecoAreaRelationDTO.setLastUpdDate(relationDO.getLastUpdDate());
            zktecoAreaRelationDTO.setLastUpdUserCode(relationDO.getLastUpdUserCode());
            zktecoAreaRelationDTO.setLastUpdUserName(relationDO.getLastUpdUserName());
            relationDTOList.add(zktecoAreaRelationDTO);
        }
        return PageUtil.getPageResult(relationDTOList, queryDTO, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 更新考勤机关联信息(只能更新部门)
     */
    public boolean zktecoAreaRelationUpdate(Long id, String country, List<Long> deptIdList, List<Long> userIdList) {
        ZktecoAreaSnRelationDO relationDO = zktecoAreaSnRelationManage.getById(id);
        if (relationDO == null) {
            throw BusinessException.get(ErrorCodeEnum.ZKTECO_AREA_AND_TERMINAL_SN_NOT_EXIST.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ZKTECO_AREA_AND_TERMINAL_SN_NOT_EXIST.getDesc()));
        }
        ZktecoAreaSnRelationDO newRelation = new ZktecoAreaSnRelationDO();
        BeanUtil.copyProperties(relationDO, newRelation);
        newRelation.setCountry(country);
        newRelation.setDeptIds(StringUtils.join(deptIdList, ","));
        newRelation.setUserIds(StringUtils.join(userIdList, ","));
        BaseDOUtil.fillDOUpdate(newRelation);
        zktecoAreaSnRelationManage.updateById(newRelation);

        thirdZktecoService.syncEmployees(
                country,
                relationDO,
                deptIdList,
                userIdList
        );
        return true;
    }

    /**
     * 通过dept_id查询所有包含的区域
     */
    public List<ZktecoAreaSnRelationDO> selectByDeptId(Long deptId) {
        return thirdZktecoQueryService.selectByDeptId(deptId);
    }

    /**
     * 通过user_id查询所有包含的区域
     */
    public List<ZktecoAreaSnRelationDO> selectByUserId(Long userId) {
        return thirdZktecoQueryService.selectByUserId(userId);
    }

    /**
     * 同步考勤机区域和机器编码的关联信息到HRMS
     */
    public boolean zktecoAreaRelationSync() {
        Map<String, List<ZktecoAreasDTO>> zktecoAreasMap = thirdZktecoService.selectSnByArea();

        for (Map.Entry<String, List<ZktecoAreasDTO>> entry : zktecoAreasMap.entrySet()) {
            List<ZktecoAreasDTO> zktecoAreasDTOList = entry.getValue();
            if (CollectionUtils.isEmpty(zktecoAreasDTOList)) {
                continue;
            }
            List<Integer> areaIdList = entry.getValue().stream()
                    .map(ZktecoAreasDTO::getId)
                    .collect(Collectors.toList());

            //获取数据库中已经存在存在的区域关联关系
            List<ZktecoAreaSnRelationDO> existRelationDOList = zktecoAreaSnRelationManage.getByAreaIdList(areaIdList);
            Map<String, List<ZktecoAreaSnRelationDO>> areaCodeMaps = existRelationDOList.stream()
                    .collect(Collectors.groupingBy(ZktecoAreaSnRelationDO::getZktecoAreaCode));

            List<ZktecoAreaSnRelationDO> insertList = new ArrayList<>();
            List<ZktecoAreaSnRelationDO> updateList = new ArrayList<>();
            // updateList.addAll(notExistRelationDOList);
            for (ZktecoAreasDTO zktecoAreasDTO : zktecoAreasDTOList) {
                List<ZktecoAreaSnRelationDO> relationDOList = areaCodeMaps.get(zktecoAreasDTO.getAreaCode());
                if (CollectionUtils.isEmpty(relationDOList)) {
                    //历史数据没有这个areaId,需要新增
                    buildZktecoRelationDO(insertList, zktecoAreasDTO);
                    continue;
                }
                //判断中控考勤机关联数据是否需要更新
                checkZktecoRelationDO(updateList, relationDOList.get(0), zktecoAreasDTO);
            }
            zktecoAreaSnRelationManage.relationSaveOrUpdate(insertList, updateList);
        }
        return true;
    }

    /**
     * 构建中控考勤机基础数据
     */
    private void buildZktecoRelationDO(List<ZktecoAreaSnRelationDO> insertList, ZktecoAreasDTO zktecoAreasDTO) {
        ZktecoAreaSnRelationDO relationDO = new ZktecoAreaSnRelationDO();
        relationDO.setId(defaultIdWorker.nextId());
        relationDO.setZktecoAreaId(zktecoAreasDTO.getId());
        relationDO.setZktecoAreaCode(zktecoAreasDTO.getAreaCode());
        relationDO.setZktecoAreaName(zktecoAreasDTO.getAreaName());
        relationDO.setTerminalSn(StringUtils.join(zktecoAreasDTO.getSnList(), ","));
        relationDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(relationDO);
        insertList.add(relationDO);
    }

    /**
     * 判断中控考勤机关联数据是否需要更新
     */
    private void checkZktecoRelationDO(List<ZktecoAreaSnRelationDO> updateList,
                                       ZktecoAreaSnRelationDO relationDO,
                                       ZktecoAreasDTO zktecoAreasDTO) {
        //看区域名称有没有变更
        if (!StringUtils.equalsIgnoreCase(relationDO.getZktecoAreaName(), zktecoAreasDTO.getAreaName())) {
            relationDO.setZktecoAreaName(zktecoAreasDTO.getAreaName());
            updateList.add(relationDO);
            return;
        }
        //看区域编码有没有变更
        if (!StringUtils.equalsIgnoreCase(relationDO.getZktecoAreaCode(), zktecoAreasDTO.getAreaCode())) {
            relationDO.setZktecoAreaCode(zktecoAreasDTO.getAreaCode());
            updateList.add(relationDO);
            return;
        }
        //看区域绑定的考勤机有没有变更
        List<String> existTerminalSnList = Arrays.asList(relationDO.getTerminalSn().split(","));
        if (existTerminalSnList.size() != zktecoAreasDTO.getSnList().size()) {
            relationDO.setTerminalSn(StringUtils.join(zktecoAreasDTO.getSnList(), ","));
            updateList.add(relationDO);
            return;
        }
        for (String sn : zktecoAreasDTO.getSnList()) {
            if (!existTerminalSnList.contains(sn)) {
                relationDO.setTerminalSn(StringUtils.join(zktecoAreasDTO.getSnList(), ","));
                updateList.add(relationDO);
                break;
            }
        }
    }
}
