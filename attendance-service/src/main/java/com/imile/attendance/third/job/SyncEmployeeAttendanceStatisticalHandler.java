package com.imile.attendance.third.job;

import com.imile.attendance.constants.BusinessConstant;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description 非司机员工每日打卡统计数据
 */
@Slf4j
@Component
public class SyncEmployeeAttendanceStatisticalHandler {


    @XxlJob(BusinessConstant.JobHandler.SYNC_EMPLOYEE_ATTENDANCE_STATISTICAL_HANDLER)
    public ReturnT<String> syncEmployeeAttendanceStatisticalHandler(String param) {
        return null;
    }
}
