package com.imile.attendance.third.mapstruct;



import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.third.dto.ZktecoAreaRelationDeptDTO;
import com.imile.attendance.third.dto.ZktecoUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/5 
 * @Description
 */
@Mapper(config = MapperConfiguration.class, imports = {RequestInfoHolder.class})
public interface ZktecoMapstruct {

    ZktecoMapstruct INSTANCE = Mappers.getMapper(ZktecoMapstruct.class);


    ZktecoUserDTO toZktecoUserDTO(AttendanceUser attendanceUser);

    List<ZktecoUserDTO> toZktecoUserDTO(List<AttendanceUser> attendanceUser);


    @Mapping(target = "deptName", expression = "java(RequestInfoHolder.isChinese()?attendanceDept.getDeptNameCn():attendanceDept.getDeptNameEn())")
    @Mapping(target = "deptId", source = "id")
    ZktecoAreaRelationDeptDTO toZktecoDeptDTO(AttendanceDept attendanceDept);




}
