package com.imile.attendance.driver.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverPunchRecordDao;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 司机考勤数据管理服务
 *
 * <AUTHOR> chen
 * @date 2025/6/24
 * @description 提供司机考勤相关数据的管理功能，包括数据清理、消息消费控制等操作
 */
@Slf4j
@Service
public class DriverDataManagementService {

    @Resource
    private DriverPunchRecordDao driverPunchRecordDao;

    /**
     * 司机消息消费统一控制开关
     * 默认值为true，表示启用所有司机相关的消息消费
     * 设置为false时将禁用DaTrackMsgListener和TmsDmsScanMsgListener的消息处理
     */
    @Value("${driver.message.consumption.enabled:true}")
    private boolean messageConsumptionEnabled;

    /**
     * 清空司机打卡记录表所有数据,物理删除driver_punch_record表中的所有记录
     */
    @Transactional(rollbackFor = Exception.class)
    public void clearAllDriverPunchRecords() {
        try {
            log.warn("开始执行司机打卡记录表数据清理操作");

            // 查询当前记录总数，用于日志记录
            long totalCount = driverPunchRecordDao.count();
            log.info("当前driver_punch_record表中共有{}条记录", totalCount);
            XxlJobLogger.log("当前driver_punch_record表中共有{}条记录", totalCount);
            
            if (totalCount == 0) {
                log.info("driver_punch_record表为空，无需清理");
                XxlJobLogger.log("driver_punch_record表为空，无需清理");
                return;
            }
            
            // 创建查询条件 - 删除所有记录（不使用逻辑删除）
            LambdaQueryWrapper<DriverPunchRecordDO> queryWrapper = Wrappers.lambdaQuery();
            // 不添加任何条件，表示删除所有记录
            
            // 执行物理删除操作
            boolean deleteResult = driverPunchRecordDao.remove(queryWrapper);
            
            if (deleteResult) {
                log.warn("司机打卡记录表数据清理完成，已删除{}条记录", totalCount);

                // 验证删除结果
                long remainingCount = driverPunchRecordDao.count();
                if (remainingCount == 0) {
                    log.info("数据清理验证通过，表中剩余记录数：{}", remainingCount);
                } else {
                    log.error("数据清理验证失败，表中仍有{}条记录未删除", remainingCount);
                    throw new RuntimeException("数据清理不完整，仍有" + remainingCount + "条记录未删除");
                }
            } else {
                log.error("司机打卡记录表数据清理失败");
                throw new RuntimeException("司机打卡记录表数据清理操作失败");
            }
            
        } catch (Exception e) {
            log.error("执行司机打卡记录表数据清理时发生异常", e);
            throw new RuntimeException("司机打卡记录表数据清理操作异常：" + e.getMessage(), e);
        }
    }

    /**
     * 检查司机消息消费是否启用
     *
     * - 统一控制所有司机相关的消息消费开关
     * - 包括DA轨迹消息和TMS DMS扫描消息
     * - 用于DaTrackMsgListener和TmsDmsScanMsgListener的消息处理控制
     *
     * @return true-启用消息消费，false-禁用消息消费
     */
    public boolean isMessageConsumptionEnabled() {
        return messageConsumptionEnabled;
    }

    /**
     * 获取司机消息消费状态信息
     *
     * @return 消息消费状态描述
     */
    public String getMessageConsumptionStatus() {
        return String.format("司机消息消费状态: %s (配置项: driver.message.consumption.enabled=%s)",
                messageConsumptionEnabled ? "启用" : "禁用", messageConsumptionEnabled);
    }
}
