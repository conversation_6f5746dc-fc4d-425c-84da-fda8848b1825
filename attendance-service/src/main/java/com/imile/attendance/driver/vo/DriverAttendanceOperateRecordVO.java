package com.imile.attendance.driver.vo;

import cn.hutool.core.util.ObjectUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.OutWithTimeZone;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.DriverAttendanceOperateRecordEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceOperateRecordVO
 * {@code @since:} 2024-01-22 14:06
 * {@code @description:}
 */
@Data
public class DriverAttendanceOperateRecordVO implements Serializable {

    private static final long serialVersionUID = 7581964710851576628L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家
     */
    private String country;

    /**
     * 操作类型：1：修改考勤数据、2：导出 ...
     */
    private Integer operationType;

    /**
     * 操作类型：1：修改考勤数据、2：导出 ...
     */
    private String operationTypeString;

    /**
     * 操作内容：被修改人：xxxD1234，日报 or 月报
     */
    private String operationContentView;

    /**
     * 操作内容：被修改人：xxxD1234，日报 or 月报
     */
    private String operationContent;

    /**
     * 操作内容：被修改人：xxxD1234，日报 or 月报
     */
    private String operationContentEn;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @OutWithTimeZone
    private Date createDate;

    private String createUserCode;

    private String createUserName;

    public String queryOperationContentView() {
        return RequestInfoHolder.isChinese() ? this.operationContent : this.operationContentEn;
    }

    public String queryOperationTypeString() {
        if (ObjectUtil.isNotNull(DriverAttendanceOperateRecordEnum.getByType(this.operationType))) {
            operationTypeString = RequestInfoHolder.isChinese() ?
                    DriverAttendanceOperateRecordEnum.getByType(this.operationType).getDesc() :
                    DriverAttendanceOperateRecordEnum.getByType(this.operationType).getDescEn();
        }
        return operationTypeString;
    }
}
