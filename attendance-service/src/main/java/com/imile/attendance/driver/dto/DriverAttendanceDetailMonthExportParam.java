package com.imile.attendance.driver.dto;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailMonthExportParam
 * {@code @since:} 2024-01-25 16:14
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DriverAttendanceDetailMonthExportParam extends ResourceQuery {
    /**
     * 国家
     */
    @ApiModelProperty(value="国家")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * startDayId 示例：20240124
     * 司机月报查询条件
     */
    @ApiModelProperty(value="startDayId 示例：20240124")
    private Long monthStartDayId;

    /**
     * endDayId 示例：20240124
     * 司机月报查询条件
     */
    @ApiModelProperty(value="endDayId 示例：20240124")
    private Long monthEndDayId;

    /**
     * startDate 示例：2024-01-24
     * 司机月报查询条件
     */
    @ApiModelProperty(value="startDate 示例：2024-01-24")
    private String monthStartDate;

    /**
     * endDate 示例：2024-01-24
     * 司机月报查询条件
     */
    @ApiModelProperty(value="endDate 示例：2024-01-24")
    private String monthEndDate;

    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    private String attendanceTypeList;

    /**
     * 用工类型
     */
    private String employeeTypeList;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 工作状态集合
     */
    private String workStatusList;

    /**
     * 供应商code
     */
    private String vendorCode;

    /**
     * 账号/姓名
     */
    private String userCodeOrName;

    /**
     * 部门id 集合
     */
    private String deptIdList;

    /**
     * 汇报上级
     */
    private Long leaderId;
}
