package com.imile.attendance.driver.dto;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceInfoDetailParam
 * {@code @since:} 2024-01-24 10:37
 * {@code @description:}
 */
@Data
@ApiModel(description = "司机考勤详情入参")
public class DriverAttendanceInfoDetailParam {
    /**
     * userId
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long userId;
    /**
     * 年
     */
    private Long year;
}
