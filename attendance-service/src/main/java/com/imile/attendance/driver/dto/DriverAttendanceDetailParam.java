package com.imile.attendance.driver.dto;

import com.imile.attendance.query.ResourceQuery;
import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverAttendanceDetailParam
 * {@code @since:} 2024-01-22 15:58
 * {@code @description:}
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "司机每日考勤入参")
public class DriverAttendanceDetailParam extends ResourceQuery {
    /**
     * 国家
     */
    @ApiModelProperty(value="国家")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * day_id 示例：20240124
     */
    @ApiModelProperty(value="day_id 示例：20240124")
    private Long dayId;

    /**
     * dayDate 示例：2024-01-24
     * 司机日报查询条件
     */
    @ApiModelProperty(value="dayDate 示例：2024-01-24")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Date dayDate;

    /**
     * dayDate 示例：2024-01-24 00:00:00,用来mybatis sql判断
     * 司机日报查询条件
     */
    @ApiModelProperty(value="dayDateString 示例：2024-01-24 00:00:00")
    private String dayDateString;
    /**
     * 出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...
     */
    @ApiModelProperty(value="出勤类型: 1：P 出勤，2：A 缺勤， 3：L 请假 ...")
    private List<Integer> attendanceTypeList;

    /**
     * 用工类型
     */
    @ApiModelProperty(value="用工类型")
    private List<String> employeeTypeList;

    /**
     * 账号状态
     */
    @ApiModelProperty(value="账号状态")
    private String status;

    /**
     * 工作状态
     */
    @ApiModelProperty(value="工作状态")
    private String workStatus;

    /**
     * 工作状态集合
     */
    @ApiModelProperty(value="工作状态")
    private List<String> workStatusList;

    /**
     * 供应商code
     */
    @ApiModelProperty(value="供应商code")
    private String vendorCode;

    /**
     * 账号/姓名
     */
    @ApiModelProperty(value="账号/姓名")
    private String userCodeOrName;

    /**
     * 部门id
     */
    @ApiModelProperty(value="部门id")
    private List<Long> deptIdList;

//    /**
//     * 汇报上级
//     */
//    @ApiModelProperty(value="汇报上级")
//    private Long leaderId;

}
