package com.imile.attendance.driver;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.constants.Constants;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthExportParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailParam;
import com.imile.attendance.driver.dto.DriverAttendanceInfoDetailParam;
import com.imile.attendance.driver.mapstruct.DriverPunchMapstruct;
import com.imile.attendance.driver.vo.DriverAttendanceDetailMonthVO;
import com.imile.attendance.driver.vo.DriverAttendanceDetailVO;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.DeptOrgTypeEnum;
import com.imile.attendance.enums.DriverAttendanceOperateRecordEnum;
import com.imile.attendance.enums.DriverAttendanceOperationTypeEnum;
import com.imile.attendance.enums.DriverAttendanceTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.hermes.dto.DictVO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.calendar.dao.BaseDayInfoDao;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import com.imile.attendance.infrastructure.repository.calendar.query.BaseDayQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.DictService;
import com.imile.attendance.infrastructure.repository.common.EntOcService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionDeptVO;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverAttendanceDetailDao;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceHolidayDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceInfoDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceMonthTitleExportDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverMonthAttendanceDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverYearAttendanceDTO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDetailDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailInfoQuery;
import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.rescources.SystemResourceManage;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigManage;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.hermes.enterprise.dto.EntOcApiDTO;
import com.imile.ucenter.api.context.RequestInfoHolder;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 司机考勤明细服务类
 * 负责处理司机考勤相关的业务逻辑，包括日报查询、月报查询、考勤详情查询和数据导出等功能
 *
 * <AUTHOR> chen
 * @Date 2025/3/25
 */
@Slf4j
@Service
public class DriverAttendanceDetailService {

    @Resource
    private SystemResourceManage systemResourceManage;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private DriverAttendanceDetailDao driverAttendanceDetailDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private CountryService countryService;
    @Resource
    private DriverPunchRecordManage driverPunchRecordManage;
    @Resource
    private DriverAttendanceManage driverAttendanceManage;
    @Resource
    private AttendancePostService postService;
    @Resource
    private EntOcService entOcService;
    @Resource
    private DictService dictService;
    @Resource
    private BaseDayInfoDao baseDayInfoDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private CompanyLeaveConfigManage companyLeaveConfigManage;


    /**
     * 查询司机考勤日报数据
     * 用于展示指定日期的司机考勤情况，包括出勤状态、部门信息、岗位信息等
     * 支持按用户名称/编码、部门等条件进行筛选
     *
     * @param param 查询参数，包含日期、筛选条件等
     * @return 分页的司机考勤明细列表
     */
    public PaginationResult<DriverAttendanceDetailVO> queryDriverAttendance(DriverAttendanceDetailParam param) {
        handlerParam(param);
        DriverAttendanceDetailQuery query = DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailQuery(param);
        if (handlerQuery(query)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        Page<DriverAttendanceDetailDTO> page = PageHelper.startPage(
                query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<DriverAttendanceDetailDTO> pageInfo = page.doSelectPageInfo(
                () -> driverAttendanceDetailDao.queryDriverAttendance(query));
        List<DriverAttendanceDetailDTO> driverAttendanceDetailList = pageInfo.getList();
        if (CollectionUtils.isEmpty(driverAttendanceDetailList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<Long> deptIds = driverAttendanceDetailList.stream()
                .map(DriverAttendanceDetailDTO::getDeptId)
                .collect(Collectors.toList());
        List<Long> postIds = driverAttendanceDetailList.stream()
                .map(DriverAttendanceDetailDTO::getPostId)
                .collect(Collectors.toList());
        List<String> ocCodes = driverAttendanceDetailList.stream()
                .map(DriverAttendanceDetailDTO::getOcCode)
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> entDeptMap = deptService.listByDeptIds(deptIds)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
        Map<Long, AttendancePost> postMap = postService.listByPostList(postIds)
                .stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity()));
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(query.getCountry());
        List<EntOcApiDTO> ocList = entOcService.getOcByCodes(BusinessConstant.DEFAULT_ORG_ID, ocCodes);
        // 将ocList按照ocCode转为map
        Map<String, EntOcApiDTO> ocMap = ocList.stream()
                .collect(Collectors.toMap(EntOcApiDTO::getOcCode, Function.identity()));

        driverAttendanceDetailList.forEach(driverAttendanceDetail -> {
            // 处理数据
            AttendanceDept attendanceDept = entDeptMap.get(driverAttendanceDetail.getDeptId());
            if (ObjectUtil.isNotNull(attendanceDept)) {
                driverAttendanceDetail.setDeptName(attendanceDept.getLocalizeName());
                if (DeptOrgTypeEnum.STATION.getValue().equals(attendanceDept.getDeptType())) {
                    // 设置运营类型
                    EntOcApiDTO oc = ocMap.get(driverAttendanceDetail.getOcCode());
                    driverAttendanceDetail.setOcType(
                            Optional.ofNullable(oc).map(EntOcApiDTO::getOcType).orElse(""));
                }
            }
            AttendancePost attendancePost = postMap.get(driverAttendanceDetail.getPostId());
            if (ObjectUtil.isNotNull(attendancePost)) {
                driverAttendanceDetail.setPostName(
                        RequestInfoHolder.isChinese() ?
                                attendancePost.getPostNameCn() : attendancePost.getPostNameEn());
            }
            if (ObjectUtil.isNotNull(driverAttendanceDetail.getLastOperatingTime())) {
                Date localLastOperatingTime = DateHelper.convertDateByTimeZonePlus(
                        countryDTO.getTimeZone(), driverAttendanceDetail.getLastOperatingTime());
                driverAttendanceDetail.setLocalLastOperatingTime(localLastOperatingTime);
            }
            // 设置考勤范围
            driverAttendanceDetail.setDayId(query.getDayId());
        });
        List<DriverAttendanceDetailVO> driverAttendanceDetailVOList =
                DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailVO(driverAttendanceDetailList);
        return PageUtil.getPageResult(driverAttendanceDetailVOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 查询单个司机的年度考勤详情
     * 用于展示指定司机整年的考勤日历，包括每天的出勤状态、请假情况等
     * 主要用于司机个人考勤查看和管理
     *
     * @param param 查询参数，包含用户ID和年份
     * @return 司机年度考勤详情，包含用户信息和按月分组的考勤数据
     */
    public DriverAttendanceInfoDetailDTO queryDriverAttendanceDetail(DriverAttendanceInfoDetailParam param) {
        log.info("查询司机考勤明细：用户id：{}", param.getUserId());
        if (ObjectUtil.isNull(param) || ObjectUtil.isNull(param.getUserId())) {
            return null;
        }
        AttendanceUser attendanceUser = userService.getByUserId(param.getUserId());
        if (attendanceUser == null || StringUtils.isBlank(attendanceUser.getUserCode())) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }

        UserInformationDTO userInformation = userInfoManage.getUserInfoInformation(param.getUserId());
        if (ObjectUtil.isNull(userInformation)) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        handlerUserInformation(userInformation);
        // 司机的考勤明细：只是查询当前用户，当前时间所在年的日历
        if (ObjectUtil.isNull(param.getYear())) {
            param.setYear(Long.parseLong(String.valueOf(DateUtil.thisYear())));
        }
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(userInformation.getLocationCountry());
        // 目标对象
        DriverAttendanceInfoDetailDTO targetDto =
                DriverPunchMapstruct.INSTANCE.toDriverAttendanceInfoDetailDTO(userInformation);
        // 查询该司机该年的所有考勤信息
        DriverAttendanceDetailInfoQuery query = DriverAttendanceDetailInfoQuery.builder()
                .year(param.getYear())
                .userCode(attendanceUser.getUserCode())
                .build();
        List<DriverAttendanceDetailDO> driverAttendanceDetailList =
                driverAttendanceDetailDao.queryDriverAttendanceByCondition(query);
        // 将driverAttendanceDetailList按照lastOperatingTime倒序,过滤掉LastOperatingTime为null的数据
        List<DriverAttendanceDetailDO> sortDriverAttendanceList = driverAttendanceDetailList.stream()
                .filter(driverAttendanceDetail -> ObjectUtil.isNotNull(driverAttendanceDetail.getLastOperatingTime()))
                .sorted(Comparator.comparing(DriverAttendanceDetailDO::getLastOperatingTime).reversed())
                .collect(Collectors.toList());

        // 设置司机考勤最近操作时间
        if (CollectionUtils.isNotEmpty(sortDriverAttendanceList)) {
            Date lastOperatingTime = sortDriverAttendanceList.get(0).getLastOperatingTime();
            if (ObjectUtil.isNull(countryDTO)) {
                log.info("查询司机考勤明细：用户id：{},查询国家信息为空，国家信息：{}",
                        param.getUserId(), userInformation.getLocationCountry());
            }
            String timeZone = ObjectUtil.isNull(countryDTO) ? "" : countryDTO.getTimeZone();
            Date localLastOperatingTime = DateHelper.convertDateByTimeZonePlus(
                    timeZone, sortDriverAttendanceList.get(0).getLastOperatingTime());
            targetDto.setLastOperatingTime(lastOperatingTime);
            targetDto.setLocalLastOperatingTime(localLastOperatingTime);
        }
        // 将司机考勤数据按照dayId转换为map
        Map<Long, DriverAttendanceDetailDO> driverAttendanceDetailMap = driverAttendanceDetailList.stream()
                .collect(Collectors.toMap(DriverAttendanceDetailDO::getDayId, Function.identity(), (oldVal, newVal) -> oldVal));

        BaseDayQuery baseDayQuery = BeanUtil.copyProperties(query, BaseDayQuery.class);
        Map<Integer, List<BaseDayInfoDO>> dayMap = baseDayInfoDao.getBaseDay(baseDayQuery)
                .stream()
                .collect(Collectors.groupingBy(BaseDayInfoDO::getMonth));

        DriverYearAttendanceDTO driverYearAttendanceDTO = new DriverYearAttendanceDTO();
        driverYearAttendanceDTO.setYear(param.getYear());
        List<DriverMonthAttendanceDTO> driverMonthAttendanceList = Lists.newArrayList();
        driverYearAttendanceDTO.setDriverMonthAttendanceList(driverMonthAttendanceList);
//        Long dayIdNow = Long.valueOf(DateUtil.format(new Date(), "yyyyMMdd"));
        for (int month = Constants.JANUARY; month <= Constants.DECEMBER; month++) {
            List<BaseDayInfoDO> baseDays = dayMap.get(month);
            // 月数据
            DriverMonthAttendanceDTO driverMonthAttendance = new DriverMonthAttendanceDTO();
            driverMonthAttendance.setMonth((long) month);
            // 具体考勤信息
            List<DriverAttendanceDTO> driverAttendanceList = Lists.newArrayList();
            driverMonthAttendance.setDriverAttendanceDetail(driverAttendanceList);
            driverMonthAttendanceList.add(driverMonthAttendance);
            // 遍历所有月份
            for (BaseDayInfoDO baseDay : baseDays) {
                Long dayId = baseDay.getId();
                Date date = DateHelper.transferDayIdToDate(dayId);

                DriverAttendanceDTO driverAttendance = new DriverAttendanceDTO();
                driverAttendanceList.add(driverAttendance);
                driverAttendance.setYear(DateHelper.year(date));
                driverAttendance.setMonth(DateHelper.month(date));
                driverAttendance.setDay(DateHelper.dayOfMonth(date));
                driverAttendance.setDate(DateUtil.beginOfDay(date));
                driverAttendance.setDay(DateUtil.dayOfMonth(driverAttendance.getDate()));
                driverAttendance.setDayId(dayId);
                DriverAttendanceDetailDO driverAttendanceDetailDO = driverAttendanceDetailMap.get(dayId);
                if (ObjectUtil.isNotNull(driverAttendanceDetailDO)) {
                    driverAttendance.setAttendanceType(driverAttendanceDetailDO.getAttendanceType());
                    DriverAttendanceTypeEnum byType =
                            DriverAttendanceTypeEnum.getByType(driverAttendanceDetailDO.getAttendanceType());
                    driverAttendance.setAttendanceTypeDesc(ObjectUtil.isNotNull(byType) ? byType.getDescEn() : "");
                    driverAttendance.setDldNumber(driverAttendanceDetailDO.getDldNumber());
                    driverAttendance.setLocusNumber(driverAttendanceDetailDO.getLocusNumber());
                }
                //if (dayIdNow.compareTo(dayId) < 1) {
                //    continue;
                //}
            }
        }
        //用户考勤记录
        targetDto.setDriverYearAttendance(driverYearAttendanceDTO);
        return targetDto;
    }

    /**
     * 查询司机月度考勤报表
     * 用于生成指定时间范围内的司机考勤月报，包括出勤统计、请假统计、出勤率等
     * 支持按部门、用户等条件筛选，主要用于管理层查看考勤汇总数据
     *
     * @param param 查询参数，包含时间范围、筛选条件等
     * @return 分页的司机月度考勤统计数据
     */
    public PaginationResult<DriverAttendanceDetailMonthVO> queryMonthDriverAttendance(DriverAttendanceDetailMonthParam param) {
        handlerMonthParam(param);
        DriverAttendanceDetailMonthQuery query = DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailMonthQuery(param);
        if (handlerMonthQuery(query)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 1. 查询该用户权限下面的 所有司机人员
        Page<DriverAttendanceDetailMonthDTO> page = PageHelper.startPage(
                query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<DriverAttendanceDetailMonthDTO> pageInfo = page.doSelectPageInfo(
                () -> driverAttendanceDetailDao.queryDriverMonthAttendance(query));
        List<DriverAttendanceDetailMonthDTO> driverAttendanceDetailMonthDTOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(driverAttendanceDetailMonthDTOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<String> userCodeList = driverAttendanceDetailMonthDTOList.stream()
                .map(DriverAttendanceDetailMonthDTO::getUserCode)
                .collect(Collectors.toList());

        List<Long> deptIds = driverAttendanceDetailMonthDTOList.stream()
                .map(DriverAttendanceDetailMonthDTO::getDeptId)
                .collect(Collectors.toList());
        List<String> ocCodes = driverAttendanceDetailMonthDTOList.stream()
                .map(DriverAttendanceDetailMonthDTO::getOcCode)
                .collect(Collectors.toList());
        List<Long> postIds = driverAttendanceDetailMonthDTOList.stream()
                .map(DriverAttendanceDetailMonthDTO::getPostId)
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIds)
                .stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));

        Map<Long, AttendancePost> postMap = postService.listByPostList(postIds)
                .stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

        // 通过userCodeList，查询出所有司机的考勤数据
        DriverAttendanceDetailInfoQuery driverAttendanceDetailInfoQuery = DriverAttendanceDetailInfoQuery.builder()
                .userCodeList(userCodeList)
                .monthStartDate(query.getMonthStartDayId())
                .monthEndDate(query.getMonthEndDayId())
                .attendanceTypeList(query.getAttendanceTypeList())
                .build();

        List<DriverAttendanceDetailDO> driverAttendanceDetailList =
                driverAttendanceDetailDao.queryDriverAttendanceByCondition(driverAttendanceDetailInfoQuery);
        // 将司机考勤数据按照userCode分组
        Map<String, List<DriverAttendanceDetailDO>> userCodeToDriverAttendance = driverAttendanceDetailList.stream()
                .collect(Collectors.groupingBy(DriverAttendanceDetailDO::getUserCode));

        // 查询该时间范围内所有请假详情
        DriverPunchRecordDetailInfoQuery driverPunchRecordDetailInfoQuery = DriverPunchRecordDetailInfoQuery.builder()
                .userCodeList(userCodeList)
                .startDayId(query.getMonthStartDayId())
                .endDayId(query.getMonthEndDayId())
                .operationType((DriverAttendanceOperationTypeEnum.LEAVE.getType()))
                .build();

        List<DriverPunchRecordDetailDO> driverPunchRecordDetail = driverPunchRecordManage.queryDriverPunchRecordDetailByCondition(driverPunchRecordDetailInfoQuery);
        // 将driverPunchRecordDetail先按照userCode分组在按照leaveType分组
        Map<String, Map<String, List<DriverPunchRecordDetailDO>>> userPunchRecordDetailMap = driverPunchRecordDetail.stream()
                        .collect(Collectors.groupingBy(DriverPunchRecordDetailDO::getUserCode,
                                Collectors.groupingBy(DriverPunchRecordDetailDO::getLeaveType)));

        // 获取该国家下面所有假期
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        companyLeaveQuery.setCountry(query.getCountry());
        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());

        // 该国家下面所有假期
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.selectLeaveConfig(companyLeaveQuery);

        // 如果没有查到司机打卡记录详情：driverPunchRecordDetail数据，这个时候就没有user_code对应的假期使用情况，所以需要初始化每一个司机假期使用都为0
        List<DriverAttendanceHolidayDTO> defaultHolidayTypeList = Lists.newArrayList();
        Map<String, List<DriverAttendanceHolidayDTO>> targetMap = Maps.newHashMap();

        // 返回默认假期数据
        buildHolidayData(companyLeaveConfigList, defaultHolidayTypeList, userPunchRecordDetailMap, targetMap);
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(query.getCountry());
        List<EntOcApiDTO> ocList = entOcService.getOcByCodes(BusinessConstant.DEFAULT_ORG_ID, ocCodes);
        // 将ocList按照ocCode转换为map
        Map<String, EntOcApiDTO> ocMap = ocList.stream().collect(Collectors.toMap(EntOcApiDTO::getOcCode, Function.identity()));
        // 遍历查询出来的所有司机，封装返回数据
        for (DriverAttendanceDetailMonthDTO detail : driverAttendanceDetailMonthDTOList) {
            detail.setAttendanceRange(query.getMonthStartDate() + "," + query.getMonthEndDate());
            // 应出勤天数
            long attendanceDays = DateUtil.betweenDay(query.getMonthStartDate(), query.getMonthEndDate(), false) + 1;
            detail.setAttendanceDays(attendanceDays);
            // 假期表头和数据
            List<DriverAttendanceHolidayDTO> holidayList = targetMap.get(detail.getUserCode());
            detail.setHolidayTypeList(CollectionUtils.isEmpty(holidayList) ? defaultHolidayTypeList : holidayList);

            // 处理数据
            buildDeptAndPost(detail, deptMap, postMap, ocMap);

            // 获取指定人员在指定范围内的司机考勤数据
            List<DriverAttendanceDetailDO> driverAttendanceList = userCodeToDriverAttendance.get(detail.getUserCode());
            if (CollectionUtils.isEmpty(driverAttendanceList)) {
                continue;
            }
            // 将driverAttendanceDetailList按照lastOperatingTime倒序，并且过滤掉lastOperatingTime为null的数据
            List<DriverAttendanceDetailDO> sortDriverAttendanceList = driverAttendanceList.stream()
                    .filter(driverAttendanceDetail -> ObjectUtil.isNotNull(driverAttendanceDetail.getLastOperatingTime()))
                    .sorted(Comparator.comparing(DriverAttendanceDetailDO::getLastOperatingTime).reversed())
                    .collect(Collectors.toList());
            // 当地时间
            if (CollectionUtils.isNotEmpty(sortDriverAttendanceList) &&
                    ObjectUtil.isNotNull(sortDriverAttendanceList.get(0).getLastOperatingTime())) {
                Date localLastOperatingTime = CommonUtil.convertDateByTimeZonePlus(
                        countryDTO.getTimeZone(), sortDriverAttendanceList.get(0).getLastOperatingTime());
                detail.setLocalLastOperatingTime(localLastOperatingTime);
            }
            buildAttendanceData(detail, driverAttendanceList, attendanceDays);

        }
        pageInfo.setList(driverAttendanceDetailMonthDTOList);
        PaginationResult<DriverAttendanceDetailMonthDTO> pageResult = PageUtil.getPageResult(pageInfo, query);
        return DriverPunchMapstruct.INSTANCE.convertPaginationResult(pageResult);
    }

    /**
     * 构建假期数据统计
     * 根据公司假期配置和员工请假记录，计算每个员工各类型假期的使用天数
     * 为没有请假记录的员工初始化默认的假期数据（全部为0）
     *
     * @param companyLeaveConfigList 公司假期配置列表
     * @param defaultHolidayTypeList 默认假期类型列表（用于没有请假记录的员工）
     * @param userPunchRecordDetailMap 员工请假记录映射（按用户编码和假期类型分组）
     * @param targetMap 目标映射，存储每个员工的假期使用统计
     */
    private void buildHolidayData(List<CompanyLeaveConfigDO> companyLeaveConfigList,
                                  List<DriverAttendanceHolidayDTO> defaultHolidayTypeList,
                                  Map<String, Map<String, List<DriverPunchRecordDetailDO>>> userPunchRecordDetailMap,
                                  Map<String, List<DriverAttendanceHolidayDTO>> targetMap) {

        // 返回默认假期数据
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            DriverAttendanceHolidayDTO driverAttendanceHoliday = new DriverAttendanceHolidayDTO();
            driverAttendanceHoliday.setTitle(companyLeaveConfig.getLeaveType());
            driverAttendanceHoliday.setValue(BigDecimal.ZERO.toString());
            defaultHolidayTypeList.add(driverAttendanceHoliday);
        }
        // 遍历查询出来的所有司机 打卡记录详情，封装返回数据
        for (Map.Entry<String, Map<String, List<DriverPunchRecordDetailDO>>> stringMapEntry : userPunchRecordDetailMap.entrySet()) {
            String userCode = stringMapEntry.getKey();
            List<DriverAttendanceHolidayDTO> holidayTypeList = Lists.newArrayList();
            Map<String, List<DriverPunchRecordDetailDO>> leaveType = stringMapEntry.getValue();
            for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
                DriverAttendanceHolidayDTO driverAttendanceHoliday = new DriverAttendanceHolidayDTO();
                driverAttendanceHoliday.setTitle(companyLeaveConfig.getLeaveType());
                // 获取指定人员下面的指定假期类型的请假详情
                List<DriverPunchRecordDetailDO> leaveTypeList = leaveType.get(companyLeaveConfig.getLeaveType());
                // 获取该用户+指定时间范围内+指定假期类型 的所有请假时长总和
                BigDecimal sumLeaveMinutes = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(leaveTypeList)) {
                    for (DriverPunchRecordDetailDO detail : leaveTypeList) {
                        sumLeaveMinutes = sumLeaveMinutes.add(detail.getLeaveMinutes());
                    }
                }
                // 将sumLeaveMinutes转换为天
                // 一天的分钟数
                BigDecimal dayMinutes = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES);
                // 天数
                BigDecimal day = sumLeaveMinutes.divide(dayMinutes, 2, RoundingMode.HALF_UP);
                driverAttendanceHoliday.setValue(day.toString());
                holidayTypeList.add(driverAttendanceHoliday);
            }
            targetMap.put(userCode, holidayTypeList);
        }

    }


    /**
     * 转换导出参数并处理参数转换逻辑
     *
     * @param exportParam 导出参数
     * @return 转换后的参数
     */
    public DriverAttendanceDetailMonthParam convertExportParam(DriverAttendanceDetailMonthExportParam exportParam) {
        return DriverPunchMapstruct.INSTANCE.convertExportParam(exportParam);
    }

    /**
     * 查询月度司机考勤数据用于导出
     * 生成适合Excel导出的月度考勤数据，包含每日考勤状态、假期统计等详细信息
     * 数据格式为Map结构，便于导出工具处理
     *
     * @param param 查询参数，包含时间范围、筛选条件等
     * @return 分页的导出数据，每条记录为Map格式
     */
    public PaginationResult<Map<String, String>> queryExportMonthDriverAttendance(DriverAttendanceDetailMonthParam param) {
        List<Map<String, String>> resultList = new ArrayList<>();
        handlerMonthParam(param);
        DriverAttendanceDetailMonthQuery query = DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailMonthQuery(param);
        if (handlerMonthQuery(query)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 1. 查询该用户权限下面的 所有司机人员
        Page<DriverAttendanceDetailMonthDTO> page = PageHelper.startPage(
                query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<DriverAttendanceDetailMonthDTO> pageInfo = page.doSelectPageInfo(
                () -> driverAttendanceDetailDao.queryDriverMonthAttendance(query));
        List<DriverAttendanceDetailMonthDTO> userInfoList = pageInfo.getList();
        if (CollectionUtils.isEmpty(userInfoList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<String> userCodeList = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getUserCode)
                .collect(Collectors.toList());

        List<Long> deptIds = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getDeptId)
                .collect(Collectors.toList());
        List<String> ocCodes = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getOcCode)
                .collect(Collectors.toList());
        List<Long> postIds = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getPostId)
                .collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIds)
                .stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));

        Map<Long, AttendancePost> postMap = postService.listByPostList(postIds).
                stream().collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

        // 通过userCodeList，查询出所有司机的考勤数据
        DriverAttendanceDetailInfoQuery driverAttendanceDetailInfoQuery = DriverAttendanceDetailInfoQuery.builder()
                .userCodeList(userCodeList)
                .monthStartDate(query.getMonthStartDayId())
                .monthEndDate(query.getMonthEndDayId())
                .attendanceTypeList(query.getAttendanceTypeList())
                .build();

        List<DriverAttendanceDetailDO> driverAttendanceDetailList =
                driverAttendanceDetailDao.queryDriverAttendanceByCondition(driverAttendanceDetailInfoQuery);

        // 将司机考勤数据按照userCode分组
        Map<String, List<DriverAttendanceDetailDO>> userCodeToDriverAttendance = driverAttendanceDetailList.stream()
                .collect(Collectors.groupingBy(DriverAttendanceDetailDO::getUserCode));

        // 查询该时间范围内所有请假详情
        DriverPunchRecordDetailInfoQuery driverPunchRecordDetailInfoQuery = DriverPunchRecordDetailInfoQuery.builder().
                userCodeList(userCodeList).
                startDayId(query.getMonthStartDayId()).
                endDayId(query.getMonthEndDayId()).
                operationType((DriverAttendanceOperationTypeEnum.LEAVE.getType())).build();

        List<DriverPunchRecordDetailDO> driverPunchRecordDetail =
                driverPunchRecordManage.queryDriverPunchRecordDetailByCondition(driverPunchRecordDetailInfoQuery);
        // 将driverPunchRecordDetail先按照userCode分组在按照leaveType分组
        Map<String, Map<String, List<DriverPunchRecordDetailDO>>> userCodeToLeaveTypeToDriverPunchRecordDetail =
                driverPunchRecordDetail.stream()
                        .collect(Collectors.groupingBy(DriverPunchRecordDetailDO::getUserCode,
                                Collectors.groupingBy(DriverPunchRecordDetailDO::getLeaveType)));

        // 获取该国家下面所有假期
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        companyLeaveQuery.setCountry(query.getCountry());
        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());

        // 该国家下面所有假期
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.selectLeaveConfig(companyLeaveQuery);

        // 如果没有查到司机打卡记录详情：driverPunchRecordDetail数据，这个时候就没有user_code对应的假期使用情况，所以需要初始化每一个司机假期使用都为0
        List<DriverAttendanceHolidayDTO> defaultHolidayTypeList = Lists.newArrayList();
        Map<String, List<DriverAttendanceHolidayDTO>> targetMap = Maps.newHashMap();

        buildHolidayData(companyLeaveConfigList, defaultHolidayTypeList, userCodeToLeaveTypeToDriverPunchRecordDetail, targetMap);
        // 获取用工类型
        Map<String, DictVO> employeeTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE);
        Map<String, DictVO> ocTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.OC_TYPE);

        // 获取当前时间
        long nowDayId = DateHelper.getDayId(DateUtil.date());
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(query.getCountry());
        List<EntOcApiDTO> ocList = entOcService.getOcByCodes(BusinessConstant.DEFAULT_ORG_ID, ocCodes);
        // 将ocList按照ocCode转换为map
        Map<String, EntOcApiDTO> ocMap = ocList.stream()
                .collect(Collectors.toMap(EntOcApiDTO::getOcCode, Function.identity()));
        for (DriverAttendanceDetailMonthDTO detail : userInfoList) {
            Map<String, String> map = new LinkedHashMap<>();
            resultList.add(map);
            map.put("attendanceRange", query.getMonthStartDate() + "-" + query.getMonthEndDate());
            map.put("employeeId", ObjectUtil.isNotEmpty(detail.getUserCode()) ? detail.getUserCode() : "");
            map.put("nameCn", ObjectUtil.isNotEmpty(detail.getUserName()) ? detail.getUserName() : "");
            map.put("nameEn", ObjectUtil.isNotEmpty(detail.getUserNameEn()) ? detail.getUserNameEn() : "");
            WorkStatusEnum workStatusEnum = WorkStatusEnum.getInstance(detail.getWorkStatus());
            if (ObjectUtil.isNotNull(workStatusEnum)) {
                map.put("workStatus", RequestInfoHolder.isChinese() ? workStatusEnum.getDesc() : workStatusEnum.getDescEn());
            } else {
                map.put("workStatus", "");
            }
            StatusEnum statusEnum = StatusEnum.getStatusEnum(detail.getStatus());
            if (ObjectUtil.isNotNull(statusEnum)) {
                map.put("accountStatus", RequestInfoHolder.isChinese() ? statusEnum.getValue() : statusEnum.getCode());
            } else {
                map.put("accountStatus", "");
            }
            // 应出勤天数
            long attendanceDays = DateUtil.betweenDay(query.getMonthStartDate(), query.getMonthEndDate(), false) + 1;
            detail.setAttendanceDays(attendanceDays);
            map.put("attendanceDays", ObjectUtil.isNotNull(detail.getAttendanceDays()) ? String.valueOf(detail.getAttendanceDays()) : "0");
            map.put("actualAttendanceDays", "0");
            map.put("actualAbsentDays", "0");
            map.put("attendanceRate", "0%");
            // 获取指定人员在指定范围内的司机考勤数据
            List<DriverAttendanceDetailDO> driverAttendanceList = userCodeToDriverAttendance.get(detail.getUserCode());
            List<DriverAttendanceDetailDO> sortDriverAttendance = Lists.newArrayList();
            Map<Long, DriverAttendanceDetailDO> dayIdToDriverAttendance = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(driverAttendanceList)) {
                // 将该driverAttendanceList按照lastOperatingTime倒序,为了该用户该考勤周期内最后一次更新时间,并过滤掉lastOperatingTime为null的数据
                sortDriverAttendance = driverAttendanceList.stream()
                        .filter(driverAttendanceDetail -> ObjectUtil.isNotNull(driverAttendanceDetail.getLastOperatingTime()))
                        .sorted(Comparator.comparing(DriverAttendanceDetailDO::getLastOperatingTime).reversed())
                        .collect(Collectors.toList());
                // 将driverAttendanceList按照dayId转换为map
                dayIdToDriverAttendance = driverAttendanceList.stream()
                        .collect(Collectors.toMap(DriverAttendanceDetailDO::getDayId, Function.identity()));
                buildAttendanceData(detail, driverAttendanceList, attendanceDays);
                map.put("actualAttendanceDays", ObjectUtil.isNotNull(detail.getActualAttendanceDays()) ? String.valueOf(detail.getActualAttendanceDays()) : "0");
                map.put("actualAbsentDays", ObjectUtil.isNotNull(detail.getActualAbsentDays()) ? String.valueOf(detail.getActualAbsentDays()) : "0");
                map.put("attendanceRate", ObjectUtil.isNotEmpty(detail.getAttendanceRate()) ? detail.getAttendanceRate() : "0%");
            }

            // 设置假期类型
            List<DriverAttendanceHolidayDTO> holidayList = targetMap.get(detail.getUserCode());
            if (CollectionUtils.isEmpty(holidayList)) {
                defaultHolidayTypeList.forEach(driverAttendanceHoliday -> {
                    map.put(driverAttendanceHoliday.getTitle(), driverAttendanceHoliday.getValue());
                });
            } else {
                holidayList.forEach(driverAttendanceHoliday -> {
                    map.put(driverAttendanceHoliday.getTitle(), driverAttendanceHoliday.getValue());
                });
            }
            // 设置所选的每一天
            Long startDayId = query.getMonthStartDayId();
            Long endDayId = query.getMonthEndDayId();
            while (startDayId <= endDayId) {
                String mapKey = "day" + startDayId.toString();
                String mapValue = "";
                map.put(mapKey, mapValue);
                // 重新赋值，保存当前时间
                Long finalTempDayId = startDayId;
                startDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(startDayId.toString()), 1), "yyyyMMdd"));
                // 拦截当前计薪周期内的未来的时间，不生成记录
                if (finalTempDayId >= nowDayId) {
                    continue;
                }

                DriverAttendanceDetailDO driverAttendanceDetail = dayIdToDriverAttendance.get(finalTempDayId);
                if (ObjectUtil.isNotNull(driverAttendanceDetail)) {
                    DriverAttendanceTypeEnum byType = DriverAttendanceTypeEnum.getByType(driverAttendanceDetail.getAttendanceType());
                    mapValue = ObjectUtil.isNotNull(byType) ? byType.getDescEn() : "";
                    map.put(mapKey, mapValue);
                }
            }
            // 设置部门和岗位
            buildDeptAndPost(detail, deptMap, postMap, ocMap);

            map.put("country", ObjectUtil.isNotEmpty(detail.getLocationCountry()) ? detail.getLocationCountry() : "");
            map.put("vendorName", ObjectUtil.isNotEmpty(detail.getVendorName()) ? detail.getVendorName() : "");
            map.put("deptName", ObjectUtil.isNotEmpty(detail.getDeptName()) ? detail.getDeptName() : "");
            String ocTypeStr = detail.getOcType();
            if (ObjectUtil.isNotEmpty(ocTypeStr)) {
                String[] ocTypeString = ocTypeStr.split(",");
                if (ObjectUtil.isNotEmpty(ocTypeString)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < ocTypeString.length; i++) {
                        DictVO ocType = ocTypeMap.get(ocTypeString[i]);
                        stringBuilder.append(ocType == null ? detail.getOcType() : ocType.getDataValue());
                        if (i != ocTypeString.length - 1) {
                            stringBuilder.append("|");
                        }
                    }
                    map.put("ocTypeNames", stringBuilder.toString());
                }
            }
            map.put("postName", ObjectUtil.isNotEmpty(detail.getPostName()) ? detail.getPostName() : "");
//            map.put("leaderName", ObjectUtil.isNotEmpty(detail.getLeaderName()) ? detail.getLeaderName() : "");
            DictVO dictVO = employeeTypeMap.get(detail.getEmployeeType());
            map.put("employeeType", dictVO == null ? detail.getEmployeeType() : dictVO.getDataValue());
            if (ObjectUtil.isNotNull(detail.getEntryDate())) {
                Date entryDate = detail.getEntryDate();
                String format = DateUtil.format(entryDate, DatePattern.NORM_DATETIME_PATTERN);
                map.put("entryDate", ObjectUtil.isNotNull(format) ? format : "");
            } else {
                map.put("entryDate", "");
            }
            if (ObjectUtil.isNotNull(detail.getActualDimissionDate())) {
                Date actualDimissionDate = detail.getActualDimissionDate();
                String format = DateUtil.format(actualDimissionDate, DatePattern.NORM_DATETIME_PATTERN);
                map.put("dimissionDate", ObjectUtil.isNotNull(format) ? format : "");
            } else {
                map.put("dimissionDate", "");
            }
            if (CollectionUtils.isNotEmpty(sortDriverAttendance)) {
                Date lastOperatingTime = sortDriverAttendance.get(0).getLastOperatingTime();
                Date localLastOperatingTime = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), lastOperatingTime);
                String format = DateUtil.format(localLastOperatingTime, DatePattern.NORM_DATETIME_PATTERN);
                map.put("lastUpdated", ObjectUtil.isNotEmpty(format) ? format : "");
            } else {
                map.put("lastUpdated", "");
            }
        }
        saveOperateRecord(query.getCountry(), "月报", "monthly");

        return PageUtil.getPageResult(resultList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 生成月度考勤导出的表头信息
     * 根据查询条件动态生成Excel导出的列标题，包括基础信息列、假期类型列、每日考勤列等
     * 支持中英文表头切换
     *
     * @param param 查询参数，用于确定时间范围和国家信息
     * @return 表头信息列表，包含列名和显示名称
     */
    public List<DriverAttendanceMonthTitleExportDTO> driverAttendanceMonthTitleExport(DriverAttendanceDetailMonthParam param) {
        long monthStartDayId = DateHelper.getDayId(new Date());
        long monthEndDayId = DateHelper.getDayId(new Date());
        if (ObjectUtil.isNotNull(param.getMonthStartDate())) {
            monthStartDayId = DateHelper.getDayId(param.getMonthStartDate());
        }
        if (ObjectUtil.isNotNull(param.getMonthEndDate())) {
            monthEndDayId = DateHelper.getDayId(param.getMonthEndDate());
        }
        param.setMonthStartDayId(monthStartDayId);
        param.setMonthEndDayId(monthEndDayId);
        List<DriverAttendanceMonthTitleExportDTO> resultList = new ArrayList<>();
        buildTitle(resultList, "attendanceRange", RequestInfoHolder.isChinese() ? "考勤范围" : "Attendance range");
        buildTitle(resultList, "employeeId", "HRMS ID");
        buildTitle(resultList, "nameCn", RequestInfoHolder.isChinese() ? "中文姓名" : "Chinese Name");
        buildTitle(resultList, "nameEn", RequestInfoHolder.isChinese() ? "英文姓名" : "English Name");
        buildTitle(resultList, "postName", RequestInfoHolder.isChinese() ? "岗位" : "Post Name");
        buildTitle(resultList, "entryDate", RequestInfoHolder.isChinese() ? "入职日期" : "Entry Date");
        buildTitle(resultList, "ocTypeNames", RequestInfoHolder.isChinese() ? "运营类型" : "Operation Type");
        buildTitle(resultList, "deptName", RequestInfoHolder.isChinese() ? "部门" : "Dept Name");
        buildTitle(resultList, "workStatus", RequestInfoHolder.isChinese() ? "工作状态" : "Work Status");
        buildTitle(resultList, "accountStatus", RequestInfoHolder.isChinese() ? "账号状态" : "Account Status");
        buildTitle(resultList, "attendanceDays", RequestInfoHolder.isChinese() ? "应出勤天数" : "Attendance Days");
        buildTitle(resultList, "actualAttendanceDays", RequestInfoHolder.isChinese() ? "实际出勤天数" : "Actual Attendance Days");
        buildTitle(resultList, "actualAbsentDays", RequestInfoHolder.isChinese() ? "实际缺勤天数" : "Actual Absent Days");
        buildTitle(resultList, "attendanceRate", RequestInfoHolder.isChinese() ? "出勤率" : "Attendance rate");
        // 获取该国家下面所有假期
        CompanyLeaveQuery companyLeaveQuery = new CompanyLeaveQuery();
        companyLeaveQuery.setCountry(param.getCountry());
        companyLeaveQuery.setStatus(StatusEnum.ACTIVE.getCode());

        // 该国家下面所有假期
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigManage.selectLeaveConfig(companyLeaveQuery);
        // 查询国家下面所有的假期
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            buildTitle(resultList, companyLeaveConfig.getLeaveType(), companyLeaveConfig.getLeaveType());
        }
        Long startDayId = param.getMonthStartDayId();
        Long endDayId = param.getMonthEndDayId();
        while (startDayId <= endDayId) {
            buildTitle(resultList, "day" + startDayId.toString(), startDayId.toString());
            startDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(startDayId.toString()), 1), "yyyyMMdd"));
        }
        buildTitle(resultList, "country", RequestInfoHolder.isChinese() ? "常驻国" : "Country");
        buildTitle(resultList, "vendorName", RequestInfoHolder.isChinese() ? "供应商" : "Vendor Name");
//        buildTitle(resultList, "leaderName", RequestInfoHolder.isChinese() ? "汇报上级" : "Leader Name");
        buildTitle(resultList, "employeeType", RequestInfoHolder.isChinese() ? "用工类型" : "Employee Type");
        buildTitle(resultList, "dimissionDate", RequestInfoHolder.isChinese() ? "离职日期" : "Dimission Date");
        buildTitle(resultList, "lastUpdated", RequestInfoHolder.isChinese() ? "最近更新时间" : "Last Updated");
        return resultList;
    }



    /**
     * 查询司机考勤日报数据用于导出
     * 生成适合Excel导出的日报考勤数据，与查询接口类似但增加了导出操作记录
     * 主要用于管理员导出指定日期的司机考勤情况
     *
     * @param param 查询参数，包含日期、筛选条件等
     * @return 分页的司机考勤明细列表，用于导出
     */
    public PaginationResult<DriverAttendanceDetailVO> queryExportDriverAttendance(DriverAttendanceDetailParam param) {
        handlerParam(param);
        DriverAttendanceDetailQuery query = DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailQuery(param);
        if (handlerQuery(query)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        Page<DriverAttendanceDetailDTO> page = PageHelper.startPage(
                query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<DriverAttendanceDetailDTO> pageInfo = page.doSelectPageInfo(
                () -> driverAttendanceDetailDao.queryDriverAttendance(query));
        List<DriverAttendanceDetailDTO> driverAttendanceDetailList = pageInfo.getList();
        if (CollectionUtils.isEmpty(driverAttendanceDetailList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<Long> deptIds = driverAttendanceDetailList.stream()
                .map(DriverAttendanceDetailDTO::getDeptId).collect(Collectors.toList());
        List<Long> postIds = driverAttendanceDetailList.stream()
                .map(DriverAttendanceDetailDTO::getPostId).collect(Collectors.toList());
        List<String> ocCodes = driverAttendanceDetailList.stream()
                .map(DriverAttendanceDetailDTO::getOcCode).collect(Collectors.toList());

        Map<Long, AttendanceDept> deptMap = deptService.listByDeptIds(deptIds).stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));

        Map<Long, AttendancePost> postMap = postService.listByPostList(postIds)
                .stream().collect(Collectors.toMap(AttendancePost::getId, Function.identity()));

        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(query.getCountry());
        List<EntOcApiDTO> ocList = entOcService.getOcByCodes(BusinessConstant.DEFAULT_ORG_ID, ocCodes);
        // 将ocList按照ocCode转为map
        Map<String, EntOcApiDTO> ocMap = ocList.stream().collect(Collectors.toMap(EntOcApiDTO::getOcCode, Function.identity()));

        driverAttendanceDetailList.forEach(driverAttendanceDetail -> {
            // 处理数据
            AttendanceDept attendanceDept = deptMap.get(driverAttendanceDetail.getDeptId());
            if (ObjectUtil.isNotNull(attendanceDept)) {
                driverAttendanceDetail.setDeptName(
                        RequestInfoHolder.isChinese() ? attendanceDept.getDeptNameCn() : attendanceDept.getDeptNameEn());
                if (DeptOrgTypeEnum.STATION.getValue().equals(attendanceDept.getDeptType())) {
                    // 设置运营类型
                    EntOcApiDTO oc = ocMap.get(driverAttendanceDetail.getOcCode());
                    driverAttendanceDetail.setOcType(Optional.ofNullable(oc).map(EntOcApiDTO::getOcType).orElse(""));
                }
            }
            AttendancePost attendancePost = postMap.get(driverAttendanceDetail.getPostId());
            if (ObjectUtil.isNotNull(attendancePost)) {
                driverAttendanceDetail.setPostName(attendancePost.getLocalizeName());
            }
            if (ObjectUtil.isNotNull(driverAttendanceDetail.getLastOperatingTime())) {
                Date localLastOperatingTime = DateHelper.convertDateByTimeZonePlus(countryDTO.getTimeZone(), driverAttendanceDetail.getLastOperatingTime());
                driverAttendanceDetail.setLocalLastOperatingTime(localLastOperatingTime);
            }
            // 设置考勤范围
            driverAttendanceDetail.setDayId(query.getDayId());
        });
        saveOperateRecord(query.getCountry(), "日报", "daily");
        List<DriverAttendanceDetailVO> driverAttendanceDetailVOList =
                DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailVO(driverAttendanceDetailList);
        return PageUtil.getPageResult(driverAttendanceDetailVOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    /**
     * 查询司机考勤数据供薪酬系统使用
     * 专门为薪酬计算提供司机考勤数据，数据格式和字段与常规导出有所不同
     * 不包含时区转换，直接使用UTC时间，符合薪酬系统的数据要求
     *
     * @param resultList 结果列表，用于接收查询结果
     * @param param 查询参数，包含时间范围和用户列表
     * @return 包含司机考勤数据的Map列表，供薪酬系统使用
     */
    public List<Map<String, String>> queryDriverAttendanceForSalary(List<Map<String, String>> resultList,
                                                                    DriverAttendanceDetailMonthParam param) {
        // 校验
        if (ObjectUtil.isNull(param)) {
            log.info("queryDriverAttendanceForSalary param is null");
            return resultList;
        }
        if (ObjectUtil.isNull(param.getMonthStartDate()) || ObjectUtil.isNull(param.getMonthEndDate())) {
            log.info("queryDriverAttendanceForSalary param monthStartDate or monthEndDate is null");
            return resultList;
        }
        if (CollectionUtils.isEmpty(param.getUserCodeList())) {
            log.info("queryDriverAttendanceForSalary param userCodeList is empty");
            return resultList;
        }
        // 参数转换
        Long monthStartDayId = DateHelper.getDayId(param.getMonthStartDate());
        String monthStartDateString = DateHelper.formatYYYYMMDDHHMMSS(param.getMonthStartDate());
        Long monthEndDayId = DateHelper.getDayId(param.getMonthEndDate());
        String monthEndDateString = DateHelper.formatYYYYMMDDHHMMSS(param.getMonthEndDate());
        param.setMonthStartDayId(monthStartDayId);
        param.setMonthEndDayId(monthEndDayId);
        param.setMonthStartDateString(monthStartDateString);
        param.setMonthEndDateString(monthEndDateString);
        DriverAttendanceDetailMonthQuery query = DriverPunchMapstruct.INSTANCE.toDriverAttendanceDetailMonthQuery(param);
        query.setIsDriver(BusinessConstant.Y);
        // 获取月度考勤数据
        List<DriverAttendanceDetailMonthDTO> userInfoList = driverAttendanceDetailDao.queryDriverMonthAttendance(query);
        if (CollectionUtils.isEmpty(userInfoList)) {
            log.info("queryDriverAttendanceForSalary userInfoList is empty");
            return resultList;
        }
        // 处理userInfoList
        // 应该要自定义写，不能直接使用原来的月报，需要重新写
        handlerDriverInfoData(userInfoList, query, resultList);
        return resultList;
    }


    /**
     * 处理入参以及获取权限
     *
     * @param param 入参
     */
    private void handlerParam(DriverAttendanceDetailParam param) {
        // 空值检查，避免NPE
        if (param == null) {
            return;
        }

        // 处理用户代码或名称，使用Optional简化空值检查
        Optional.ofNullable(param.getUserCodeOrName())
                .ifPresent(name -> param.setUserCodeOrName(name.trim()));

        // 处理日期参数
        Date dateToProcess = Optional.ofNullable(param.getDayDate()).orElse(new Date());
        long dayId = DateHelper.getDayId(dateToProcess);
        String dayDateString = DateHelper.formatYYYYMMDDHHMMSS(dateToProcess);

        // 设置处理后的日期参数
        param.setDayId(dayId);
        param.setDayDateString(dayDateString);

        // 处理数据权限
        systemResourceManage.setResource(
                param,
                DriverAttendanceDetailParam::setResourceType,
                DriverAttendanceDetailParam::setOrganizationIds
        );
    }

    /**
     * 处理日报查询条件和权限控制
     * 验证用户的部门权限和国家权限，确保只能查询授权范围内的数据
     *
     * @param query 查询条件对象
     * @return true表示无权限或无数据，false表示有权限可继续查询
     */
    private boolean handlerQuery(DriverAttendanceDetailQuery query) {
        // 1. 权限控制：检查部门权限
        PermissionDeptVO permissionDept = userResourceService.getPermissionDept(query.getDeptIdList());
        if (!permissionDept.getHasDeptPermission()) {
            return true;
        }
        query.setDeptList(permissionDept.getDeptIdList());

        // 2. 国家权限检查：获取授权的业务国家列表
        List<String> authorizedBizCountryList = userResourceService.getAuthorizedBizCountryList(
                StringUtils.isBlank(query.getCountry()) ?
                        new ArrayList<>() :
                        Collections.singletonList(query.getCountry()));
        if (CollectionUtils.isEmpty(authorizedBizCountryList)) {
            return true;
        }
        query.setCountryList(authorizedBizCountryList);

        // 3. 设置司机标识
        query.setIsDriver(BusinessConstant.Y);
        return false;
    }

    /**
     * 处理用户信息的国际化显示
     * 根据当前用户的语言偏好设置部门名称和岗位名称的显示语言
     *
     * @param userInformation 用户信息对象
     */
    private void handlerUserInformation(UserInformationDTO userInformation) {
        userInformation.setPostName(RequestInfoHolder.isChinese() ? userInformation.getPostName() : userInformation.getPostNameEn());
        userInformation.setDeptName(RequestInfoHolder.isChinese() ? userInformation.getDeptName() : userInformation.getDeptNameEn());
    }

    /**
     * 处理入参以及获取权限
     *
     * @param param 如惨
     */
    private void handlerMonthParam(DriverAttendanceDetailMonthParam param) {
        if (ObjectUtil.isNotEmpty(param.getUserCodeOrName())) {
            param.setUserCodeOrName(param.getUserCodeOrName().trim());
        }
        long monthStartDayId = DateHelper.getDayId(new Date());
        long monthEndDayId = DateHelper.getDayId(new Date());
        String monthStartDateString = DateHelper.formatYYYYMMDDHHMMSS(new Date());
        String monthEndDateString = DateHelper.formatYYYYMMDDHHMMSS(new Date());
        if (ObjectUtil.isNotNull(param.getMonthStartDate())) {
            monthStartDayId = DateHelper.getDayId(param.getMonthStartDate());
            monthStartDateString = DateHelper.formatYYYYMMDDHHMMSS(param.getMonthStartDate());
        }
        if (ObjectUtil.isNotNull(param.getMonthEndDate())) {
            monthEndDayId = DateHelper.getDayId(param.getMonthEndDate());
            monthEndDateString = DateHelper.formatYYYYMMDDHHMMSS(param.getMonthEndDate());
        }
        param.setMonthStartDayId(monthStartDayId);
        param.setMonthEndDayId(monthEndDayId);
        param.setMonthStartDateString(monthStartDateString);
        param.setMonthEndDateString(monthEndDateString);
        //处理数据权限
        systemResourceManage.setResource(
                param,
                DriverAttendanceDetailMonthParam::setResourceType,
                DriverAttendanceDetailMonthParam::setOrganizationIds);
    }

    /**
     * 处理月报查询条件和权限控制
     * 验证用户的部门权限和国家权限，确保只能查询授权范围内的月度数据
     *
     * @param query 月报查询条件对象
     * @return true表示无权限或无数据，false表示有权限可继续查询
     */
    private boolean handlerMonthQuery(DriverAttendanceDetailMonthQuery query) {
        //权限控制
        PermissionDeptVO permissionDept = userResourceService.getPermissionDept(query.getDeptIdList());
        if (!permissionDept.getHasDeptPermission()) {
            return true;
        }
        query.setDeptList(permissionDept.getDeptIdList());
        List<String> authorizedBizCountryList = userResourceService.getAuthorizedBizCountryList(
                StringUtils.isBlank(query.getCountry()) ? new ArrayList<>() : Collections.singletonList(query.getCountry()));
        if (CollectionUtils.isEmpty(authorizedBizCountryList)) {
            return true;
        }
        query.setCountryList(authorizedBizCountryList);
        // 查询是司机的用户
        query.setIsDriver(BusinessConstant.Y);
        return false;
    }

    /**
     * 创建表头
     *
     * @param resultList 表头接收list
     * @param title      title
     * @param name       name
     */
    private void buildTitle(List<DriverAttendanceMonthTitleExportDTO> resultList, String title, String name) {
        DriverAttendanceMonthTitleExportDTO exportDTO = new DriverAttendanceMonthTitleExportDTO();
        exportDTO.setTitle(title);
        exportDTO.setName(name);
        resultList.add(exportDTO);
    }

    /**
     * 保存司机考勤操作记录
     *
     * @param country            国家
     * @param operationContent   操作内容
     * @param operationContentEn 操作内容英文
     */
    private void saveOperateRecord(String country, String operationContent, String operationContentEn) {
        // 新增司机考勤操作记录
        DriverAttendanceOperateRecordDO driverAttendanceOperateRecord = buildDriverOperateRecord(country, operationContent, operationContentEn);
        // 保存司机考勤操作记录
        driverAttendanceManage.saveOperateRecord(driverAttendanceOperateRecord);
    }

    /**
     * 构建司机操作记录
     *
     * @param country            国家
     * @param operationContent   操作内容
     * @param operationContentEn 操作内容
     * @return HrmsDriverAttendanceOperateRecordDO
     */
    private DriverAttendanceOperateRecordDO buildDriverOperateRecord(String country, String operationContent, String operationContentEn) {
        DriverAttendanceOperateRecordDO driverAttendanceOperateRecord = new DriverAttendanceOperateRecordDO();
        driverAttendanceOperateRecord.setId(defaultIdWorker.nextId());
        driverAttendanceOperateRecord.setCountry(country);
        driverAttendanceOperateRecord.setOperationType(DriverAttendanceOperateRecordEnum.EXPORT.getType());
        driverAttendanceOperateRecord.setOperationContent(operationContent);
        driverAttendanceOperateRecord.setOperationContentEn(operationContentEn);
        BaseDOUtil.fillDOInsert(driverAttendanceOperateRecord);
        return driverAttendanceOperateRecord;
    }

    /**
     * 处理司机信息
     *
     * @param userInfoList 用户信息
     * @param query        查询条件
     * @param resultList   结果集合
     */
    private void handlerDriverInfoData(List<DriverAttendanceDetailMonthDTO> userInfoList,
                                       DriverAttendanceDetailMonthQuery query,
                                       List<Map<String, String>> resultList) {
        List<String> userCodeList = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getUserCode)
                .collect(Collectors.toList());

        List<Long> deptIds = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getDeptId)
                .collect(Collectors.toList());
        List<String> ocCodes = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getOcCode)
                .collect(Collectors.toList());
        List<Long> postIds = userInfoList.stream()
                .map(DriverAttendanceDetailMonthDTO::getPostId)
                .collect(Collectors.toList());
        Map<Long, AttendanceDept> entDeptMap = deptService.listByDeptIds(deptIds).stream()
                .collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));

        Map<Long, AttendancePost> postMap = postService.listByPostList(postIds).stream()
                .collect(Collectors.toMap(AttendancePost::getId, Function.identity()));


        // 通过userCodeList，查询出所有司机的考勤数据
        DriverAttendanceDetailInfoQuery driverAttendanceDetailInfoQuery = DriverAttendanceDetailInfoQuery.builder()
                .userCodeList(userCodeList)
                .monthStartDate(query.getMonthStartDayId())
                .monthEndDate(query.getMonthEndDayId())
                .attendanceTypeList(query.getAttendanceTypeList())
                .build();

        List<DriverAttendanceDetailDO> driverAttendanceDetailList =
                driverAttendanceDetailDao.queryDriverAttendanceByCondition(driverAttendanceDetailInfoQuery);
        // 将司机考勤数据按照userCode分组
        Map<String, List<DriverAttendanceDetailDO>> userCodeToDriverAttendance =
                driverAttendanceDetailList.stream().collect(Collectors.groupingBy(DriverAttendanceDetailDO::getUserCode));

        // 查询该时间范围内所有请假详情
        DriverPunchRecordDetailInfoQuery driverPunchRecordDetailInfoQuery = DriverPunchRecordDetailInfoQuery.builder().
                userCodeList(userCodeList).
                startDayId(query.getMonthStartDayId()).
                endDayId(query.getMonthEndDayId()).
                operationType((DriverAttendanceOperationTypeEnum.LEAVE.getType())).build();

        List<DriverPunchRecordDetailDO> driverPunchRecordDetail =
                driverPunchRecordManage.queryDriverPunchRecordDetailByCondition(driverPunchRecordDetailInfoQuery);
        // 将driverPunchRecordDetail先按照userCode分组在按照leaveType分组
        Map<String, Map<String, List<DriverPunchRecordDetailDO>>> userCodeToLeaveTypeToDriverPunchRecordDetail =
                driverPunchRecordDetail.stream()
                        .collect(Collectors.groupingBy(DriverPunchRecordDetailDO::getUserCode,
                                Collectors.groupingBy(DriverPunchRecordDetailDO::getLeaveType)));

        Map<String, List<DriverAttendanceHolidayDTO>> targetMap = Maps.newHashMap();

        // 处理假期数据
        // 遍历查询出来的所有司机 打卡记录详情，封装返回数据，如果没有则不处理
        buildExistHolidayData(userCodeToLeaveTypeToDriverPunchRecordDetail, targetMap);


        // 获取用工类型
        Map<String, DictVO> employeeTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE);
        Map<String, DictVO> ocTypeMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.OC_TYPE);

        // 获取当前时间
        long nowDayId = DateHelper.getDayId(DateUtil.date());
        // 时间转换为当地时间：下面时区转换不需要了，给薪酬提供不需要
        //CountryDTO countryDTO = countryService.queryCountry(query.getCountry());
        List<EntOcApiDTO> ocList = entOcService.getOcByCodes(BusinessConstant.DEFAULT_ORG_ID, ocCodes);
        // 将ocList按照ocCode转换为map
        Map<String, EntOcApiDTO> ocMap = ocList.stream().collect(Collectors.toMap(EntOcApiDTO::getOcCode, Function.identity()));
        for (DriverAttendanceDetailMonthDTO detail : userInfoList) {
            Map<String, String> map = new LinkedHashMap<>();
            resultList.add(map);
            map.put("attendanceRange", query.getMonthStartDate() + "-" + query.getMonthEndDate());
            map.put("employeeId", ObjectUtil.isNotEmpty(detail.getUserCode()) ? detail.getUserCode() : "");
            map.put("nameCn", ObjectUtil.isNotEmpty(detail.getUserName()) ? detail.getUserName() : "");
            map.put("nameEn", ObjectUtil.isNotEmpty(detail.getUserNameEn()) ? detail.getUserNameEn() : "");
            WorkStatusEnum workStatusEnum = WorkStatusEnum.getInstance(detail.getWorkStatus());
            if (ObjectUtil.isNotNull(workStatusEnum)) {
                map.put("workStatus", RequestInfoHolder.isChinese() ? workStatusEnum.getDesc() : workStatusEnum.getDescEn());
            } else {
                map.put("workStatus", "");
            }
            StatusEnum statusEnum = StatusEnum.getStatusEnum(detail.getStatus());
            if (ObjectUtil.isNotNull(statusEnum)) {
                map.put("accountStatus", RequestInfoHolder.isChinese() ? statusEnum.getValue() : statusEnum.getCode());
            } else {
                map.put("accountStatus", "");
            }
            // 应出勤天数
            long attendanceDays = DateUtil.betweenDay(query.getMonthStartDate(), query.getMonthEndDate(), false) + 1;
            detail.setAttendanceDays(attendanceDays);
            map.put("attendanceDays", ObjectUtil.isNotNull(detail.getAttendanceDays()) ? String.valueOf(detail.getAttendanceDays()) : "0");
            map.put("present", "0");
            map.put("absentDays", "0");
            map.put("attendanceRate", "0%");
            // 获取指定人员在指定范围内的司机考勤数据
            List<DriverAttendanceDetailDO> driverAttendanceList = userCodeToDriverAttendance.get(detail.getUserCode());
            Map<Long, DriverAttendanceDetailDO> dayIdToDriverAttendance = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(driverAttendanceList)) {
                // 将driverAttendanceList按照dayId转换为map
                dayIdToDriverAttendance = driverAttendanceList.stream()
                        .collect(Collectors.toMap(DriverAttendanceDetailDO::getDayId, Function.identity()));
                buildAttendanceData(detail, driverAttendanceList, attendanceDays);
                map.put("present",
                        ObjectUtil.isNotNull(detail.getActualAttendanceDays()) ?
                                String.valueOf(detail.getActualAttendanceDays()) : "0");
                map.put("absentDays",
                        ObjectUtil.isNotNull(detail.getActualAbsentDays()) ?
                                String.valueOf(detail.getActualAbsentDays()) : "0");
                map.put("attendanceRate",
                        ObjectUtil.isNotEmpty(detail.getAttendanceRate())
                                ? detail.getAttendanceRate() : "0%");
            }

            // 设置假期类型
            List<DriverAttendanceHolidayDTO> holidayList = targetMap.get(detail.getUserCode());
            if (CollectionUtils.isNotEmpty(holidayList)) {
                holidayList.forEach(driverAttendanceHoliday -> {
                    map.put(driverAttendanceHoliday.getTitle(), driverAttendanceHoliday.getValue());
                });
            }
            // 设置所选的每一天
            Long startDayId = query.getMonthStartDayId();
            Long endDayId = query.getMonthEndDayId();
            while (startDayId <= endDayId) {
                String mapKey = "day" + startDayId.toString();
                String mapValue = "";
                map.put(mapKey, mapValue);
                // 重新赋值，保存当前时间
                Long finalTempDayId = startDayId;
                startDayId = Long.valueOf(DateUtil.format(DateUtil.offsetDay(DateUtil.parse(startDayId.toString()), 1), "yyyyMMdd"));
                // 拦截当前计薪周期内的未来的时间，不生成记录
                if (finalTempDayId >= nowDayId) {
                    continue;
                }

                DriverAttendanceDetailDO driverAttendanceDetail = dayIdToDriverAttendance.get(finalTempDayId);
                if (ObjectUtil.isNotNull(driverAttendanceDetail)) {
                    DriverAttendanceTypeEnum byType = DriverAttendanceTypeEnum.getByType(driverAttendanceDetail.getAttendanceType());
                    mapValue = ObjectUtil.isNotNull(byType) ? byType.getDescEn() : "";
                    map.put(mapKey, mapValue);
                }
            }
            // 设置部门和岗位
            buildDeptAndPost(detail, entDeptMap, postMap, ocMap);

            map.put("country", ObjectUtil.isNotEmpty(detail.getLocationCountry()) ? detail.getLocationCountry() : "");
            map.put("vendorName", ObjectUtil.isNotEmpty(detail.getVendorName()) ? detail.getVendorName() : "");
            map.put("deptName", ObjectUtil.isNotEmpty(detail.getDeptName()) ? detail.getDeptName() : "");
            String ocTypeStr = detail.getOcType();
            if (ObjectUtil.isNotEmpty(ocTypeStr)) {
                String[] ocTypeString = ocTypeStr.split(",");
                if (ObjectUtil.isNotEmpty(ocTypeString)) {
                    StringBuilder stringBuilder = new StringBuilder();
                    for (int i = 0; i < ocTypeString.length; i++) {
                        DictVO ocType = ocTypeMap.get(ocTypeString[i]);
                        stringBuilder.append(ocType == null ? detail.getOcType() : ocType.getDataValue());
                        if (i != ocTypeString.length - 1) {
                            stringBuilder.append("|");
                        }
                    }
                    map.put("ocTypeNames", stringBuilder.toString());
                }
            }
            map.put("postName", ObjectUtil.isNotEmpty(detail.getPostName()) ? detail.getPostName() : "");
//            map.put("leaderName", ObjectUtil.isNotEmpty(detail.getLeaderName()) ? detail.getLeaderName() : "");
            DictVO dictVO = employeeTypeMap.get(detail.getEmployeeType());
            map.put("employeeType", dictVO == null ? detail.getEmployeeType() : dictVO.getDataValue());
            if (ObjectUtil.isNotNull(detail.getEntryDate())) {
                Date entryDate = detail.getEntryDate();
                String format = DateUtil.format(entryDate, DatePattern.NORM_DATETIME_PATTERN);
                map.put("entryDate", ObjectUtil.isNotNull(format) ? format : "");
            } else {
                map.put("entryDate", "");
            }
            if (ObjectUtil.isNotNull(detail.getActualDimissionDate())) {
                Date actualDimissionDate = detail.getActualDimissionDate();
                String format = DateUtil.format(actualDimissionDate, DatePattern.NORM_DATETIME_PATTERN);
                map.put("dimissionDate", ObjectUtil.isNotNull(format) ? format : "");
            } else {
                map.put("dimissionDate", "");
            }
        }

    }

    /**
     * 构建考勤统计数据
     *
     * @param detail               参数
     * @param driverAttendanceList 司机考勤集合
     * @param attendanceDays       应出勤天数
     */
    private void buildAttendanceData(DriverAttendanceDetailMonthDTO detail,
                                     List<DriverAttendanceDetailDO> driverAttendanceList,
                                     long attendanceDays) {
        // 获取attendanceType = 'P'的数据数量：实际出勤天数
        long actualAttendanceCount = driverAttendanceList.stream()
                .filter(driverAttendanceDetail -> ObjectUtil.equal(driverAttendanceDetail.getAttendanceType(), DriverAttendanceTypeEnum.PRESENT.getType()))
                .count();
        detail.setActualAttendanceDays(actualAttendanceCount);
        // 获取attendanceType = 'A'的数据数量：实际缺勤天数
        long actualAbsentDaysCount = driverAttendanceList.stream()
                .filter(driverAttendanceDetail -> ObjectUtil.equal(driverAttendanceDetail.getAttendanceType(), DriverAttendanceTypeEnum.ABSENT.getType()))
                .count();
        detail.setActualAbsentDays(actualAbsentDaysCount);
        // 出勤率
        BigDecimal bigDecimal = NumberUtil.div(
                String.valueOf(actualAttendanceCount), String.valueOf(attendanceDays), 4, RoundingMode.HALF_UP);
        BigDecimal attendanceRate = bigDecimal.multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP);
        String attendanceRateStr = attendanceRate + "%";
        detail.setAttendanceRate(attendanceRateStr);
    }

    /**
     * 构建部门和岗位信息
     * 根据部门ID和岗位ID填充对应的名称信息，支持国际化显示
     * 对于站点类型的部门，还会设置运营类型信息
     *
     * @param detail 司机考勤详情对象
     * @param deptMap 部门信息映射
     * @param postMap 岗位信息映射
     * @param ocMap 运营中心信息映射
     */
    private void buildDeptAndPost(DriverAttendanceDetailMonthDTO detail,
                                  Map<Long, AttendanceDept> deptMap,
                                  Map<Long, AttendancePost> postMap,
                                  Map<String, EntOcApiDTO> ocMap) {
        // 处理数据
        AttendanceDept dept = deptMap.get(detail.getDeptId());
        if (ObjectUtil.isNotNull(dept)) {
            detail.setDeptName(RequestInfoHolder.isChinese() ? dept.getDeptNameCn() : dept.getDeptNameEn());
            if (DeptOrgTypeEnum.STATION.getValue().equals(dept.getDeptType())) {
                // 设置运营类型
                EntOcApiDTO oc = ocMap.get(detail.getOcCode());
                detail.setOcType(
                        Optional.ofNullable(oc).map(EntOcApiDTO::getOcType).orElse(""));
            }
        }
        AttendancePost attendancePost = postMap.get(detail.getPostId());
        if (ObjectUtil.isNotNull(attendancePost)) {
            detail.setPostName(attendancePost.getLocalizeName());
        }
    }

    /**
     * 构建已存在的假期数据
     *
     * @param userCodeToLeaveTypeToDriverPunchRecordDetail 原始数据
     * @param targetMap                                    目标数据
     */
    private void buildExistHolidayData(Map<String, Map<String, List<DriverPunchRecordDetailDO>>> userCodeToLeaveTypeToDriverPunchRecordDetail,
                                       Map<String, List<DriverAttendanceHolidayDTO>> targetMap) {
        for (Map.Entry<String, Map<String, List<DriverPunchRecordDetailDO>>> stringMapEntry : userCodeToLeaveTypeToDriverPunchRecordDetail.entrySet()) {
            String userCode = stringMapEntry.getKey();
            if (ObjectUtil.isEmpty(userCode)) {
                log.info("userCode:{} is empty", userCode);
                continue;
            }
            List<DriverAttendanceHolidayDTO> holidayTypeList = Lists.newArrayList();
            Map<String, List<DriverPunchRecordDetailDO>> leaveType = stringMapEntry.getValue();
            // 遍历leaveType
            for (Map.Entry<String, List<DriverPunchRecordDetailDO>> entry : leaveType.entrySet()) {
                // 假期类型
                String leaveTypeKey = entry.getKey();
                if (ObjectUtil.isEmpty(leaveTypeKey)) {
                    log.info("leaveTypeKey:{} is empty", leaveTypeKey);
                    continue;
                }
                DriverAttendanceHolidayDTO driverAttendanceHoliday = new DriverAttendanceHolidayDTO();
                driverAttendanceHoliday.setTitle(leaveTypeKey);
                // 假期类型对应数据
                List<DriverPunchRecordDetailDO> leaveTypeList = entry.getValue();
                // 获取该用户+指定时间范围内+指定假期类型 的所有请假时长总和
                BigDecimal sumLeaveMinutes = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(leaveTypeList)) {
                    for (DriverPunchRecordDetailDO detail : leaveTypeList) {
                        sumLeaveMinutes = sumLeaveMinutes.add(detail.getLeaveMinutes());
                    }
                }
                // 将sumLeaveMinutes转换为天
                // 一天的分钟数
                BigDecimal dayMinutes = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS.multiply(BusinessConstant.MINUTES);
                // 天数
                BigDecimal day = sumLeaveMinutes.divide(dayMinutes, 2, RoundingMode.HALF_UP);
                driverAttendanceHoliday.setValue(day.toString());
                holidayTypeList.add(driverAttendanceHoliday);

            }
            targetMap.put(userCode, holidayTypeList);
        }
    }


}
