package com.imile.attendance.driver.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} DriverYearAttendanceVO
 * {@code @since:} 2024-01-24 10:50
 * {@code @description:}
 */
@Data
public class DriverYearAttendanceVO implements Serializable {

    private static final long serialVersionUID = -4573224911147506526L;

    /**
     * 年
     */
    private Long year;

    /**
     * 司机月考勤
     */
    private List<DriverMonthAttendanceVO> driverMonthAttendanceList;
}
