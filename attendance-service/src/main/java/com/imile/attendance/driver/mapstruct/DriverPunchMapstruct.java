package com.imile.attendance.driver.mapstruct;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthExportParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailMonthParam;
import com.imile.attendance.driver.dto.DriverAttendanceDetailParam;
import com.imile.attendance.driver.dto.DriverAttendanceOperateRecordParam;
import com.imile.attendance.driver.dto.DriverPunchRecordParam;
import com.imile.attendance.driver.vo.DriverAttendanceDetailMonthVO;
import com.imile.attendance.driver.vo.DriverAttendanceDetailVO;
import com.imile.attendance.driver.vo.DriverAttendanceOperateRecordVO;
import com.imile.attendance.driver.vo.DriverPunchRecordVO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceDetailMonthDTO;
import com.imile.attendance.infrastructure.repository.driver.dto.DriverAttendanceInfoDetailDTO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailMonthQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceDetailQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverAttendanceOperateRecordQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.util.DateConvertUtils;
import com.imile.attendance.util.DateHelper;
import com.imile.common.page.PaginationResult;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import com.imile.attendance.infrastructure.repository.employee.dto.UserInformationDTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/7
 * @Description
 */
@Mapper(config = MapperConfiguration.class, imports = {RequestInfoHolder.class, DateConvertUtils.class})
public interface DriverPunchMapstruct {

    DriverPunchMapstruct INSTANCE = Mappers.getMapper(DriverPunchMapstruct.class);


    @Mapping(target = "operationType", ignore = true)
    @Mapping(target = "dayIdList", ignore = true)
    DriverPunchRecordDetailQuery toPunchRecordDetailQuery(DriverPunchRecordParam driverPunchRecordParam);


    default Date dealWithLocalCreateDate(String timeZone, Date punchRecordCreateDate) {
        return DateHelper.convertDateByTimeZonePlus(timeZone, punchRecordCreateDate);
    }

    @Mapping(target = "operationContentView", expression = "java(RequestInfoHolder.isChinese() ? driverPunchRecordDO.getOperationContent():driverPunchRecordDO.getOperationContentEn())")
    @Mapping(target = "localCreateDate", expression = "java(dealWithLocalCreateDate(timeZone, driverPunchRecordDO.getCreateDate()))")
    @Mapping(target = "operatingTime", expression = "java(DateConvertUtils.toLocalDateTime(driverPunchRecordDO.getOperatingTime()))")
    DriverPunchRecordVO toDriverPunchRecordVO(DriverPunchRecordDO driverPunchRecordDO, String timeZone);

    default List<DriverPunchRecordVO> toDriverPunchRecordVO(List<DriverPunchRecordDO> driverPunchRecordDOList, String timeZone) {
        return driverPunchRecordDOList.stream()
                .map(item -> toDriverPunchRecordVO(item, timeZone))
                .collect(Collectors.toList());
    }

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum",  ignore = true)
    @Mapping(target = "isDriver",  ignore = true)
    @Mapping(target = "deptList",  ignore = true)
    @Mapping(target = "deptId",  ignore = true)
    @Mapping(target = "countryList",  ignore = true)
    DriverAttendanceDetailQuery toDriverAttendanceDetailQuery(DriverAttendanceDetailParam detailParam);


    @Mapping(target = "userCodeAndName",  ignore = true)
    @Mapping(target = "pageSize",  ignore = true)
    @Mapping(target = "pageNum",  ignore = true)
    @Mapping(target = "isDriver",  ignore = true)
    @Mapping(target = "deptList",  ignore = true)
    @Mapping(target = "deptId",  ignore = true)
    @Mapping(target = "countryList",  ignore = true)
    DriverAttendanceDetailMonthQuery toDriverAttendanceDetailMonthQuery(DriverAttendanceDetailMonthParam monthParam);


    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum",  ignore = true)
    DriverAttendanceOperateRecordQuery toDriverAttendanceOperateRecordQuery(DriverAttendanceOperateRecordParam operateRecordParam);



    @Mapping(target = "operationTypeString", ignore = true)
    @Mapping(target = "operationContentView", ignore = true)
    DriverAttendanceOperateRecordVO toBaseOperateRecordVO(DriverAttendanceOperateRecordDO driverAttendanceOperateRecordDO);


    default DriverAttendanceOperateRecordVO toOperateRecordVO(DriverAttendanceOperateRecordDO driverAttendanceOperateRecordDO){
        DriverAttendanceOperateRecordVO driverAttendanceOperateRecordVO = toBaseOperateRecordVO(driverAttendanceOperateRecordDO);
        driverAttendanceOperateRecordVO.setOperationContentView(driverAttendanceOperateRecordVO.queryOperationContentView());
        driverAttendanceOperateRecordVO.setOperationTypeString(driverAttendanceOperateRecordVO.queryOperationTypeString());
        return driverAttendanceOperateRecordVO;
    }

    default List<DriverAttendanceOperateRecordVO> toOperateRecordVO(List<DriverAttendanceOperateRecordDO> driverAttendanceOperateRecordDOList){
        return driverAttendanceOperateRecordDOList.stream()
                .map(this::toOperateRecordVO)
                .collect(Collectors.toList());
    }



    @Mapping(target = "workStatusDesc", ignore = true)
    @Mapping(target = "statusDesc", ignore = true)
    @Mapping(target = "ocTypeNames", ignore = true)
    DriverAttendanceDetailVO toDriverAttendanceDetailVO(DriverAttendanceDetailDTO driverAttendanceDetailDTO);

    List<DriverAttendanceDetailVO> toDriverAttendanceDetailVO(List<DriverAttendanceDetailDTO> driverAttendanceDetailDTOList);

    /**
     * 将导出参数转换为月度考勤参数
     *
     * @param exportParam 导出参数
     * @return 月度考勤参数
     */
    default DriverAttendanceDetailMonthParam convertExportParam(DriverAttendanceDetailMonthExportParam exportParam) {
        if (exportParam == null) {
            return null;
        }

        DriverAttendanceDetailMonthParam param = toDriverAttendanceDetailMonthParam(exportParam);

        // 日期字符串转换为Date对象
        if (ObjectUtil.isNotEmpty(exportParam.getMonthStartDate())) {
            param.setMonthStartDate(DateHelper.parseYYYYMMDDHHMMSS(exportParam.getMonthStartDate()));
        }
        if (ObjectUtil.isNotEmpty(exportParam.getMonthEndDate())) {
            param.setMonthEndDate(DateHelper.parseYYYYMMDDHHMMSS(exportParam.getMonthEndDate()));
        }

        // JSON字符串转换为List对象
        if (ObjectUtil.isNotEmpty(exportParam.getAttendanceTypeList())) {
            List<Integer> attendanceTypeList = new ArrayList<>(JSON.parseArray(exportParam.getAttendanceTypeList(), Integer.class));
            param.setAttendanceTypeList(attendanceTypeList);
        }
        if (ObjectUtil.isNotEmpty(exportParam.getEmployeeTypeList())) {
            List<String> employeeTypeList = new ArrayList<>(JSON.parseArray(exportParam.getEmployeeTypeList(), String.class));
            param.setEmployeeTypeList(employeeTypeList);
        }
        if (ObjectUtil.isNotEmpty(exportParam.getWorkStatusList())) {
            List<String> workStatusList = new ArrayList<>(JSON.parseArray(exportParam.getWorkStatusList(), String.class));
            param.setWorkStatusList(workStatusList);
        }
        if (ObjectUtil.isNotEmpty(exportParam.getDeptIdList())) {
            List<Long> deptIdList = new ArrayList<>(JSON.parseArray(exportParam.getDeptIdList(), Long.class));
            param.setDeptIdList(deptIdList);
        }

        // 设置导出数量限制
        param.setShowCount(50000);

        return param;
    }

    /**
     * 基础字段映射，忽略需要特殊处理的字段
     */
    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum", ignore = true)
    @Mapping(target = "monthStartDate", ignore = true)
    @Mapping(target = "monthEndDate", ignore = true)
    @Mapping(target = "monthStartDateString", ignore = true)
    @Mapping(target = "monthEndDateString", ignore = true)
    @Mapping(target = "attendanceTypeList", ignore = true)
    @Mapping(target = "employeeTypeList", ignore = true)
    @Mapping(target = "workStatusList", ignore = true)
    @Mapping(target = "deptIdList", ignore = true)
    @Mapping(target = "userCodeList", ignore = true)
    DriverAttendanceDetailMonthParam toDriverAttendanceDetailMonthParam(DriverAttendanceDetailMonthExportParam exportParam);

    /**
     * UserInfoInformationDTO转换为DriverAttendanceInfoDetailDTO
     *
     * @param userInformation 用户信息DTO
     * @return 司机考勤信息详情DTO
     */
    @Mapping(target = "localLastOperatingTime", ignore = true)
    @Mapping(target = "driverYearAttendance", ignore = true)
    DriverAttendanceInfoDetailDTO toDriverAttendanceInfoDetailDTO(UserInformationDTO userInformation);

    /**
     * DriverAttendanceDetailMonthDTO转换为DriverAttendanceDetailMonthVO
     *
     * @param monthDTO 月度考勤DTO
     * @return 月度考勤VO
     */
    @Mapping(target = "workStatusDesc", ignore = true)
    @Mapping(target = "attendanceTypeString", ignore = true)
    @Mapping(target = "employeeTypeDesc", ignore = true)
    @Mapping(target = "ocTypeNames", ignore = true)
    @Mapping(target = "originCountry", ignore = true)
    DriverAttendanceDetailMonthVO toDriverAttendanceDetailMonthVO(DriverAttendanceDetailMonthDTO monthDTO);

    List<DriverAttendanceDetailMonthVO> toDriverAttendanceDetailMonthVO(List<DriverAttendanceDetailMonthDTO> monthDTOList);

    /**
     * 转换分页结果
     *
     * @param pageResult 分页结果
     * @return 转换后的分页结果
     */
    default PaginationResult<DriverAttendanceDetailMonthVO> convertPaginationResult(PaginationResult<DriverAttendanceDetailMonthDTO> pageResult) {
        if (pageResult == null) {
            return null;
        }

        PaginationResult<DriverAttendanceDetailMonthVO> result = new PaginationResult<>();
        result.setPagination(pageResult.getPagination());

        if (pageResult.getResults() != null) {
            result.setResults(toDriverAttendanceDetailMonthVO(pageResult.getResults()));
        }
        return result;
    }
}
