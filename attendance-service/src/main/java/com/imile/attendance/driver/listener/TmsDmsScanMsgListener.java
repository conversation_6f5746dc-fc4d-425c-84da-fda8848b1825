package com.imile.attendance.driver.listener;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.driver.DriverPunchRecordService;
import com.imile.attendance.driver.service.DriverDataManagementService;
import com.imile.saas.tms.api.event.EventConstant;
import com.imile.saas.tms.api.event.EventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description tms配送系统扫描消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.tms.sign.topic}",
        consumerGroup = "${rocket.mq.tms.sign.consumer.group}",
        selectorExpression = EventConstant.BizType.DMS_SCAN,
        consumeThreadMax = 4)
public class TmsDmsScanMsgListener implements RocketMQListener<MessageExt>, InitializingBean {

    @Resource
    private DriverPunchRecordService driverPunchRecordService;
    @Resource
    private DriverDataManagementService driverDataManagementService;

    @Value("${rocket.mq.tms.sign.topic}")
    private String topic;
    @Value("${rocket.mq.tms.sign.consumer.group}")
    private String consumeGroup;


    @Override
    public void onMessage(MessageExt messageExt) {
        log.info("收到TMS->Attendance的消息，msgId={}msgKey={}", messageExt.getMsgId(), messageExt.getKeys());

        // 检查司机消息消费统一开关
        if (!driverDataManagementService.isMessageConsumptionEnabled()) {
            log.info("司机消息消费已禁用，跳过TMS DMS扫描消息处理。msgId={}, {}",
                    messageExt.getMsgId(), driverDataManagementService.getMessageConsumptionStatus());
            return;
        }

        String tags = messageExt.getTags();
        String body = new String(messageExt.getBody());
        log.info("TmsDmsScanMsgListener 收到消息id：{},tags:{},body:{}", messageExt.getMsgId(), tags, body);

        EventDTO eventDTO = JSON.parseObject(new String(messageExt.getBody()), EventDTO.class);
        if (ObjectUtil.isNull(eventDTO)) {
            log.info("TmsDmsScanMsgListener|execute｜eventDTO为null");
            return;
        }

        log.info("TmsDmsScanMsgListener|execute｜eventDTO:{}", JSON.toJSON(eventDTO));

        String operateTags = messageExt.getTags();
        log.info("监听TMS签收数据tag：{}", operateTags);
        try {
            // 业务数据：DeliveredPayloadDTO
            if (ObjectUtil.equal(eventDTO.getBizType(), EventConstant.BizType.DMS_SCAN) &&
                    ObjectUtil.equal(eventDTO.getOperateType(), EventConstant.OperateType.DELIVERED) &&
                    (ObjectUtil.equal(eventDTO.getScanType(), EventConstant.ScanType.DELIVERED) ||
                            ObjectUtil.equal(eventDTO.getScanType(), EventConstant.ScanType.RETURN_DELIVERED) ||
                            ObjectUtil.equal(eventDTO.getScanType(), EventConstant.ScanType.REFUND_DELIVERED))) {

                log.info("DMS_SCAN|delivered｜TMS签收入口|满足条件进入if");
                driverPunchRecordService.handleDeliveredPayload(eventDTO);
            }
        } catch (Exception e) {
            log.error("接收TMS->Attendance的DMS_SCAN mq消息发生异常，msgId={}", messageExt.getMsgId(), e);
            throw e;
        }
    }


    @Override
    public void afterPropertiesSet() {
        log.info("TmsDmsScanMsgListener consumer for topic: {} and group: {} is started successfully.", topic, consumeGroup);
    }
}
