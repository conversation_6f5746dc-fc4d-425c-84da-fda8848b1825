package com.imile.attendance.driver;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.driver.dto.DriverPunchRecordParam;
import com.imile.attendance.driver.dto.ModifyDriverAttendanceParam;
import com.imile.attendance.driver.dto.ModifyDriverDayIdToType;
import com.imile.attendance.driver.mapstruct.DriverPunchMapstruct;
import com.imile.attendance.driver.vo.DriverPunchRecordVO;
import com.imile.attendance.enums.DriverAttendanceOperateRecordEnum;
import com.imile.attendance.enums.DriverAttendanceOperationTypeEnum;
import com.imile.attendance.enums.DriverAttendanceSourceTypeEnum;
import com.imile.attendance.enums.DriverAttendanceTypeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.driver.dao.DriverPunchRecordDao;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceDetailDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverAttendanceOperateRecordDO;
import com.imile.attendance.infrastructure.repository.driver.model.DriverPunchRecordDO;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordDetailQuery;
import com.imile.attendance.infrastructure.repository.driver.query.DriverPunchRecordQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.exception.BaseBusinessException;
import com.imile.dtrack.api.dto.event.DtrackHubEventPayload;
import com.imile.framework.redis.client.ImileRedisClient;
import com.imile.saas.tms.api.event.DeliveredPayloadDTO;
import com.imile.saas.tms.api.event.EventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/7
 * @Description
 */
@Slf4j
@Service
public class DriverPunchRecordService {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private CountryService countryService;
    @Resource
    private ImileRedisClient redissonClient;
    @Resource
    private DriverPunchRecordDao driverPunchRecordDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private DriverPunchRecordManage driverPunchRecordManage;
    @Resource
    private DriverAttendanceManage driverAttendanceManage;

    private static final String LOCK_KEY = "DRIVER_PUNCH_RECORD:LOCK:";


    /**
     * 处理DA轨迹打卡数据
     *
     * @param eventDTO 参数
     */
    public void handleDtrackHubEventPayload(EventDTO eventDTO) {
        DtrackHubEventPayload dtrackHubEventPayload = JSON.parseObject(JSON.toJSONString(eventDTO.getPayload()),
                DtrackHubEventPayload.class);
        if (checkDtrackHub(dtrackHubEventPayload)) {
            return;
        }

        LocalDateTime eventTime = dtrackHubEventPayload.getEventTime();
        // 根据司机code查询司机信息
        AttendanceUser attendanceUser = userService.getByUserCode(dtrackHubEventPayload.getDriverCode());
        if (ObjectUtil.isNull(attendanceUser)) {
            log.info("DriverPunchRecordServiceImpl|handleDtrackHubEventPayload|根据司机code查询司机信息：attendanceUser:{}", "null");
            return;
        }
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(attendanceUser.getLocationCountry());
        // 本地时间
        String eventTimeString = DateUtil.formatLocalDateTime(eventTime);
        Date localEventTime = DateHelper.convertDateByTimeZonePlus(
                countryDTO.getTimeZone(),
                DateUtil.parse(eventTimeString, DatePattern.NORM_DATETIME_PATTERN)
        );
        Long dayId = DateHelper.getDayId(localEventTime);
        DriverPunchRecordQuery query = buildDriverPunchRecordQuery(
                dtrackHubEventPayload.getDriverCode(),
                dayId,
                DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType()
        );
        log.info("DriverPunchRecordServiceImpl|handleDtrackHubEventPayload|DriverPunchRecordQuery:{}", query);
        // 司机code + day_id + 操作类型 作为key【对于 dld签收、da打卡类型，同一个用户同一天】
        String lockKey = LOCK_KEY + dtrackHubEventPayload.getDriverCode() + ":" + dayId + ":" +
                DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType();

        boolean flag = false;
        try {
            //上锁
            flag = redissonClient.tryLock(lockKey, 5);
            log.info("handleDtrackHubEventPayload | flag:{},userCode:{},dayId:{}",
                    flag, dtrackHubEventPayload.getDriverCode(), dayId);
            // 加锁，防止同一司机同一天同一类型的数据重复插入
            if (flag) {
                long startTime = System.currentTimeMillis();
                log.info("handleDtrackHubEventPayload | startTime:{},userCode:{},dayId:{}",
                        startTime, dtrackHubEventPayload.getDriverCode(), dayId);

                // 1. 通过user_code + day_id + 操作类型 查询该用户当天是否已经在500m以内 打过卡，如果打过卡，就不操作了，没有，新增一条打卡记录
                List<DriverPunchRecordDO> driverPunchRecordList =
                        driverPunchRecordDao.listByUserCodeAndDayIdAndOperationType(query);

                long queryEndTime = System.currentTimeMillis();
                log.info("handleDeliveredPayload | queryEndTime:{},userCode:{},dayId:{}",
                        queryEndTime, dtrackHubEventPayload.getDriverCode(), dayId);
                log.info("handleDeliveredPayload | queryTake:{},userCode:{},dayId:{}",
                        queryEndTime - startTime, dtrackHubEventPayload.getDriverCode(), dayId);

                log.info("DriverPunchRecordServiceImpl|handleDtrackHubEventPayload| 通过user_code + day_id + 操作类型 查询列表：driverPunchRecordList:{}", driverPunchRecordList);
                if (CollectionUtils.isEmpty(driverPunchRecordList)) {
                    DriverPunchRecordDO driverPunchRecordDO = buildDriverPunchRecord(
                            localEventTime,
                            dtrackHubEventPayload.getDriverCode(),
                            dtrackHubEventPayload.getDriverName(),
                            DriverAttendanceSourceTypeEnum.DA.getType(),
                            DriverAttendanceOperationTypeEnum.DA_LOCUS_PUNCH.getType(),
                            "轨迹打卡",
                            "Track punch in");
                    driverPunchRecordDao.save(driverPunchRecordDO);
                }
                long endTime = System.currentTimeMillis();
                log.info("handleDtrackHubEventPayload | endTime:{},userCode:{},dayId:{}",
                        endTime, dtrackHubEventPayload.getDriverCode(), dayId);
                log.info("handleDtrackHubEventPayload | take:{},userCode:{},dayId:{}",
                        endTime - startTime, dtrackHubEventPayload.getDriverCode(), dayId);
            }
        } catch (InterruptedException e) {
            log.warn("handleDtrackHubEventPayload：获取分布式锁异常:{}", e.getMessage());
            Thread.currentThread().interrupt();
            throw new BaseBusinessException(e);
        } finally {
            if (flag) {
                redissonClient.unlock(lockKey);
            }
        }
    }

    /**
     * 处理TMS签收数据
     *
     * @param eventDTO 参数
     */
    public void handleDeliveredPayload(EventDTO eventDTO) {
        DeliveredPayloadDTO deliveredPayload = JSON.parseObject(JSON.toJSONString(eventDTO.getPayload()),
                DeliveredPayloadDTO.class);

        if (checkDeliveredPayload(deliveredPayload)) {
            return;
        }
        // 签收时间
        LocalDateTime deliveredDate = deliveredPayload.getDeliveredInfo().getDeliveredDate();
        // 根据司机code查询司机信息
        AttendanceUser attendanceUser = userService.getByUserCode(deliveredPayload.getDeliveredInfo().getCourierCode());
        if (ObjectUtil.isNull(attendanceUser)) {
            log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|根据司机code查询司机信息：hrmsUserInfoDO:{}", "null");
            return;
        }
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(attendanceUser.getLocationCountry());
        // 本地时间
        String deliverdDateString = DateUtil.formatLocalDateTime(deliveredDate);
        Date localDeliveredDate = DateHelper.convertDateByTimeZonePlus(countryDTO.getTimeZone(),
                DateUtil.parse(deliverdDateString, DatePattern.NORM_DATETIME_PATTERN));

        Long dayId = DateHelper.getDayId(localDeliveredDate);
        DriverPunchRecordQuery query = buildDriverPunchRecordQuery(
                deliveredPayload.getDeliveredInfo().getCourierCode(),
                dayId,
                DriverAttendanceOperationTypeEnum.DLD_SIGN.getType()
        );
        log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|HrmsDriverPunchRecordQuery:{}", query);

        String lockKey = LOCK_KEY + deliveredPayload.getDeliveredInfo().getCourierCode() + ":" + dayId + ":" +
                DriverAttendanceOperationTypeEnum.DLD_SIGN.getType();
        boolean flag = false;
        try {
            //上锁
            flag = redissonClient.tryLock(lockKey, 5);
            log.info("handleDeliveredPayload | flag:{},userCode:{},dayId:{}",
                    flag, deliveredPayload.getDeliveredInfo().getCourierCode(), dayId);
            // 加锁，防止同一司机同一天同一类型的数据重复插入
            if (flag) {
                long startTime = System.currentTimeMillis();
                log.info("handleDeliveredPayload | startTime:{},userCode:{},dayId:{}",
                        startTime, deliveredPayload.getDeliveredInfo().getCourierCode(), dayId);

                // 1. 通过user_code + day_id + 操作类型 查询该用户当天是否已经在500m以内 打过卡，如果打过卡，就不操作了，没有，新增一条打卡记录
                List<DriverPunchRecordDO> driverPunchRecordList =
                        driverPunchRecordDao.listByUserCodeAndDayIdAndOperationType(query);

                long queryEndTime = System.currentTimeMillis();
                log.info("handleDeliveredPayload | queryEndTime:{},userCode:{},dayId:{}",
                        queryEndTime, deliveredPayload.getDeliveredInfo().getCourierCode(), dayId);
                log.info("handleDeliveredPayload | queryTake:{},userCode:{},dayId:{}",
                        queryEndTime - startTime, deliveredPayload.getDeliveredInfo().getCourierCode(), dayId);

                log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload| 通过user_code + day_id + 操作类型 查询列表：driverPunchRecordList:{}", driverPunchRecordList);
                if (CollectionUtils.isEmpty(driverPunchRecordList)) {
                    DriverPunchRecordDO driverPunchRecordDO = buildDriverPunchRecord(
                            localDeliveredDate,
                            deliveredPayload.getDeliveredInfo().getCourierCode(),
                            deliveredPayload.getDeliveredInfo().getCourier(),
                            DriverAttendanceSourceTypeEnum.TMS.getType(),
                            DriverAttendanceOperationTypeEnum.DLD_SIGN.getType(),
                            "DLD签收",
                            "DLD sign"
                    );
                    driverPunchRecordDao.save(driverPunchRecordDO);
                } else {
                    Date now = new Date();
                    // 如果该司机该天已经存在签收数据，就累加签收次数
                    DriverPunchRecordDO driverPunchRecordDO = driverPunchRecordList.get(0);
                    driverPunchRecordDO.setNumber(driverPunchRecordDO.getNumber() + 1);
                    // 这边数据库当天已经存在签收记录，后面就不需要再次插入了，需要更新number字段、创建时间、修改时间，因为考勤是根据创建时间来计算的，这边需要更新
                    driverPunchRecordDO.setCreateDate(now);
                    driverPunchRecordDO.setLastUpdDate(now);
                    driverPunchRecordDao.updateById(driverPunchRecordDO);
                }
                long endTime = System.currentTimeMillis();
                log.info("handleDeliveredPayload | endTime:{},userCode:{},dayId:{}",
                        endTime, deliveredPayload.getDeliveredInfo().getCourierCode(), dayId);
                log.info("handleDeliveredPayload | take:{},userCode:{},dayId:{}",
                        endTime - startTime, deliveredPayload.getDeliveredInfo().getCourierCode(), dayId);
            }
        } catch (InterruptedException e) {
            log.warn("handleDeliveredPayload：获取分布式锁异常:{}", e.getMessage());
            Thread.currentThread().interrupt();
            throw new BaseBusinessException(e);
        } finally {
            if (flag) {
                redissonClient.unlock(lockKey);
            }
        }
    }


    public List<DriverPunchRecordVO> selectPunchRecordDetail(DriverPunchRecordParam param) {
        List<AttendanceUser> userList = userService.listByUserCodes(Collections.singletonList(param.getUserCode()));
        if (CollectionUtils.isEmpty(userList)) {
            log.info("DriverPunchRecordServiceImpl|modifyDriverAttendanceDetail|校验参数：user不能为:{}", "null");
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        // 时间转换为当地时间
        CountryDTO countryDTO = countryService.queryCountry(userList.get(0).getLocationCountry());
        DriverPunchRecordDetailQuery query = DriverPunchMapstruct.INSTANCE.toPunchRecordDetailQuery(param);
        List<DriverPunchRecordDO> driverPunchRecordInfoList = driverPunchRecordDao.listPunchRecordDetail(query);
        return DriverPunchMapstruct.INSTANCE.toDriverPunchRecordVO(driverPunchRecordInfoList,countryDTO.getTimeZone());
    }

    /**
     * 修改司机考勤
     *
     * @param param 参数
     */
    public void modifyDriverAttendanceDetail(ModifyDriverAttendanceParam param) {
        // 校验参数
        checkParam(param);
        AttendanceUser attendanceUser = userService.getByUserCode(param.getUserCode());
        if (ObjectUtil.isNull(attendanceUser)) {
            log.info("DriverPunchRecordServiceImpl|modifyDriverAttendanceDetail|校验参数：hrmsUserInfo不能为:{}", "null");
            throw BusinessLogicException.getException(ErrorCodeEnum.ACCOUNT_NOT_EXITS);
        }
        List<DriverPunchRecordDO> addDriverPunchRecordList = Lists.newArrayList();

        List<DriverAttendanceDetailDO> addDriverAttendanceDetailInfoList = Lists.newArrayList();
        List<DriverAttendanceDetailDO> delDriverAttendanceDetailInfoList = Lists.newArrayList();
        Long dayIdNow = DateHelper.getDayId(new Date());

        // 遍历修改的日期
        for (ModifyDriverDayIdToType modifyDriverDayIdToType : param.getModifyDriverDayIdToTypeList()) {
            if (ObjectUtil.isNull(modifyDriverDayIdToType)) {
                continue;
            }
            DateTime dateTime = DateUtil.beginOfDay(
                    DateHelper.transferDayIdToDate(modifyDriverDayIdToType.getDayId())
            );
            // 当前时间小于等于需要修改考勤的时间，则只新增打卡数据，不实时计算考勤
            if (dayIdNow.compareTo(modifyDriverDayIdToType.getDayId()) < 1) {
                // 新增打卡数据
                buildDriverPunchRecord(modifyDriverDayIdToType, dateTime, attendanceUser, addDriverPunchRecordList);
                continue;
            }
            // 新增打卡数据
            buildDriverPunchRecord(modifyDriverDayIdToType, dateTime, attendanceUser, addDriverPunchRecordList);
            // 实时计算考勤
            driverAttendanceManage.buildDriverAttendanceDetailData(
                    modifyDriverDayIdToType.getDayId(),
                    attendanceUser.getUserCode(),
                    attendanceUser.getLocationCountry(),
                    addDriverAttendanceDetailInfoList,
                    delDriverAttendanceDetailInfoList
            );
        }
        // 新增司机考勤操作记录
        DriverAttendanceOperateRecordDO driverAttendanceOperateRecord = buildDriverOperateRecord(attendanceUser);
        // 2. 实时计算考勤 生成司机考勤信息
        driverAttendanceManage.batchSaveOrUpdateDriverAttendanceDetail(
                addDriverAttendanceDetailInfoList,
                delDriverAttendanceDetailInfoList,
                driverAttendanceOperateRecord
        );
    }


    private boolean checkDtrackHub(DtrackHubEventPayload dtrackHubEventPayload) {
        if (ObjectUtil.isNull(dtrackHubEventPayload)) {
            log.info("DriverPunchRecordServiceImpl|handleDtrackHubEventPayload|业务数据：DtrackHubEventPayload:{}", "null");
            return true;
        }
        if (ObjectUtil.isEmpty(dtrackHubEventPayload.getDriverCode())) {
            log.info("DriverPunchRecordServiceImpl|handleDtrackHubEventPayload|司机编码：driverCode:{}", "");
            return true;
        }
        if (ObjectUtil.isNull(dtrackHubEventPayload.getEventTime())) {
            log.info("DriverPunchRecordServiceImpl|handleDtrackHubEventPayload|业务数据打卡时间：eventTime:{}", "null");
            return true;
        }
        return false;
    }

    /**
     * 封装查询参数
     *
     * @param driverCode    司机编码
     * @param dayId         日期
     * @param operationType 操作类型
     */
    private DriverPunchRecordQuery buildDriverPunchRecordQuery(String driverCode, Long dayId, Integer operationType) {
        return DriverPunchRecordQuery.builder()
                .userCode(driverCode)
                .dayId(dayId)
                .operationType(operationType)
                .build();
    }

    private DriverPunchRecordDO buildDriverPunchRecord(Date operatingTime, String driverCode,
                                                       String driverName, Integer sourceType,
                                                       Integer operationType, String operationContent,
                                                       String operationContentEn) {
        Long dayId = DateHelper.getDayId(operatingTime);
        DriverPunchRecordDO driverPunchRecordDO = new DriverPunchRecordDO();
        driverPunchRecordDO.setId(defaultIdWorker.nextId());
        driverPunchRecordDO.setUserCode(driverCode);
        driverPunchRecordDO.setUserName(driverName);
        driverPunchRecordDO.setDayId(dayId);
        driverPunchRecordDO.setSourceType(sourceType);
        driverPunchRecordDO.setOperationType(operationType);
        driverPunchRecordDO.setOperationContent(operationContent);
        driverPunchRecordDO.setOperationContentEn(operationContentEn);
        driverPunchRecordDO.setOperatingTime(operatingTime);
        driverPunchRecordDO.setNumber(1L);
        BaseDOUtil.fillDOInsert(driverPunchRecordDO);
        return driverPunchRecordDO;
    }


    private void buildDriverPunchRecord(ModifyDriverDayIdToType modifyDriverDayIdToType,
                                        DateTime modifyDate,
                                        AttendanceUser attendanceUser,
                                        List<DriverPunchRecordDO> addDriverPunchRecordList) {
        DriverAttendanceTypeEnum before = DriverAttendanceTypeEnum.getByType(modifyDriverDayIdToType.getBeforeDriverAttendanceType());
        String beforeStr = ObjectUtil.isNull(before) ? "" : before.getDescEn();
        DriverAttendanceTypeEnum after = DriverAttendanceTypeEnum.getByType(modifyDriverDayIdToType.getAfterDriverAttendanceType());
        String afterStr = ObjectUtil.isNull(after) ? "" : after.getDescEn();
        String modifyDateString = DateUtil.format(modifyDate, DatePattern.NORM_DATE_PATTERN);
        String operationContent = "被修改日期：" + modifyDateString + "," +
                "修改内容：" + beforeStr +
                "修改为" + afterStr;
        String operationContentEn = "The date on which it was modified：" + modifyDateString + "," +
                "Modifications：" + beforeStr +
                " Modified to：" + afterStr;
        DriverPunchRecordDO driverPunchRecord = new DriverPunchRecordDO();
        driverPunchRecord.setId(defaultIdWorker.nextId());
        driverPunchRecord.setUserCode(attendanceUser.getUserCode());
        driverPunchRecord.setUserName(attendanceUser.getUserName());
        driverPunchRecord.setDayId(modifyDriverDayIdToType.getDayId());
        driverPunchRecord.setSourceType(DriverAttendanceSourceTypeEnum.AS.getType());
        driverPunchRecord.setOperationType(DriverAttendanceOperationTypeEnum.MODIFY_ATTENDANCE.getType());
        driverPunchRecord.setOperationContent(operationContent);
        driverPunchRecord.setOperationContentEn(operationContentEn);
        // 这边保存OperatingTime【该字段暂时没用】 为请假当天的时间 比如“2024-01-23 00:00:00，因为请假一个范围不能具体时间
        DateTime dateTime = DateUtil.beginOfDay(DateUtil.parse(modifyDriverDayIdToType.getDayId().toString()));
        driverPunchRecord.setOperatingTime(dateTime);
        // number、modify_attendance_type数据库默认值
        driverPunchRecord.setFormId(null);
        driverPunchRecord.setModifyAttendanceType(modifyDriverDayIdToType.getAfterDriverAttendanceType());
        BaseDOUtil.fillDOInsert(driverPunchRecord);
        // 这边直接提交保存
        driverPunchRecordManage.saveOrUpdate(driverPunchRecord);
        //addDriverPunchRecordList.add(driverPunchRecord);
    }

    private boolean checkDeliveredPayload(DeliveredPayloadDTO deliveredPayload) {
        if (ObjectUtil.isNull(deliveredPayload)) {
            log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|业务数据：DeliveredPayloadDTO:{}", "null");
            return true;
        }
        if (ObjectUtil.isNull(deliveredPayload.getDeliveredInfo())) {
            log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|deliveredInfo|业务数据：deliveredPayload.getDeliveredInfo():{}", "null");
            return true;
        }
        if (ObjectUtil.isNull(deliveredPayload.getDeliveredInfo().getDeliveredDate())) {
            log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|deliveredInfo|业务数据：deliveredPayload.getDeliveredInfo().getDeliveredTime():{}", "null");
            return true;
        }
        if (ObjectUtil.isEmpty(deliveredPayload.getDeliveredInfo().getCourierCode())) {
            log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|deliveredInfo|业务数据：deliveredPayload.getDeliveredInfo().getCourierCode():{}", "");
            return true;
        }
        if (ObjectUtil.isNotEmpty(deliveredPayload.getDeliveredInfo().getCourierType()) && !EmploymentTypeEnum.TYPE_OF_DEFAULT_WAREHOUSE.contains(deliveredPayload.getDeliveredInfo().getCourierType())) {
            log.info("DriverPunchRecordServiceImpl|handleDeliveredPayload|deliveredInfo|业务数据：deliveredPayload.getDeliveredInfo().getCourierType():{}", "");
            return true;
        }
        return false;
    }

    /**
     * 参数校验
     *
     * @param param 参数
     */
    private void checkParam(ModifyDriverAttendanceParam param) {
        if (ObjectUtil.isNull(param)) {
            log.info("DriverPunchRecordServiceImpl|modifyDriverAttendanceDetail|校验参数：ModifyDriverAttendanceParam param不能为:{}", "null");
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        if (ObjectUtil.isEmpty(param.getUserCode())) {
            log.info("DriverPunchRecordServiceImpl|modifyDriverAttendanceDetail|校验参数：param.getUserCode()不能为:{}", "");
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        if (ObjectUtil.isEmpty(param.getCountry())) {
            log.info("DriverPunchRecordServiceImpl|modifyDriverAttendanceDetail|校验参数：param.getCountry()不能为:{}", "");
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        if (CollectionUtils.isEmpty(param.getModifyDriverDayIdToTypeList())) {
            log.info("DriverPunchRecordServiceImpl|modifyDriverAttendanceDetail|校验参数：param.getModifyDriverDayIdToTypeList()不能为:{}", "");
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
    }


    /**
     * 构建司机操作记录
     *
     * @param attendanceUser 用户信息
     */
    private DriverAttendanceOperateRecordDO buildDriverOperateRecord(AttendanceUser attendanceUser) {
        DriverAttendanceOperateRecordDO driverAttendanceOperateRecord = new DriverAttendanceOperateRecordDO();
        driverAttendanceOperateRecord.setId(defaultIdWorker.nextId());
        driverAttendanceOperateRecord.setCountry(attendanceUser.getLocationCountry());
        driverAttendanceOperateRecord.setOperationType(DriverAttendanceOperateRecordEnum.MODIFY_ATTENDANCE.getType());
        String userName = ObjectUtil.isNotEmpty(attendanceUser.getUserName()) ? attendanceUser.getUserName() : "";
        String operationContent = "被修改人：" + userName + " " + attendanceUser.getUserCode();
        String userNameEn = ObjectUtil.isNotEmpty(attendanceUser.getUserNameEn()) ? attendanceUser.getUserNameEn() : "";
        String operationContentEn = "The person being modified：" + userNameEn + " " + attendanceUser.getUserCode();
        driverAttendanceOperateRecord.setOperationContent(operationContent);
        driverAttendanceOperateRecord.setOperationContentEn(operationContentEn);
        BaseDOUtil.fillDOInsert(driverAttendanceOperateRecord);
        return driverAttendanceOperateRecord;
    }
}
