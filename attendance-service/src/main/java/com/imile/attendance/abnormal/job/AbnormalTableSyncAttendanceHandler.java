package com.imile.attendance.abnormal.job;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.infrastructure.config.EnableNewAttendanceConfig;
import com.imile.attendance.migration.AbnormalMigrationService;
import com.imile.attendance.migration.dto.AbnormalSyncDTO;
import com.imile.attendance.util.DateHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 异常相关表定时全量同步新系统
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Component
@Slf4j
public class AbnormalTableSyncAttendanceHandler {
    @Resource
    private AbnormalMigrationService abnormalMigrationService;
    @Resource
    private AttendanceCountryService attendanceCountryService;
    @Resource
    private EnableNewAttendanceConfig enableNewAttendanceConfig;

    @XxlJob(BusinessConstant.JobHandler.ABNORMAL_TABLE_SYNC_ATTENDANCE_HANDLER)
    public ReturnT<String> syncAttendanceHandler(String content) {
        String handler = BusinessConstant.JobHandler.ABNORMAL_TABLE_SYNC_ATTENDANCE_HANDLER;
        XxlJobLogger.log("XXL-JOB,  {} Start.The Param:{}", handler, content);

        if (!enableNewAttendanceConfig.getAbnormalDoubleWriteEnabled()) {
            XxlJobLogger.log("XXL-JOB,  {} 新系统双写未开启执行结束", handler);
            return ReturnT.SUCCESS;
        }

        AbnormalTableSyncAttendanceHandler.HandlerParam param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, AbnormalTableSyncAttendanceHandler.HandlerParam.class) :
                new AbnormalTableSyncAttendanceHandler.HandlerParam();

        if (StringUtils.isBlank(param.getCountryList())) {
            XxlJobLogger.log("XXL-JOB:{} the country does not exist", handler);
            return ReturnT.FAIL;
        }

        List<String> countryList = Arrays.asList(param.getCountryList().split(BusinessConstant.DEFAULT_DELIMITER));
        // 获取对应国家的当前时间
        Map<String, Date> countryCurrentDate = attendanceCountryService.getCountryCurrentDate(countryList);

        List<String> userCodes = null;
        if (StringUtils.isNotBlank(param.getUserCodeList())) {
            userCodes = Arrays.asList(param.getUserCodeList().split(BusinessConstant.DEFAULT_DELIMITER));
        }

        for (String country : countryList) {
            Long attendanceDayId = param.getDayId();
            Date date = countryCurrentDate.get(country);
            if (Objects.isNull(date)) {
                XxlJobLogger.log("XXL-JOB:{} the time for the current country was not obtained:{}", handler, country);
                continue;
            }
            if (Objects.isNull(attendanceDayId) || attendanceDayId <= 0) {
                attendanceDayId = DateHelper.getDayId(DateUtil.offsetMonth(date, -2));
                XxlJobLogger.log("XXL-JOB:{} locationCountry:{} attendanceDayId:{}", handler, country, attendanceDayId);
            }

            AbnormalSyncDTO abnormalSyncDTO = new AbnormalSyncDTO();
            abnormalSyncDTO.setStartDayId(attendanceDayId);
            abnormalSyncDTO.setCountryList(Collections.singletonList(country));
            abnormalSyncDTO.setUserCodeList(userCodes);
            abnormalMigrationService.syncNewSystemAbnormalRecord(abnormalSyncDTO);

            log.info("XXL-JOB: {},country：{},同步结束", handler, country);
        }
        XxlJobLogger.log("XXL-JOB,  {} End",
                BusinessConstant.JobHandler.ABNORMAL_TABLE_SYNC_ATTENDANCE_HANDLER);
        return ReturnT.SUCCESS;
    }

    @Data
    private static class HandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 部门集合
         */
        private String deptIdList;

        /**
         * 用户集合
         */
        private String userCodeList;
        /**
         * 日期
         */
        private Long dayId;

        /**
         * 跳过灰度范围
         */
        private Boolean skipGrayscale = Boolean.TRUE;
    }
}
