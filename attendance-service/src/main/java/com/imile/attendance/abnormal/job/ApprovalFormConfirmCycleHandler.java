package com.imile.attendance.abnormal.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.infrastructure.form.FormAttrUtils;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.idwork.IdWorkerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/3 
 * @Description 用户考勤单据确认周期监控
 */
@Slf4j
@Component
public class ApprovalFormConfirmCycleHandler {

    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private RpcBpmApprovalClient rpcBpmApprovalClient;
    @Resource
    private MigrationService migrationService;


    @XxlJob(BusinessConstant.JobHandler.APPROVAL_FORM_CONFIRM_CYCLE_HANDLER)
    public ReturnT<String> approvalFormConfirmCycleHandler(String content) {

        ApprovalFormConfirmCycleHandler.HandlerParam param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, ApprovalFormConfirmCycleHandler.HandlerParam.class) :
                new ApprovalFormConfirmCycleHandler.HandlerParam();

        // 获取待处理的用户列表
        List<AttendanceUser> userList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodes().split(","));
            userList = userService.listUsersByUserCodes(userCodeList);
        } else if (StringUtils.isNotBlank(param.getCountryList())) {
            // 直接按国家获取用户
            List<String> countryList = Arrays.asList(param.getCountryList().split(","));
            UserDaoQuery userDaoQuery = new UserDaoQuery();
            userDaoQuery.setLocationCountryList(countryList);
            userList = userService.listUsersByQuery(userDaoQuery);
        }

        List<Long> userIdList = userList.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIdList)) {
            XxlJobLogger.log("用户考勤单据确认周期监控，筛选条件过滤后的人员为空，全量查询所有请假外勤补卡的审核中单据");
        } else {
            XxlJobLogger.log("用户考勤单据确认周期监控,筛选条件过滤后的人员:{}", userIdList.size());
        }

        // 查询所有用户的审批单据
        ApplicationFormQuery applicationFormQuery = new ApplicationFormQuery();
        applicationFormQuery.setUserIdList(userIdList);
        applicationFormQuery.setStatusList(Collections.singletonList(FormStatusEnum.IN_REVIEW.getCode()));
        applicationFormQuery.setFromTypeList(Arrays.asList(FormTypeEnum.LEAVE.getCode(),
                FormTypeEnum.OUT_OF_OFFICE.getCode(), FormTypeEnum.REISSUE_CARD.getCode()));
        List<AttendanceFormDO> formDOList = attendanceFormManage.selectForm(applicationFormQuery);

        if (CollectionUtils.isEmpty(formDOList)) {
            XxlJobLogger.log("用户考勤单据确认周期监控,没有请假外勤补卡的审核中单据,不处理");
            return ReturnT.SUCCESS;
        }

        XxlJobLogger.log("查询到待处理单据总数: {}", formDOList.size());

        // 过滤在灰度名单的人员
        List<Long> originalUserIdList = formDOList.stream()
                .map(AttendanceFormDO::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 查询在灰度名单的人员
        Map<Long, Boolean> userMigrationMap;

        try {
            userMigrationMap = migrationService.verifyUsersIsEnableNewAttendance(originalUserIdList);
        } catch (Exception e) {
            log.error("批量验证用户迁移状态异常", e);
            XxlJobLogger.log("迁移验证异常，跳过处理: {}", e.getMessage());
            return ReturnT.FAIL;
        }

        // 统计启用新系统的用户
        List<Long> enableNewAttendanceUserIdList = userMigrationMap.entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        int enableNewAttendanceUserSize = enableNewAttendanceUserIdList.size();
        XxlJobLogger.log("用户迁移状态统计 - 总数: {}, 启用新系统: {}, 未启用: {}",
                originalUserIdList.size(), enableNewAttendanceUserSize, (originalUserIdList.size() - enableNewAttendanceUserSize));

        if (enableNewAttendanceUserSize == 0) {
            XxlJobLogger.log("没有启用新考勤系统的用户，跳过处理");
            return ReturnT.SUCCESS;
        }

        // 过滤启用新系统的用户的单据
        formDOList = formDOList.stream()
                .filter(i -> enableNewAttendanceUserIdList.contains(i.getUserId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formDOList)) {
            XxlJobLogger.log("用户考勤单据确认周期监控，当前人员全部都在灰度人员里，无异常考勤要处理");
            return ReturnT.SUCCESS;
        }

        List<Long> formIdList = formDOList.stream()
                .map(AttendanceFormDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormAttrDO> attrDOList = attendanceFormManage.selectFormAttrByFormIdList(formIdList);
        Map<Long, List<AttendanceFormAttrDO>> attrMap = attrDOList.stream()
                .collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId));

        List<AttendanceFormRelationDO> relationDOList = attendanceFormManage.selectRelationByFormIdList(formIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());
        List<Long> abnormalIdList = relationDOList.stream()
                .map(AttendanceFormRelationDO::getRelationId)
                .collect(Collectors.toList());
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = abnormalAttendanceManage.selectByIdList(abnormalIdList);
        Map<Long, List<EmployeeAbnormalAttendanceDO>> abnormalMap = abnormalAttendanceDOList.stream()
                .collect(Collectors.groupingBy(EmployeeAbnormalAttendanceDO::getId));

        // 过滤需要处理的单据
        List<AttendanceFormDO> filterFormList = new ArrayList<>();

        log.info("用户考勤单据确认周期监控|请假外勤补卡的审核中单据数量:{}", formDOList.size());
        XxlJobLogger.log("用户考勤单据确认周期监控|请假外勤补卡的审核中单据数量:{}", formDOList.size());
        for (AttendanceFormDO formDO : formDOList) {
            List<AttendanceFormAttrDO> formAttrList = attrMap.get(formDO.getId());
            if (CollectionUtils.isEmpty(formAttrList)) {
                continue;
            }
            //获取用户的考勤周期配置
            AttendanceCycleConfigDO attendanceCycleConfigDO = null;
            try {
                attendanceCycleConfigDO = attendanceCycleConfigService.getUserAttendanceCycleConfig(formDO.getUserId());
            } catch (Exception e) {
                log.info("用户考勤单据确认周期监控|查询用户的考勤周期配置异常,userId:{}", formDO.getUserId(),e);
                XxlJobLogger.log("用户考勤单据确认周期监控|查询用户的考勤周期配置异常,userId:{},{}", formDO.getUserId(),e.getMessage());
                continue;
            }
            if (ObjectUtil.isNull(attendanceCycleConfigDO)) {
                log.info("用户考勤单据确认周期监控 用户的考勤周期配置不存在,userId:{}", formDO.getUserId());
                XxlJobLogger.log("用户考勤单据确认周期监控|用户的考勤周期配置不存在,userId:{}", formDO.getUserId());
                continue;
            }
            Date nowDate = new Date();
            Boolean tag = true;
            Date leaveStartDate = null;
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                AttendanceFormAttrDO leaveStartDateAttr = FormAttrUtils.getFormAttr(formAttrList, ApplicationFormAttrKeyEnum.leaveStartDate);
                if (Objects.isNull(leaveStartDateAttr)) {
                    continue;
                }
                leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateAttr.getAttrValue());
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                AttendanceFormAttrDO outOfOfficeStartDateAttr = FormAttrUtils.getFormAttr(formAttrList, ApplicationFormAttrKeyEnum.outOfOfficeStartDate);
                if (Objects.isNull(outOfOfficeStartDateAttr)) {
                    continue;
                }
                leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateAttr.getAttrValue());
            }
            //补卡
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.REISSUE_CARD.getCode())) {
                AttendanceFormAttrDO reissueCardDayIdAttr = FormAttrUtils.getFormAttr(formAttrList, ApplicationFormAttrKeyEnum.reissueCardDayId);
                if (Objects.isNull(reissueCardDayIdAttr)) {
                    continue;
                }
                leaveStartDate = DateUtil.parse(reissueCardDayIdAttr.getAttrValue(), "yyyyMMdd");
            }
            if (leaveStartDate == null) {
                continue;
            }

            // 执行周期校验逻辑
            tag = confirmCycleCheck(nowDate, attendanceCycleConfigDO, leaveStartDate);
            log.debug("用户考勤单据确认周期监控 确认周期校验：{}, userId:{}, formId:{}", tag, formDO.getUserId(), formDO.getId());
            XxlJobLogger.log("用户考勤单据确认周期监控 确认周期校验：{}, userId:{}, formId:{}", tag, formDO.getUserId(), formDO.getId());
            if (tag) {
                XxlJobLogger.log("用户考勤单据确认周期监控| 单据在周期内，不处理");
                continue;
            }
            //超过期限了
            filterFormList.add(formDO);
        }

        log.info("用户考勤单据确认周期监控|超过考勤周期范围的单据数量:{}", filterFormList.size());
        XxlJobLogger.log("用户考勤单据确认周期监控|超过考勤周期范围的单据数量:{}", filterFormList.size());

        List<AttendanceFormDO> updateFormList = new ArrayList<>();
        List<AttendanceFormAttrDO> addAttrList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> addRecordList = new ArrayList<>();
        for (AttendanceFormDO formDO : filterFormList) {
            //需要通知审批中心
            if (formDO.getApprovalId() != null) {
                try {
                    rpcBpmApprovalClient.backApply(formDO.getApprovalId());
                } catch (Exception e) {
                    log.info("用户考勤单据确认周期监控|单据的approvalId不存在,applicationCode:{}", formDO.getApplicationCode());
                    continue;
                }
            }
            formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
            BaseDOUtil.fillDOUpdate(formDO);
            updateFormList.add(formDO);
            //加个备注
            AttendanceFormAttrDO applicationFormAttrDO = new AttendanceFormAttrDO();
            applicationFormAttrDO.setId(IdWorkerUtil.getId());
            applicationFormAttrDO.setFormId(formDO.getId());
            applicationFormAttrDO.setAttrKey(ApplicationFormAttrKeyEnum.terminatedReason.getCode());
            applicationFormAttrDO.setAttrValue("用户考勤单据确认周期监控取消的单据");
            BaseDOUtil.fillDOInsert(applicationFormAttrDO);
            addAttrList.add(applicationFormAttrDO);

            //查看有没有关联异常，如果有，就确认异常
            List<AttendanceFormRelationDO> existRelationDOList = relationDOList.stream().filter(item -> item.getFormId().equals(formDO.getId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(existRelationDOList)) {
                continue;
            }
            List<EmployeeAbnormalAttendanceDO> existAbnormal = abnormalMap.get(existRelationDOList.get(0).getRelationId());
            if (CollectionUtils.isEmpty(existAbnormal)) {
                continue;
            }
            //需要确认异常
            EmployeeAbnormalAttendanceDO abnormalAttendanceDO = existAbnormal.get(0);
            abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.EXPIRED.getCode());
            BaseDOUtil.fillDOUpdate(abnormalAttendanceDO);
            updateAbnormalList.add(abnormalAttendanceDO);

            //添加对异常的操作记录
            EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
            abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
            abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
            abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_EXPIRED.getCode());
            BaseDOUtil.fillDOInsert(abnormalOperationRecordDO);
            addRecordList.add(abnormalOperationRecordDO);
        }
        log.info("用户考勤单据确认周期监控|updateFormList:{},updateAbnormalList:{}", updateFormList.size(), updateAbnormalList.size());
        XxlJobLogger.log("用户考勤单据确认周期监控|updateFormList:{},updateAbnormalList:{}", updateFormList.size(), updateAbnormalList.size());
        //落库
        abnormalAttendanceManage.updateApprovalFormConfirmCycle(updateFormList, addAttrList, updateAbnormalList, addRecordList);
        return ReturnT.SUCCESS;
    }

    /**
     * 确认周期校验
     *
     * @param nowDate                     当前时间
     * @param attendanceCycleConfigDO 考勤周期配置
     * @param specificTime                指定日期
     * @return Boolean
     */
    private Boolean confirmCycleCheck(Date nowDate, AttendanceCycleConfigDO attendanceCycleConfigDO, Date specificTime) {
        // 区分月维度、周维度,获取请假可请范围
        Long specificTimeDayId = DateHelper.getDayId(specificTime);

        // 如果参数不符合规则，直接返回true
        if (ObjectUtil.isNull(nowDate) || ObjectUtil.isNull(attendanceCycleConfigDO) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleStart()) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleEnd()) ||
                ObjectUtil.isNull(attendanceCycleConfigDO.getAbnormalExpired())) {
            return true;
        }

        Integer cycleType = attendanceCycleConfigDO.getCycleType();
        String cycleEnd = attendanceCycleConfigDO.getCycleEnd();

        CycleTypeEnum cycleTypeEnum = ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType()) ?
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name()) :
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            return true;
        }

        Integer abnormalExpired = cycleTypeEnum.getActualAbnormalExpired(
                nowDate,
                attendanceCycleConfigDO.getCycleStart(),
                attendanceCycleConfigDO.getCycleEnd(),
                attendanceCycleConfigDO.getAbnormalExpired()
        );

        // 周期偏移日期
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(nowDate, cycleEnd, abnormalExpired);
        Long offsetCycleEndDayId = DateHelper.getDayId(offsetCycleEndDate);

        // 指定时间小于等于周期偏移日期。需要处理掉
        if (specificTimeDayId.compareTo(offsetCycleEndDayId) <= 0) {
            return false;
        }
        return true;
    }


    @Data
    private static class HandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;
    }

}
