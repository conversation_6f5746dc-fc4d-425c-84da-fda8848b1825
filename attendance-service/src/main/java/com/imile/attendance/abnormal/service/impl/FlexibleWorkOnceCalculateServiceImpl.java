package com.imile.attendance.abnormal.service.impl;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.abnormal.AttendanceCalculateContext;
import com.imile.attendance.abnormal.AttendanceMinuteCalculateService;
import com.imile.attendance.abnormal.dto.AbnormalExtendDTO;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.abnormal.dto.UserAttendancePunchConfigDTO;
import com.imile.attendance.abnormal.service.AttendanceCalculateCommonService;
import com.imile.attendance.abnormal.service.AttendanceCalculateService;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 灵活打卡一次考勤计算
 *
 * <AUTHOR>
 * @since 2025/5/20
 */
@Slf4j
@Service
@Strategy(value = AttendanceCalculateService.class, implKey = "FlexibleWorkOnceCalculateServiceImpl")
public class FlexibleWorkOnceCalculateServiceImpl extends AttendanceCalculateCommonService implements AttendanceCalculateService {
    @Resource
    private AttendanceMinuteCalculateService attendanceMinuteCalculateService;

    @Override
    public boolean isMatch(List<UserAttendancePunchConfigDTO> userAttendancePunchConfigList, String punchConfigType) {
        return CollectionUtils.isNotEmpty(userAttendancePunchConfigList)
                && Objects.equals(BusinessConstant.Y, userAttendancePunchConfigList.get(0).getIsActualPunch())
                && Objects.equals(PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode(), punchConfigType);
    }

    @Override
    public void execute(AttendanceCalculateContext calculateContext) {
        UserInfoDO user = calculateContext.getUser();
        AttendanceCalculateHandlerDTO calculateHandlerDTO = calculateContext.getCalculateHandlerDTO();
        log.info("attendanceCalculate userCode:{}, date:{}, 灵活打卡一次计算", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());

        UserAttendancePunchConfigDTO userAttendancePunchConfig = calculateContext.getUserAttendancePunchConfigDTOList().get(0);
        List<PunchClassItemConfigDTO> classItemConfigList = userAttendancePunchConfig.getClassConfigDO().getClassItemConfigList();
        if (classItemConfigList.size() > 1) {
            log.info("attendanceCalculate userCode:{}, date:{} , classId:{}, 灵活打卡一次排班中的班次存在多时段", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(),
                    userAttendancePunchConfig.getClassConfigDO().getId());
            return;
        }

        List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList = new ArrayList<>();
        List<AttendanceEmployeeDetailDO> updateEmployeeDetailDOList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalAttendanceDOList = new ArrayList<>();

        deletePendingAttendanceRecords(calculateContext.getAttendanceEmployeeDetailDOList(), calculateContext.getUserAbnormalAttendanceDOList(),
                updateEmployeeDetailDOList, updateAbnormalAttendanceDOList);

        //需要先将改天的所有审批通过的请假/外勤拼接起来，一个时刻可以有多个审批通过的假期，每次请10分钟，请5次，就有5个单据
        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = attendanceMinuteCalculateService.dayFormInfoBuild(calculateContext.getUserPassFormBOList(), Collections.emptyList());

        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigList);

        DayItemConfigDateDTO itemConfigDate = attendanceMinuteCalculateService.buildDayItemConfigDateDTO(calculateHandlerDTO.getAttendanceDayId(), itemConfigDOList.get(0),
                itemConfigDOList, calculateContext.getPunchRecordDOList());
        if (Objects.isNull(itemConfigDate)) {
            return;
        }
        log.info("attendanceCalculate userCode:{}, date:{},当天的班次的具体信息 itemConfigDate:{}",
                user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), JSON.toJSONString(itemConfigDate));

        List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
        BigDecimal usedMinutes = calculateLeaveHandler(calculateContext, addEmployeeDetailDOList, updateEmployeeDetailDOList, handlerFormDTOList, itemConfigDate, filterFormDTOList);
        if (usedMinutes == null) {
            return;
        }
        log.info("attendanceCalculate userCode:{}, date:{},itemTotalMinutes:{},usedMinutes:{}", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId(), itemConfigDate.getItemTotalMinutes(), usedMinutes);

        //当前时刻完全请假
        if ((itemConfigDate.getItemTotalMinutes().subtract(usedMinutes)).compareTo(BigDecimal.ZERO) < 1) {
            //当前时刻完全请假
            attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, null, updateAbnormalAttendanceDOList);
            return;
        }
        //没有全部请假，需要看打卡时间（可能打卡时间够，正常考勤，也可能不够，异常考勤）
        List<UserPunchRecordBO> itemPunchRecordList = getEffectiveUserPunchRecord(itemConfigDate.getEarliestPunchInTime(), itemConfigDate.getLatestPunchOutTime(), calculateContext.getPunchRecordDOList());

        //情况1:当天没有打卡时间
        if (CollectionUtils.isEmpty(itemPunchRecordList)) {
            log.info("attendanceCalculate userCode:{}, date:{},当天灵活打卡一次没有打卡时间", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
            onceNoPunchTimeHandler(user, calculateHandlerDTO, itemConfigDate.getPunchInTime(), calculateContext.getAttendanceType(), calculateContext.getPunchConfigDO().getId()
                    , userAttendancePunchConfig.getClassConfigDO().getId(), itemConfigDOList.get(0).getId(), addAbnormalAttendanceDOList);
            addAbnormalAttendanceDOList = filterAbnormalAttendanceList(calculateContext.getUserAbnormalAttendanceDOList(), addAbnormalAttendanceDOList);
            attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, addAbnormalAttendanceDOList, updateAbnormalAttendanceDOList);
            return;
        }

        //情况2:当天完全没请假，看打卡记录
        if (CollectionUtils.isEmpty(filterFormDTOList)) {
            log.info("attendanceCalculate userCode:{}, date:{}, 当天灵活打卡一次有打卡记录且完全没请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
            normalTimeHandler(user, calculateHandlerDTO, addEmployeeDetailDOList, calculateContext.getAttendanceType(),
                    itemConfigDate.getItemTotalMinutes().subtract(usedMinutes), calculateContext.getPunchConfigDO().getId());
            attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, null, updateAbnormalAttendanceDOList);
            return;
        }
        //情况3: 存在请假,存在打卡
        log.info("attendanceCalculate userCode:{}, date:{}, 当天灵活打卡一次有打卡记录且有请假", user.getUserCode(), calculateHandlerDTO.getAttendanceDayId());
        normalTimeHandler(user, calculateHandlerDTO, addEmployeeDetailDOList, calculateContext.getAttendanceType(),
                itemConfigDate.getItemTotalMinutes().subtract(usedMinutes), calculateContext.getPunchConfigDO().getId());
        attendanceEmployeeDetailManage.attendanceGenerateUpdate(addEmployeeDetailDOList, updateEmployeeDetailDOList, null, updateAbnormalAttendanceDOList);
    }


    /**
     * 一次打卡正常计算
     */
    private void normalTimeHandler(UserInfoDO user,
                                   AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                   List<AttendanceEmployeeDetailDO> addEmployeeDetailDOList,
                                   String attendanceType,
                                   BigDecimal itemTotalMinutes,
                                   Long punchConfigId) {
        AttendanceEmployeeDetailDO userAttendance = buildUserAttendance(user, calculateHandlerDTO, attendanceType, AttendanceConcreteTypeEnum.P.getCode(), BusinessConstant.Y,
                null, null, null, BigDecimal.ZERO, itemTotalMinutes, BigDecimal.ZERO, null, punchConfigId);
        addEmployeeDetailDOList.add(userAttendance);
    }


    /**
     * 无打卡记录
     */
    private void onceNoPunchTimeHandler(UserInfoDO user,
                                        AttendanceCalculateHandlerDTO calculateHandlerDTO,
                                        Date punchInTime,
                                        String attendanceType,
                                        Long punchConfigId,
                                        Long classId,
                                        Long classItemId,
                                        List<EmployeeAbnormalAttendanceDO> addAbnormalAttendanceDOList) {
        //1条异常，未打卡
        AbnormalExtendDTO abnormalExtendDTO = new AbnormalExtendDTO();
        abnormalExtendDTO.setCorrectPunchTime(punchInTime);
        EmployeeAbnormalAttendanceDO noPunchAbnormalAttendanceDO = buildAbnormal(user, calculateHandlerDTO, AttendanceAbnormalTypeEnum.NO_PUNCH.getCode(), attendanceType,
                punchConfigId, classId, classItemId, JSON.toJSONString(abnormalExtendDTO));
        addAbnormalAttendanceDOList.add(noPunchAbnormalAttendanceDO);
    }
}
