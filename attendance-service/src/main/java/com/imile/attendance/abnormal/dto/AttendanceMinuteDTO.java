package com.imile.attendance.abnormal.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2025/6/20
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AttendanceMinuteDTO {

    /**
     * 实际出勤时长
     */
    private BigDecimal actualAttendanceMinutes = BigDecimal.ZERO;

    /**
     * 最终工作时长
     */
    private BigDecimal finalWorkMinutes = BigDecimal.ZERO;
}
