package com.imile.attendance.abnormal.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.bpm.RpcBpmApprovalClient;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.cycleConfig.AttendanceCycleConfigService;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.ApplicationRelationTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormRelationDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.idwork.IdWorkerUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/6/3
 * @Description 异常考勤确认周期监控
 */
@Slf4j
@Component
public class AbnormalConfirmCycleHandler {

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private AttendanceCycleConfigService attendanceCycleConfigService;
    @Resource
    private RpcBpmApprovalClient rpcBpmApprovalClient;
    @Resource
    private MigrationService migrationService;


    @XxlJob(BusinessConstant.JobHandler.ABNORMAL_CONFIRM_CYCLE_HANDLER)
    public ReturnT<String> abnormalConfirmCycleHandler(String content) {

        AbnormalConfirmCycleHandler.HandlerParam param = StringUtils.isNotBlank(content) ?
                JSON.parseObject(content, AbnormalConfirmCycleHandler.HandlerParam.class) :
                new AbnormalConfirmCycleHandler.HandlerParam();

        if (StringUtils.isBlank(param.getCountryList())) {
            XxlJobLogger.log("XXL-JOB:{} country does not exist", BusinessConstant.JobHandler.ABNORMAL_CONFIRM_CYCLE_HANDLER);
            return ReturnT.FAIL;
        }

        long startTime = System.currentTimeMillis();
        String[] countryList = param.getCountryList().split(BusinessConstant.DEFAULT_DELIMITER);
        List<AttendanceFormDO> updateFormList = new ArrayList<>();
        List<AttendanceFormAttrDO> addAttrList = new ArrayList<>();
        List<EmployeeAbnormalAttendanceDO> updateAbnormalList = new ArrayList<>();
        List<EmployeeAbnormalOperationRecordDO> addRecordList = new ArrayList<>();

        UserDaoQuery userQuery = UserDaoQuery.builder()
                .isDriver(BusinessConstant.N)
                .build();
        if (ObjectUtil.isNotEmpty(param.getUserCodes())) {
            List<String> userCodeList = Arrays.asList(param.getUserCodes().split(BusinessConstant.DEFAULT_DELIMITER));
            userQuery.setUserCodes(userCodeList);
        }
        for (String country : countryList) {
            userQuery.setLocationCountry(country);
            if (CountryCodeEnum.SPECIAL_COUNTRY_FOR_PAGE_SHIFT.contains(country)) {
                userQuery.setEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            } else {
                userQuery.setEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
            }
            int currentPage = 1;
            int pageSize = 1000;
            Page<UserInfoDO> userPage = PageHelper.startPage(currentPage, pageSize, true);
            PageInfo<UserInfoDO> userPageInfo = userPage.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
            // 总记录数
            List<UserInfoDO> userInfoList = userPageInfo.getList();
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                userAbnormalCycleHandler(updateFormList, addAttrList, updateAbnormalList, addRecordList, userInfoList);
            }

            while (currentPage < userPageInfo.getPages()) {
                currentPage++;
                XxlJobLogger.log("异常考勤确认周期监控,currentPage：{},pages：{}", currentPage, userPageInfo.getPages());

                userPage = PageHelper.startPage(currentPage, pageSize, true);
                userPageInfo = userPage.doSelectPageInfo(() -> userInfoDao.userList(userQuery));
                userInfoList = userPageInfo.getList();
                if (CollUtil.isNotEmpty(userInfoList)) {
                    userAbnormalCycleHandler(updateFormList, addAttrList, updateAbnormalList, addRecordList, userInfoList);
                }
                XxlJobLogger.log("异常考勤确认周期监控 currentPage {},while循环结束", currentPage);
            }
        }

        XxlJobLogger.log("异常考勤确认周期监控|updateFormList:{},addAttrList:{},updateAbnormalList:{},addRecordList:{}", updateFormList.size(), addAttrList.size(), updateAbnormalList.size(), addRecordList.size());
        XxlJobLogger.log("异常考勤确认周期监控|耗时：{}", System.currentTimeMillis() - startTime);
        //落库
        abnormalAttendanceManage.updateApprovalFormConfirmCycle(updateFormList, addAttrList, updateAbnormalList, addRecordList);

        return ReturnT.SUCCESS;
    }

    private void userAbnormalCycleHandler(List<AttendanceFormDO> updateFormList,
                                          List<AttendanceFormAttrDO> addAttrList,
                                          List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                          List<EmployeeAbnormalOperationRecordDO> addRecordList,
                                          List<UserInfoDO> userInfoList) {
        List<Long> userIdList = userInfoList.stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());

        int abnormalCurrentPage = 1;
        int abnormalPageSize = 1000;
        Page<EmployeeAbnormalAttendanceDO> abnormalPage = PageHelper.startPage(abnormalCurrentPage, abnormalPageSize, true);
        PageInfo<EmployeeAbnormalAttendanceDO> abnormalPageInfo = abnormalPage.doSelectPageInfo(() -> abnormalAttendanceManage.selectAbnormalByUserIdList(userIdList));
        // 总记录数
        List<EmployeeAbnormalAttendanceDO> abnormalInfoList = abnormalPageInfo.getList();
        if (CollectionUtils.isNotEmpty(abnormalInfoList)) {
            XxlJobLogger.log("异常考勤确认周期监控 abnormalInfoList:{}", abnormalInfoList.size());
            handlerAbnormalAttendance(abnormalInfoList, updateAbnormalList, addRecordList, updateFormList, addAttrList);
        }
        while (abnormalCurrentPage < abnormalPageInfo.getPages()) {
            abnormalCurrentPage++;
            XxlJobLogger.log("异常考勤确认周期监控,abnormalCurrentPage: {},abnormalPage: {}", abnormalCurrentPage, abnormalPageInfo.getPages());

            abnormalPage = PageHelper.startPage(abnormalCurrentPage, abnormalPageSize, true);
            abnormalPageInfo = abnormalPage.doSelectPageInfo(() -> abnormalAttendanceManage.selectAbnormalByUserIdList(userIdList));
            abnormalInfoList = abnormalPageInfo.getList();
            if (CollUtil.isNotEmpty(abnormalInfoList)) {
                handlerAbnormalAttendance(abnormalInfoList, updateAbnormalList, addRecordList, updateFormList, addAttrList);
            }
            XxlJobLogger.log("异常考勤确认周期监控 abnormalCurrentPage {},while循环结束", abnormalCurrentPage);
        }
    }

    /**
     * 处理异常考勤数据
     *
     * @param abnormalAttendanceDOList 异常考勤数据
     * @param updateAbnormalList       待更新的异常考勤数据
     * @param addRecordList            待添加的异常操作记录
     * @param updateFormList           待更新的审批单
     * @param addAttrList              待添加的审批单属性
     */
    private void handlerAbnormalAttendance(List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList,
                                           List<EmployeeAbnormalAttendanceDO> updateAbnormalList,
                                           List<EmployeeAbnormalOperationRecordDO> addRecordList,
                                           List<AttendanceFormDO> updateFormList,
                                           List<AttendanceFormAttrDO> addAttrList) {

        // 过滤在灰度名单的人员
        List<Long> originalUserIdList = abnormalAttendanceDOList.stream()
                .map(EmployeeAbnormalAttendanceDO::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 查询在灰度名单的人员
        Map<Long, Boolean> userMigrationMap;

        try {
            userMigrationMap = migrationService.verifyUsersIsEnableNewAttendance(originalUserIdList);
        } catch (Exception e) {
            log.error("批量验证用户迁移状态异常", e);
            XxlJobLogger.log("迁移验证异常，跳过处理: {}", e.getMessage());
            return;
        }

        // 统计启用新系统的用户
        List<Long> enableNewAttendanceUserIdList = userMigrationMap.entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        int enableNewAttendanceUserSize = enableNewAttendanceUserIdList.size();
        XxlJobLogger.log("用户迁移状态统计 - 总数: {}, 启用新系统: {}, 未启用: {}",
                originalUserIdList.size(), enableNewAttendanceUserSize, (originalUserIdList.size() - enableNewAttendanceUserSize));

        if (enableNewAttendanceUserSize == 0) {
            XxlJobLogger.log("没有启用新考勤系统的用户，跳过处理");
            return;
        }

        // 过滤启用新系统的用户的异常考勤
        abnormalAttendanceDOList = abnormalAttendanceDOList.stream()
                .filter(i -> enableNewAttendanceUserIdList.contains(i.getUserId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(abnormalAttendanceDOList)) {
            XxlJobLogger.log("异常考勤确认周期监控，当前人员全部都在灰度人员里，无异常考勤要处理");
            return;
        }

        List<Long> abnormalIdList = abnormalAttendanceDOList.stream()
                .map(EmployeeAbnormalAttendanceDO::getId)
                .collect(Collectors.toList());
        List<AttendanceFormRelationDO> relationDOList = attendanceFormManage.selectRelationByRelationIdList(abnormalIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRelationType(), ApplicationRelationTypeEnum.ABNORMAL.getCode()))
                .collect(Collectors.toList());

        List<Long> formIdList = relationDOList.stream()
                .map(AttendanceFormRelationDO::getFormId)
                .collect(Collectors.toList());

        List<AttendanceFormDO> formDOList = attendanceFormManage.selectByIdList(formIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getFormStatus(), FormStatusEnum.IN_REVIEW.getCode()))
                .collect(Collectors.toList());

        log.info("异常考勤确认周期监控|abnormalAttendanceDOList:{}", abnormalAttendanceDOList.size());
        XxlJobLogger.log("异常考勤确认周期监控|abnormalAttendanceDOList:{}", abnormalAttendanceDOList.size());
        for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : abnormalAttendanceDOList) {
            //获取用户的考勤周期
            AttendanceCycleConfigDO attendanceCycleConfigDO = null;
            try {
                attendanceCycleConfigDO = attendanceCycleConfigService.getUserAttendanceCycleConfig(abnormalAttendanceDO.getUserId());
            } catch (Exception e) {
                log.info("异常考勤确认周期监控|查询用户的考勤周期发生异常,userId:{}", abnormalAttendanceDO.getUserId(), e);
                XxlJobLogger.log("异常考勤确认周期监控|查询用户的考勤周期发生异常,userId:{},{}", abnormalAttendanceDO.getUserId(), e.getMessage());
                continue;
            }
            if (ObjectUtil.isNull(attendanceCycleConfigDO)) {
                log.info("异常考勤确认周期监控 用户考勤周期不存在,userId:{}", abnormalAttendanceDO.getUserId());
                XxlJobLogger.log("异常考勤确认周期监控 用户考勤周期不存在,userId:{}", abnormalAttendanceDO.getUserId());
                continue;
            }
            Date nowDate = new Date();
            // 获取是否需要确认标志
            Boolean tag = confirmCycleCheck(nowDate, attendanceCycleConfigDO,
                    DateUtil.beginOfDay(DateUtil.parse(abnormalAttendanceDO.getDayId().toString(), "yyyyMMdd")));
            log.info("异常考勤确认周期监控 确认周期校验：{}", tag);
            XxlJobLogger.log("异常考勤确认周期监控 确认周期校验：{}", tag);
            if (tag) {
                XxlJobLogger.log("异常考勤确认周期监控| 异常在周期内，不处理");
                continue;
            }
            //待处理/已驳回/审核中
            if (StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.UN_PROCESSED.getCode())
                    || StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.REJECT.getCode())
                    || StringUtils.equalsIgnoreCase(abnormalAttendanceDO.getStatus(), AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                String oldStatus = abnormalAttendanceDO.getStatus();
                //需要确认异常
                abnormalAttendanceDO.setStatus(AbnormalAttendanceStatusEnum.EXPIRED.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalAttendanceDO);
                updateAbnormalList.add(abnormalAttendanceDO);
                //添加对异常的操作记录
                EmployeeAbnormalOperationRecordDO abnormalOperationRecordDO = new EmployeeAbnormalOperationRecordDO();
                abnormalOperationRecordDO.setId(IdWorkerUtil.getId());
                abnormalOperationRecordDO.setAbnormalId(abnormalAttendanceDO.getId());
                abnormalOperationRecordDO.setOperationType(AbnormalOperationTypeEnum.ABNORMAL_EXPIRED.getCode());
                BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalOperationRecordDO);
                addRecordList.add(abnormalOperationRecordDO);

                if (!StringUtils.equalsIgnoreCase(oldStatus, AbnormalAttendanceStatusEnum.IN_REVIEW.getCode())) {
                    continue;
                }

                //针对审核中的特殊处理
                List<AttendanceFormRelationDO> existAbnormalRelationDOList = relationDOList.stream()
                        .filter(item -> item.getRelationId().equals(abnormalAttendanceDO.getId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existAbnormalRelationDOList)) {
                    continue;
                }
                List<AttendanceFormDO> existAbnormalFormDOList = formDOList.stream()
                        .filter(item -> item.getId().equals(existAbnormalRelationDOList.get(0).getFormId()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(existAbnormalFormDOList)) {
                    continue;
                }
                AttendanceFormDO formDO = existAbnormalFormDOList.get(0);
                formDO.setFormStatus(FormStatusEnum.CANCEL.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(formDO);
                updateFormList.add(formDO);
                //加个备注
                AttendanceFormAttrDO attendanceFormAttrDO = new AttendanceFormAttrDO();
                attendanceFormAttrDO.setId(IdWorkerUtil.getId());
                attendanceFormAttrDO.setFormId(formDO.getId());
                attendanceFormAttrDO.setAttrKey(ApplicationFormAttrKeyEnum.terminatedReason.getCode());
                attendanceFormAttrDO.setAttrValue("异常考勤确认周期监控取消的单据");
                BaseDOUtil.fillDOInsertByUsrOrSystem(attendanceFormAttrDO);
                addAttrList.add(attendanceFormAttrDO);
                if (formDO.getApprovalId() != null) {
                    try {
                        rpcBpmApprovalClient.backApply(formDO.getApprovalId());
                        log.info("异常考勤确认周期监控|审批单ID:{}撤销", formDO.getApprovalId());
                        XxlJobLogger.log("审批单ID:{}撤销", formDO.getApprovalId());
                    } catch (Exception e) {
                        log.info("异常考勤确认周期监控|单据的approvalId不存在,applicationCode:{}", formDO.getApplicationCode());
                        XxlJobLogger.log("异常考勤确认周期监控|单据的approvalId不存在,applicationCode:{}", formDO.getApplicationCode());
                    }
                }
            }
        }
    }

    private Boolean confirmCycleCheck(Date nowDate, AttendanceCycleConfigDO attendanceCycleConfigDO, DateTime specificTime) {
        // 区分月维度、周维度,获取请假可请范围
        Long specificTimeDayId = DateHelper.getDayId(specificTime);

        // 如果参数不符合规则，直接返回true
        if (ObjectUtil.isNull(nowDate) || ObjectUtil.isNull(attendanceCycleConfigDO) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleStart()) ||
                ObjectUtil.isEmpty(attendanceCycleConfigDO.getCycleEnd()) ||
                ObjectUtil.isNull(attendanceCycleConfigDO.getAbnormalExpired())) {
            return true;
        }

        Integer cycleType = attendanceCycleConfigDO.getCycleType();
        String cycleEnd = attendanceCycleConfigDO.getCycleEnd();

        CycleTypeEnum cycleTypeEnum = ObjectUtil.equal(cycleType, AttendanceCycleTypeEnum.WEEK.getType()) ?
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.WEEK.name()) :
                CycleTypeEnum.getInstance(AttendanceCycleTypeEnum.MONTH.name());

        if (ObjectUtil.isNull(cycleTypeEnum)) {
            return true;
        }

        Integer abnormalExpired = cycleTypeEnum.getActualAbnormalExpired(
                nowDate,
                attendanceCycleConfigDO.getCycleStart(),
                attendanceCycleConfigDO.getCycleEnd(),
                attendanceCycleConfigDO.getAbnormalExpired()
        );

        // 周期偏移日期
        Date offsetCycleEndDate = cycleTypeEnum.getCycleDate(nowDate, cycleEnd, abnormalExpired);
        Long offsetCycleEndDayId = DateHelper.getDayId(offsetCycleEndDate);

        // 指定时间小于等于周期偏移日期。需要处理掉
        if (specificTimeDayId.compareTo(offsetCycleEndDayId) <= 0) {
            return false;
        }
        return true;
    }


    @Data
    private static class HandlerParam {
        /**
         * 国家
         */
        private String countryList;
        /**
         * 用户编码
         */
        private String userCodes;
    }
}
