package com.imile.attendance.abnormal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.abnormal.dto.DayItemInfoDTO;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.DateHelper;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 考勤相关时长计算
 *
 * <AUTHOR>
 * @since 2025/6/20
 */
@Slf4j
@Service
public class AttendanceMinuteCalculateService {

    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;

    /**
     * 根据班次时段计算得到当天考勤班次时段内的具体每个时间
     *
     * @param attendanceDayId  考勤日
     * @param itemConfigDO     当前班次时段
     * @param itemConfigDOList 班次时段集合
     * @param punchRecordList  打卡记录集合
     * @return 计算后的当日班次时段时间
     */
    public DayItemConfigDateDTO buildDayItemConfigDateDTO(Long attendanceDayId,
                                                          PunchClassItemConfigDO itemConfigDO,
                                                          List<PunchClassItemConfigDO> itemConfigDOList,
                                                          List<UserPunchRecordBO> punchRecordList) {
        DayItemConfigDateDTO itemConfigDateDTO = new DayItemConfigDateDTO();

        //获取当前时刻的正常时间
        DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(attendanceDayId, itemConfigDO.getId(), itemConfigDOList);

        if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
            return null;
        }

        //获取打卡时间的所有点
        //最早上班打卡时间
        Date earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
        //上班时间早于最早打卡时间，跨天
        Date punchInTime;
        if (itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime())) {
            punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(earliestPunchInTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getPunchInTime()));
        } else {
            punchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(earliestPunchInTime), DateHelper.formatHHMMSS(itemConfigDO.getPunchInTime()));
        }
        itemConfigDateDTO.setPunchInTime(punchInTime);
        itemConfigDateDTO.setEarliestPunchInTime(earliestPunchInTime);

        //最晚上班打卡时间
        Date latestPunchInTime;
        if (itemConfigDO.getLatestPunchInTime().before(itemConfigDO.getPunchInTime())) {
            latestPunchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(punchInTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getLatestPunchInTime()));
        } else {
            latestPunchInTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(punchInTime), DateHelper.formatHHMMSS(itemConfigDO.getLatestPunchInTime()));
        }
        itemConfigDateDTO.setLatestPunchInTime(latestPunchInTime);

        //休息开始时间
        Date restStartTime = null;
        Date restEndTime = null;
        if (itemConfigDO.getRestStartTime() != null) {
            if (itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime())) {
                restStartTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(punchInTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getRestStartTime()));
            } else {
                restStartTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(punchInTime), DateHelper.formatHHMMSS(itemConfigDO.getRestStartTime()));
            }
            itemConfigDateDTO.setRestStartTime(restStartTime);
            if (itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime())) {
                restEndTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(restStartTime, 1)), DateHelper.formatHHMMSS(itemConfigDO.getRestEndTime()));
            } else {
                restEndTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(restStartTime), DateHelper.formatHHMMSS(itemConfigDO.getRestEndTime()));
            }
            itemConfigDateDTO.setRestEndTime(restEndTime);
        }

        //最晚下班打卡时间
        Date latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();
        //下班时间
        Date punchOutTime;
        if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
            punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(DateHelper.pushDate(latestPunchOutTime, -1)), DateHelper.formatHHMMSS(itemConfigDO.getPunchOutTime()));
        } else {
            punchOutTime = DateHelper.concatDateAndTime(DateHelper.formatYYYYMMDD(latestPunchOutTime), DateHelper.formatHHMMSS(itemConfigDO.getPunchOutTime()));
        }
        itemConfigDateDTO.setPunchOutTime(punchOutTime);
        itemConfigDateDTO.setLatestPunchOutTime(latestPunchOutTime);

        BigDecimal itemTotalMinutes = BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE));
        if (restStartTime != null) {
            itemTotalMinutes = itemTotalMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
        }
        itemConfigDateDTO.setItemTotalMinutes(itemTotalMinutes);

        // 获取该班次最早上班打卡时间和最晚上班打卡时间之间的打卡记录
        Date finalLatestPunchInTime = latestPunchInTime;
        List<UserPunchRecordBO> punchRecordBOList = punchRecordList.stream()
                .filter(item -> item.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && item.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1)
                .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime)).collect(Collectors.toList());
        // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
        if (CollUtil.isNotEmpty(punchRecordBOList)) {
            // 获取最新的一个打卡记录
            UserPunchRecordBO userPunchRecordDTO = punchRecordBOList.get(0);
            // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
            if (userPunchRecordDTO.getFormatPunchTime().compareTo(punchInTime) > -1 && userPunchRecordDTO.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1) {
                // 获取两个时间相差分钟数
                long betweenMinutes = DateUtil.between(userPunchRecordDTO.getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
                itemConfigDateDTO.setBetweenMinutes(betweenMinutes);
            }
        }
        return itemConfigDateDTO;
    }


    /**
     * 筛选组装得到当天外勤或请假的单据
     *
     * @param userPassFormBOList 用户考勤当天审批通过的单据集合
     * @return List<DayAttendanceHandlerFormDTO>
     */
    public List<DayAttendanceHandlerFormDTO> dayFormInfoBuild(List<AttendanceFormDetailBO> userPassFormBOList, List<Long> usedFormIdList) {
        if (CollectionUtils.isEmpty(userPassFormBOList)) {
            return Collections.emptyList();
        }
        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = new ArrayList<>();
        for (AttendanceFormDetailBO attendanceFormDetailBO : userPassFormBOList) {
            AttendanceFormDO formDO = attendanceFormDetailBO.getFormDO();
            if (usedFormIdList.contains(formDO.getId())) {
                continue;
            }
            List<AttendanceFormAttrDO> userPassFormAttrDOList = attendanceFormDetailBO.getAttrDOList();
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())) {
                List<AttendanceFormAttrDO> leaveStartDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveEndDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> configIdDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.configID.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveNameDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveType.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(leaveStartDateDO) || CollectionUtils.isEmpty(leaveEndDateDO) || CollectionUtils.isEmpty(leaveNameDO)) {
                    continue;
                }

                Date leaveStartDate = DateHelper.parseYYYYMMDDHHMMSS(leaveStartDateDO.get(0).getAttrValue());
                Date leaveEndDate = DateHelper.parseYYYYMMDDHHMMSS(leaveEndDateDO.get(0).getAttrValue());

                Long leaveStartDayId = DateHelper.getDayId(leaveStartDate);
                Long leaveEndDayId = DateHelper.getDayId(leaveEndDate);

                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                if (CollectionUtils.isNotEmpty(configIdDO) && Objects.nonNull(configIdDO.get(0).getAttrValue())) {
                    dayAttendanceHandlerFormDTO.setConfigId(Long.valueOf(configIdDO.get(0).getAttrValue()));
                }
                dayAttendanceHandlerFormDTO.setLeaveType(leaveNameDO.get(0).getAttrValue());
                dayAttendanceHandlerFormDTO.setStartTime(leaveStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(leaveStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(leaveEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(leaveEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }

            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())) {
                List<AttendanceFormAttrDO> outOfOfficeStartDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode()))
                        .collect(Collectors.toList());
                List<AttendanceFormAttrDO> outOfOfficeEndDateDO = userPassFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
                    continue;
                }

                Date outOfOfficeStartDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDateDO.get(0).getAttrValue());
                Date outOfOfficeEndDate = DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDateDO.get(0).getAttrValue());

                Long outOfOfficeStartDayId = DateHelper.getDayId(outOfOfficeStartDate);
                Long outOfOfficeEndDayId = DateHelper.getDayId(outOfOfficeEndDate);

                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(outOfOfficeStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(outOfOfficeStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(outOfOfficeEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(outOfOfficeEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
        }
        return handlerFormDTOList.stream().sorted(Comparator.comparing(DayAttendanceHandlerFormDTO::getStartTime)).collect(Collectors.toList());
    }


    /**
     * 根据单据时间结合上下班打卡时间和可能存在得休息时间计算请假时长
     * 组装有效的申请单据
     *
     * @param handlerFormDTO    今日外勤或请假的单据
     * @param punchInTime       上班时间
     * @param punchOutTime      下班时间
     * @param restStartTime     休息开始时间
     * @param restEndTime       休息结束时间
     * @param betweenMinutes    弹性时长
     * @param filterFormDTOList 计算后有效的外勤或请假单据
     * @return 实际请假的时长
     */
    public BigDecimal shiftDayLeaveMinuteHandler(DayAttendanceHandlerFormDTO handlerFormDTO,
                                                 Date punchInTime,
                                                 Date punchOutTime,
                                                 Date restStartTime,
                                                 Date restEndTime,
                                                 long betweenMinutes,
                                                 List<DayAttendanceHandlerFormDTO> filterFormDTOList) {
        BigDecimal leaveMinutes = BigDecimal.ZERO;
        //没有交集
        if (handlerFormDTO.getEndTime().compareTo(punchInTime) < 1) {
            return leaveMinutes;
        }

        // 这边根据上班时间来设置下班时间是正常的班次时间还是弹性后的时间
        Date actualDayPunchEndTime = punchOutTime;
        if (betweenMinutes != 0) {
            actualDayPunchEndTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
        }

        if (handlerFormDTO.getStartTime().compareTo(actualDayPunchEndTime) > -1) {
            return leaveMinutes;
        }

        Date actualLeaveStartTime;
        Date actualLeaveEndTime;
        DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = BeanUtils.convert(handlerFormDTO, DayAttendanceHandlerFormDTO.class);
        //请假开始时间小于正常上班时间
        if (handlerFormDTO.getStartTime().compareTo(punchInTime) < 1) {
            actualLeaveStartTime = punchInTime;
            actualLeaveEndTime = handlerFormDTO.getEndTime();
            //请假时间大于该时刻的下班时间
            if (handlerFormDTO.getEndTime().compareTo(punchOutTime) > -1) {
                actualLeaveEndTime = punchOutTime;
            }
            dayAttendanceHandlerFormDTO.setStartTime(actualLeaveStartTime);
            dayAttendanceHandlerFormDTO.setEndTime(actualLeaveEndTime);
            filterFormDTOList.add(dayAttendanceHandlerFormDTO);
            leaveMinutes = BigDecimal.valueOf(DateUtil.between(actualLeaveStartTime, actualLeaveEndTime, DateUnit.MINUTE));
            //看看有没有休息时间，有旧减去休息时间
            if (restStartTime != null) {
                if (actualLeaveEndTime.compareTo(restStartTime) < 1) {
                    return leaveMinutes;
                }
                if (actualLeaveEndTime.compareTo(restEndTime) > -1) {
                    leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
                    return leaveMinutes;
                }
                //卡住休息时间中间
                leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, actualLeaveEndTime, DateUnit.MINUTE)));
                return leaveMinutes;
            }
            return leaveMinutes;
        }

        //请假开始时间大于正常上班时间
        actualLeaveStartTime = handlerFormDTO.getStartTime();
        actualLeaveEndTime = handlerFormDTO.getEndTime();
        //请假结束时间大于该时刻的下班时间
        if (handlerFormDTO.getEndTime().compareTo(punchOutTime) > -1) {
            actualLeaveEndTime = punchOutTime;
            // 如果betweenMinutes不为0 并且请假结束时间大于正常下班时间，说明请假结束时间需要弹性调整
            if (handlerFormDTO.getEndTime().compareTo(punchOutTime) > 0) {
                if (handlerFormDTO.getEndTime().compareTo(actualDayPunchEndTime) > 0) {
                    actualLeaveEndTime = actualDayPunchEndTime;
                } else {
                    actualLeaveEndTime = handlerFormDTO.getEndTime();
                }
            }
        }

        leaveMinutes = BigDecimal.valueOf(DateUtil.between(actualLeaveStartTime, actualLeaveEndTime, DateUnit.MINUTE));
        dayAttendanceHandlerFormDTO.setStartTime(actualLeaveStartTime);
        dayAttendanceHandlerFormDTO.setEndTime(actualLeaveEndTime);
        filterFormDTOList.add(dayAttendanceHandlerFormDTO);
        //看看有没有休息时间，有旧减去休息时间
        if (restStartTime != null) {
            if (actualLeaveStartTime.compareTo(restEndTime) > -1) {
                return leaveMinutes;
            }
            if (actualLeaveEndTime.compareTo(restStartTime) < 1) {
                return leaveMinutes;
            }
            //休息时间完全被请假时间包含
            if (actualLeaveStartTime.compareTo(restStartTime) < 1 && actualLeaveEndTime.compareTo(restEndTime) > -1) {
                leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
                return leaveMinutes;
            }
            //请假时间完全被休息时间包含
            if (actualLeaveStartTime.compareTo(restStartTime) > -1 && actualLeaveEndTime.compareTo(restEndTime) < 1) {
                leaveMinutes = BigDecimal.ZERO;
                return leaveMinutes;
            }
            //左交集
            if (actualLeaveStartTime.compareTo(restStartTime) < 1) {
                leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, actualLeaveEndTime, DateUnit.MINUTE)));
                return leaveMinutes;
            }
            //右交集
            leaveMinutes = leaveMinutes.subtract(BigDecimal.valueOf(DateUtil.between(actualLeaveStartTime, restEndTime, DateUnit.MINUTE)));
        }
        return leaveMinutes;
    }


    /**
     * 根据打卡时间和单据中时间计算组装不含请假或外勤的每一段时间
     *
     * @param dayItemInfoList    打卡记录不包含单据时间范围的时间段集合
     * @param handlerFormDTOList 当日有效单据集合
     * @param punchRecordBOList  打卡时间集合
     */
    public void multiplePunchHandler(List<DayItemInfoDTO> dayItemInfoList,
                                     List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                     List<UserPunchRecordBO> punchRecordBOList) {
        Date beginPunchTime = punchRecordBOList.get(0).getFormatPunchTime();
        Date endPunchTime = punchRecordBOList.get(punchRecordBOList.size() - 1).getFormatPunchTime();
        //比较请假时间和单据起始截止打卡时间的交集
        //整体在请假时间左边，没有交集
        if (endPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) < 1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(beginPunchTime);
            dayItemInfoDTO.setEndItemTime(endPunchTime);
            dayItemInfoList.add(dayItemInfoDTO);
            return;
        }

        //整体在请假时间右边，没有交集
        if (beginPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) > -1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(beginPunchTime);
            dayItemInfoDTO.setEndItemTime(endPunchTime);
            dayItemInfoList.add(dayItemInfoDTO);
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 第一种情况：
         * 若单据只有一段 9:00 - 17:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 9:00 , 17:00 - 18:00;
         * 若单据只有一段 10:00 - 18:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 10:00 , 18:00 - 18:00;
         * 若单据只有一段 09:00 - 18:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 09:00 , 18:00 - 18:00;
         *
         * 第二种情况：
         * 若单据存在多段 9:00 - 11:00 , 12:00 - 14:00 , 15:00 - 18:00
         * 则dayItemInfoList 包含4段信息 9:00 - 9:00 , 11:00 - 12:00, 14:00 - 15:00, 18:00 - 18:00;
         *
         */
        //打卡完全包含请假（可能完全包含）
        if (beginPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) < 1
                && endPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) > -1) {
            List<Date> punchTimeList = new ArrayList<>();
            punchTimeList.add(beginPunchTime);
            for (DayAttendanceHandlerFormDTO formDTO : handlerFormDTOList) {
                punchTimeList.add(formDTO.getStartTime());
                punchTimeList.add(formDTO.getEndTime());
            }
            punchTimeList.add(endPunchTime);
            for (int i = 0; i < punchTimeList.size(); ) {
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(punchTimeList.get(i));
                i = i + 1;
                dayItemInfoDTO.setEndItemTime(punchTimeList.get(i));
                i = i + 1;
                dayItemInfoList.add(dayItemInfoDTO);
            }
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 若单据仅一段说明当天完全请假;
         *
         * 若单据存在多段 6:00 - 8:00 , 10:00 - 13:00 , 15:00 - 18:00;
         * 则dayItemInfoList 包含2段信息 9:00 - 10:00 , 13:00 - 15:00;
         *
         * 若单据存在多段 6:00 - 11:00 , 13:00 - 19:00;
         * 则dayItemInfoList 包含1段信息 11:00 - 13:00;
         *
         */
        //请假完全包含打卡
        if (beginPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) > -1
                && endPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) < 1) {
            if (handlerFormDTOList.size() == 1) {
                return;
            }
            for (int i = 0; i < handlerFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) < 1) {
                    return;
                }
                int k = i + 1;
                if (beginPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) < 1) {
                    beginPunchTime = handlerFormDTOList.get(i).getEndTime();
                    if (endPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                        DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                        dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoDTO);
                        return;
                    }
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(k).getStartTime());
                    dayItemInfoList.add(dayItemInfoDTO);
                    beginPunchTime = handlerFormDTOList.get(k).getStartTime();
                    continue;
                }

                if (beginPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) > -1) {
                    if (endPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                        DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                        dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoDTO);
                        return;
                    }
                    if (beginPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                        DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                        dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(k).getStartTime());
                        dayItemInfoList.add(dayItemInfoDTO);
                        beginPunchTime = handlerFormDTOList.get(k).getStartTime();
                        continue;
                    }
                    beginPunchTime = handlerFormDTOList.get(k).getStartTime();
                }
            }
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 若单据仅一段 10:00 - 19:00;
         * 则dayItemInfoList 包含1段信息 9:00 - 10:00;
         *
         * 若单据存在多段 11:00 - 12:00 , 14:00 - 19:00;
         * 则dayItemInfoList 包含2段信息 09:00 - 11:00 , 12:00 - 14:00;
         *
         * 若单据存在多段 10:00 - 11:00 , 13:00 - 15:00, 19:00 - 20:00;
         * 则dayItemInfoList 包含3段信息 09:00 - 10:00 , 11:00 - 13:00 , 15:00 - 18:00;
         *
         */
        //最早打卡时间在请假开始时间之前
        if (beginPunchTime.compareTo(handlerFormDTOList.get(0).getStartTime()) < 1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(beginPunchTime);
            dayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(0).getStartTime());
            dayItemInfoList.add(dayItemInfoDTO);
            for (int i = 0; i < handlerFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(handlerFormDTOList.get(i).getEndTime()) < 1) {
                    return;
                }
                int k = i + 1;
                if (endPunchTime.compareTo(handlerFormDTOList.get(k).getStartTime()) < 1) {
                    DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                    betweenDayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(i).getEndTime());
                    betweenDayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoList.add(betweenDayItemInfoDTO);
                    return;
                }
                DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                betweenDayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(i).getEndTime());
                betweenDayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(k).getStartTime());
                dayItemInfoList.add(betweenDayItemInfoDTO);
            }
            return;
        }

        /**
         * 假设打卡时间段为 9:00 - 18:00
         * 若单据仅一段 6:00 - 15:00;
         * 则dayItemInfoList 包含1段信息 15:00 - 18:00;
         *
         * 若单据存在多段 6:00 - 8:00 , 9:00 - 13:00;
         * 则dayItemInfoList 包含1段信息 13:00 - 18:00;
         *
         * 若单据存在多段 6:00 - 10:00 , 13:00 - 15:00;
         * 则dayItemInfoList 包含2段信息 10:00 - 13:00 , 15:00 - 18:00;
         *
         */
        //最晚打卡时间在请假结束时间之后
        if (endPunchTime.compareTo(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime()) > -1) {
            DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
            dayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(handlerFormDTOList.size() - 1).getEndTime());
            dayItemInfoDTO.setEndItemTime(endPunchTime);
            dayItemInfoList.add(dayItemInfoDTO);
            for (int i = handlerFormDTOList.size() - 1; i >= 0; i--) {
                if (beginPunchTime.compareTo(handlerFormDTOList.get(i).getStartTime()) > -1) {
                    return;
                }
                int k = i - 1;
                if (beginPunchTime.compareTo(handlerFormDTOList.get(k).getEndTime()) > -1) {
                    DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                    betweenDayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    betweenDayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(i).getStartTime());
                    dayItemInfoList.add(betweenDayItemInfoDTO);
                    return;
                }
                DayItemInfoDTO betweenDayItemInfoDTO = new DayItemInfoDTO();
                betweenDayItemInfoDTO.setBeginItemTime(handlerFormDTOList.get(k).getEndTime());
                betweenDayItemInfoDTO.setEndItemTime(handlerFormDTOList.get(i).getStartTime());
                dayItemInfoList.add(betweenDayItemInfoDTO);
            }
        }
    }


    /**
     * 计算打卡的出勤时长,会过滤外勤、请假、休息时长
     *
     * @param punchAfterCardEmpty 下班后打卡打卡记录是否为空
     * @param restStartTime       休息开始时间
     * @param restEndTime         休息结束时间
     * @param beginPunchTime      最早打卡时间
     * @param endPunchTime        最晚打卡时间
     * @param dayItemInfoList     打卡范围时间集合
     * @param filterFormDTOList   用户外勤或请假通过的当日单据集合
     * @return 打卡出勤时长
     */
    public BigDecimal getPresentMinutes(boolean punchAfterCardEmpty,
                                        Date restStartTime,
                                        Date restEndTime,
                                        Date beginPunchTime,
                                        Date endPunchTime,
                                        List<DayItemInfoDTO> dayItemInfoList,
                                        List<DayAttendanceHandlerFormDTO> filterFormDTOList) {
        if (CollectionUtils.isEmpty(dayItemInfoList)){
            dayItemInfoList = new ArrayList<>();
        }
        if (punchAfterCardEmpty) {
            for (int i = 0; i < filterFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoList.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                dayItemInfoDTO.setEndItemTime(filterFormDTOList.get(i).getStartTime());
                dayItemInfoList.add(dayItemInfoDTO);
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    beginPunchTime = filterFormDTOList.get(i).getEndTime();
                    if (i == filterFormDTOList.size() - 1) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }
        } else {
            for (int i = filterFormDTOList.size() - 1; i >= 0; i--) {
                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoList.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(filterFormDTOList.get(i).getEndTime());
                dayItemInfoDTO.setEndItemTime(endPunchTime);
                dayItemInfoList.add(dayItemInfoDTO);

                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    endPunchTime = filterFormDTOList.get(i).getStartTime();
                    if (i == 0) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoList.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }
        }
        return subtractRestTimeHandler(dayItemInfoList, restStartTime, restEndTime);
    }


    /**
     * 打卡出勤时长移除休息时间段
     *
     * @param dayItemInfoList 打卡范围时间集合
     * @param restStartTime   休息开始时间
     * @param restEndTime     休息结束时间
     * @return 过滤掉休息时间段的打卡出勤时长
     */
    public BigDecimal subtractRestTimeHandler(List<DayItemInfoDTO> dayItemInfoList, Date restStartTime, Date restEndTime) {
        BigDecimal presentMinutes = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(dayItemInfoList)) {
            return presentMinutes;
        }
        dayItemInfoList = dayItemInfoList.stream().filter(item -> item.getBeginItemTime().compareTo(item.getEndItemTime()) != 0).collect(Collectors.toList());
        if (restStartTime == null) {
            for (DayItemInfoDTO dayItemInfoDTO : dayItemInfoList) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
            return presentMinutes;
        }
        for (DayItemInfoDTO dayItemInfoDTO : dayItemInfoList) {
            if (dayItemInfoDTO.getEndItemTime().compareTo(restStartTime) < 1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restEndTime) > -1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restStartTime) > -1 && dayItemInfoDTO.getEndItemTime().compareTo(restEndTime) < 1) {
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restStartTime) < 1 && dayItemInfoDTO.getEndItemTime().compareTo(restEndTime) > -1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
                presentMinutes = presentMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getBeginItemTime().compareTo(restStartTime) < 1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(dayItemInfoDTO.getBeginItemTime(), restStartTime, DateUnit.MINUTE)));
                continue;
            }
            if (dayItemInfoDTO.getEndItemTime().compareTo(restEndTime) > -1) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(restEndTime, dayItemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
        }
        return presentMinutes;
    }
}
