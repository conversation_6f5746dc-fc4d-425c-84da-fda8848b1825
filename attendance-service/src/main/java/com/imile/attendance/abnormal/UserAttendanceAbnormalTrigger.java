package com.imile.attendance.abnormal;

import com.google.common.base.Throwables;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 触发用户考勤异常计算Trigger实现
 *
 * <AUTHOR>
 * @since 2025/8/5
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class UserAttendanceAbnormalTrigger {
    private final EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    private final AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    private final AttendanceGenerateService attendanceGenerateService;
    private final UserInfoDao userInfoDao;

    public void userShiftAbnormalCalculateHandler(List<Long> userIdList, Long dayId) {
        if (CollectionUtils.isEmpty(userIdList) || Objects.isNull(dayId)) {
            return;
        }
        try {
            //若当天的异常过滤已处理和已过期,重新计算
            List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = employeeAbnormalAttendanceDao.selectAbnormalAttendanceByDayIdList(userIdList, Collections.singletonList(dayId))
                    .stream()
                    .filter(abnormal -> !AbnormalAttendanceStatusEnum.TYPE_OF_PASS_OR_EXPIRED.contains(abnormal.getStatus()))
                    .collect(Collectors.toList());

            //查询当天正常表数据
            List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = attendanceEmployeeDetailDao.selectByUserId(userIdList, dayId);
            if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList) && CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
                return;
            }
            Set<Long> userIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(employeeAbnormalAttendanceDOList)) {
                userIds.addAll(employeeAbnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getUserId).collect(Collectors.toSet()));
            }
            if (CollectionUtils.isNotEmpty(attendanceEmployeeDetailDOList)) {
                userIds.addAll(attendanceEmployeeDetailDOList.stream().map(AttendanceEmployeeDetailDO::getUserId).collect(Collectors.toSet()));
            }
            String userCodes = userInfoDao.getByUserIds(new ArrayList<>(userIds))
                    .stream()
                    .map(UserInfoDO::getUserCode)
                    .collect(Collectors.joining(BusinessConstant.DEFAULT_DELIMITER));
            AttendanceCalculateHandlerDTO calculateHandlerDTO = AttendanceCalculateHandlerDTO
                    .builder()
                    .attendanceDayId(dayId)
                    .userCodes(userCodes)
                    .build();
            attendanceGenerateService.attendanceCalculateHandler(calculateHandlerDTO);
        } catch (Exception e) {
            log.error("排班触发异常计算：{}", Throwables.getStackTraceAsString(e));
        }
    }
}
