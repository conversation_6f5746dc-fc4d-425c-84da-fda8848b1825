package com.imile.attendance.cycleConfig.vo;

import lombok.Data;

/**
 * <AUTHOR> chen
 * @Date 2025/4/21 
 * @Description
 */
@Data
public class CycleDetailSelectVO {

    /**
     * 周期开始日期编码 如果是月，则值为1，2,3,4.....28,END_OF_MONTH表示月底
     * 如果是周，则值为 1,2,3,4,5,6,7表示周日
     */
    private String cycleStart;
    /**
     * 周期开始 描述
     */
    private String cycleStartDesc;
    /**
     * 周期结束编码
     */
    private String cycleEnd;
    /**
     * 周期结束 描述
     */
    private String cycleEndDesc;
}
