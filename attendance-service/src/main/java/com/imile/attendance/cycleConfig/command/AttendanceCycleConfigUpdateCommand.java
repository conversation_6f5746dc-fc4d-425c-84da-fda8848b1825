package com.imile.attendance.cycleConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "考勤周期修改入参")
public class AttendanceCycleConfigUpdateCommand extends AttendanceCycleConfigAddCommand {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long id;
}
