package com.imile.attendance.cycleConfig.mapstruct;

import com.imile.attendance.cycleConfig.vo.CycleDetailSelectVO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.cycleConfig.command.AttendanceCycleConfigAddCommand;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigDetailDTO;
import com.imile.attendance.cycleConfig.dto.AttendanceCycleConfigPageDTO;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.cycleConfig.enums.CycleTypeEnum;
import com.imile.attendance.cycleConfig.query.AttendanceCycleConfigListQuery;
import com.imile.attendance.cycleConfig.vo.CycleDetailSelectVO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigPageQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.StatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceCycleConfigMapstruct {

    AttendanceCycleConfigMapstruct INSTANCE = Mappers.getMapper(AttendanceCycleConfigMapstruct.class);

    // ------------------------ AddCommand -> Model 转换方法 ------------------------

    @Mapping(target = "recordVersion", ignore = true)
    @Mapping(target = "lastUpdUserName", ignore = true)
    @Mapping(target = "lastUpdUserCode", ignore = true)
    @Mapping(target = "lastUpdDate", ignore = true)
    @Mapping(target = "isDelete", ignore = true)
    @Mapping(target = "createUserName", ignore = true)
    @Mapping(target = "createUserCode", ignore = true)
    @Mapping(target = "createDate", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "cycleEnd", ignore = true)
    AttendanceCycleConfigDO toBaseModel(AttendanceCycleConfigAddCommand addCommand);


    default AttendanceCycleConfigDO toModel(AttendanceCycleConfigAddCommand addCommand,
                                            Long id) {
        AttendanceCycleConfigDO cycleConfigDO = toBaseModel(addCommand);
        cycleConfigDO.setId(id);
        CycleTypeEnum.CycleDetail cycleDetail = CycleTypeEnum.getCycleDetail(addCommand.getCycleTypeString(), addCommand.getCycleStart());
        cycleConfigDO.setCycleEnd(cycleDetail.getCycleEnd());
        cycleConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
        BaseDOUtil.fillDOInsert(cycleConfigDO);
        return cycleConfigDO;
    }

    // ------------------------ Model -> DetailDTO 转换方法 ------------------------

    @Mapping(target = "cycleTypeString", ignore = true)
    @Mapping(target = "cycleStartDesc", ignore = true)
    @Mapping(target = "cycleEndDesc", ignore = true)
    AttendanceCycleConfigDetailDTO toBaseDetail(AttendanceCycleConfigDO attendanceCycleConfig);


    default AttendanceCycleConfigDetailDTO toDetail(AttendanceCycleConfigDO attendanceCycleConfig) {
        AttendanceCycleConfigDetailDTO dto = toBaseDetail(attendanceCycleConfig);
        // 处理数据
        dto.setCycleTypeString(AttendanceCycleTypeEnum.getCycleTypeString(attendanceCycleConfig.getCycleType()));
        // 周期开始日期
        Optional.ofNullable(dto.getCycleStart())
                .map(cycleStart -> CycleTypeEnum.getCycleDetail(dto.getCycleTypeString(), cycleStart))
                .ifPresent(cycleDetail -> dto.setCycleStartDesc(cycleDetail.getCycleStartDesc()));
        // 周期结束日期
        Optional.ofNullable(dto.getCycleEnd())
                .map(cycleEnd -> CycleTypeEnum.getCycleDetail(dto.getCycleTypeString(), cycleEnd))
                .ifPresent(cycleDetail -> dto.setCycleEndDesc(cycleDetail.getCycleStartDesc()));
        return dto;
    }

    // ------------------------ Model -> PageDTO 转换方法 ------------------------

    @Mapping(target = "cycleTypeString", ignore = true)
    @Mapping(target = "cycleStartDesc", ignore = true)
    @Mapping(target = "cycleEndDesc", ignore = true)
    AttendanceCycleConfigPageDTO toBasePageDTO(AttendanceCycleConfigDO cycleConfigDO);

    default AttendanceCycleConfigPageDTO toPageDTO(AttendanceCycleConfigDO cycleConfigDO) {
        AttendanceCycleConfigPageDTO pageDTO = toBasePageDTO(cycleConfigDO);
        pageDTO.setCycleTypeString(AttendanceCycleTypeEnum.getCycleTypeString(pageDTO.getCycleType()));
        // 周期开始日期
        Optional.ofNullable(pageDTO.getCycleStart()).map(cycleStart -> CycleTypeEnum.getCycleDetail(pageDTO.getCycleTypeString(), cycleStart))
                .ifPresent(cycleDetail -> pageDTO.setCycleStartDesc(cycleDetail.getCycleStartDesc()));
        // 周期结束日期
        Optional.ofNullable(pageDTO.getCycleEnd()).map(cycleEnd -> CycleTypeEnum.getCycleDetail(pageDTO.getCycleTypeString(), cycleEnd))
                .ifPresent(cycleDetail -> pageDTO.setCycleEndDesc(cycleDetail.getCycleStartDesc()));
        return pageDTO;
    }

    default List<AttendanceCycleConfigPageDTO> toPageDTO(List<AttendanceCycleConfigDO> cycleConfigDOList) {
        return cycleConfigDOList.stream()
                .map(this::toPageDTO)
                .collect(Collectors.toList());
    }

    // ------------------------ ListQuery -> PageQuery 转换方法 ------------------------

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "pageNum", ignore = true)
    @Mapping(target = "countryList", ignore = true)
    AttendanceCycleConfigPageQuery toPageQuery(AttendanceCycleConfigListQuery listQuery);


    // ------------------------ any -> vo 转换方法 ------------------------

    CycleDetailSelectVO toCycleDetailSelectVO(CycleTypeEnum.CycleDetail cycleDetail);

    List<CycleDetailSelectVO> toCycleDetailSelectVO(List<CycleTypeEnum.CycleDetail> cycleDetailList);



//    AttendanceCycleConfigPageVO toPageVO(AttendanceCycleConfigPageDTO cycleConfigPageDTO);
//
//    List<AttendanceCycleConfigPageVO> toPageVO(List<AttendanceCycleConfigPageDTO> cycleConfigPageDTOList);
//
//    default PaginationResult<AttendanceCycleConfigPageVO> toPageVO(PaginationResult<AttendanceCycleConfigPageDTO> cycleConfigPageDTOList) {
//        PaginationResult<AttendanceCycleConfigPageVO> result = new PaginationResult<>();
//        result.setPagination(cycleConfigPageDTOList.getPagination());
//        result.setResults(toPageVO(cycleConfigPageDTOList.getResults()));
//        return result;
//    }

}
