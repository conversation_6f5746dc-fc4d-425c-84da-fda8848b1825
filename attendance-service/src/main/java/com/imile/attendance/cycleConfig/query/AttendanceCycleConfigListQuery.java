package com.imile.attendance.cycleConfig.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AttendanceCycleConfigListQuery extends ResourceQuery {

    /**
     * 国家
     */
    private String country;

    /**
     * 状态
     */
    private String status;
}
