package com.imile.attendance.cycleConfig.command;

import com.imile.common.constant.ValidCodeConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR> chen
 * @Date 2025/2/24
 * @Description
 */
@Data
public class AttendanceCycleConfigAddCommand {

    /**
     * 常驻地国家
     */
    @ApiModelProperty(value = "常驻地国家")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String country;

    /**
     * 考勤周期类型：1:月，2:周
     */
    @ApiModelProperty(value = "考勤周期类型：1:月，2:周，3:自定义")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer cycleType;

    /**
     * 翻译考勤周期类型：1:月，2:周，不需要传
     */
    @ApiModelProperty(value = "翻译考勤周期类型：1:月，2:周，不需要传")
    private String cycleTypeString;

    /**
     * 周期开始
     */
    @ApiModelProperty(value = "周期开始：必填")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK)
    private String cycleStart;

    /**
     * 考勤异常过期设置：比如2，不包含本周期，往前推2个周期。
     */
    @ApiModelProperty(value = "考勤异常过期设置：比如2，不包含本周期，往前推2个周期。")
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Integer abnormalExpired;
}
