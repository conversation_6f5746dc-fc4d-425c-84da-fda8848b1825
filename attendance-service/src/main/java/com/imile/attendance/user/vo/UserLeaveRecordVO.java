package com.imile.attendance.user.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserLeaveRecordVO implements Serializable {
    private static final long serialVersionUID = -2210301005918021016L;

    /**
     * 假期规则主键
     */
    private Long configId;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 变更类型
     */
    private String type;
    /**
     * 变更日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date leaveDate;
    /**
     * 变更天数
     */
    //private BigDecimal dayNum;

    /**
     * 变更分钟
     */
    private BigDecimal minutesNum;
    /**
     * 操作人账号
     */
    private String operationCode;
    /**
     * 操作人名称
     */
    private String operationName;
    /**
     * 操作日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
    /**
     * 备注
     */
    private String remark;
}
