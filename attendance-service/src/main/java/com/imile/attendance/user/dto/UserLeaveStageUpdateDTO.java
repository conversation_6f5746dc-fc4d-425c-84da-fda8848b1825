package com.imile.attendance.user.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserLeaveStageUpdateDTO {

    private Long id;
    /**
     * 阶段
     */
    private Integer stage;
    /**
     * 假期余额
     */
    private BigDecimal leaveResidueMinutes;
    /**
     * 假期余额调整值（初始化为0，前端没传，调整值就是0，防止空指针）
     */
    private BigDecimal leaveAdjustmentMinutes = BigDecimal.ZERO;
    /**
     * 薪资比例
     */
    private BigDecimal percentSalary;
    /**
     * 备注
     */
    private String remark;
}
