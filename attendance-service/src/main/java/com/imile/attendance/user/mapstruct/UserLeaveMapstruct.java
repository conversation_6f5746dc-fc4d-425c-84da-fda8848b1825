package com.imile.attendance.user.mapstruct;


import com.google.common.collect.Lists;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserLeaveRecordImportDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveQuery;
import com.imile.attendance.infrastructure.repository.employee.dto.UserBaseInfoDTO;
import com.imile.attendance.user.dto.UserLeaveDTO;
import com.imile.attendance.user.dto.UserLeaveInfoDTO;
import com.imile.attendance.user.dto.UserLeaveRecordDTO;
import com.imile.attendance.user.query.UserLeaveDetailInfoQuery;
import com.imile.attendance.user.query.UserLeavePageQuery;
import com.imile.attendance.user.vo.UserBaseInfoVO;
import com.imile.attendance.util.BaseDOUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/5/8
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface UserLeaveMapstruct {

    UserLeaveMapstruct INSTANCE = Mappers.getMapper(UserLeaveMapstruct.class);

    UserLeaveInfoDTO toUserLeaveInfoDTO(AttendanceUser user);
    @Mapping(target = "userId", source = "id")
    UserLeaveDetailQuery toUserLeaveDetailQuery(UserLeaveDetailInfoQuery userLeaveDetailInfoQuery);

    UserLeaveQuery pageToQuery(UserLeavePageQuery pageQuery);

    UserBaseInfoVO toUserBaseInfoVO(UserBaseInfoDTO userBaseInfoDTO);

    UserDTO toUserDTO(UserBaseInfoDTO userBaseInfoDTO);

    @Mapping(target = "country", source = "locationCountry")
    UserLeaveDTO toUserLeaveDTO(AttendanceUser userInfo);
    List<UserLeaveDTO> toUserLeaveDTO(List<AttendanceUser> userInfoList);

    default Date convertLeaveDate(UserLeaveRecordDO userLeaveRecord) {
        return userLeaveRecord.getDate();
    }

    default Date convertCreateDate(UserLeaveRecordDO userLeaveRecord) {
        return userLeaveRecord.getLastUpdDate();
    }

    @Mapping(target = "leaveDate", source = "date")
    @Mapping(target = "minutesNum", source = "leaveMinutes")
    @Mapping(target = "operationCode", source = "lastUpdUserCode")
    @Mapping(target = "operationName", source = "lastUpdUserName")
    @Mapping(target = "createDate", source = "lastUpdDate")
    UserLeaveRecordDTO toUserLeaveRecordDTO(UserLeaveRecordDO userLeaveRecord);

    List<UserLeaveRecordDTO> toUserLeaveRecordDTO(List<UserLeaveRecordDO> userLeaveRecordList);

    UserLeaveRecordDO toUserLeaveRecordDO(UserLeaveRecordImportDTO userLeaveRecord);
    default UserLeaveRecordDO convertToDO(UserLeaveRecordImportDTO userLeaveRecordImportDTO) {
        UserLeaveRecordDO userLeaveRecordDO = toUserLeaveRecordDO(userLeaveRecordImportDTO);
        BaseDOUtil.fillDOInsertByUsrOrSystem(userLeaveRecordDO);
        return userLeaveRecordDO;
    }
    default List<UserLeaveRecordDO> toUserLeaveRecordDO(List<UserLeaveRecordImportDTO> userLeaveRecordList){
        if (CollectionUtils.isEmpty(userLeaveRecordList)) {
            return Lists.newArrayList();
        }
        return userLeaveRecordList.stream()
                .map(this::convertToDO)
                .collect(Collectors.toList());
    }

}
