package com.imile.attendance.user.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserLeaveRecordDTO {

    /**
     * 假期规则主键
     */
    private Long configId;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 变更类型
     */
    private String type;
    /**
     * 变更日期
     */
    private Date leaveDate;
    /**
     * 变更天数
     */
    //private BigDecimal dayNum;
    /**
     * 变更分钟
     */
    private BigDecimal minutesNum;
    /**
     * 操作人账号
     */
    private String operationCode;
    /**
     * 操作人名称
     */
    private String operationName;
    /**
     * 操作日期
     */
    private Date createDate;
    /**
     * 备注
     */
    private String remark;

}
