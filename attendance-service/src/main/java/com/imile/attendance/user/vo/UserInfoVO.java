package com.imile.attendance.user.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * <AUTHOR>
 * @since 2025/7/31
 */
@Data
public class UserInfoVO {
    @ApiModelProperty(value = "员工id")
    private Long id;

    @ApiModelProperty(value = "人员编码")
    private String userCode;

    @ApiModelProperty(value = "姓名")
    private String userName;

    @ApiModelProperty(value = "常驻地国家")
    private String locationCountry;

    @ApiModelProperty(value = "是否全球派遣（0:否 1:是）")
    private Integer isGlobalRelocation;

    @ApiModelProperty(value = "用工类型（详见数据字典EmploymentType)")
    private String employeeType;

    @ApiModelProperty(value = "是否司机")
    private Integer isDriver;

    @ApiModelProperty(value = "是否仓内员工")
    private Integer isWarehouseStaff;

    @ApiModelProperty(value = "岗位ID")
    private Long postId;

    @ApiModelProperty(value = "岗位名称")
    private String postName;

    @ApiModelProperty(value = "所属部门ID：hrms_ent_dept.id")
    private Long deptId;

    @ApiModelProperty(value = "部门名称")
    private String deptName;

    @ApiModelProperty(value = "状态(ACTIVE 生效,DISABLED)")
    private String status;

    @ApiModelProperty(value = "工作状态 在职、离职等")
    private String workStatus;
}
