package com.imile.attendance.user.query;

import com.imile.attendance.query.ResourceQuery;
import lombok.Data;


@Data
public class UserLeaveDetailInfoQuery extends ResourceQuery {

    /**
     * 用户id
     */
    private Long id;

    /**
     * 是否派遣
     */
    private Integer isDispatch;

    /**
     * 国家
     */
    private String country;

    /**
     * 假期类型
     */
    private String leaveType;

    /**
     * 状态
     */
    private String status;

    /**
     * 有效期开始时间
     */
    private Long effectStartTime;

    /**
     * 有效期结束时间
     */
    private Long effectEndTime;

}
