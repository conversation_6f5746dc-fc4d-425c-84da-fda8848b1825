package com.imile.attendance.user.impl;

import com.imile.attendance.abnormal.AttendanceGenerateService;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.abnormal.dto.AttendanceCalculateHandlerDTO;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.EntryStatusEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.abnormal.dao.AttendanceEmployeeDetailDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalAttendanceDao;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.abnormal.query.AbnormalAttendanceQuery;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.day.job.service.AttendanceDayReportJobService;
import com.imile.attendance.user.UserEntryAndDimissionService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/6/16
 */
@Slf4j
@Service
public class UserEntryAndDimissionServiceImpl implements UserEntryAndDimissionService {
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;
    @Resource
    private EmployeeAbnormalAttendanceManage employeeAbnormalAttendanceManage;
    @Resource
    private AttendanceEmployeeDetailDao attendanceEmployeeDetailDao;
    @Resource
    private EmployeeAbnormalAttendanceDao employeeAbnormalAttendanceDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceGenerateService attendanceGenerateService;
    @Resource
    private MigrationService migrationService;
    @Resource
    private AttendanceDayReportJobService attendanceDayReportJobService;

    @Override
    public void entryAttendanceHandler(Long userId) {
        log.info("员工确认入职: {}确认入职考勤侧处理", userId);
        if (Objects.isNull(userId)) {
            return;
        }
        //查询用户信息
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (userInfoDO == null || !WorkStatusEnum.ON_JOB.getCode().equals(userInfoDO.getWorkStatus())) {
            return;
        }
        if (!migrationService.verifyUserIsEnableNewAttendance(userInfoDO.getId())) {
            log.info("entryAttendanceHandler | userInfo is on old attendance, userCode:{}", userInfoDO.getUserCode());
            return;
        }
        //查询用户入职时间
        UserEntryRecordDO userEntryRecordDO = userEntryRecordDao.getById(userInfoDO.getId());
        if (Objects.isNull(userEntryRecordDO)
                || !Objects.equals(EntryStatusEnum.ENTRY.getCode(), userEntryRecordDO.getEntryStatus())
                || Objects.isNull(userEntryRecordDO.getConfirmDate())) {
            return;
        }

        //实际确认入职时间
        Date confirmDate = userEntryRecordDO.getConfirmDate();
        Long confirmDayId = DateHelper.getDayId(confirmDate);

        //当前HR确认入职操作时间
        Long confirmOperateDayId = DateHelper.getDayId(userEntryRecordDO.getLastUpdDate());

        if (Objects.equals(confirmDayId, confirmOperateDayId)) {
            log.info("HR确认员工实际入职时间未延迟");
            // 当天入职需要生成日报
            DayReportJobParam param = DayReportJobParam.builder()
                    .userCodeList(Arrays.asList(userInfoDO.getUserCode()))
                    .employeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                    .countryArrayList(Arrays.asList(userInfoDO.getLocationCountry()))
                    .isUseCustomLocalTime(true)
                    .localDate(DateHelper.getString(confirmDayId))
                    .build();
            attendanceDayReportJobService.attendanceDayReportInit(param);
            return;
        }

        if (confirmOperateDayId < confirmDayId) {
            log.info("HR提前确认员工实际入职时间,confirmDayId:{},confirmOperateDayId:{}", confirmDayId, confirmOperateDayId);
            return;
        }
        while (confirmDayId.compareTo(confirmOperateDayId) < 1) {
            AttendanceCalculateHandlerDTO calculateHandlerDTO = AttendanceCalculateHandlerDTO.builder()
                    .userCodes(userInfoDO.getUserCode())
                    .attendanceDayId(confirmDayId)
                    .build();
            // 日报生成
            DayReportJobParam param = DayReportJobParam.builder()
                    .userCodeList(Arrays.asList(userInfoDO.getUserCode()))
                    .employeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT)
                    .countryArrayList(Arrays.asList(userInfoDO.getLocationCountry()))
                    .isUseCustomLocalTime(true)
                    .localDate(DateHelper.getString(confirmDayId))
                    .build();
            attendanceDayReportJobService.attendanceDayReportInit(param);
            // 异常计算
            attendanceGenerateService.attendanceCalculateHandler(calculateHandlerDTO);
            //后移一天
            confirmDayId = DateHelper.getNextDayId(confirmDayId);
        }
    }

    @Override
    public void dimissionAttendanceHandler(Long userId) {
        log.info("员工确认离职: {} 确认离职考勤侧处理", userId);
        if (Objects.isNull(userId)) {
            return;
        }
        //查询用户信息
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (Objects.isNull(userInfoDO)) {
            return;
        }
        if (!migrationService.verifyUserIsEnableNewAttendance(userInfoDO.getId())) {
            log.info("entryAttendanceHandler | userInfo is on old attendance, userCode:{}", userInfoDO.getUserCode());
            return;
        }
        //清理离职后的异常考勤数据
        UserDimissionRecordDO userDimissionRecordDO = userDimissionRecordDao.getByUserId(userId, DimissionStatusEnum.DIMISSION.getCode());
        if (Objects.isNull(userDimissionRecordDO)
                || !Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), userDimissionRecordDO.getDimissionStatus())
                || Objects.isNull(userDimissionRecordDO.getActualDimissionDate())) {
            return;
        }

        //实际离职时间
        Long actualDimissionDayId = DateHelper.getDayId(userDimissionRecordDO.getActualDimissionDate());
        Long nextActualDimissionDayId = DateHelper.getNextDayId(actualDimissionDayId);

        //当前时间
        Long currentDayId = DateHelper.getDayId(new Date());
        if (actualDimissionDayId.compareTo(currentDayId) > -1) {
            //当天或者提前确认离职
            return;
        }

        //清理正常考勤记录
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = attendanceEmployeeDetailDao.selectAttendanceRecordByDay(userInfoDO.getId(), nextActualDimissionDayId);
        if (CollectionUtils.isNotEmpty(attendanceEmployeeDetailDOList)) {
            attendanceEmployeeDetailDOList.forEach(normal -> {
                normal.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(normal);
            });
        }

        //清理异常考勤记录
        AbnormalAttendanceQuery query = new AbnormalAttendanceQuery();
        query.setDayId(nextActualDimissionDayId.toString());
        query.setUserIds(Collections.singletonList(userInfoDO.getId()));
        List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = employeeAbnormalAttendanceDao.listAbnormal(query);
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)) {
            employeeAbnormalAttendanceManage.batchAbnormalUpdate(null, null, attendanceEmployeeDetailDOList);
            return;
        }
        employeeAbnormalAttendanceDOList.forEach(abnormal -> {
            abnormal.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(abnormal);
        });

        List<Long> abnormalIds = employeeAbnormalAttendanceDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).distinct().collect(Collectors.toList());
        List<EmployeeAbnormalOperationRecordDO> employeeAbnormalOperationRecordDOList = employeeAbnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
        if (CollectionUtils.isEmpty(employeeAbnormalOperationRecordDOList)) {
            employeeAbnormalAttendanceManage.batchAbnormalUpdate(employeeAbnormalAttendanceDOList, null, attendanceEmployeeDetailDOList);
            return;
        }

        employeeAbnormalOperationRecordDOList.forEach(abnormalRecord -> {
            abnormalRecord.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(abnormalRecord);
        });
        employeeAbnormalAttendanceManage.batchAbnormalUpdate(employeeAbnormalAttendanceDOList, employeeAbnormalOperationRecordDOList, attendanceEmployeeDetailDOList);
    }
}
