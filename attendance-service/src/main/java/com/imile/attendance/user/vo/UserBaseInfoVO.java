package com.imile.attendance.user.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @Date 2025/6/9
 * @Description 员工常用基本信息VO
 */
@Data
public class UserBaseInfoVO {
    /**
     * 姓名
     */
    private String userName;

    /**
     * 账号
     */
    private String userCode;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    private String workStatus;

    /**
     * 工作状态描述
     */
    private String workStatusDesc;

    /**
     * 状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型名称
     */
    private String employeeTypeDesc;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 是否派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位ID
     */
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 入职时间
     */
    private Date entryDate;

    /**
     * 离职时间
     */
    private Date dimissionDate;
}
