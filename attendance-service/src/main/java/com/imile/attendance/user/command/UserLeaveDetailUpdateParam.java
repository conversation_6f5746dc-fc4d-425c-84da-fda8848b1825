package com.imile.attendance.user.command;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserLeaveDetailUpdateParam implements Serializable {
    private static final long serialVersionUID = 4710891713328392126L;

    private Long id;
    /**
     * 假期规则主键
     */
    private Long configId;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 假期类型
     */
    private String leaveType;

    private List<UserLeaveStageUpdateParam> stageUpdateParamList;
}
