package com.imile.attendance.user.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserLeaveItemDetailVO implements Serializable {
    private static final long serialVersionUID = -29538884622752902L;

    private Long id;
    /**
     * 阶段
     */
    private Integer stage;

    /**
     * 是否结转: 0：表示非结转，1：结转，...
     */
    private Integer leaveMark;

    /**
     * 是否失效: 0：表示未失效，1：失效，...
     */
    private Integer isInvalid;

    /**
     * 发薪比例
     */
    private BigDecimal percentSalary;

    /**
     * 假期剩余分钟数据
     */
    private BigDecimal leaveResidueMinutes;

    /**
     * 假期已使用分钟数
     */
    private BigDecimal leaveUsedMinutes;

    /**
     * 假期总分钟数
     */
    private BigDecimal leaveTotalMinutes;

    /**
     * 发放日期
     */
    private String issueDate;

    /**
     * 失效日期
     */
    private String invalidDate;

    /**
     * 创建时间
     */
    private Date createDate;
}
