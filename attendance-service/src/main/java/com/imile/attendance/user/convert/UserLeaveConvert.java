package com.imile.attendance.user.convert;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.user.command.UserLeaveDetailUpdateParam;
import com.imile.attendance.user.command.UserLeaveStageUpdateParam;
import com.imile.attendance.user.command.UserLeaveUpdateParam;
import com.imile.attendance.user.dto.UserLeaveDetailDTO;
import com.imile.attendance.user.dto.UserLeaveDetailUpdateDTO;
import com.imile.attendance.user.dto.UserLeaveInfoDTO;
import com.imile.attendance.user.dto.UserLeaveStageDetailDTO;
import com.imile.attendance.user.dto.UserLeaveStageUpdateDTO;
import com.imile.attendance.user.dto.UserLeaveUpdateDTO;
import com.imile.attendance.user.vo.UserLeaveDetailVO;
import com.imile.attendance.user.vo.UserLeaveInfoVO;
import com.imile.attendance.user.vo.UserLeaveItemDetailVO;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-16
 * @version: 1.0
 */
public class UserLeaveConvert {
    public static UserLeaveInfoVO convertFromDTO(UserLeaveInfoDTO dto) {
        if (dto != null) {
            UserLeaveInfoVO vo = new UserLeaveInfoVO();
            vo.setId(dto.getId());
            vo.setDeptId(dto.getDeptId());
            vo.setDeptName(dto.getDeptName());
            vo.setPostId(dto.getPostId());
            vo.setPostName(dto.getPostName());
            vo.setUserCode(dto.getUserCode());
            vo.setUserName(dto.getUserName());
            vo.setSex(dto.getSex());
            vo.setEntryDate(dto.getEntryDate());
            List<UserLeaveDetailDTO> userLeaveDetailList = dto.getUserLeaveDetailList();
            if (CollectionUtils.isEmpty(userLeaveDetailList)) {
                vo.setDetailVOList(new ArrayList<>());
                return vo;
            }
            List<UserLeaveDetailVO> detailVOList = new ArrayList<>();
            for (UserLeaveDetailDTO leaveDetailDTO : userLeaveDetailList) {
                UserLeaveDetailVO leaveDetailVO = new UserLeaveDetailVO();
                leaveDetailVO.setId(leaveDetailDTO.getId());
                leaveDetailVO.setConfigId(leaveDetailDTO.getConfigId());
                leaveDetailVO.setCountry(leaveDetailDTO.getCountry());
                leaveDetailVO.setLeaveName(leaveDetailDTO.getLeaveName());
                leaveDetailVO.setLeaveType(leaveDetailDTO.getLeaveType());
                leaveDetailVO.setIsInvalid(leaveDetailDTO.getIsInvalid());
                leaveDetailVO.setUseCycle(leaveDetailDTO.getUseCycle());
                leaveDetailVO.setAvailability(leaveDetailDTO.getAvailability());
                leaveDetailVO.setUseCondition(leaveDetailDTO.getUseCondition());
                leaveDetailVO.setIsDispatch(leaveDetailDTO.getIsDispatch());
                //员工一旦有该种类型的假，就一定有该假的阶段信息
                List<UserLeaveStageDetailDTO> userLeaveStageList = leaveDetailDTO.getUserLeaveStageList();
                List<UserLeaveItemDetailVO> itemDetailVOList = new ArrayList<>();
                for (UserLeaveStageDetailDTO stageDetailDTO : userLeaveStageList) {
                    UserLeaveItemDetailVO itemDetailVO = new UserLeaveItemDetailVO();
                    itemDetailVO.setId(stageDetailDTO.getId());
                    itemDetailVO.setLeaveResidueMinutes(stageDetailDTO.getLeaveResidueMinutes());
                    itemDetailVO.setLeaveUsedMinutes(stageDetailDTO.getLeaveUsedMinutes());
                    itemDetailVO.setLeaveTotalMinutes(stageDetailDTO.getLeaveTotalMinutes());
                    itemDetailVO.setLeaveMark(stageDetailDTO.getLeaveMark());
                    itemDetailVO.setIsInvalid(stageDetailDTO.getIsInvalid());
                    itemDetailVO.setPercentSalary(stageDetailDTO.getPercentSalary().multiply(new BigDecimal("100")));
                    itemDetailVO.setStage(stageDetailDTO.getStage());
                    itemDetailVO.setCreateDate(stageDetailDTO.getCreateDate());
                    itemDetailVO.setIssueDate(stageDetailDTO.getIssueDate());
                    itemDetailVO.setInvalidDate(stageDetailDTO.getInvalidDate());
                    itemDetailVOList.add(itemDetailVO);
                }
                leaveDetailVO.setItemDetailVOList(itemDetailVOList);
                detailVOList.add(leaveDetailVO);
            }
            vo.setDetailVOList(detailVOList);
            return vo;
        }
        return null;
    }

    public static UserLeaveUpdateDTO convertFromParam(UserLeaveUpdateParam param) {
        if (param != null) {
            UserLeaveUpdateDTO leaveUpdateDTO = new UserLeaveUpdateDTO();
            leaveUpdateDTO.setId(param.getId());
            List<UserLeaveDetailUpdateDTO> detailDTOList = new ArrayList<>();
            List<UserLeaveDetailUpdateParam> detailParamList = param.getDetailParamList();
            if (CollectionUtils.isEmpty(detailParamList)) {
                leaveUpdateDTO.setDetailDTOList(new ArrayList<>());
                return leaveUpdateDTO;
            }
            for (UserLeaveDetailUpdateParam detailUpdateParam : detailParamList) {
                UserLeaveDetailUpdateDTO detailUpdateDTO = new UserLeaveDetailUpdateDTO();
                detailUpdateDTO.setId(detailUpdateParam.getId());
                detailUpdateDTO.setConfigId(detailUpdateParam.getConfigId());
                detailUpdateDTO.setLeaveName(detailUpdateParam.getLeaveName());
                detailUpdateDTO.setLeaveType(detailUpdateParam.getLeaveType());
                List<UserLeaveStageUpdateParam> stageUpdateParamList = detailUpdateParam.getStageUpdateParamList();
                List<UserLeaveStageUpdateDTO> stageUpdateDTOList = new ArrayList<>();
                for (UserLeaveStageUpdateParam stageUpdateParam : stageUpdateParamList) {
                    UserLeaveStageUpdateDTO userLeaveStageUpdateDTO = new UserLeaveStageUpdateDTO();
                    userLeaveStageUpdateDTO.setId(stageUpdateParam.getId());
                    // 如果有一个调整值大于999，就拦截，操作最大调整值
                    if (stageUpdateParam.getLeaveAdjustmentMinutes().compareTo(BigDecimal.valueOf(999)
                            .multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS)
                            .multiply(BusinessConstant.MINUTES)) > 0) {
                        throw BusinessLogicException.getException(ErrorCodeEnum.THE_HOLIDAY_ADJUSTMENT_IS_NOT_LEGAL);
                    }
                    // 如果调整额为正数，表示增加额度，如果为负数，表示减少额度【stageUpdateParam.getLeaveResidueDay()可为正可为负】

                    BigDecimal leaveAdjustmentMinutes = stageUpdateParam.getLeaveAdjustmentMinutes();

                    userLeaveStageUpdateDTO.setLeaveResidueMinutes(stageUpdateParam.getLeaveResidueMinutes().add(leaveAdjustmentMinutes));
                    userLeaveStageUpdateDTO.setLeaveAdjustmentMinutes(stageUpdateParam.getLeaveAdjustmentMinutes());
                    userLeaveStageUpdateDTO.setPercentSalary(stageUpdateParam.getPercentSalary());
                    userLeaveStageUpdateDTO.setStage(stageUpdateParam.getStage());
                    userLeaveStageUpdateDTO.setRemark(stageUpdateParam.getRemark());
                    stageUpdateDTOList.add(userLeaveStageUpdateDTO);
                }
                detailUpdateDTO.setStageUpdateDTOList(stageUpdateDTOList);
                detailDTOList.add(detailUpdateDTO);
            }
            leaveUpdateDTO.setDetailDTOList(detailDTOList);
            return leaveUpdateDTO;
        }
        return null;
    }
}
