package com.imile.attendance.user.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025/7/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDynamicInfoVO implements Serializable{


    /**
     * 人员ID（为了满足历史功能，不建议下游使用或存储，统一使用userCode）
     */
    private Long id;

    /**
     * 人员编码
     */
    private String userCode;

    /**
     * 人员名称
     */
    private String userName;

    /**
     * 是否司机（0:否 1:是）
     */
    private Integer isDriver;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 账号状态
     */
    private String status;

    /**
     * 工作状态（在职：ON_JOB 离职：DIMISSION）
     */
    private String workStatus;

    /**
     * 人员动态字段映射
     */
    private Map<String, String> dynamicFieldMap;
}
