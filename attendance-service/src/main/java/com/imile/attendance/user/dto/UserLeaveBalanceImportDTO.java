package com.imile.attendance.user.dto;

import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.common.excel.ExcelImport;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2023-8-10
 * @version: 1.0
 */
@Data
public class UserLeaveBalanceImportDTO extends ExcelImport {

    /**
     * 姓名
     */
    private String userName;

    /**
     * 帐号
     */
    private String userCode;

    /**
     * 是否派遣假
     */
    private String isDispatch;

    private Integer isDispatchNum;

    /**
     * 假期国家
     */
    private String country;

    /**
     * 假期类型
     */
    private String leaveType;

    private String leaveTypeCode;

    /**
     * 假期名称
     */
    private String leaveName;

    /**
     * 是否结转
     */
    private String leaveMark;

    private Integer leaveMarkNum;

    /**
     * 发薪比例
     */
    private String percentSalary;

    private BigDecimal percentSalaryNum;

    /**
     * 假期额度(天)
     */
    private String leaveBalanceDays;

    private BigDecimal leaveBalanceMinutes;

    /**
     * 备注
     */
    private String remark;

    // --- 导入后台转换使用

    /**
     * 员工主键
     */
    private Long userId;

    /**
     * 用户导入假期余额时对应的假期id（导入使用）
     */
    private Long configId;

    /**
     * 用户导入假期余额时对应的假期（导入使用）
     */
    private CompanyLeaveConfigDO userLeaveConfigForImport;

    /**
     * 用户对应的假期结转规则
     */
    private CompanyLeaveConfigCarryOverDO userLeaveConfigCarryOverForImport;

    /**
     * 用户对应的假期结转规则范围
     */
    private List<CompanyLeaveConfigCarryOverRangeDO> userLeaveConfigCarryOverRangeForImport;

    /**
     * 用户对应的假期比例阶段（导入使用）
     */
    private List<CompanyLeaveItemConfigDO> userLeaveItemConfigListForImport;

    /**
     * 用户对应的假期详情（导入使用）
     */
    private UserLeaveDetailDO userLeaveDetailForImport;

    /**
     * 用户对应的假期余额（导入使用，只有一条唯一记录的数据）
     */
    private UserLeaveStageDetailDO userLeaveStageDetailForImport;

    /**
     * 用户对应的假期余额（导入使用，当前假期所有比例的余额）
     */
    private List<UserLeaveStageDetailDO> userLeaveStageDetailListForImport;

    /**
     * 用户入职记录导入使用）
     */
    private AttendanceUserEntryRecord userEntryRecord;
}
