package com.imile.attendance.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.vacation.AttendanceLeaveRestrictionEnum;
import com.imile.attendance.enums.vacation.LeaveChangeType;
import com.imile.attendance.enums.vacation.LeaveConfigRangRangeTypeEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.hermes.dto.DictVO;
import com.imile.attendance.hrms.RpcUserClient;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.DictService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.UserLeaveConfigHistoryDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.user.dto.UserAvailableLeaveDTO;
import com.imile.attendance.user.dto.UserLeaveAggregationDTO;
import com.imile.attendance.user.dto.UserLeaveDTO;
import com.imile.attendance.user.dto.UserLeaveDetailDTO;
import com.imile.attendance.user.dto.UserLeaveDetailUpdateDTO;
import com.imile.attendance.user.dto.UserLeaveInfoDTO;
import com.imile.attendance.user.dto.UserLeaveRecordDTO;
import com.imile.attendance.user.dto.UserLeaveStageDetailDTO;
import com.imile.attendance.user.dto.UserLeaveStageUpdateDTO;
import com.imile.attendance.user.dto.UserLeaveUpdateDTO;
import com.imile.attendance.user.dto.UserStageLeaveDTO;
import com.imile.attendance.user.mapstruct.UserLeaveMapstruct;
import com.imile.attendance.user.query.UserLeaveDetailInfoQuery;
import com.imile.attendance.user.query.UserLeavePageQuery;
import com.imile.attendance.user.query.UserLeaveRecordPageQuery;
import com.imile.attendance.user.vo.UserLeaveResidualVO;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigCarryOverService;
import com.imile.attendance.vacation.CompanyLeaveConfigManage;
import com.imile.attendance.vacation.CompanyLeaveConfigRangService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.UserLeaveConfigHistoryService;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveRecordService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.attendance.vacation.factory.UserLeaveRecordFactory;
import com.imile.attendance.vacation.factory.UserLeaveStageDetailFactory;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.WorkStatusEnum;
import com.imile.hrms.api.user.enums.UserDynamicFieldEnum;
import com.imile.hrms.api.user.result.UserDynamicInfoDTO;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户假期余额服务
 *
 * <AUTHOR>
 * @menu 假期
 * @date 2025/5/7
 */
@Service
@Slf4j
public class UserLeaveService {
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private UserLeaveStageDetailFactory userLeaveStageDetailFactory;
    @Resource
    private UserLeaveRecordService userLeaveRecordService;
    @Resource
    private UserLeaveRecordFactory userLeaveRecordFactory;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private CompanyLeaveConfigManage companyLeaveConfigManage;
    @Resource
    private CompanyLeaveConfigRangService companyLeaveConfigRangService;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Resource
    private UserLeaveConfigHistoryService userLeaveConfigHistoryService;
    @Resource
    private DictService dictService;
    @Resource
    private RpcUserClient rpcUserClient;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private MigrationService migrationService;

    // 用户假期余额相关方法

    /**
     * 查询该员工还可以请的假期(年假不允许请负假)
     *
     * @param userId
     * @param userCode
     * @return
     */
    public List<UserAvailableLeaveDTO> selectUserOwnLeaveType(Long userId, String userCode) {
        //查询该员工有哪些可请的假期
        UserLeaveDetailQuery query = UserLeaveDetailQuery.builder()
                .userId(userId)
                .userCode(userCode)
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(query);
        if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
            return new ArrayList<>();
        }
        List<Long> leaveIdList = userLeaveDetailDOList.stream().map(record -> record.getId()).collect(Collectors.toList());
        //判断这些可请的假期还有没有余额
        List<UserLeaveStageDetailDO> leaveStageDetailDOList = userLeaveStageDetailService.selectByLeaveId(leaveIdList);
        //根据leaveId分组
        Map<Long, List<UserLeaveStageDetailDO>> doMaps = leaveStageDetailDOList.stream().collect(Collectors.groupingBy(record -> record.getLeaveId()));
        List<UserAvailableLeaveDTO> resultList = new ArrayList<>();
        for (UserLeaveDetailDO leaveDetailDO : userLeaveDetailDOList) {
            List<UserLeaveStageDetailDO> recordList = doMaps.get(leaveDetailDO.getId());
            if (CollectionUtils.isEmpty(recordList)) {
                continue;
            }
            //判断该类型假期的所有阶段是否还有可请的假期余额
            List<UserLeaveStageDetailDO> filterList = recordList.stream().filter(record -> record.getLeaveResidueMinutes().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterList)) {
                continue;
            }
            buildAvailableLeaveDTO(resultList, leaveDetailDO, filterList);
        }
        return resultList;
    }

    /**
     * 个人假期分页列表查询
     *
     * @param query
     * @return
     */
    public PaginationResult<UserLeaveDTO> list(UserLeavePageQuery query) {
        // 转换参数
        UserLeaveQuery userLeaveQuery = UserLeaveMapstruct.INSTANCE.pageToQuery(query);
        if (ObjectUtil.isEmpty(query.getEmploymentTypeList())) {
            // 如果用工类型为空表示全部
            userLeaveQuery.setEmployeeTypeList(EmploymentTypeEnum.TYPE_OF_DEFAULT_WELFARE_LEAVE);
        }
        // 权限设置
        String country = query.getCountry();
        List<String> paramCountryList = new ArrayList<>();
        if (StringUtils.isNotBlank(country)) {
            paramCountryList.add(country);
        }
        Boolean isChooseDept = Boolean.FALSE;
        if (CollUtil.isNotEmpty(query.getDeptIdList())) {
            // 设置标志
            isChooseDept = Boolean.TRUE;
        }
        PermissionCountryDeptVO permissionDept = userResourceService.getPermissionCountryDeptVO(query.getDeptIdList(), Lists.newArrayList(paramCountryList));
        Boolean hasOrDeptAndCountryPermission = permissionDept.getHasOrDeptAndCountryPermission();
        log.info("abnormal permissionDept:{}", JSON.toJSONString(permissionDept));

        if (CollectionUtils.isNotEmpty(query.getDeptIdList()) && permissionDept.getHasDeptPermission().equals(Boolean.FALSE)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        if (hasOrDeptAndCountryPermission.equals(Boolean.FALSE)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        userLeaveQuery.setDeptIdList(permissionDept.getDeptIdList());
        userLeaveQuery.setAuthLocationCountryList(permissionDept.getCountryList());

        // 非系统管理员
        userLeaveQuery.setHasDeptPermission(permissionDept.getHasDeptPermission());
        userLeaveQuery.setHasCountryPermission(permissionDept.getHasCountryPermission());
        userLeaveQuery.setHasOrDeptAndCountryPermission(permissionDept.getHasOrDeptAndCountryPermission());
        userLeaveQuery.setHasAndDeptAndCountryPermission(permissionDept.getHasAndDeptAndCountryPermission());
        userLeaveQuery.setIsChooseDept(isChooseDept);

        Page<AttendanceUser> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<AttendanceUser> pageInfo = page.doSelectPageInfo(() -> userService.selectLeaveUser(userLeaveQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        //在根据员工信息查询年假余额
        List<Long> userIds = pageInfo.getList().stream().map(o -> o.getId()).collect(Collectors.toList());
        List<Long> deptIdList = pageInfo.getList().stream().map(o -> o.getDeptId()).collect(Collectors.toList());
        List<AttendanceDept> entDeptList = deptService.listByDeptIds(deptIdList);
        Map<Long, List<AttendanceDept>> deptMaps = entDeptList.stream().collect(Collectors.groupingBy(AttendanceDept::getId));

        List<Long> postIdList = pageInfo.getList().stream().map(o -> o.getPostId()).collect(Collectors.toList());
        List<AttendancePost> entPostList = postService.listByPostList(postIdList);
        Map<Long, List<AttendancePost>> postMaps = entPostList.stream().collect(Collectors.groupingBy(AttendancePost::getId));

        //查询员工假期变更记录
        List<UserLeaveRecordDO> userLeaveRecordList = userLeaveRecordService.selectRecordByUserIdList(userIds);
        Map<Long, List<UserLeaveRecordDO>> recordMap = userLeaveRecordList.stream()
                .sorted(Comparator.comparing(UserLeaveRecordDO::getCreateDate).reversed())
                .collect(Collectors.groupingBy(UserLeaveRecordDO::getUserId));

        List<UserLeaveDTO> userLeaveDTOList = UserLeaveMapstruct.INSTANCE.toUserLeaveDTO(pageInfo.getList());
        for (UserLeaveDTO item : userLeaveDTOList) {
            if (item.getDeptId() != null) {
                List<AttendanceDept> userentDeptList = deptMaps.get(item.getDeptId());
                if (CollectionUtils.isNotEmpty(userentDeptList)) {
                    item.setDeptName(RequestInfoHolder.isChinese() ? userentDeptList.get(0).getDeptNameCn() : userentDeptList.get(0).getDeptNameEn());
                }
            }
            if (item.getPostId() != null) {
                List<AttendancePost> userentPostList = postMaps.get(item.getPostId());
                if (CollectionUtils.isNotEmpty(userentPostList)) {
                    item.setPostName(RequestInfoHolder.isChinese() ? userentPostList.get(0).getPostNameCn() : userentPostList.get(0).getPostNameEn());
                }
            }

            List<UserLeaveRecordDO> userLeaveRecordDOS = recordMap.get(item.getId());
            if (CollectionUtils.isNotEmpty(userLeaveRecordDOS)) {
                item.setLastUpdDate(userLeaveRecordDOS.get(0).getCreateDate());
                item.setLastUpdUserName(userLeaveRecordDOS.get(0).getOperationUserName());
            }
        }
        return PageUtil.getPageResult(userLeaveDTOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 用户假期余额详情
     *
     * @param query
     * @return
     */
    public UserLeaveInfoDTO detail(UserLeaveDetailInfoQuery query) {
        Long userId = query.getId();
        if (userId == null || userId.compareTo(0L) <= 0) {
            return new UserLeaveInfoDTO();
        }
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null) {
            return new UserLeaveInfoDTO();
        }
        UserLeaveMapstruct userMapStruct = UserLeaveMapstruct.INSTANCE;
        UserLeaveInfoDTO userLeaveInfoDTO = userMapStruct.toUserLeaveInfoDTO(userInfo);
        AttendanceUserEntryRecord userEntryRecordInfo = userEntryRecordService.selectUserEntryByUserId(userId);
        if (ObjectUtil.isNotNull(userEntryRecordInfo) && ObjectUtil.isNotNull(userEntryRecordInfo.getConfirmDate())) {
            String entryDateString = DateUtil.format(userEntryRecordInfo.getConfirmDate(), DatePattern.NORM_DATE_PATTERN);
            userLeaveInfoDTO.setEntryDate(entryDateString);
        }
        UserLeaveDetailQuery convertQuery = userMapStruct.toUserLeaveDetailQuery(query);
        convertQuery.setStatus(StatusEnum.ACTIVE.getCode());
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(convertQuery);
        if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
            return userLeaveInfoDTO;
        }
        // 人员假期通过configId关联
        List<Long> companyLeaveConfigIdList = userLeaveDetailDOList.stream()
                .filter(item -> Objects.nonNull(item.getConfigId()))
                .map(item -> item.getConfigId())
                .collect(Collectors.toList());
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .idList(companyLeaveConfigIdList)
                .leaveType(query.getLeaveType())
                .country(query.getCountry())
                .isDispatch(query.getIsDispatch()).build();
        List<CompanyLeaveConfigDO> companyLeaveConfigDOList = companyLeaveConfigService.selectCompanyLeave(companyLeaveQuery);
        companyLeaveConfigDOList = companyLeaveConfigService.filterUserCompanyConfig(companyLeaveConfigDOList, userInfo);
        Map<Long, List<CompanyLeaveConfigDO>> configIdMap = companyLeaveConfigDOList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigDO::getId));

        List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverDOList = companyLeaveConfigCarryOverService.selectByLeaveId(companyLeaveConfigIdList);
        Map<Long, List<CompanyLeaveConfigCarryOverDO>> carryOverMap = companyLeaveConfigCarryOverDOList.stream().collect(Collectors.groupingBy(CompanyLeaveConfigCarryOverDO::getLeaveId));

        List<Long> leaveIds = userLeaveDetailDOList.stream().map(o -> o.getId()).collect(Collectors.toList());
        UserLeaveStageDetailQuery userLeaveStageDetailQuery = UserLeaveStageDetailQuery.builder().leaveIdList(leaveIds).build();
        List<UserLeaveStageDetailDO> stageDetailDOList = userLeaveStageDetailService.selectByCondition(userLeaveStageDetailQuery);
        // 过滤有效期
        if (Objects.nonNull(query.getEffectStartTime())
                && Objects.nonNull(query.getEffectEndTime())) {
            stageDetailDOList = stageDetailDOList
                    .stream()
                    .filter(item -> Objects.nonNull(item.getIssueDate())
                            && Objects.nonNull(item.getInvalidDate())
                            && this.checkInterSection(query.getEffectStartTime(),
                            query.getEffectEndTime(),
                            Long.valueOf(item.getIssueDate()),
                            Long.valueOf(item.getInvalidDate())))
                    .collect(Collectors.toList());
        }
        Map<Long, List<UserLeaveStageDetailDO>> stageMap = stageDetailDOList
                .stream()
                .collect(Collectors.groupingBy(o -> o.getLeaveId()));
        List<UserLeaveDetailDTO> userLeaveDetailList = new ArrayList<>();
        for (UserLeaveDetailDO detailDO : userLeaveDetailDOList) {
            List<CompanyLeaveConfigDO> userLeaveConfig = configIdMap.get(detailDO.getConfigId());
            if (CollUtil.isEmpty(userLeaveConfig)) {
                continue;
            }
            UserLeaveDetailDTO detailDTO = BeanUtils.convert(detailDO, UserLeaveDetailDTO.class);
            CompanyLeaveConfigDO companyLeaveConfigDO = userLeaveConfig.get(0);
            Optional.ofNullable(carryOverMap.get(companyLeaveConfigDO.getId()))
                    .ifPresent(item -> detailDTO.setIsInvalid(item.get(0).getIsInvalid()));
            detailDTO.setCountry(companyLeaveConfigDO.getCountry());
            detailDTO.setUseCycle(companyLeaveConfigDO.getUseCycle());
            detailDTO.setIsDispatch(companyLeaveConfigDO.getIsDispatch());
            //通过入职单判断该用户是否满足使用条件
            Integer restriction = companyLeaveConfigDO.getLeaveUsageRestrictions();
            if (Objects.nonNull(restriction) && restriction > BusinessConstant.ZERO) {
                AttendanceLeaveRestrictionEnum isInvalidEnum = AttendanceLeaveRestrictionEnum.getIsInvalidEnum(restriction);
                detailDTO.setUseCondition(isInvalidEnum.getCode());
            }
            List<UserLeaveStageDetailDTO> userLeaveStageList = new ArrayList<>();
            List<UserLeaveStageDetailDO> userLeaveStageDetailDOList = stageMap.get(detailDO.getId());
            if (CollectionUtils.isNotEmpty(userLeaveStageDetailDOList)) {
                for (UserLeaveStageDetailDO stageDetailDO : userLeaveStageDetailDOList) {
                    UserLeaveStageDetailDTO stageDetailDTO = BeanUtils.convert(stageDetailDO, UserLeaveStageDetailDTO.class);
                    stageDetailDTO.setIsInvalid(stageDetailDO.getIsInvalid());
                    stageDetailDTO.setLeaveMark(stageDetailDO.getLeaveMark());
                    stageDetailDTO.setLeaveResidueMinutes(stageDetailDO.getLeaveResidueMinutes());
                    stageDetailDTO.setLeaveUsedMinutes(stageDetailDO.getLeaveUsedMinutes());
                    stageDetailDTO.setLeaveTotalMinutes(stageDetailDO.getLeaveResidueMinutes().add(stageDetailDO.getLeaveUsedMinutes()));
                    stageDetailDTO.setCreateDate(stageDetailDO.getCreateDate());
                    stageDetailDTO.setIssueDate(stageDetailDO.getIssueDate());
                    stageDetailDTO.setInvalidDate(stageDetailDO.getInvalidDate());
                    userLeaveStageList.add(stageDetailDTO);
                }
            }
            detailDTO.setUserLeaveStageList(userLeaveStageList);
            userLeaveDetailList.add(detailDTO);
        }
        userLeaveDetailList = userLeaveDetailList.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getUserLeaveStageList()))
                .sorted(Comparator.comparing(UserLeaveDetailDTO::getIsDispatch).reversed())
                .collect(Collectors.toList());
        userLeaveInfoDTO.setUserLeaveDetailList(userLeaveDetailList);

        // 部门信息获取
        if (Objects.nonNull(userInfo.getDeptId())) {
            List<AttendanceDept> entDeptList = deptService.listByDeptIds(Arrays.asList(userInfo.getDeptId()));
            if (CollectionUtils.isNotEmpty(entDeptList)) {
                AttendanceDept entDept = entDeptList.get(0);
                userLeaveInfoDTO.setDeptName(RequestInfoHolder.isChinese() ? entDept.getDeptNameCn() : entDept.getDeptNameEn());
                userLeaveInfoDTO.setOrganizationCode("");
            }
        }

        // 岗位获取
        if (Objects.nonNull(userInfo.getPostId())) {
            List<AttendancePost> entPostList = postService.listByPostList(Arrays.asList(userInfo.getPostId()));
            if (CollectionUtils.isNotEmpty(entPostList)) {
                AttendancePost entPost = entPostList.get(0);
                userLeaveInfoDTO.setPostName(RequestInfoHolder.isChinese() ? entPost.getPostNameCn() : entPost.getPostNameEn());
            }
        }

        return userLeaveInfoDTO;
    }

    /**
     * 假期余额更新
     *
     * @param updateDTO
     * @return
     */
    public boolean update(UserLeaveUpdateDTO updateDTO) {
        AttendanceUser userInfo = userService.getByUserId(updateDTO.getId());
        if (userInfo == null) {
            return false;
        }
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveByUserId(updateDTO.getId());
        if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
            return false;
        }
        List<Long> detailIds = userLeaveDetailDOList.stream().map(o -> o.getId()).collect(Collectors.toList());
        UserLeaveStageDetailQuery stageDetailQuery = UserLeaveStageDetailQuery.builder()
                .leaveIdList(detailIds)
                .build();
        List<UserLeaveStageDetailDO> stageDetailDOList = userLeaveStageDetailService.selectByCondition(stageDetailQuery);
        if (CollectionUtils.isEmpty(stageDetailDOList)) {
            return false;
        }

        Map<Long, UserLeaveDetailUpdateDTO> detailUpdateDTOMap = updateDTO.getDetailDTOList()
                .stream()
                .collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
        Map<Long, List<UserLeaveStageDetailDO>> leaveStageGroup = stageDetailDOList.stream().collect(Collectors.groupingBy(o -> o.getLeaveId()));

        List<UserLeaveStageDetailDO> updateDOList = new ArrayList<>();
        List<UserLeaveRecordDO> recordDOList = new ArrayList<>();
        for (Map.Entry<Long, List<UserLeaveStageDetailDO>> entry : leaveStageGroup.entrySet()) {
            Long detailId = entry.getKey();
            UserLeaveDetailUpdateDTO userLeaveDetailUpdateDTO = detailUpdateDTOMap.get(detailId);
            if (userLeaveDetailUpdateDTO == null) {
                continue;
            }
            List<UserLeaveStageUpdateDTO> stageUpdateDTOList = userLeaveDetailUpdateDTO.getStageUpdateDTOList();
            List<UserLeaveStageDetailDO> stageList = entry.getValue();

            Map<Long, UserLeaveStageUpdateDTO> stageMap = stageUpdateDTOList.stream().collect(Collectors.toMap(o -> o.getId(), o -> o, (v1, v2) -> v1));
            for (UserLeaveStageDetailDO stageDetailDO : stageList) {
                UserLeaveStageUpdateDTO stageUpdateDTO = stageMap.get(stageDetailDO.getId());
                if (stageUpdateDTO == null) {
                    continue;
                }
                // 如果调整值为0，不做任何操作
                if (stageUpdateDTO.getLeaveAdjustmentMinutes().compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                // 获取新的假期余额
                BigDecimal nowLeaveResidueMinutes = stageUpdateDTO.getLeaveResidueMinutes();
                if (nowLeaveResidueMinutes == null) {
                    nowLeaveResidueMinutes = BigDecimal.ZERO;
                }
                UserLeaveRecordDO recordDO = new UserLeaveRecordDO();
                recordDO.setId(defaultIdWorker.nextId());
                recordDO.setConfigId(userLeaveDetailUpdateDTO.getConfigId());
                recordDO.setUserId(userInfo.getId());
                recordDO.setUserCode(userInfo.getUserCode());
                recordDO.setDate(new Date());
                recordDO.setDayId(Long.parseLong(DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN)));
                recordDO.setLeaveName(userLeaveDetailUpdateDTO.getLeaveName());
                recordDO.setLeaveType(userLeaveDetailUpdateDTO.getLeaveType());
                // 调整值为正数：销假  为负数：请假
                recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode());
                if (stageUpdateDTO.getLeaveAdjustmentMinutes().compareTo(BigDecimal.ZERO) >= 0) {
                    recordDO.setType(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode());
                }
                recordDO.setLeaveMinutes(stageUpdateDTO.getLeaveAdjustmentMinutes().abs());
                recordDO.setRemark(stageUpdateDTO.getRemark());
                BaseDOUtil.fillDOInsertByUsrOrSystem(recordDO);
                recordDO.setOperationUserCode(RequestInfoHolder.getUserCode());
                recordDO.setOperationUserName(RequestInfoHolder.getUserName());
                recordDOList.add(recordDO);

                stageDetailDO.setLeaveResidueMinutes(nowLeaveResidueMinutes);
                updateDOList.add(stageDetailDO);
            }
        }
        if (CollectionUtils.isNotEmpty(recordDOList)) {
            userLeaveRecordFactory.batchInsert(recordDOList);
        }
        if (CollectionUtils.isNotEmpty(updateDOList)) {
            updateDOList.forEach(item -> BaseDOUtil.fillDOUpdateByUserOrSystem(item));
            userLeaveStageDetailFactory.batchUpdateStageDetail(updateDOList);
        }
        return true;
    }

    /**
     * 用户假期余额操作记录查询
     *
     * @param pageQuery
     * @return
     */
    public PaginationResult<UserLeaveRecordDTO> record(UserLeaveRecordPageQuery pageQuery) {
        Page<UserLeaveRecordDO> page = PageHelper.startPage(pageQuery.getCurrentPage(), pageQuery.getShowCount(), pageQuery.getShowCount() > 0);
        // 查询假期记录
        UserLeaveRecordQuery query = UserLeaveRecordQuery.builder()
                .userId(pageQuery.getId())
                .configId(pageQuery.getConfigId())
                .leaveName(pageQuery.getLeaveName())
                .leaveType(pageQuery.getLeaveType())
                .build();
        PageInfo<UserLeaveRecordDO> pageInfo = page.doSelectPageInfo(() -> userLeaveRecordService.selectUserLeaveDetail(query));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), pageQuery);
        }
        List<UserLeaveRecordDTO> userLeaveRecordDTOList = UserLeaveMapstruct.INSTANCE.toUserLeaveRecordDTO(pageInfo.getList());
        userLeaveRecordDTOList.forEach(recordDTO -> {
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.LEAVE.getCode())) {
                //请假是支出
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.REDUCE.getDesc() : LeaveChangeType.REDUCE.getCode());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.CANCEL.getCode())) {
                //销假是收入
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.INCREASE.getDesc() : LeaveChangeType.INCREASE.getCode());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.ADMIN_OPERATION_LEAVE.getCode())) {
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.REDUCE.getDesc() : LeaveChangeType.REDUCE.getCode());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode())) {
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.INCREASE.getDesc() : LeaveChangeType.INCREASE.getCode());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.REISSUE.getCode())) {
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.REISSUE.getDesc() : LeaveChangeType.REISSUE.getCode());
                recordDTO.setRemark(RequestInfoHolder.isChinese() ? LeaveTypeEnum.REISSUE.getDesc() : LeaveTypeEnum.REISSUE.getDescEn());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.RECALCULATE.getCode())) {
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.REISSUE.getDesc() : LeaveChangeType.REISSUE.getCode());
                recordDTO.setRemark(RequestInfoHolder.isChinese() ? LeaveTypeEnum.RECALCULATE.getDesc() : LeaveTypeEnum.RECALCULATE.getDescEn());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.DEDUCTION.getCode())) {
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.DEDUCTION.getDesc() : LeaveChangeType.DEDUCTION.getCode());
                recordDTO.setRemark(RequestInfoHolder.isChinese() ? LeaveTypeEnum.DEDUCTION.getDesc() : LeaveTypeEnum.DEDUCTION.getDescEn());
            }
            if (StringUtils.equalsIgnoreCase(recordDTO.getType(), LeaveTypeEnum.IMPORT.getCode())) {
                recordDTO.setType(RequestInfoHolder.isChinese() ? LeaveChangeType.IMPORT.getDesc() : LeaveChangeType.IMPORT.getCode());
            }
        });
        return PageUtil.getPageResult(userLeaveRecordDTOList, pageQuery, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 查询员工假期聚合信息
     *
     * @param userCodeList
     * @return
     */
    public List<UserLeaveAggregationDTO> selectUserLeaveAggregation(List<String> userCodeList) {

        List<UserLeaveAggregationDTO> userLeaveAggregationList = Lists.newArrayList();
        // 查询用户
        List<AttendanceUser> userInfoList = userService.listByUserCodes(userCodeList);
        if (CollectionUtils.isEmpty(userInfoList)) {
            return Collections.emptyList();
        }
        // 查询假期规则
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder().status(StatusEnum.ACTIVE.getCode()).build();
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(companyLeaveQuery);

        // 查询用户对应的假期范围数据
        List<CompanyLeaveConfigRangDO> companyLeaveConfigRangList = companyLeaveConfigRangService.selectRangByUserCode(userCodeList);
        List<Long> rangeConfigIds = Optional.ofNullable(companyLeaveConfigRangList).orElse(Lists.newArrayList())
                .stream().map(item -> item.getLeaveId()).distinct().collect(Collectors.toList());
        List<CompanyLeaveConfigDO> companyLeaveConfigListForRange = companyLeaveConfigList
                .stream().filter(item -> rangeConfigIds.contains(item.getId())).collect(Collectors.toList());
        Map<String, List<CompanyLeaveConfigRangDO>> userRangeMap = Optional.ofNullable(companyLeaveConfigRangList)
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.groupingBy(CompanyLeaveConfigRangDO::getUserCode));

        // 查询用户的入职信息（计算失效日期使用）
        List<Long> userIds = userInfoList.stream().map(AttendanceUser::getId).collect(Collectors.toList());
        List<AttendanceUserEntryRecord> attendanceUserEntryRecords = userEntryRecordService.selectUserEntryByUserIds(userIds);
        Map<Long, List<AttendanceUserEntryRecord>> userEntryMap = Optional.ofNullable(attendanceUserEntryRecords)
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.groupingBy(AttendanceUserEntryRecord::getUserId));

        // 查询用户拥有的假期详情
        UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder().userIds(userIds).build();
        List<UserLeaveDetailDO> userLeaveDetailList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);

        // 查询详情对应的假期规则
        List<Long> detailConfigIds = Optional.ofNullable(userLeaveDetailList).orElse(Lists.newArrayList())
                .stream().map(item -> item.getConfigId()).distinct().collect(Collectors.toList());
        List<CompanyLeaveConfigDO> companyLeaveConfigListForDetail = companyLeaveConfigList
                .stream().filter(item -> detailConfigIds.contains(item.getId())).collect(Collectors.toList());
        Map<Long, List<UserLeaveDetailDO>> userDetailMap = Optional.ofNullable(userLeaveDetailList)
                .orElse(Lists.newArrayList())
                .stream().collect(Collectors.groupingBy(UserLeaveDetailDO::getUserId));

        // 查询拥有得假期规则对应得比例
        detailConfigIds.addAll(rangeConfigIds);
        List<CompanyLeaveItemConfigDO> userLeaveItemConfigList = companyLeaveConfigService.selectItemByConfigId(detailConfigIds);
        // 查询拥有的假期规则对应的结转规则及范围
        List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList = companyLeaveConfigCarryOverService.selectByLeaveId(detailConfigIds);
        List<Long> carryOverIds = Optional.ofNullable(companyLeaveConfigCarryOverList.stream().map(item -> item.getId()).collect(Collectors.toList())).orElse(Lists.newArrayList());
        List<CompanyLeaveConfigCarryOverRangeDO> companyLeaveConfigCarryOverRangeList = companyLeaveConfigCarryOverService.selectRangeByCarryOverId(carryOverIds);

        // 查询对应的假期余额
        List<Long> leaveIds = userLeaveDetailList.stream().map(item -> item.getId()).distinct().collect(Collectors.toList());
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = userLeaveStageDetailService.selectUserLeaveStageByLeaveIds(leaveIds);

        // 封装聚合数据
        for (AttendanceUser attendanceUser : userInfoList) {
            UserLeaveAggregationDTO userLeaveAggregation = UserLeaveAggregationDTO
                    .builder()
                    .id(attendanceUser.getId())
                    .userCode(attendanceUser.getUserCode())
                    .userName(attendanceUser.getUserName())
                    .companyLeaveConfigCarryOverList(companyLeaveConfigCarryOverList)
                    .companyLeaveConfigCarryOverRangeList(companyLeaveConfigCarryOverRangeList)
                    .userLeaveConfigRangList(Optional.ofNullable(userRangeMap.get(attendanceUser.getUserCode())).orElse(Lists.newArrayList()))
                    .userLeaveDetailList(Optional.ofNullable(userDetailMap.get(attendanceUser.getId())).orElse(Lists.newArrayList()))
                    .userLeaveItemConfigList(userLeaveItemConfigList)
                    .userEntryRecord(CollectionUtils.isEmpty(userEntryMap.get(attendanceUser.getId()))
                            ? null : userEntryMap.get(attendanceUser.getId()).get(0))
                    .build();
            // 封装用户假期范围对应的假期规则
            if (CollectionUtils.isNotEmpty(userLeaveAggregation.getUserLeaveConfigRangList())) {
                List<Long> configIds = Optional.ofNullable(userLeaveAggregation.getUserLeaveConfigRangList()
                                .stream().map(item -> item.getLeaveId()).distinct().collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());
                List<CompanyLeaveConfigDO> userLeaveConfigListForRange = Optional.ofNullable(companyLeaveConfigListForRange)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(item -> configIds.contains(item.getId())).collect(Collectors.toList());
                userLeaveAggregation.setUserLeaveConfigListForRange(Optional.ofNullable(userLeaveConfigListForRange)
                        .orElse(Lists.newArrayList()));
            }
            // 封装用户假期详情对应的假期规则及余额
            if (CollectionUtils.isNotEmpty(userLeaveAggregation.getUserLeaveDetailList())) {
                // 封装用户详情对应的假期规则
                List<Long> configIds = Optional.ofNullable(userLeaveAggregation.getUserLeaveDetailList()
                                .stream().map(item -> item.getConfigId()).distinct().collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());
                List<CompanyLeaveConfigDO> userLeaveConfigListForDetail = Optional.ofNullable(companyLeaveConfigListForDetail)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(item -> configIds.contains(item.getId())).collect(Collectors.toList());
                userLeaveAggregation.setUserLeaveConfigListForDetail(Optional.ofNullable(userLeaveConfigListForDetail)
                        .orElse(Lists.newArrayList()));
                // 封装余额
                List<Long> detailIds = Optional.ofNullable(userLeaveAggregation.getUserLeaveDetailList()
                                .stream().map(item -> item.getId()).distinct().collect(Collectors.toList()))
                        .orElse(Lists.newArrayList());
                List<UserLeaveStageDetailDO> userSelfStageList = Optional.ofNullable(userLeaveStageDetailList)
                        .orElse(Lists.newArrayList())
                        .stream()
                        .filter(item -> detailIds.contains(item.getLeaveId())).collect(Collectors.toList());
                userLeaveAggregation.setUserLeaveStageDetailList(Optional.ofNullable(userSelfStageList)
                        .orElse(Lists.newArrayList()));
            }
            userLeaveAggregationList.add(userLeaveAggregation);
        }
        return userLeaveAggregationList;
    }

    /**
     * 获取用户假期余额信息(审批使用)
     *
     * @param userId
     * @return
     */
    public List<UserLeaveResidualVO> selectUserLeaveResidual(Long userId) {
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (userInfo == null) {
            throw BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc()));
        }
        UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                .userId(userId)
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
        if (CollectionUtils.isEmpty(userLeaveDetailDOList)) {
            return new ArrayList<>();
        }
        List<Long> userLeaveIdList = userLeaveDetailDOList
                .stream()
                .map(item -> item.getId())
                .collect(Collectors.toList());
        List<UserLeaveStageDetailDO> userLeaveStageDetailDOList = userLeaveStageDetailService.selectByLeaveId(userLeaveIdList);
        Map<Long, List<UserLeaveStageDetailDO>> userLeaveIdMap = userLeaveStageDetailDOList
                .stream()
                .collect(Collectors.groupingBy(UserLeaveStageDetailDO::getLeaveId));
        // 人员假期通过configId关联
        List<Long> companyLeaveConfigIdList = userLeaveDetailDOList.stream()
                .filter(item -> Objects.nonNull(item.getConfigId()))
                .map(item -> item.getConfigId())
                .collect(Collectors.toList());
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .idList(companyLeaveConfigIdList).build();
        List<CompanyLeaveConfigDO> companyLeaveConfigDOList = companyLeaveConfigService.selectCompanyLeave(companyLeaveQuery);
        companyLeaveConfigDOList = companyLeaveConfigService.filterUserCompanyConfig(companyLeaveConfigDOList, userInfo);
        if (CollectionUtils.isEmpty(companyLeaveConfigDOList)) {
            return new ArrayList<>();
        }
        // 过滤用户请假时可选的假期
        this.filterUserLeaveCompanyConfig(companyLeaveConfigDOList, userInfo);
        //获取假期类型枚举(后续改为假期名称)
        Map<String, DictVO> leaveTypeEnumMap = dictService.getByTypeCode(BusinessConstant.SysDictDataTypeConstant.ATTENDANCE_LEAVE_TYPE);
        Map<String, DictVO> lowerleaveTypeEnumMap = leaveTypeEnumMap.entrySet().stream().collect(Collectors.toMap(item -> item.getKey().toLowerCase(), Map.Entry::getValue));

        Map<Long, List<CompanyLeaveConfigDO>> configIdMap = companyLeaveConfigDOList
                .stream()
                .collect(Collectors.groupingBy(CompanyLeaveConfigDO::getId));
        List<UserLeaveResidualVO> leaveResidualVOS = new ArrayList<>();
        for (UserLeaveDetailDO userLeaveDetailDO : userLeaveDetailDOList) {
            UserLeaveResidualVO residualVO = new UserLeaveResidualVO();
            residualVO.setConfigId(userLeaveDetailDO.getConfigId());
            residualVO.setLeaveName(userLeaveDetailDO.getLeaveName());
            residualVO.setLeaveNameByLang(userLeaveDetailDO.getLeaveName());
            residualVO.setLeaveType(userLeaveDetailDO.getLeaveType());
            residualVO.setLeaveTypeByLang(userLeaveDetailDO.getLeaveType());
            List<CompanyLeaveConfigDO> userLeaveConfig = configIdMap.get(userLeaveDetailDO.getConfigId());
            if (CollectionUtils.isEmpty(userLeaveConfig)) {
                continue;
            }
            // 多语转换
            DictVO dictLeaveName = lowerleaveTypeEnumMap.get(userLeaveDetailDO.getLeaveName().toLowerCase());
            if (Objects.nonNull(dictLeaveName)) {
                residualVO.setLeaveNameByLang(dictLeaveName.getDataValue());
            }
            residualVO.setLeaveShortName(userLeaveConfig.get(0).getLeaveShortName());
            residualVO.setConsumeLeaveType(userLeaveConfig.get(0).getConsumeLeaveType());
            residualVO.setIsUploadAttachment(userLeaveConfig.get(0).getIsUploadAttachment());
            residualVO.setUploadAttachmentCondition(userLeaveConfig.get(0).getUploadAttachmentCondition());
            residualVO.setAttachmentUnit(userLeaveConfig.get(0).getAttachmentUnit());
            residualVO.setLeaveUnit(userLeaveConfig.get(0).getLeaveUnit());
            residualVO.setMiniLeaveDuration(userLeaveConfig.get(0).getMiniLeaveDuration());
            List<UserLeaveStageDetailDO> stageDetailDOS = userLeaveIdMap.get(userLeaveDetailDO.getId());
            if (CollectionUtils.isEmpty(stageDetailDOS)) {
                continue;
            }
            BigDecimal leaveResidueMinutes = BigDecimal.ZERO;
            for (UserLeaveStageDetailDO stageDetailDO : stageDetailDOS) {
                if (stageDetailDO.getLeaveResidueMinutes() == null) {
                    continue;
                }
                leaveResidueMinutes = leaveResidueMinutes.add(stageDetailDO.getLeaveResidueMinutes());
            }
            residualVO.setLeaveResidueMinutes(leaveResidueMinutes);
            leaveResidualVOS.add(residualVO);
        }
        return leaveResidualVOS;
    }

    /**
     * 根据请假规则筛选可以选择的假期类型
     *
     * @param companyLeaveConfigList
     * @param userInfo
     */
    private void filterUserLeaveCompanyConfig(List<CompanyLeaveConfigDO> companyLeaveConfigList,
                                              AttendanceUser userInfo) {
        // 根据请假规则筛选可以选择的假期类型
        AttendanceUserEntryRecord userEntryRecord = userEntryRecordService.selectUserEntryByUserId(userInfo.getId());
        List<UserDynamicInfoDTO> userDynamicInfo = rpcUserClient.listUserDynamicInfo(Lists.newArrayList(userInfo.getUserCode())
                , Lists.newArrayList(UserDynamicFieldEnum.IS_PROBATION_PASS));
        Boolean isProbationPass;
        // 获取转正标识
        if (CollectionUtils.isNotEmpty(userDynamicInfo)) {
            String result = userDynamicInfo.get(0)
                    .getDynamicFieldMap()
                    .get(UserDynamicFieldEnum.IS_PROBATION_PASS.getKey());
            isProbationPass = StringUtils.isBlank(result) ? true : Boolean.parseBoolean(result);
        } else {
            isProbationPass = Boolean.TRUE;
        }
        // 通过请假条件筛选用户可选的假期
        if (Objects.isNull(userEntryRecord)) {
            return;
        }
        companyLeaveConfigList.removeIf(item -> {
            if (Objects.isNull(userEntryRecord.getConfirmDate())) {
                return false;
            }
            Integer restriction = item.getLeaveUsageRestrictions();
            if (Objects.nonNull(restriction) && restriction > BusinessConstant.ZERO) {
                AttendanceLeaveRestrictionEnum isInvalidEnum = AttendanceLeaveRestrictionEnum.getIsInvalidEnum(restriction);
                switch (isInvalidEnum) {
                    case ONE_YEAR:
                        return !DateFormatterUtil.compareOfYear(userEntryRecord.getConfirmDate(), Calendar.getInstance().getTime(), 1);
                    case TWO_YEAR:
                        return !DateFormatterUtil.compareOfYear(userEntryRecord.getConfirmDate(), Calendar.getInstance().getTime(), 2);
                    case REGULAR:
                        return !isProbationPass;
                    default:
                        return false;
                }
            }
            return false;
        });
    }

    /**
     * 校验两个时间是否有交集
     *
     * @param startTime
     * @param endTime
     * @param issueTime
     * @param invalidTime
     * @return
     */
    private Boolean checkInterSection(Long startTime, Long endTime, Long issueTime, Long invalidTime) {
        // max(startTime,issueTime)
        Long maxStartTime = Long.compare(startTime, issueTime) >= 0 ? startTime : issueTime;
        // min(endTime,invalidTime)
        Long minEndTime = Long.compare(endTime, invalidTime) <= 0 ? endTime : invalidTime;
        if (maxStartTime.compareTo(minEndTime) <= 0) {
            return true;
        }
        return false;
    }


    /**
     * 构建可用假期实体（拷贝hrms方法）
     *
     * @param resultList
     * @param leaveDetailDO
     * @param stageDetailDOList
     */
    private void buildAvailableLeaveDTO(List<UserAvailableLeaveDTO> resultList,
                                        UserLeaveDetailDO leaveDetailDO,
                                        List<UserLeaveStageDetailDO> stageDetailDOList) {
        UserAvailableLeaveDTO userAvailableLeaveDTO = new UserAvailableLeaveDTO();
        userAvailableLeaveDTO.setId(leaveDetailDO.getId());
        userAvailableLeaveDTO.setLeaveType(leaveDetailDO.getLeaveType());
        userAvailableLeaveDTO.setUserId(leaveDetailDO.getUserId());
        userAvailableLeaveDTO.setUserCode(leaveDetailDO.getUserCode());
        List<UserStageLeaveDTO> userStageLeaveDTOList = new ArrayList<>();
        stageDetailDOList.forEach(record -> {
            UserStageLeaveDTO userStageLeaveDTO = new UserStageLeaveDTO();
            userStageLeaveDTO.setId(record.getId());
            userStageLeaveDTO.setPercentSalary(record.getPercentSalary());
            userStageLeaveDTO.setStage(record.getStage());
            userStageLeaveDTO.setStatus(record.getStatus());
            userStageLeaveDTOList.add(userStageLeaveDTO);
        });
        userAvailableLeaveDTO.setUserStageLeaveDTOList(userStageLeaveDTOList);
        resultList.add(userAvailableLeaveDTO);
    }

    // 用户事件通知相关方法

    /**
     * 人员基础信息变动事件通知修改假期绑定范围
     *
     * @param userInfo
     */
    @Transactional
    public void userLeaveConfigRangeHandler(UserInfoDO userInfo) {
        if (Objects.isNull(userInfo) || StringUtils.isBlank(userInfo.getUserCode())) {
            log.info("userLeaveConfigRangeHandler | 参数异常");
            return;
        }
        log.info("userLeaveConfigRangeHandler:{}", userInfo.getUserCode());
        // 处理假期数据
        // 1. 删除之前绑定的范围数据
        List<CompanyLeaveConfigRangDO> updateConfigRang = companyLeaveConfigRangService.selectRangByUserCode(Collections.singletonList(userInfo.getUserCode()));
        // 2. 保存用户原假期范围历史记录
        handlerCompanyLeaveConfigHistory(userInfo.getId(), updateConfigRang);
        // 3. 新增新的范围数据
        handlerCompanyLeaveConfigRang(userInfo.getId(), updateConfigRang);
    }

    /**
     * 新员工入职，增加假期信息
     *
     * @param attendanceUser
     */
    public void userEntryAddLeaveInfo(AttendanceUser attendanceUser) {
        log.info("userEntryAddLeaveInfo user entry confirm add holiday param：{}", attendanceUser);
        if (attendanceUser == null || attendanceUser.getId() == null || attendanceUser.getLocationCountry() == null) {
            return;
        }
        //查询用户信息
        UserInfoDO userInfo = userInfoManage.getByUserId(attendanceUser.getId());
        if (userInfo == null) {
            return;
        }
        //已经离职的不处理
        if (WorkStatusEnum.DIMISSION.getCode().equals(userInfo.getWorkStatus())) {
            return;
        }
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(attendanceUser.getId())) {
            log.info("userEntryAddLeaveInfo | userInfo is on old attendance, userCode:{}" + userInfo.getUserCode());
            return;
        }
        // 过滤用工类型
        if (ObjectUtil.isNotEmpty(userInfo.getEmployeeType())) {
            List<String> defaultEmployeeType = Arrays.asList(EmploymentTypeEnum.EMPLOYEE.getCode(), EmploymentTypeEnum.SUB_EMPLOYEE.getCode(),
                    EmploymentTypeEnum.INTERN.getCode(), EmploymentTypeEnum.PART_TIMER.getCode(), EmploymentTypeEnum.CONSULTANT.getCode());
            if (!defaultEmployeeType.contains(userInfo.getEmployeeType())) {
                log.info("userEntryAddLeaveInfo user entry confirm：{}, employeeType：{}，no need add holiday", userInfo.getUserCode(), userInfo.getEmployeeType());
                return;
            }
        }
        // 查询国家假期配置
        List<CompanyLeaveConfigDO> companyLeaveConfigList;
        if (BusinessConstant.Y.equals(userInfo.getIsGlobalRelocation())) {
            // 派遣人员根据国籍查询对应假期
            String countryCode = userInfo.getCountryCode();
            if (StringUtils.isBlank(countryCode)) {
                log.info("userEntryAddLeaveInfo | userCode：{},countryCode：{},No countryCode", userInfo.getUserCode(), userInfo.getCountryCode());
                return;
            }
            String locationCountryCode = CountryCodeEnum.convert2InternalCountryCode(countryCode);
            if (StringUtils.isBlank(locationCountryCode)) {
                log.info("userEntryAddLeaveInfo | userCode：{},locationCountry：{},No locationCountryCode", userInfo.getUserCode(), locationCountryCode);
                return;
            }
            CompanyLeaveQuery leaveQuery = CompanyLeaveQuery.builder()
                    .country(locationCountryCode)
                    .isDispatch(BusinessConstant.Y)
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            companyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(leaveQuery);
        } else {
            // 非派遣人员根据常驻国查询对应假期
            companyLeaveConfigList = companyLeaveConfigService.selectLeaveConfigByCountryList(Collections.singletonList(userInfo.getLocationCountry()));
            if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
                log.info("userEntryAddLeaveInfo | userCode：{},locationCountry：{},No national holidays configured", userInfo.getUserCode(), userInfo.getLocationCountry());
                return;
            }
        }
        //默认有事假
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            //todo
            log.info("userEntryAddLeaveInfo: user entry confirm：{} country：{} National holidays are not configured", userInfo.getUserCode(), userInfo.getLocationCountry());
            return;
        }
        log.info("start bind the holiday range");
        List<CompanyLeaveConfigRangDO> targetListRange = new ArrayList<>();
        // 处理该假期配置的范围
        for (CompanyLeaveConfigDO leaveConfig : companyLeaveConfigList) {
            log.info("假期id：{}，假期类型：{}", leaveConfig.getId(), leaveConfig.getLeaveType());
            // 重构修改 判断是否满足假期配置条件
            if (companyLeaveConfigService.checkInCondition(leaveConfig, userInfo)) {
                targetListRange.add(this.buildConfigRange(leaveConfig, userInfo.getUserCode()));
            }
        }
        if (CollUtil.isEmpty(targetListRange)) {
            log.info("该用户：{}，不满足该国家：{}，下所有假期的条件，未绑定假期范围表", userInfo.getUserCode(), userInfo.getLocationCountry());
            return;
        }
        log.info("用户：{}，绑定假期范围表：{}", userInfo.getUserCode(), targetListRange);
        // 保存用户假期范围
        companyLeaveConfigManage.handlerUserLeaveConfigRange(targetListRange, null);
    }

    private void handlerCompanyLeaveConfigHistory(Long userId,
                                                  List<CompanyLeaveConfigRangDO> updateConfigRang) {
        log.info("handlerCompanyLeaveConfigHistory | userId:{}", userId);
        if (ObjectUtil.isNull(userId)) {
            log.info("handlerCompanyLeaveConfigHistory | userId is null");
            return;
        }
        // 查询用户信息
        AttendanceUser userInfo = userService.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo)) {
            log.info("handlerCompanyLeaveConfigHistory | userInfo is null");
            return;
        }
        // 已经离职的不处理
        if (WorkStatusEnum.DIMISSION.getCode().equals(userInfo.getWorkStatus())) {
            return;
        }
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(userId)) {
            log.info("handlerCompanyLeaveConfigHistory | userInfo is on old attendance, userCode:{}" + userInfo.getUserCode());
            return;
        }
        // 获取当前用户需要删除的历史假期范围
        List<UserLeaveConfigHistoryDO> deleteUserLeaveHistory = userLeaveConfigHistoryService.getUserLeaveHistoryByUserId(userId);
        // 获取当前用户需要新增的历史假期范围
        List<UserLeaveConfigHistoryDO> addUserLeaveHistory = userLeaveConfigHistoryService.getAddUserLeaveHistoryByRange(updateConfigRang, userInfo);
        if (CollUtil.isEmpty(addUserLeaveHistory)) {
            log.info("该用户：{}，原先没有绑定的假期范围，不需要新增假期范围历史记录", userInfo.getUserCode());
        }
        log.info("用户：{}，绑定假期历史范围表：{}", userInfo.getUserCode(), addUserLeaveHistory);
        // 新增/修改假期历史数据范围
        companyLeaveConfigManage.handlerUserLeaveConfigHistoryRange(addUserLeaveHistory, deleteUserLeaveHistory);
    }

    private void handlerCompanyLeaveConfigRang(Long userId,
                                               List<CompanyLeaveConfigRangDO> updateConfigRang) {
        log.info("handlerCompanyLeaveConfigRang | userId:{}", userId);
        if (ObjectUtil.isNull(userId)) {
            log.info("handlerCompanyLeaveConfigRang | userId is null");
            return;
        }
        // 查询用户信息
        UserInfoDO userInfo = userInfoManage.getByUserId(userId);
        if (ObjectUtil.isNull(userInfo)) {
            log.info("handlerCompanyLeaveConfigRang | userInfo is null");
            return;
        }
        // 已经离职的不处理
        if (WorkStatusEnum.DIMISSION.getCode().equals(userInfo.getWorkStatus())) {
            return;
        }
        // 不在灰度人员里走旧系统，新系统不处理
        if (!migrationService.verifyUserIsEnableNewAttendance(userId)) {
            log.info("handlerCompanyLeaveConfigHistory | userInfo is on old attendance, userCode:{}" + userInfo.getUserCode());
            return;
        }

        // 查询国家假期配置
        List<CompanyLeaveConfigDO> companyLeaveConfigList;
        if (BusinessConstant.Y.equals(userInfo.getIsGlobalRelocation())) {
            // 派遣人员根据国籍查询对应假期
            String countryCode = userInfo.getCountryCode();
            if (StringUtils.isBlank(countryCode)) {
                log.info("handlerCompanyLeaveConfigRang userCode：{},countryCode：{},No countryCode", userInfo.getUserCode(), userInfo.getCountryCode());
                return;
            }
            String locationCountryCode = CountryCodeEnum.convert2InternalCountryCode(countryCode);
            if (StringUtils.isBlank(locationCountryCode)) {
                log.info("handlerCompanyLeaveConfigRang userCode：{},locationCountry：{},No locationCountryCode", userInfo.getUserCode(), locationCountryCode);
                return;
            }
            CompanyLeaveQuery leaveQuery = CompanyLeaveQuery.builder()
                    .country(locationCountryCode)
                    .isDispatch(BusinessConstant.Y)
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            companyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(leaveQuery);
        } else {
            // 非派遣人员根据常驻国查询对应假期
            companyLeaveConfigList = companyLeaveConfigService.selectLeaveConfigByCountryList(Collections.singletonList(userInfo.getLocationCountry()));
            if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
                log.info("handlerCompanyLeaveConfigRang userCode：{},locationCountry：{},No national holidays configured", userInfo.getUserCode(), userInfo.getLocationCountry());
                return;
            }
        }

        if (CollUtil.isNotEmpty(updateConfigRang)) {
            for (CompanyLeaveConfigRangDO rang : updateConfigRang) {
                rang.setIsDelete(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdateByUserOrSystem(rang);
            }
        }
        // 处理新增假期范围
        List<CompanyLeaveConfigRangDO> targetListRange = new ArrayList<>();
        // 处理该假期配置的范围
        for (CompanyLeaveConfigDO leaveConfig : companyLeaveConfigList) {
            log.info("假期id：{}，假期类型：{}", leaveConfig.getId(), leaveConfig.getLeaveType());

            // 重构修改 判断是否满足假期配置条件
            if (companyLeaveConfigService.checkInCondition(leaveConfig, userInfo)) {
                targetListRange.add(this.buildConfigRange(leaveConfig, userInfo.getUserCode()));
            }
        }
        if (CollUtil.isEmpty(targetListRange)) {
            log.info("该用户：{}，不满足该国家：{}，下所有假期的条件，未绑定假期范围表", userInfo.getUserCode(), userInfo.getLocationCountry());
        }
        log.info("用户：{}，绑定假期范围表：{}", userInfo.getUserCode(), targetListRange);
        // 新增/修改假期范围
        companyLeaveConfigManage.handlerUserLeaveConfigRange(targetListRange, updateConfigRang);
    }

    private CompanyLeaveConfigRangDO buildConfigRange(CompanyLeaveConfigDO leaveConfig,
                                                      String userCode) {
        // 给用户设置假期范围
        CompanyLeaveConfigRangDO leaveConfigRang = new CompanyLeaveConfigRangDO();
        leaveConfigRang.setId(defaultIdWorker.nextId());
        leaveConfigRang.setLeaveId(leaveConfig.getId());
        leaveConfigRang.setRangeType(LeaveConfigRangRangeTypeEnum.DEPT.getType());
        leaveConfigRang.setUserCode(userCode);
        BaseDOUtil.fillDOInsertByUsrOrSystem(leaveConfigRang);
        return leaveConfigRang;
    }

}
