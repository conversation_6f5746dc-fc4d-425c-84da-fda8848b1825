package com.imile.attendance.report.day.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.day.job.service.AttendanceDayReportJobService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 考勤日报定时任务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/18
 */
@Slf4j
@Component
public class AttendanceDayReportHandler {

    @Resource
    AttendanceDayReportJobService dayReportJobService;

    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_DAY_REPORT_HANDLER)
    public ReturnT<String> attendanceDayReportHandler(String content) {
        DayReportJobParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, DayReportJobParam.class)
                : new DayReportJobParam();
        // 校验参数
        if (!this.checkParam(param)) {
            return ReturnT.SUCCESS;
        }
        // 定时任务执行默认参数
        this.buildDefaultParam(param);
        dayReportJobService.attendanceDayReportInit(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 参数校验
     *
     * @param param
     * @return
     */
    private Boolean checkParam(DayReportJobParam param) {
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHandler】参数为空");
            return false;
        }

        if (param.getIsUseCustomLocalTime() && StringUtils.isBlank(param.getLocalDate())) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHandler】参数自定义日期未填写");
            return false;
        }
        return true;
    }

    /**
     * 参数默认值设置
     *
     * @param param
     */
    private void buildDefaultParam(DayReportJobParam param) {
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            param.setUserCodeList(Arrays.asList(param.getUserCodes().split(",")));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            param.setCountryArrayList(Arrays.asList(param.getCountryList().split(",")));
        }
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            param.setEmployeeTypeList(Arrays.asList(param.getEmployeeType().split(",")));
        } else {
            param.setEmployeeTypeList(EmploymentTypeEnum.ALL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
        }
    }

}
