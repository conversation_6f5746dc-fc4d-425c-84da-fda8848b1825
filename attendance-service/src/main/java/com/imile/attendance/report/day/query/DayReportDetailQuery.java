package com.imile.attendance.report.day.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.common.constant.ValidCodeConstant;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class DayReportDetailQuery {

    /**
     * 考勤日报id
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL)
    private Long dayReportId;

    /**
     * 当前日期(yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "dateTime can not be null")
    private Date dateTime;
}
