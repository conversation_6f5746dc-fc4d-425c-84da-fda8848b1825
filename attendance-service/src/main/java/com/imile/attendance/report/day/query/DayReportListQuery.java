package com.imile.attendance.report.day.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.query.ResourceQuery;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class DayReportListQuery extends ResourceQuery {
    private static final long serialVersionUID = -8148427358861211135L;

    /**
     * 用工类型
     */
    private List<String> employeeTypeList;

    /**
     * 用户id(多选)
     */
    private List<Long> userIds;

    /**
     * 岗位id(多选)
     */
    private List<Long> postIds;

    /**
     * 部门id(多选)
     */
    private List<Long> deptIds;

    /**
     * 常驻国
     */
    private String locationCountry;
    /**
     * 常驻省
     */
    private String locationProvince;
    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 打卡类型
     */
    private String punchType;

    /**
     * 查询开始时间(yyyyMMdd)
     */
    @NotNull(message = "start day can not be null")
    private Long startDay;

    /**
     * 查询结束时间(yyyyMMdd)
     */
    @NotNull(message = "end day can not be null")
    private Long endDay;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 初始考勤结果
     */
    private String initResult;

    /**
     * 最终考勤结果
     */
    private String finalResult;

    /**
     * 当前日期(yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dateTime;

}
