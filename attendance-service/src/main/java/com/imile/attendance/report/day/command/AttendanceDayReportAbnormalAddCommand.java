package com.imile.attendance.report.day.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Data
public class AttendanceDayReportAbnormalAddCommand {

    @ApiModelProperty(value = "员工异常表主键")
    private Long abnormalId;

    @ApiModelProperty(value = "异常类型")
    private String abnormalType;

    @ApiModelProperty(value = "异常状态")
    private String abnormalStatus;

    @ApiModelProperty(value = "异常班次主键")
    private Long punchClassId;

    @ApiModelProperty(value = "异常时段主键")
    private Long punchClassItemConfigId;
}
