package com.imile.attendance.report.day.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Data
public class AttendanceDayReportFormAddCommand {

    @ApiModelProperty(value = "单据主键")
    private Long formId;

    @ApiModelProperty(value = "单据编码")
    private String applicationCode;

    @ApiModelProperty(value = "审批单id")
    private Long approvalId;

    @ApiModelProperty(value = "单据类型")
    private String formType;

    @ApiModelProperty(value = "单据状态")
    private String formStatus;
}
