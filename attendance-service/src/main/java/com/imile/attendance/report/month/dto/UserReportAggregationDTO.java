package com.imile.attendance.report.month.dto;

import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserDayConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工月报聚合DTO
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserReportAggregationDTO {

    /**
     * 员工主键
     */
    private Long id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 帐号
     */
    private String userCode;

    /**
     * 员工考勤明细
     */
    List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailList;

    /**
     * 员工时间范围内单据
     */
    List<AttendanceFormAttrDO> formAttrList;

    /**
     * 已通过单据
     */
    List<AttendanceFormDO> passAndInViewFormList;

    List<AttendanceFormAttrDO> passAndInViewFormAttrList;

    /**
     * 员工对应日历
     */
    CalendarConfigDO calendarConfig;
    List<CalendarConfigDetailDO> calendarConfigDetailList;

    /**
     * 员工对应打卡记录
     */
    List<UserPunchRecordDTO> userPunchRecordList;

    /**
     * 员工时间范围内异常
     */
    List<EmployeeAbnormalAttendanceDO> abnormalAttendanceList;

    /**
     * 员工时间范围内异常操作记录
     */
    List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList;

    /**
     * 员工时间范围排班
     */
    List<UserShiftConfigDO> userShiftConfigList;

    /**
     * 员工时间范围内班次
     */
    List<PunchClassConfigDO> classConfigList;

    /**
     * 员工时间范围内班次时段
     */
    List<PunchClassItemConfigDO> classItemConfigList;

    /**
     * 员工时间范围内打卡规则
     */
    List<UserDayConfigDTO> userDayConfigDTOList;
}
