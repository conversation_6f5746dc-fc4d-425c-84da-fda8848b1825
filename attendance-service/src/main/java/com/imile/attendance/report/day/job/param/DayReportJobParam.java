package com.imile.attendance.report.day.job.param;

import com.imile.attendance.common.param.CountryDateParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/18
 * @Description 用户考勤日报定时任务参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class DayReportJobParam extends CountryDateParam {
    /**
     * 所属国
     */
    private String countryList;

    /**
     * 用户编码
     */
    private String userCodes;

    /**
     * 用工类型
     */
    private String employeeType;


    /**
     * 程序逻辑转换
     */
    @NotNull
    private List<String> employeeTypeList = new ArrayList<>();
    @NotNull
    private List<String> userCodeList = new ArrayList<>();
    @NotNull
    private List<String> countryArrayList = new ArrayList<>();
    @NotNull
    private List<Long> userIdList = new ArrayList<>();
}