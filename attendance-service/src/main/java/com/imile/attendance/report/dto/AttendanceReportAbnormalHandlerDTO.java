package com.imile.attendance.report.dto;

import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 异常计算日报处理参数
 *
 * <AUTHOR>
 * @since 2025/6/19
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceReportAbnormalHandlerDTO {

    /**
     * 考勤日期
     */
    private Long attendanceDayId;

    /**
     * 用户
     */
    private UserInfoDO userInfoDO;

    /**
     * 用户入职日期
     */
    private Date entryDate;

    /**
     * 用户离职日期
     */
    private Date dimissionDate;

    /**
     * 日历
     */
    private CalendarConfigDO calendarConfigDO;

    /**
     * 打卡规则
     */
    private PunchConfigDO punchConfigDO;

    /**
     * 排班信息
     */
    private UserShiftConfigDO userShiftConfigDO;

    /**
     * 班次信息
     */
    private PunchClassConfigDTO punchClassConfigDTO;

    /**
     * 补卡规则
     */
    private ReissueCardConfigDO reissueCardConfigDO;

    /**
     * 加班规则
     */
    private OverTimeConfigDO overTimeConfigDO;

    /**
     * 正常出勤
     */
    private List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList;

    /**
     * 用户异常考勤数据
     */
    private List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList;

    /**
     * 用户正常考勤快照数据
     */
    private List<AttendanceEmployeeDetailSnapshotDO> attendanceEmployeeDetailSnapshotDOList;

    /**
     * 用户异常考勤快照数据
     */
    private List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList;

    /**
     * 打卡记录
     */
    private List<UserPunchRecordBO> punchRecordDOList;

    /**
     * 用户审批通过的单据
     */
    private List<AttendanceFormDetailBO> userPassFormBOList;
}
