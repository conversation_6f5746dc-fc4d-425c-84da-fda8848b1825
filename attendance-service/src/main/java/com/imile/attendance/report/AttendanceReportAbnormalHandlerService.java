package com.imile.attendance.report;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.AttendanceMinuteCalculateService;
import com.imile.attendance.abnormal.dto.AttendanceMinuteDTO;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemConfigDateDTO;
import com.imile.attendance.abnormal.dto.DayItemInfoDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceConcreteTypeEnum;
import com.imile.attendance.enums.AttendanceDataSourceEnum;
import com.imile.attendance.enums.AttendanceReportStatusEnum;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalAttendanceStatusEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.form.AttendanceApprovalFormManage;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.form.dao.AttendanceFormDao;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportAbnormalDao;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportDao;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportFormDao;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.dao.UserShiftConfigDao;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.report.day.dto.DayClassTimeDTO;
import com.imile.attendance.report.dto.AttendanceReportAbnormalHandlerDTO;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 异常计算后考勤日报处理
 *
 * <AUTHOR>
 * @since 2025/6/19
 */

@Service
@Slf4j
public class AttendanceReportAbnormalHandlerService {
    @Resource
    private AttendanceDayReportDao attendanceDayReportDao;
    @Resource
    private AttendanceDayReportFormDao attendanceDayReportFormDao;
    @Resource
    private AttendanceDayReportAbnormalDao attendanceDayReportAbnormalDao;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceFormDao attendanceFormDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendanceDayReportManage attendanceDayReportManage;
    @Resource
    private AttendanceApprovalFormManage overTimeFormManage;
    @Resource
    private AttendanceMinuteCalculateService attendanceMinuteCalculateService;
    @Resource
    private AttendanceFormManage attendanceFormManage;
    @Resource
    private UserShiftConfigDao userShiftConfigDao;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;

    @Async("bizTaskThreadPool")
    public void execute(AttendanceReportAbnormalHandlerDTO param) {
        if (!checkValidParam(param)) {
            log.info("考勤日报处理参数校验异常, {}", JSON.toJSONString(param));
            return;
        }
        UserInfoDO userInfoDO = param.getUserInfoDO();
        AttendanceDayReportDO attendanceDayReportDO = attendanceDayReportDao.selectByUserCodeAndDayId(userInfoDO.getUserCode(), param.getAttendanceDayId());
        if (Objects.isNull(attendanceDayReportDO)) {
            return;
        }
        // 走更新逻辑
        // 封装用户日报基本信息
        this.buildUserBaseReportInfo(attendanceDayReportDO, param);
        // 异常计算
        Date finalDateNow = DateUtil.endOfDay(DateHelper.transferDayIdToDate(param.getAttendanceDayId()));
        ReissueCardConfigDO reissueCardConfigDO = reissueCardConfigManage.selectByUserIdAndDate(userInfoDO.getId(), finalDateNow);
        if (Objects.nonNull(reissueCardConfigDO)) {
            attendanceDayReportDO.setReissueCardConfigId(reissueCardConfigDO.getId());
        }
        OverTimeConfigDO overTimeConfigDO = overTimeConfigManage.selectByUserIdAndDate(userInfoDO.getId(), finalDateNow);
        if (Objects.nonNull(overTimeConfigDO)) {
            attendanceDayReportDO.setOverTimeConfigId(overTimeConfigDO.getId());
        }
        // 日报逻辑计算
        List<AttendanceDayReportFormDO> addAttendanceDayReportFormDOList = new ArrayList<>();
        List<AttendanceDayReportFormDO> updateAttendanceDayReportFormDOList = new ArrayList<>();
        List<AttendanceDayReportAbnormalDO> addAttendanceDayReportAbnormalDOList = new ArrayList<>();
        List<AttendanceDayReportAbnormalDO> updateAttendanceDayReportAbnormalDOList = new ArrayList<>();
        this.dayReportHandler(attendanceDayReportDO, param,
                addAttendanceDayReportFormDOList, updateAttendanceDayReportFormDOList,
                addAttendanceDayReportAbnormalDOList, updateAttendanceDayReportAbnormalDOList);
        attendanceDayReportManage.attendanceDayReportUpdate(attendanceDayReportDO, addAttendanceDayReportAbnormalDOList, updateAttendanceDayReportAbnormalDOList, addAttendanceDayReportFormDOList, updateAttendanceDayReportFormDOList);
    }

    /**
     * 批量处理
     *
     * @param paramList
     */
    public void batchExecute(List<AttendanceReportAbnormalHandlerDTO> paramList) {
        List<AttendanceDayReportDO> addDayReportDOList = new ArrayList<>();
        List<AttendanceDayReportDO> updateDayReportDOList = new ArrayList<>();
        List<AttendanceDayReportFormDO> addAttendanceDayReportFormDOList = new ArrayList<>();
        List<AttendanceDayReportFormDO> updateAttendanceDayReportFormDOList = new ArrayList<>();
        List<AttendanceDayReportAbnormalDO> addAttendanceDayReportAbnormalDOList = new ArrayList<>();
        List<AttendanceDayReportAbnormalDO> updateAttendanceDayReportAbnormalDOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(paramList)) {
            log.info("考勤日报批量处理参数为空");
            return;
        }
        for (AttendanceReportAbnormalHandlerDTO param : paramList) {
            if (!checkValidParam(param)) {
                log.info("考勤日报处理参数校验异常, {}", JSON.toJSONString(param));
                return;
            }
            UserInfoDO userInfoDO = param.getUserInfoDO();
            AttendanceDayReportDO attendanceDayReportDO = attendanceDayReportDao.selectByUserCodeAndDayId(userInfoDO.getUserCode(), param.getAttendanceDayId());
            if (Objects.isNull(attendanceDayReportDO)) {
                // 走新增逻辑
                attendanceDayReportDO = new AttendanceDayReportDO();
                attendanceDayReportDO.setId(defaultIdWorker.nextId());
                BaseDOUtil.fillDOInsertByUsrOrSystem(attendanceDayReportDO);
                addDayReportDOList.add(attendanceDayReportDO);
            } else {
                // 走更新逻辑
                updateDayReportDOList.add(attendanceDayReportDO);
            }
            // 封装用户日报基本信息
            this.buildUserBaseReportInfo(attendanceDayReportDO, param);
            // 日报逻辑计算
            this.dayReportHandler(attendanceDayReportDO, param,
                    addAttendanceDayReportFormDOList, updateAttendanceDayReportFormDOList,
                    addAttendanceDayReportAbnormalDOList, updateAttendanceDayReportAbnormalDOList);
        }
        // 默认值
        buildDefaultVal(addDayReportDOList);
        attendanceDayReportManage.attendanceDayReportBatch(addDayReportDOList, updateDayReportDOList,
                addAttendanceDayReportAbnormalDOList, updateAttendanceDayReportAbnormalDOList,
                addAttendanceDayReportFormDOList, updateAttendanceDayReportFormDOList);
    }

    /**
     * 异常快照生成修改日报
     *
     * @param employeeDetailSnapshotList
     * @param abnormalAttendanceSnapshotList
     * @param noNeedWorkUserIdList
     */
    public void batchSnapShotExecute(Long dayId,
                                     List<AttendanceEmployeeDetailSnapshotDO> employeeDetailSnapshotList,
                                     List<EmployeeAbnormalAttendanceSnapshotDO> abnormalAttendanceSnapshotList,
                                     Set<Long> noNeedWorkUserIdList) {
        if (CollectionUtils.isEmpty(employeeDetailSnapshotList)
                && CollectionUtils.isEmpty(abnormalAttendanceSnapshotList)
                && CollectionUtils.isEmpty(noNeedWorkUserIdList)) {
            log.info("batchSnapShotExecute | 快照表及免打卡数据为空, 跳过...");
            return;
        }
        if (Objects.isNull(dayId)) {
            log.info("batchSnapShotExecute | dayId为空, 跳过...");
            return;
        }
        // 1.获取需要更新的用户
        List<Long> userIds = employeeDetailSnapshotList
                .stream()
                .map(AttendanceEmployeeDetailSnapshotDO::getUserId)
                .distinct()
                .collect(Collectors.toList());
        userIds.addAll(abnormalAttendanceSnapshotList
                .stream()
                .map(EmployeeAbnormalAttendanceSnapshotDO::getUserId)
                .distinct()
                .collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(noNeedWorkUserIdList)) {
            userIds.addAll(noNeedWorkUserIdList);
        }
        userIds = userIds.stream().distinct().collect(Collectors.toList());
        // 2.获取日报表信息
        List<AttendanceDayReportDO> attendanceDayReportDOList = attendanceDayReportManage.listDayReportByUserIdsAndDayIds(userIds, Arrays.asList(dayId));
        if (CollectionUtils.isEmpty(attendanceDayReportDOList)) {
            log.info("batchSnapShotExecute | 没有获取到日报信息, 跳过...");
            return;
        }
        // 3.需要更新的集合
        List<AttendanceDayReportDO> updateDayReportDOList = new ArrayList<>();
        Map<Long, List<AttendanceEmployeeDetailSnapshotDO>> userEmployeeDetailSnapShotMap = employeeDetailSnapshotList
                .stream().collect(Collectors.groupingBy(AttendanceEmployeeDetailSnapshotDO::getUserId));
        Map<Long, List<EmployeeAbnormalAttendanceSnapshotDO>> userAbnormalSnapShotMap = abnormalAttendanceSnapshotList
                .stream().collect(Collectors.groupingBy(EmployeeAbnormalAttendanceSnapshotDO::getUserId));
        for (AttendanceDayReportDO dayReportDO : attendanceDayReportDOList) {
            Long userId = dayReportDO.getUserId();
            // 免打卡且不是休息日
            if (Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), dayReportDO.getPunchConfigType())
                    && !DayShiftRuleEnum.isRestDay(dayReportDO.getDayShiftRule())) {
                log.info("batchSnapShotExecute | 当前用户日报中打卡规则是免打卡, 用户编码:" + dayReportDO.getUserCode());
                dayReportDO.setInitResult(AttendanceReportStatusEnum.NORMAL.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(dayReportDO);
                updateDayReportDOList.add(dayReportDO);
                continue;
            }
            List<AttendanceEmployeeDetailSnapshotDO> userEmployeeDetailSnapshotList = userEmployeeDetailSnapShotMap.get(userId);
            List<EmployeeAbnormalAttendanceSnapshotDO> userAbnormalAttendanceSnapshotList = userAbnormalSnapShotMap.get(userId);
            if (CollectionUtils.isEmpty(userEmployeeDetailSnapshotList)
                    && CollectionUtils.isEmpty(userAbnormalAttendanceSnapshotList)) {
                log.info("batchSnapShotExecute | 没有获取当前用户快照信息, 用户编码:" + dayReportDO.getUserCode());
                continue;
            }
            dayReportDO.setInitResult(this.getInitResult(userAbnormalAttendanceSnapshotList, dayReportDO));
            BaseDOUtil.fillDOUpdateByUserOrSystem(dayReportDO);
            updateDayReportDOList.add(dayReportDO);
        }
        // 4.批量更新
        attendanceDayReportDao.updateBatchById(updateDayReportDOList);
    }

    private boolean checkValidParam(AttendanceReportAbnormalHandlerDTO param) {
        if (Objects.isNull(param)
                || Objects.isNull(param.getAttendanceDayId())
                || Objects.isNull(param.getUserInfoDO())) {
            return false;
        }
        return true;
    }

    private void buildFormMinutes(List<Long> formIdList,
                                  AttendanceDayReportDO attendanceDayReportDO,
                                  List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList) {
        //请假/外勤时长
        BigDecimal leaveMinutes = BigDecimal.ZERO;
        BigDecimal oooMinutes = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
            attendanceDayReportDO.setLeaveMinutes(leaveMinutes);
            attendanceDayReportDO.setOooMinutes(oooMinutes);
            return;
        }

        for (AttendanceEmployeeDetailDO attendanceEmployeeDetailDO : attendanceEmployeeDetailDOList) {
            if (Objects.isNull(attendanceEmployeeDetailDO.getFormId())) {
                continue;
            }
            formIdList.add(attendanceEmployeeDetailDO.getFormId());
            if (Objects.equals(AttendanceConcreteTypeEnum.OOO.getCode(), attendanceEmployeeDetailDO.getConcreteType())
                    && Objects.nonNull(attendanceEmployeeDetailDO.getAttendanceMinutes())
                    && attendanceEmployeeDetailDO.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0) {
                oooMinutes = oooMinutes.add(attendanceEmployeeDetailDO.getAttendanceMinutes());
            }
            if (Objects.nonNull(attendanceEmployeeDetailDO.getLeaveMinutes())
                    && attendanceEmployeeDetailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                leaveMinutes = leaveMinutes.add(attendanceEmployeeDetailDO.getLeaveMinutes());
            }
        }
        attendanceDayReportDO.setLeaveMinutes(leaveMinutes);
        attendanceDayReportDO.setOooMinutes(oooMinutes);
    }

    private void buildOverTimeMinutes(Long attendanceDayId,
                                      UserInfoDO userInfoDO,
                                      AttendanceDayReportDO attendanceDayReportDO) {
        OverTimeListQuery overTimeQuery = OverTimeListQuery.builder()
                .userCode(userInfoDO.getUserCode())
                .formTypeList(Collections.singletonList(FormTypeEnum.OVER_TIME.getCode()))
                .dayId(attendanceDayId)
                .formStatus(FormStatusEnum.PASS.getCode())
                .build();
        List<OverTimeApprovalListDTO> overTimeApprovalListDTOList = overTimeFormManage.selectListByCondition(overTimeQuery);
        BigDecimal overTimeMinutes = BigDecimal.ZERO;
        for (OverTimeApprovalListDTO overTimeForm : overTimeApprovalListDTOList) {
            if (Objects.isNull(overTimeForm.getEstimateDuration())) {
                continue;
            }
            overTimeMinutes = overTimeMinutes.add(overTimeForm.getEstimateDuration());
        }
        attendanceDayReportDO.setOverTimeMinutes(overTimeMinutes);
    }

    private static String getDayShiftRule(UserShiftConfigDO userShiftConfigDO, AttendanceDayReportDO attendanceDayReportDO) {
        String dayShiftRule;
        if (Objects.isNull(userShiftConfigDO)) {
            dayShiftRule = DayShiftRuleEnum.NO_CLASS.getCode();
        } else {
            if (Objects.nonNull(userShiftConfigDO.getPunchClassConfigId()) && userShiftConfigDO.getPunchClassConfigId() > 0) {
                dayShiftRule = DayShiftRuleEnum.CLASS.getCode();
                attendanceDayReportDO.setPunchClassConfigId(userShiftConfigDO.getPunchClassConfigId());
            } else {
                dayShiftRule = userShiftConfigDO.getDayShiftRule();
            }
        }
        return dayShiftRule;
    }

    private Integer getFinalResult(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList,
                                   List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList,
                                   AttendanceDayReportDO dayReportDO) {
        log.info("getFinalResult | userCode:{}, attendanceEmployeeDetailDOList:{}, employeeAbnormalAttendanceDOList:{}"
                , dayReportDO.getUserCode(), attendanceEmployeeDetailDOList, employeeAbnormalAttendanceDOList);
        Integer finalResult = AttendanceReportStatusEnum.NORMAL.getCode();
        // 判断休息日/节假日
        if (DayShiftRuleEnum.isRestDay(dayReportDO.getDayShiftRule())) {
            finalResult = AttendanceReportStatusEnum.OFF.getCode();
            // 节假日存在加班
            if (dayReportDO.getOverTimeMinutes().compareTo(BigDecimal.ZERO) > 0) {
                finalResult = AttendanceReportStatusEnum.OVER_TIME.getCode();
            }
            return finalResult;
        }

        if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)
                && CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)) {
            return null;
        }

        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)) {
            return finalResult;
        }

        Optional<EmployeeAbnormalAttendanceDO> unPassAbnormalOptional = employeeAbnormalAttendanceDOList.stream()
                .filter(abnormal -> !Objects.equals(AbnormalAttendanceStatusEnum.PASS.getCode(), abnormal.getStatus()))
                .findFirst();
        if (unPassAbnormalOptional.isPresent()) {
            finalResult = AttendanceReportStatusEnum.ABNORMAL.getCode();
            return finalResult;
        }

        List<Long> passAbnormalIds = employeeAbnormalAttendanceDOList.stream()
                .filter(abnormal -> Objects.equals(AbnormalAttendanceStatusEnum.PASS.getCode(), abnormal.getStatus()))
                .map(EmployeeAbnormalAttendanceDO::getId)
                .collect(Collectors.toList());

        Optional<EmployeeAbnormalOperationRecordDO> confirmAbnormalRecordOptional = employeeAbnormalOperationRecordDao.selectByAbnormalList(passAbnormalIds)
                .stream()
                .filter(abnormalRecord -> Objects.equals(AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode(), abnormalRecord.getOperationType()))
                .findFirst();
        if (confirmAbnormalRecordOptional.isPresent()) {
            finalResult = AttendanceReportStatusEnum.ABNORMAL.getCode();
        }
        return finalResult;
    }

    private Integer getInitResult(List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList,
                                  AttendanceDayReportDO dayReportDO) {
        Integer initResult = AttendanceReportStatusEnum.NORMAL.getCode();
        // 判断休息日/节假日
        if (DayShiftRuleEnum.isRestDay(dayReportDO.getDayShiftRule())) {
            initResult = AttendanceReportStatusEnum.OFF.getCode();
            // 节假日存在加班
            if (dayReportDO.getOverTimeMinutes().compareTo(BigDecimal.ZERO) > 0) {
                initResult = AttendanceReportStatusEnum.OVER_TIME.getCode();
            }
            return initResult;
        }

        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceSnapshotDOList)) {
            return initResult;
        }

        Optional<EmployeeAbnormalAttendanceSnapshotDO> unPassAbnormalOptional = employeeAbnormalAttendanceSnapshotDOList.stream()
                .filter(abnormal -> !Objects.equals(AbnormalAttendanceStatusEnum.PASS.getCode(), abnormal.getStatus()))
                .findFirst();
        if (unPassAbnormalOptional.isPresent()) {
            initResult = AttendanceReportStatusEnum.ABNORMAL.getCode();
            return initResult;
        }

        List<Long> passAbnormalIds = employeeAbnormalAttendanceSnapshotDOList.stream()
                .filter(abnormal -> Objects.equals(AbnormalAttendanceStatusEnum.PASS.getCode(), abnormal.getStatus()))
                .map(EmployeeAbnormalAttendanceSnapshotDO::getId)
                .collect(Collectors.toList());

        Optional<EmployeeAbnormalOperationRecordDO> confirmAbnormalRecordOptional = employeeAbnormalOperationRecordDao.selectByAbnormalList(passAbnormalIds)
                .stream()
                .filter(abnormalRecord -> Objects.equals(AbnormalOperationTypeEnum.ABNORMAL_CONFIRM.getCode(), abnormalRecord.getOperationType()))
                .findFirst();
        if (confirmAbnormalRecordOptional.isPresent()) {
            initResult = AttendanceReportStatusEnum.ABNORMAL.getCode();
        }
        return initResult;
    }

    private List<AttendanceDayReportFormDO> buildAttendanceDayReportFormDOList(Long dayRecordId,
                                                                               List<Long> formIdList) {
        if (CollectionUtils.isEmpty(formIdList)) {
            return Collections.emptyList();
        }

        List<AttendanceFormDO> attendanceFormDOList = attendanceFormDao.selectByIds(formIdList);
        if (CollectionUtils.isEmpty(attendanceFormDOList)) {
            return Collections.emptyList();
        }

        log.info("考勤日报处理, 单据明细: {}", JSON.toJSONString(attendanceFormDOList));
        return attendanceFormDOList.stream().map(form -> {
            AttendanceDayReportFormDO dayReportFormDO = new AttendanceDayReportFormDO();
            dayReportFormDO.setId(defaultIdWorker.nextId());
            dayReportFormDO.setDayReportId(dayRecordId);
            dayReportFormDO.setFormId(form.getId());
            dayReportFormDO.setFormType(form.getFormType());
            dayReportFormDO.setFormStatus(form.getFormStatus());
            dayReportFormDO.setApplicationCode(form.getApplicationCode());
            dayReportFormDO.setApprovalId(form.getApprovalId());
            BaseDOUtil.fillDOInsertByUsrOrSystem(dayReportFormDO);
            return dayReportFormDO;
        }).collect(Collectors.toList());
    }

    private List<AttendanceDayReportAbnormalDO> buildAttendanceDayReportAbnormalDOList(Long dayRecordId, List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList) {
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)) {
            return Collections.emptyList();
        }

        log.info("考勤日报处理, 异常明细: {}", JSON.toJSONString(employeeAbnormalAttendanceDOList));
        return employeeAbnormalAttendanceDOList.stream().map(abnormal -> {
            AttendanceDayReportAbnormalDO abnormalDO = new AttendanceDayReportAbnormalDO();
            abnormalDO.setId(defaultIdWorker.nextId());
            abnormalDO.setDayReportId(dayRecordId);
            abnormalDO.setAbnormalId(abnormal.getId());
            abnormalDO.setAbnormalType(abnormal.getAbnormalType());
            abnormalDO.setAbnormalStatus(abnormal.getStatus());
            abnormalDO.setPunchClassId(abnormal.getPunchClassConfigId());
            abnormalDO.setPunchClassItemConfigId(abnormal.getPunchClassItemConfigId());
            BaseDOUtil.fillDOInsertByUsrOrSystem(abnormalDO);
            return abnormalDO;
        }).collect(Collectors.toList());
    }

    private void buildActualAttendanceMinutes(AttendanceReportAbnormalHandlerDTO param,
                                              AttendanceDayReportDO attendanceDayReportDO,
                                              List<DayAttendanceHandlerFormDTO> handlerFormDTOList) {

        String dayShiftRule = attendanceDayReportDO.getDayShiftRule();

        List<UserPunchRecordBO> punchRecordDOList = Optional.ofNullable(param.getPunchRecordDOList()).orElse(Collections.emptyList());

        //免打卡
        if (Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), attendanceDayReportDO.getPunchConfigType())) {
            noNeedWork(attendanceDayReportDO);
            log.info("考勤日报处理,{} 实际出勤时长: {} , 最终工作时长: {}", attendanceDayReportDO.getPunchConfigType(), attendanceDayReportDO.getActualAttendanceMinutes(), attendanceDayReportDO.getFinalWorkMinutes());
            return;
        }

        //休息日/法假
        if (Objects.equals(DayShiftRuleEnum.OFF.getCode(), dayShiftRule)
                || Objects.equals(DayShiftRuleEnum.H.getCode(), dayShiftRule)) {
            noWorkDay(attendanceDayReportDO, handlerFormDTOList, punchRecordDOList);
            log.info("考勤日报处理,{} 实际出勤时长: {} , 最终工作时长: {}", attendanceDayReportDO.getPunchConfigType(), attendanceDayReportDO.getActualAttendanceMinutes(), attendanceDayReportDO.getFinalWorkMinutes());
            return;
        }

        //无排班计划
        if (Objects.equals(DayShiftRuleEnum.NO_CLASS.getCode(), dayShiftRule)) {
            noSchedulingPlan(param.getAttendanceEmployeeDetailDOList(), attendanceDayReportDO);
            log.info("考勤日报处理,{} 实际出勤时长: {} , 最终工作时长: {}", attendanceDayReportDO.getPunchConfigType(), attendanceDayReportDO.getActualAttendanceMinutes(), attendanceDayReportDO.getFinalWorkMinutes());
            return;
        }

        //灵活打卡两次
        if (Objects.equals(PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode(), attendanceDayReportDO.getPunchConfigType())) {
            flexibleWorkTwice(param.getAttendanceDayId(), param.getPunchConfigDO(), param.getPunchClassConfigDTO(), attendanceDayReportDO, punchRecordDOList, handlerFormDTOList, param.getEmployeeAbnormalAttendanceDOList());
            log.info("考勤日报处理,{} 实际出勤时长: {} , 最终工作时长: {}", attendanceDayReportDO.getPunchConfigType(), attendanceDayReportDO.getActualAttendanceMinutes(), attendanceDayReportDO.getFinalWorkMinutes());
        }

        //灵活打卡一次
        if (Objects.equals(PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode(), attendanceDayReportDO.getPunchConfigType())) {
            flexiblePunchOnce(param, attendanceDayReportDO, handlerFormDTOList, punchRecordDOList);
            log.info("考勤日报处理,{} 实际出勤时长: {} , 最终工作时长: {}", attendanceDayReportDO.getPunchConfigType(), attendanceDayReportDO.getActualAttendanceMinutes(), attendanceDayReportDO.getFinalWorkMinutes());
        }

        //固定班次
        if (Objects.equals(PunchConfigTypeEnum.FIXED_WORK.getCode(), attendanceDayReportDO.getPunchConfigType())) {
            fixedPunchOnce(param, attendanceDayReportDO, handlerFormDTOList, punchRecordDOList);
            log.info("考勤日报处理,{} 实际出勤时长: {} , 最终工作时长: {}", attendanceDayReportDO.getPunchConfigType(), attendanceDayReportDO.getActualAttendanceMinutes(), attendanceDayReportDO.getFinalWorkMinutes());
        }

        // 批量/单个处理异常情,特殊处理
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = param.getAttendanceEmployeeDetailDOList();
        if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
            return;
        }
        for (AttendanceEmployeeDetailDO employeeDetailDO : attendanceEmployeeDetailDOList) {
            if (AttendanceDataSourceEnum.isAbnormalHandler(employeeDetailDO.getDataSource())) {
                attendanceDayReportDO.setFinalWorkMinutes(employeeDetailDO.getAttendanceMinutes());
                attendanceDayReportDO.setActualAttendanceMinutes(employeeDetailDO.getAttendanceMinutes());
                return;
            }
        }
    }

    private void flexiblePunchOnce(AttendanceReportAbnormalHandlerDTO param,
                                   AttendanceDayReportDO attendanceDayReportDO,
                                   List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                   List<UserPunchRecordBO> punchRecordDOList) {
        PunchClassConfigDTO punchClassConfigDTO = param.getPunchClassConfigDTO();
        List<PunchClassItemConfigDTO> classItemConfigList = punchClassConfigDTO.getClassItemConfigList();

        //灵活一次班次多时段
        if (CollectionUtils.isEmpty(classItemConfigList) || classItemConfigList.size() > 1) {
            log.info("考勤日报处理, 灵活打卡一次匹配班次多时段");
            return;
        }

        BigDecimal actualAttendanceMinutes = BigDecimal.ZERO;
        BigDecimal finalWorkMinutes = BigDecimal.ZERO;

        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigList);
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
            DayItemConfigDateDTO itemConfigDate = attendanceMinuteCalculateService.buildDayItemConfigDateDTO(param.getAttendanceDayId(), itemConfigDO, itemConfigDOList, param.getPunchRecordDOList());
            if (Objects.isNull(itemConfigDate)) {
                continue;
            }

            //班次要求出勤时长（班次上下班时长间隔-休息时长）
            BigDecimal itemTotalMinutes = itemConfigDate.getItemTotalMinutes();
            //已出勤时长
            BigDecimal usedMinutes = BigDecimal.ZERO;
            //外勤时长
            BigDecimal outOffOfficeMinutes = BigDecimal.ZERO;

            List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
            for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
                //计算班次范围请假或外勤时长
                BigDecimal leaveMinutes = attendanceMinuteCalculateService.shiftDayLeaveMinuteHandler(handlerFormDTO, itemConfigDate.getPunchInTime(), itemConfigDate.getPunchOutTime(),
                        itemConfigDate.getRestStartTime(), itemConfigDate.getRestEndTime(), itemConfigDate.getBetweenMinutes(), filterFormDTOList);
                usedMinutes = usedMinutes.add(leaveMinutes);

                if (Objects.equals(FormTypeEnum.OUT_OF_OFFICE.getCode(), handlerFormDTO.getFormType())) {
                    outOffOfficeMinutes = outOffOfficeMinutes.add(leaveMinutes);
                }
            }

            //当前时刻完全请假
            if (itemTotalMinutes.subtract(usedMinutes).compareTo(BigDecimal.ZERO) < 1) {
                actualAttendanceMinutes = actualAttendanceMinutes.add(outOffOfficeMinutes);
                finalWorkMinutes = actualAttendanceMinutes;
                continue;
            }

            // 获取打卡时长
            List<UserPunchRecordBO> itemPunchRecordList = punchRecordDOList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(itemConfigDate.getEarliestPunchInTime()) > -1
                            && item.getFormatPunchTime().compareTo(itemConfigDate.getLatestPunchOutTime()) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime))
                    .collect(Collectors.toList());

            //情况1:当天没有打卡时间 看外勤时长
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                actualAttendanceMinutes = actualAttendanceMinutes.add(outOffOfficeMinutes);
                finalWorkMinutes = actualAttendanceMinutes;
                continue;
            }

            //情况2:当天存在请假 有打卡
            if (CollectionUtils.isNotEmpty(filterFormDTOList)) {
                BigDecimal totalLeaveMinutes = Optional.ofNullable(param.getAttendanceEmployeeDetailDOList()).orElse(Lists.newArrayList())
                        .stream()
                        .filter(employeeDetail -> Objects.nonNull(employeeDetail.getFormId())
                                && Objects.nonNull(employeeDetail.getLeaveMinutes())
                                && employeeDetail.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0)
                        .map(AttendanceEmployeeDetailDO::getLeaveMinutes)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                actualAttendanceMinutes = itemConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES).subtract(totalLeaveMinutes);
                finalWorkMinutes = actualAttendanceMinutes;
                continue;
            }

            //情况3:无请假 有打卡
            actualAttendanceMinutes = itemConfigDO.getLegalWorkingHours().multiply(BusinessConstant.MINUTES);
            finalWorkMinutes = actualAttendanceMinutes;
        }
        attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
        attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
    }

    private void fixedPunchOnce(AttendanceReportAbnormalHandlerDTO param,
                                AttendanceDayReportDO attendanceDayReportDO,
                                List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                List<UserPunchRecordBO> punchRecordDOList) {
        PunchClassConfigDTO punchClassConfigDTO = param.getPunchClassConfigDTO();
        List<PunchClassItemConfigDTO> classItemConfigList = punchClassConfigDTO.getClassItemConfigList();

        BigDecimal actualAttendanceMinutes = BigDecimal.ZERO;
        BigDecimal finalWorkMinutes = BigDecimal.ZERO;
        BigDecimal delayMinutes = BigDecimal.ZERO;
        BigDecimal lateMinutes = BigDecimal.ZERO;
        BigDecimal leaveEarlyMinutes = BigDecimal.ZERO;

        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigList);
        for (PunchClassItemConfigDO itemConfigDO : itemConfigDOList) {
            DayItemConfigDateDTO itemConfigDate = attendanceMinuteCalculateService.buildDayItemConfigDateDTO(param.getAttendanceDayId(), itemConfigDO, itemConfigDOList, param.getPunchRecordDOList());
            if (Objects.isNull(itemConfigDate)) {
                continue;
            }

            //班次要求出勤时长（班次上下班时长间隔-休息时长）
            BigDecimal itemTotalMinutes = itemConfigDate.getItemTotalMinutes();
            //已出勤时长
            BigDecimal usedMinutes = BigDecimal.ZERO;
            //外勤时长
            BigDecimal outOffOfficeMinutes = BigDecimal.ZERO;

            List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
            for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
                //计算班次范围请假或外勤时长
                BigDecimal leaveMinutes = attendanceMinuteCalculateService.shiftDayLeaveMinuteHandler(handlerFormDTO, itemConfigDate.getPunchInTime(), itemConfigDate.getPunchOutTime(),
                        itemConfigDate.getRestStartTime(), itemConfigDate.getRestEndTime(), itemConfigDate.getBetweenMinutes(), filterFormDTOList);
                usedMinutes = usedMinutes.add(leaveMinutes);

                if (Objects.equals(FormTypeEnum.OUT_OF_OFFICE.getCode(), handlerFormDTO.getFormType())) {
                    outOffOfficeMinutes = outOffOfficeMinutes.add(leaveMinutes);
                }
            }

            itemTotalMinutes = itemTotalMinutes.subtract(usedMinutes);

            //当前时刻完全请假
            if (itemTotalMinutes.compareTo(BigDecimal.ZERO) < 1) {
                actualAttendanceMinutes = actualAttendanceMinutes.add(outOffOfficeMinutes);
                finalWorkMinutes = actualAttendanceMinutes;
                continue;
            }
            // 获取打卡时长
            List<UserPunchRecordBO> itemPunchRecordList = punchRecordDOList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(itemConfigDate.getEarliestPunchInTime()) > -1
                            && item.getFormatPunchTime().compareTo(itemConfigDate.getLatestPunchOutTime()) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime))
                    .collect(Collectors.toList());
            // 计算延时时长,早退时长,迟到时长
            // 存在打卡时间且当天无异常则计算延时时长
            List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = param.getEmployeeAbnormalAttendanceDOList();
            if (CollectionUtils.isNotEmpty(itemPunchRecordList)
                    && CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)) {
                // 班次上班时间
                Date punchInTime = itemConfigDate.getPunchInTime();
                // 班次下班时间
                Date punchOutTime = itemConfigDate.getPunchOutTime();
                // 弹性时长
                BigDecimal betweenMinutes = BigDecimal.valueOf(itemConfigDate.getBetweenMinutes());
                // 最晚打卡时间
                Date latestPunchTime = itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime();
                // 计算延时时长(不存在弹性)
                if (itemPunchRecordList.size() == 1 ||
                        (betweenMinutes.compareTo(BigDecimal.ZERO) == 0 && latestPunchTime.compareTo(punchOutTime) > 0)) {
                    delayMinutes = delayMinutes.add(DateHelper.diffMins(latestPunchTime, punchOutTime));
                }
                // 计算延时时长(存在弹性)
                if (betweenMinutes.compareTo(BigDecimal.ZERO) > 0 && itemPunchRecordList.size() > 1) {
                    // 最早打卡时间
                    Date earliestPunchTime = itemPunchRecordList.get(0).getFormatPunchTime();
                    // 最早打卡时间小于等于上班时间且最晚打卡时间大于下班时间
                    if (earliestPunchTime.compareTo(punchInTime) <= 0 && latestPunchTime.compareTo(punchOutTime) > 0) {
                        delayMinutes = delayMinutes.add(DateHelper.diffMins(latestPunchTime, punchOutTime));
                    }
                    // 最早打卡时间大于上班时间且小于等于弹性时间
                    if (earliestPunchTime.compareTo(punchInTime) > 0
                            && latestPunchTime.compareTo(punchOutTime) > 0) {
                        // 获取最早打卡时间和上班时间时间差
                        BigDecimal earlyBetweenMinutes = DateHelper.diffMins(earliestPunchTime, punchInTime);
                        // 获取最晚打卡时间和下班时间时间差
                        BigDecimal lateBetweenMinutes = DateHelper.diffMins(latestPunchTime, punchOutTime);
                        // 最早打卡时间差 <= 弹性 且 最晚打卡时间差 > 最早打卡时间差
                        if (betweenMinutes.compareTo(earlyBetweenMinutes) >= 0
                                && lateBetweenMinutes.compareTo(earlyBetweenMinutes) > 0) {
                            delayMinutes = delayMinutes.add(lateBetweenMinutes.subtract(earlyBetweenMinutes));
                        }
                    }
                }
            }
            // 存在打卡时间且当天有对应异常则计算迟到和早退时长
            if (CollectionUtils.isNotEmpty(itemPunchRecordList)
                    && CollectionUtils.isNotEmpty(employeeAbnormalAttendanceDOList)) {

                Date earliestPunchTime = itemPunchRecordList.get(0).getFormatPunchTime();
                Date latestPunchTime = itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime();
                for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : employeeAbnormalAttendanceDOList) {
                    if (!abnormalAttendanceDO.getPunchClassItemConfigId().equals(itemConfigDO.getId())) {
                        continue;
                    }
                    Long betweenMinutes = itemConfigDate.getBetweenMinutes();
                    Date punchInTime = itemConfigDate.getPunchInTime();
                    if (AttendanceAbnormalTypeEnum.LATE.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                        // 根据班次上班时间计算迟到时间
                        if (earliestPunchTime.compareTo(punchInTime) <= 0) {
                            continue;
                        }
                        BigDecimal intervalInTime = DateHelper.diffMins(earliestPunchTime, punchInTime);
                        if (Objects.isNull(betweenMinutes) || betweenMinutes == 0) {
                            lateMinutes = lateMinutes.add(intervalInTime);
                            continue;
                        }
                        lateMinutes = lateMinutes.add(intervalInTime.subtract(BigDecimal.valueOf(betweenMinutes)));
                    }
                    if (AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                        // 根据班次下班时间计算早退时间
                        Date punchOutTime = itemConfigDate.getPunchOutTime();
                        if (punchOutTime.compareTo(latestPunchTime) < 0) {
                            continue;
                        }
                        BigDecimal intervalOutTime = DateHelper.diffMins(punchOutTime, latestPunchTime);
                        if (Objects.isNull(betweenMinutes) || betweenMinutes == 0) {
                            leaveEarlyMinutes = leaveEarlyMinutes.add(intervalOutTime);
                            continue;
                        }
                        if (earliestPunchTime.compareTo(punchInTime) > 0) {
                            BigDecimal intervalInTime = DateHelper.diffMins(earliestPunchTime, punchInTime);
                            leaveEarlyMinutes = leaveEarlyMinutes.add(intervalOutTime.add(intervalInTime));
                            continue;
                        }
                        leaveEarlyMinutes = leaveEarlyMinutes.add(intervalOutTime.add(BigDecimal.valueOf(betweenMinutes)));
                    }
                }
            }
            //情况1:当天没有打卡时间
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                actualAttendanceMinutes = actualAttendanceMinutes.add(outOffOfficeMinutes);
                finalWorkMinutes = actualAttendanceMinutes;
                continue;
            }

            //情况2:当天完全没请假，看打卡记录
            if (CollectionUtils.isEmpty(filterFormDTOList)) {
                AttendanceMinuteDTO attendanceMinuteDTO = normalPunchHandler(itemPunchRecordList, itemConfigDate);
                actualAttendanceMinutes = actualAttendanceMinutes.add(attendanceMinuteDTO.getActualAttendanceMinutes());
                finalWorkMinutes = finalWorkMinutes.add(attendanceMinuteDTO.getFinalWorkMinutes());
                continue;
            }

            //情况3:当天存在请假  有一条打卡记录
            if (itemPunchRecordList.size() == 1) {
                actualAttendanceMinutes = actualAttendanceMinutes.add(outOffOfficeMinutes);
                finalWorkMinutes = actualAttendanceMinutes;
                continue;
            }

            //情况4: 当天存在请假 多条打卡记录
            AttendanceMinuteDTO attendanceMinuteDTO = leaveDayBatchHandler(itemPunchRecordList, filterFormDTOList, itemConfigDate, itemTotalMinutes);
            actualAttendanceMinutes = actualAttendanceMinutes.add(attendanceMinuteDTO.getActualAttendanceMinutes()).add(outOffOfficeMinutes);
            finalWorkMinutes = finalWorkMinutes.add(attendanceMinuteDTO.getFinalWorkMinutes()).add(outOffOfficeMinutes);
        }
        attendanceDayReportDO.setDelayMinutes(delayMinutes);
        attendanceDayReportDO.setLateMinutes(lateMinutes);
        attendanceDayReportDO.setLeaveEarlyMinutes(leaveEarlyMinutes);
        attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
        attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
    }

    private void flexibleWorkTwice(Long attendanceDayId,
                                   PunchConfigDO punchConfigDO,
                                   PunchClassConfigDTO punchClassConfigDTO,
                                   AttendanceDayReportDO attendanceDayReportDO,
                                   List<UserPunchRecordBO> punchRecordDOList,
                                   List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                   List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList) {
        List<PunchClassItemConfigDTO> classItemConfigList = punchClassConfigDTO.getClassItemConfigList();
        if (CollectionUtils.isEmpty(classItemConfigList) || classItemConfigList.size() > 1) {
            log.info("考勤日报处理, 灵活打卡两次匹配班次多时段");
            return;
        }
        //打卡规则上下班时间间隔
        BigDecimal punchTimeIntervalMinutes = punchConfigDO.getPunchTimeInterval().multiply(BusinessConstant.MINUTES);

        BigDecimal actualAttendanceMinutes = BigDecimal.ZERO;
        BigDecimal finalWorkMinutes = BigDecimal.ZERO;
        BigDecimal delayMinutes = BigDecimal.ZERO;
        BigDecimal lateMinutes = BigDecimal.ZERO;
        BigDecimal leaveEarlyMinutes = BigDecimal.ZERO;

        String earliestPunchInTimeString = DateHelper.formatHHMMSS(classItemConfigList.get(0).getEarliestPunchInTime());
        String earliestPunchInTimeDayString = DateHelper.formatYYYYMMDD(DateHelper.transferDayIdToDate(attendanceDayId));

        Date startDate = DateHelper.concatDateAndTime(earliestPunchInTimeDayString, earliestPunchInTimeString);
        Date endDate = DateHelper.pushDate(startDate, 1);

        List<UserPunchRecordBO> effectPunchRecordDOList = punchRecordDOList.stream()
                .filter(punch -> punch.getFormatPunchTime().compareTo(startDate) > -1 && punch.getFormatPunchTime().compareTo(endDate) < 1)
                .sorted(Comparator.comparing(UserPunchRecordBO::getFormatPunchTime))
                .collect(Collectors.toList());

        // 计算迟到时长和早退时长(自由打卡打卡两次才出现迟到和早退)
        if (CollectionUtils.isNotEmpty(employeeAbnormalAttendanceDOList)
                && CollectionUtils.isNotEmpty(effectPunchRecordDOList)
                && effectPunchRecordDOList.size() > 1) {
            Date earliestPunchTime = effectPunchRecordDOList.get(0).getFormatPunchTime();
            Date latestPunchTime = effectPunchRecordDOList.get(effectPunchRecordDOList.size() - 1).getFormatPunchTime();
            for (EmployeeAbnormalAttendanceDO abnormalAttendanceDO : employeeAbnormalAttendanceDOList) {
                if (AttendanceAbnormalTypeEnum.LATE.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                    // 根据最晚打卡时间往前推,最早时间过了此时间则迟到
                    Date lateDate = DateHelper.calcMinutesFromDate(latestPunchTime, punchTimeIntervalMinutes, 2);
                    if (earliestPunchTime.compareTo(lateDate) > 0) {
                        lateMinutes = lateMinutes.add(DateHelper.diffMins(earliestPunchTime, lateDate));
                    }
                }
                if (AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode().equals(abnormalAttendanceDO.getAbnormalType())) {
                    // 根据最早打卡时间往后推,最晚时间小于此时间则早退
                    Date lateDate = DateHelper.calcMinutesFromDate(earliestPunchTime, punchTimeIntervalMinutes, 1);
                    if (latestPunchTime.compareTo(lateDate) < 0) {
                        leaveEarlyMinutes = leaveEarlyMinutes.add(DateHelper.diffMins(lateDate, latestPunchTime));
                    }
                }
            }
            attendanceDayReportDO.setLateMinutes(lateMinutes);
            attendanceDayReportDO.setLeaveEarlyMinutes(leaveEarlyMinutes);
        }

        //存在打卡记录
        if (CollectionUtils.isNotEmpty(effectPunchRecordDOList) && effectPunchRecordDOList.size() > 1) {
            //存在请假或外勤记录
            if (attendanceDayReportDO.getOooMinutes().compareTo(BigDecimal.ZERO) > 0
                    || attendanceDayReportDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
                buildFilterFormDTOList(startDate, endDate, handlerFormDTOList, filterFormDTOList);

                List<DayItemInfoDTO> dayItemInfoList = new ArrayList<>();
                //整个时段内，除去请假外的打卡记录的区间
                attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoList, filterFormDTOList, effectPunchRecordDOList);
                //打卡时间段去除休息时间段得到的出勤时长
                BigDecimal presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoList, null, null);
                actualAttendanceMinutes = presentMinutes.add(attendanceDayReportDO.getOooMinutes());
            } else {
                actualAttendanceMinutes = BigDecimal.valueOf(DateUtil.between(effectPunchRecordDOList.get(0).getFormatPunchTime(), effectPunchRecordDOList.get(effectPunchRecordDOList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE));
            }

            if (actualAttendanceMinutes.compareTo(punchTimeIntervalMinutes) > 0) {
                finalWorkMinutes = punchTimeIntervalMinutes;
                // 设置延时时长
                attendanceDayReportDO.setDelayMinutes(actualAttendanceMinutes.subtract(punchTimeIntervalMinutes));
            } else {
                finalWorkMinutes = actualAttendanceMinutes;
            }
            attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
            attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
            return;
        }

        // 存在两次打卡记录且无异常，计算延时时长
        if (CollectionUtils.isEmpty(employeeAbnormalAttendanceDOList)
                && CollectionUtils.isNotEmpty(effectPunchRecordDOList)
                && effectPunchRecordDOList.size() > 1) {
            Date earliestPunchTime = effectPunchRecordDOList.get(0).getFormatPunchTime();
            Date latestPunchTime = effectPunchRecordDOList.get(effectPunchRecordDOList.size() - 1).getFormatPunchTime();
            BigDecimal betweenMinutes = DateHelper.diffMins(latestPunchTime, earliestPunchTime);
            if (betweenMinutes.compareTo(punchTimeIntervalMinutes) > 0) {
                delayMinutes = betweenMinutes.subtract(punchTimeIntervalMinutes);
            }
        }

        //有外勤
        if (attendanceDayReportDO.getOooMinutes().compareTo(BigDecimal.ZERO) > 0) {
            finalWorkMinutes = actualAttendanceMinutes.add(attendanceDayReportDO.getOooMinutes());
        }
        attendanceDayReportDO.setDelayMinutes(delayMinutes);
        attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
        attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
    }

    private void noSchedulingPlan(List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList,
                                  AttendanceDayReportDO attendanceDayReportDO) {
        BigDecimal actualAttendanceMinutes = BigDecimal.ZERO;
        BigDecimal finalWorkMinutes = BigDecimal.ZERO;
        if (CollectionUtils.isEmpty(attendanceEmployeeDetailDOList)) {
            attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
            attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
            return;
        }

        //优先看HR确认的操作
        for (AttendanceEmployeeDetailDO attendanceEmployeeDetailDO : attendanceEmployeeDetailDOList) {
            if (Objects.equals(AbnormalOperationTypeEnum.PH.getCode(), attendanceEmployeeDetailDO.getConcreteType())
                    || Objects.equals(AbnormalOperationTypeEnum.OFF.getCode(), attendanceEmployeeDetailDO.getConcreteType())) {
                attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
                attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
                return;
            }
            if (Objects.equals(AbnormalOperationTypeEnum.P.getCode(), attendanceEmployeeDetailDO.getConcreteType())) {
                attendanceDayReportDO.setActualAttendanceMinutes(attendanceEmployeeDetailDO.getAttendanceMinutes());
                attendanceDayReportDO.setFinalWorkMinutes(attendanceEmployeeDetailDO.getAttendanceMinutes());
                return;
            }
        }

        //其次看外勤的时长
        if (attendanceDayReportDO.getOooMinutes().compareTo(BigDecimal.ZERO) > 0) {
            finalWorkMinutes = actualAttendanceMinutes.add(attendanceDayReportDO.getOooMinutes());
        }
        attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
        attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
    }

    private void noWorkDay(AttendanceDayReportDO attendanceDayReportDO,
                           List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                           List<UserPunchRecordBO> punchRecordDOList) {

        DayClassTimeDTO dayClassTimeDTO = buildDayClassTimeDTO(attendanceDayReportDO.getUserId(), attendanceDayReportDO.getDayId());
        List<UserPunchRecordBO> effectPunchRecordDOList = punchRecordDOList.stream()
                .filter(punch -> punch.getFormatPunchTime().compareTo(dayClassTimeDTO.getPunchStartTime()) > -1
                        && punch.getFormatPunchTime().compareTo(dayClassTimeDTO.getPunchEndTime()) < 1)
                .collect(Collectors.toList());

        BigDecimal actualAttendanceMinutes = BigDecimal.ZERO;
        BigDecimal finalWorkMinutes = BigDecimal.ZERO;

        //存在打卡记录
        if (CollectionUtils.isNotEmpty(effectPunchRecordDOList) && effectPunchRecordDOList.size() > 1) {
            //存在请假或外勤记录
            if (attendanceDayReportDO.getOooMinutes().compareTo(BigDecimal.ZERO) > 0 || attendanceDayReportDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
                buildFilterFormDTOList(dayClassTimeDTO.getPunchStartTime(), dayClassTimeDTO.getPunchEndTime(), handlerFormDTOList, filterFormDTOList);

                List<DayItemInfoDTO> dayItemInfoList = new ArrayList<>();
                //整个时段内，除去外勤和请假外的打卡记录的区间
                attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoList, filterFormDTOList, effectPunchRecordDOList);
                //打卡时间段去除休息时间段得到的出勤时长
                BigDecimal presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoList, null, null);
                actualAttendanceMinutes = presentMinutes.add(attendanceDayReportDO.getOooMinutes());
                finalWorkMinutes = actualAttendanceMinutes;
            } else {
                actualAttendanceMinutes = BigDecimal.valueOf(DateUtil.between(effectPunchRecordDOList.get(0).getFormatPunchTime(), effectPunchRecordDOList.get(effectPunchRecordDOList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE));
                finalWorkMinutes = actualAttendanceMinutes;

            }
            attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
            attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
            return;
        }

        //有外勤
        if (attendanceDayReportDO.getOooMinutes().compareTo(BigDecimal.ZERO) > 0) {
            finalWorkMinutes = actualAttendanceMinutes.add(attendanceDayReportDO.getOooMinutes());
        }
        attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
        attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
    }

    private DayClassTimeDTO buildDayClassTimeDTO(Long userId, Long attendanceDayId) {
        Date startDate;
        Date endDate;
        Long preDayId = DateHelper.getPreviousDayId(attendanceDayId);
        //查询前一天的排班
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigDao.selectRecordByUserIdList(Collections.singletonList(userId), preDayId, preDayId);
        if (CollectionUtils.isEmpty(userShiftConfigDOList) || Objects.isNull(userShiftConfigDOList.get(0).getPunchClassConfigId())) {
            startDate = DateHelper.beginOfDay(DateHelper.transferDayIdToDate(attendanceDayId));
            endDate = DateUtil.offsetDay(startDate, 1);
            return new DayClassTimeDTO(startDate, endDate);
        }

        Long punchClassId = userShiftConfigDOList.get(0).getPunchClassConfigId();
        Map<Long, PunchClassConfigDTO> punchClassConfigMap = punchClassConfigManage.selectByIds(Collections.singletonList(punchClassId));
        PunchClassConfigDTO punchClassConfigDTO = punchClassConfigMap.get(punchClassId);
        List<PunchClassItemConfigDO> itemConfigDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(punchClassConfigDTO.getClassItemConfigList());

        Date dayPunchStartTime = null;
        Date dayPunchEndTime = null;
        for (int i = 0; i < itemConfigDOList.size(); i++) {
            DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(preDayId, itemConfigDOList.get(i).getId(), itemConfigDOList);
            if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                continue;
            }
            if (i == 0) {
                dayPunchStartTime = dayPunchTimeDTO.getDayPunchStartTime();
            }
            dayPunchEndTime = dayPunchTimeDTO.getDayPunchEndTime();
        }

        if (Objects.isNull(dayPunchStartTime) || Objects.isNull(dayPunchEndTime)) {
            startDate = DateHelper.beginOfDay(DateHelper.transferDayIdToDate(attendanceDayId));
            endDate = DateUtil.offsetDay(startDate, 1);
            return new DayClassTimeDTO(startDate, endDate);
        }

        startDate = dayPunchEndTime;
        endDate = DateUtil.offsetDay(DateHelper.beginOfDay(DateHelper.transferDayIdToDate(attendanceDayId)), 1);
        return new DayClassTimeDTO(startDate, endDate);
    }

    private void noNeedWork(AttendanceDayReportDO attendanceDayReportDO) {
        BigDecimal finalWorkMinutes = BusinessConstant.DEFAULT_LEGAL_WORKING_MINUTES;
        BigDecimal actualAttendanceMinutes = BusinessConstant.DEFAULT_LEGAL_WORKING_MINUTES;
        //若存在请假 最终工作时长 = 实际出勤时长 = 8 - 请假时长
        if (attendanceDayReportDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
            actualAttendanceMinutes = actualAttendanceMinutes.subtract(attendanceDayReportDO.getLeaveMinutes());
            finalWorkMinutes = actualAttendanceMinutes;
        }
        attendanceDayReportDO.setActualAttendanceMinutes(actualAttendanceMinutes);
        attendanceDayReportDO.setFinalWorkMinutes(finalWorkMinutes);
    }


    public void buildFilterFormDTOList(Date punchInTime,
                                       Date punchOutTime,
                                       List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                       List<DayAttendanceHandlerFormDTO> filterFormDTOList) {
        for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
            attendanceMinuteCalculateService.shiftDayLeaveMinuteHandler(handlerFormDTO, punchInTime, punchOutTime, null,
                    null, 0, filterFormDTOList);
        }
    }


    /**
     * 时刻没有请假，正常打卡
     */
    private AttendanceMinuteDTO normalPunchHandler(List<UserPunchRecordBO> itemPunchRecordList,
                                                   DayItemConfigDateDTO itemConfigDate) {
        AttendanceMinuteDTO attendanceMinuteDTO = new AttendanceMinuteDTO();
        Date punchInTime = itemConfigDate.getPunchInTime();
        Date latestPunchInTime = itemConfigDate.getLatestPunchInTime();
        Date punchOutTime = itemConfigDate.getPunchOutTime();
        Date restStartTime = itemConfigDate.getRestStartTime();
        Date restEndTime = itemConfigDate.getRestEndTime();
        BigDecimal itemTotalMinutes = itemConfigDate.getItemTotalMinutes();
        //一条打卡记录
        if (itemPunchRecordList.size() == 1) {
            return attendanceMinuteDTO;
        }

        Date firstPunchTime = itemPunchRecordList.get(0).getFormatPunchTime();
        Date lastPunchTime = itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime();

        //多条打卡记录
        //下班未打卡
        if (lastPunchTime.compareTo(punchInTime) < 1) {
            BigDecimal minutes = BigDecimal.valueOf(DateUtil.between(firstPunchTime, lastPunchTime, DateUnit.MINUTE));
            attendanceMinuteDTO.setActualAttendanceMinutes(minutes);
            return attendanceMinuteDTO;
        }

        //上班未打卡
        if (firstPunchTime.compareTo(punchOutTime) > -1) {
            BigDecimal minutes = BigDecimal.valueOf(DateUtil.between(firstPunchTime, lastPunchTime, DateUnit.MINUTE));
            attendanceMinuteDTO.setActualAttendanceMinutes(minutes);
            return attendanceMinuteDTO;
        }

        if (firstPunchTime.compareTo(punchInTime) < 1) {
            //早退  下班时间在正常上班之前
            if (lastPunchTime.compareTo(punchInTime) < 1) {
                BigDecimal minutes = BigDecimal.valueOf(DateUtil.between(firstPunchTime, lastPunchTime, DateUnit.MINUTE));
                attendanceMinuteDTO.setActualAttendanceMinutes(minutes);
                return attendanceMinuteDTO;
            }

            //早退
            if (lastPunchTime.compareTo(punchOutTime) < 0) {
                //需要和休息时间比较
                BigDecimal actualAttendanceMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
                BigDecimal finalWorkMinutes = compareRestTimeHandler(punchInTime, lastPunchTime, restStartTime, restEndTime);
                attendanceMinuteDTO.setActualAttendanceMinutes(actualAttendanceMinutes);
                attendanceMinuteDTO.setFinalWorkMinutes(finalWorkMinutes);
                return attendanceMinuteDTO;
            }

            BigDecimal actualAttendanceMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
            BigDecimal finalWorkMinutes = compareRestTimeHandler(punchInTime, punchOutTime, restStartTime, restEndTime);
            attendanceMinuteDTO.setActualAttendanceMinutes(actualAttendanceMinutes);
            attendanceMinuteDTO.setFinalWorkMinutes(finalWorkMinutes);
            return attendanceMinuteDTO;
        }

        if (firstPunchTime.compareTo(latestPunchInTime) < 1) {
            //早退
            if (lastPunchTime.compareTo(punchOutTime) < 0) {
                BigDecimal actualAttendanceMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
                attendanceMinuteDTO.setActualAttendanceMinutes(actualAttendanceMinutes);
                attendanceMinuteDTO.setFinalWorkMinutes(actualAttendanceMinutes);
                return attendanceMinuteDTO;
            }

            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(firstPunchTime, lastPunchTime, DateUnit.MINUTE)).compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) > -1) {
                BigDecimal actualAttendanceMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
                attendanceMinuteDTO.setActualAttendanceMinutes(actualAttendanceMinutes);
                attendanceMinuteDTO.setFinalWorkMinutes(itemTotalMinutes);
                return attendanceMinuteDTO;
            }

            BigDecimal actualAttendanceMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
            attendanceMinuteDTO.setActualAttendanceMinutes(actualAttendanceMinutes);
            attendanceMinuteDTO.setFinalWorkMinutes(actualAttendanceMinutes);
            return attendanceMinuteDTO;
        }

        BigDecimal actualAttendanceMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
        attendanceMinuteDTO.setActualAttendanceMinutes(actualAttendanceMinutes);
        BigDecimal finalWorkMinutes;
        if (lastPunchTime.compareTo(punchOutTime) > 0) {
            finalWorkMinutes = compareRestTimeHandler(firstPunchTime, punchOutTime, restStartTime, restEndTime);
        } else {
            finalWorkMinutes = compareRestTimeHandler(firstPunchTime, lastPunchTime, restStartTime, restEndTime);
        }
        attendanceMinuteDTO.setFinalWorkMinutes(finalWorkMinutes);
        return attendanceMinuteDTO;
    }


    private BigDecimal compareRestTimeHandler(Date startDate, Date endDate, Date restStartTime, Date restEndTime) {
        BigDecimal minutes = BigDecimal.valueOf(DateUtil.between(startDate, endDate, DateUnit.MINUTE));
        if (restStartTime == null) {
            return minutes;
        }
        //没有交集
        if (endDate.compareTo(restStartTime) < 1 || startDate.compareTo(restEndTime) > -1) {
            return minutes;
        }
        //被休息时间包含
        if (startDate.compareTo(restStartTime) > -1 && endDate.compareTo(restEndTime) < 1) {
            return BigDecimal.ZERO;
        }
        //休息时间被包含
        if (startDate.compareTo(restStartTime) < 1 && endDate.compareTo(restEndTime) > -1) {
            return minutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
        }
        if (startDate.compareTo(restStartTime) < 0) {
            return minutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, endDate, DateUnit.MINUTE)));
        }
        return minutes.subtract(BigDecimal.valueOf(DateUtil.between(startDate, restEndTime, DateUnit.MINUTE)));
    }

    /**
     * 时刻有请假，正常打卡
     */
    private AttendanceMinuteDTO leaveDayBatchHandler(List<UserPunchRecordBO> itemPunchRecordList,
                                                     List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                                     DayItemConfigDateDTO itemConfigDate,
                                                     BigDecimal itemTotalMinutes) {
        AttendanceMinuteDTO attendanceMinuteDTO = new AttendanceMinuteDTO();
        Date earliestPunchInTime = itemConfigDate.getEarliestPunchInTime();
        Date punchInTime = itemConfigDate.getPunchInTime();
        Date punchOutTime = itemConfigDate.getPunchOutTime();
        Date latestPunchOutTime = itemConfigDate.getLatestPunchOutTime();
        Date restStartTime = itemConfigDate.getRestStartTime();
        Date restEndTime = itemConfigDate.getRestEndTime();
        long betweenMinutes = itemConfigDate.getBetweenMinutes();

        List<UserPunchRecordBO> punchBeforeCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && o.getFormatPunchTime().compareTo(punchInTime) < 0)
                .collect(Collectors.toList());

        List<UserPunchRecordBO> punchBetweenCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(punchInTime) > -1 && o.getFormatPunchTime().compareTo(punchOutTime) < 1)
                .collect(Collectors.toList());

        List<UserPunchRecordBO> punchAfterCardList = itemPunchRecordList.stream()
                .filter(o -> o.getFormatPunchTime().compareTo(punchOutTime) > 0 && o.getFormatPunchTime().compareTo(latestPunchOutTime) < 1)
                .collect(Collectors.toList());

        BigDecimal presentMinutes;

        //打卡时间全部在正规上下班中
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            List<DayItemInfoDTO> dayItemInfoDTOS = new ArrayList<>();
            //整个时段内，除去请假外的打卡记录的区间
            attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoDTOS, filterFormDTOList, punchBetweenCardList);
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            attendanceMinuteDTO.setActualAttendanceMinutes(presentMinutes);
            attendanceMinuteDTO.setFinalWorkMinutes(presentMinutes);
            return attendanceMinuteDTO;
        }

        //开始时间和中间时间为空,直接是上班卡未打异常
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            List<DayItemInfoDTO> dayItemInfoDTOS = new ArrayList<>();
            attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoDTOS, filterFormDTOList, punchAfterCardList);
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            attendanceMinuteDTO.setActualAttendanceMinutes(presentMinutes);
            return attendanceMinuteDTO;
        }

        //结束时间和中间时间为空,直接是下班卡未打异常
        if (CollectionUtils.isEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            List<DayItemInfoDTO> dayItemInfoDTOS = new ArrayList<>();
            attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoDTOS, filterFormDTOList, punchBeforeCardList);
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            attendanceMinuteDTO.setActualAttendanceMinutes(presentMinutes);
            return attendanceMinuteDTO;
        }

        if (CollectionUtils.isNotEmpty(punchBeforeCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            Date endPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
            attendanceMinuteDTO.setActualAttendanceMinutes(attendanceMinuteCalculateService.getPresentMinutes(true, restStartTime, restEndTime, punchBeforeCardList.get(0).getFormatPunchTime(), endPunchTime, null, filterFormDTOList));
            attendanceMinuteDTO.setFinalWorkMinutes(attendanceMinuteCalculateService.getPresentMinutes(true, restStartTime, restEndTime, punchInTime, endPunchTime, null, filterFormDTOList));
            return attendanceMinuteDTO;
        }

        if (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchBeforeCardList)) {
            Date beginPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            Date endPunchTime = punchOutTime;
            if (betweenMinutes != 0) {
                // 说明存在弹性时长，这边计算用户应该的的下班打卡时间（应该就是正常上下班的时间，包含弹性时长）
                Date shouldEndPunchTime = DateUtil.offsetMinute(punchOutTime, (int) betweenMinutes);
                // 用户真实下班打卡时间
                Date lastPunchOutTime = punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime();
                // 获取最新的下班时间，判断是否大于弹性下班时长，大于等于的话，取弹性下班时间，否则取正常下班时长
                if (lastPunchOutTime.compareTo(shouldEndPunchTime) > -1) {
                    endPunchTime = shouldEndPunchTime;
                }
            }
            attendanceMinuteDTO.setActualAttendanceMinutes(attendanceMinuteCalculateService.getPresentMinutes(false, restStartTime, restEndTime, beginPunchTime, punchAfterCardList.get(punchAfterCardList.size() - 1).getFormatPunchTime(), null, filterFormDTOList));
            attendanceMinuteDTO.setFinalWorkMinutes(attendanceMinuteCalculateService.getPresentMinutes(false, restStartTime, restEndTime, beginPunchTime, endPunchTime, null, filterFormDTOList));
            return attendanceMinuteDTO;

        }

        //全部打卡,（或者前后都有打卡，就中间没打卡）没有异常，就看P的时间
        if ((CollectionUtils.isNotEmpty(punchAfterCardList)
                && CollectionUtils.isNotEmpty(punchBetweenCardList)
                && CollectionUtils.isNotEmpty(punchBeforeCardList))
                || (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))) {
            List<DayItemInfoDTO> dayItemInfoDTOS = new ArrayList<>();
            attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoDTOS, filterFormDTOList, itemPunchRecordList);
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            attendanceMinuteDTO.setActualAttendanceMinutes(presentMinutes);
            attendanceMinuteDTO.setFinalWorkMinutes(itemTotalMinutes);
            return attendanceMinuteDTO;
        }
        return attendanceMinuteDTO;
    }

    /**
     * 构建基本信息
     *
     * @param attendanceDayReportDO
     * @param param
     */
    private void buildUserBaseReportInfo(AttendanceDayReportDO attendanceDayReportDO,
                                         AttendanceReportAbnormalHandlerDTO param) {
        UserInfoDO userInfoDO = param.getUserInfoDO();
        attendanceDayReportDO.setDayId(param.getAttendanceDayId());
        attendanceDayReportDO.setUserId(userInfoDO.getId());
        attendanceDayReportDO.setUserCode(userInfoDO.getUserCode());
        attendanceDayReportDO.setUserName(userInfoDO.getUserName());
        attendanceDayReportDO.setEmployeeType(userInfoDO.getEmployeeType());
        attendanceDayReportDO.setStatus(userInfoDO.getStatus());
        attendanceDayReportDO.setWorkStatus(userInfoDO.getWorkStatus());
        attendanceDayReportDO.setDeptId(userInfoDO.getDeptId());
        attendanceDayReportDO.setPostId(userInfoDO.getPostId());
        attendanceDayReportDO.setLocationCountry(userInfoDO.getLocationCountry());
        attendanceDayReportDO.setLocationProvince(userInfoDO.getLocationProvince());
        attendanceDayReportDO.setLocationCity(userInfoDO.getLocationCity());
        attendanceDayReportDO.setClassNature(StringUtils.isBlank(userInfoDO.getClassNature())
                ? BusinessConstant.EMPTY_STR : userInfoDO.getClassNature());
        CalendarConfigDO calendarConfigDO = param.getCalendarConfigDO();
        if (Objects.nonNull(calendarConfigDO)) {
            attendanceDayReportDO.setCalendarId(calendarConfigDO.getId());
        } else {
            attendanceDayReportDO.setCalendarId(null);
        }
        PunchConfigDO punchConfigDO = param.getPunchConfigDO();
        if (Objects.nonNull(punchConfigDO)) {
            attendanceDayReportDO.setPunchConfigId(punchConfigDO.getId());
            attendanceDayReportDO.setPunchConfigType(punchConfigDO.getConfigType());
        } else {
            attendanceDayReportDO.setPunchConfigId(null);
            attendanceDayReportDO.setPunchConfigType(null);
        }
        ReissueCardConfigDO reissueCardConfigDO = param.getReissueCardConfigDO();
        if (Objects.nonNull(reissueCardConfigDO)) {
            attendanceDayReportDO.setReissueCardConfigId(reissueCardConfigDO.getId());
        } else {
            attendanceDayReportDO.setReissueCardConfigId(null);
        }
        OverTimeConfigDO overTimeConfigDO = param.getOverTimeConfigDO();
        if (Objects.nonNull(overTimeConfigDO)) {
            attendanceDayReportDO.setOverTimeConfigId(overTimeConfigDO.getId());
        } else {
            attendanceDayReportDO.setOverTimeConfigId(null);
        }
        attendanceDayReportDO.setDayShiftRule(getDayShiftRule(param.getUserShiftConfigDO(), attendanceDayReportDO));
        if (Objects.isNull(param.getUserShiftConfigDO())) {
            attendanceDayReportDO.setPunchClassConfigId(null);
        } else {
            attendanceDayReportDO.setPunchClassConfigId(param.getUserShiftConfigDO().getPunchClassConfigId());
        }
        BaseDOUtil.fillDOUpdateByUserOrSystem(attendanceDayReportDO);

    }

    /**
     * 日报逻辑计算
     */
    private void dayReportHandler(AttendanceDayReportDO attendanceDayReportDO,
                                  AttendanceReportAbnormalHandlerDTO param,
                                  List<AttendanceDayReportFormDO> addAttendanceDayReportFormDOList,
                                  List<AttendanceDayReportFormDO> updateAttendanceDayReportFormDOList,
                                  List<AttendanceDayReportAbnormalDO> addAttendanceDayReportAbnormalDOList,
                                  List<AttendanceDayReportAbnormalDO> updateAttendanceDayReportAbnormalDOList) {
        List<EmployeeAbnormalAttendanceDO> employeeAbnormalAttendanceDOList = param.getEmployeeAbnormalAttendanceDOList();
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = param.getAttendanceEmployeeDetailDOList();
        //工时异常时长默认赋值0 目前仅仓内场景使用
        attendanceDayReportDO.setAbnormalWorkMinutes(BigDecimal.ZERO);
        //请假&外勤时长
        List<Long> formIdList = new ArrayList<>();
        buildFormMinutes(formIdList, attendanceDayReportDO, attendanceEmployeeDetailDOList);
        //加班时长
        buildOverTimeMinutes(param.getAttendanceDayId(), param.getUserInfoDO(), attendanceDayReportDO);

        //查询补卡单据
        selectReissueCardFormList(attendanceDayReportDO.getUserId(), attendanceDayReportDO.getDayId(), formIdList);

        List<AttendanceEmployeeDetailSnapshotDO> attendanceEmployeeDetailSnapshotDOList = param.getAttendanceEmployeeDetailSnapshotDOList();
        List<EmployeeAbnormalAttendanceSnapshotDO> employeeAbnormalAttendanceSnapshotDOList = param.getEmployeeAbnormalAttendanceSnapshotDOList();

        //初始考勤结果
        if (CollectionUtils.isNotEmpty(attendanceEmployeeDetailSnapshotDOList)
                || CollectionUtils.isNotEmpty(employeeAbnormalAttendanceSnapshotDOList)) {
            attendanceDayReportDO.setInitResult(getInitResult(employeeAbnormalAttendanceSnapshotDOList, attendanceDayReportDO));
        }

        //最终考勤结果 (免打卡默认正常)
        attendanceDayReportDO.setFinalResult(getFinalResult(attendanceEmployeeDetailDOList
                    , employeeAbnormalAttendanceDOList, attendanceDayReportDO));
        if (Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), attendanceDayReportDO.getPunchConfigType())
                && !DayShiftRuleEnum.isRestDay(attendanceDayReportDO.getDayShiftRule())) {
            attendanceDayReportDO.setFinalResult(AttendanceReportStatusEnum.NORMAL.getCode());
        }

        //获取员工当日请假或外勤通过的单据信息
        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = attendanceMinuteCalculateService.dayFormInfoBuild(param.getUserPassFormBOList(), Collections.emptyList());

        //实际出勤时长&最终工作时长
        buildActualAttendanceMinutes(param, attendanceDayReportDO, handlerFormDTOList);

        //出勤结果封装
        String attendanceResult = this.buildAttendanceResult(param, attendanceDayReportDO);
        attendanceDayReportDO.setAttendanceResult(attendanceResult);
        //缺勤结果封装
        attendanceDayReportDO.setAbsentResult(attendanceResult.contains("A")
                ? WhetherEnum.YES.getKey()
                : WhetherEnum.NO.getKey());

        //构造新增的单据关联子表
        addAttendanceDayReportFormDOList.addAll(buildAttendanceDayReportFormDOList(attendanceDayReportDO.getId(), formIdList));
        List<AttendanceDayReportFormDO> dayReportFormDOList = attendanceDayReportFormDao.selectByReportId(attendanceDayReportDO.getId());
        dayReportFormDOList.forEach(form -> {
            form.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(form);
        });
        updateAttendanceDayReportFormDOList.addAll(dayReportFormDOList);

        //构造新增的异常关联子表
        addAttendanceDayReportAbnormalDOList.addAll(buildAttendanceDayReportAbnormalDOList(attendanceDayReportDO.getId(), employeeAbnormalAttendanceDOList));
        List<AttendanceDayReportAbnormalDO> abnormalDOList = attendanceDayReportAbnormalDao.selectByReportId(attendanceDayReportDO.getId());
        abnormalDOList.forEach(form -> {
            form.setIsDelete(IsDeleteEnum.YES.getCode());
            BaseDOUtil.fillDOUpdateByUserOrSystem(form);
        });
        updateAttendanceDayReportAbnormalDOList.addAll(abnormalDOList);
    }

    /**
     * 考勤结果封装
     *
     * @param param
     * @param attendanceDayReportDO
     */
    private String buildAttendanceResult(AttendanceReportAbnormalHandlerDTO param,
                                         AttendanceDayReportDO attendanceDayReportDO) {
        // 拼接出勤结果
        StringBuilder flag = new StringBuilder();
        String attendanceResult = BusinessConstant.EMPTY_STR;
        Long dayId = attendanceDayReportDO.getDayId();
        UserInfoDO userInfo = param.getUserInfoDO();
        //该用户是否离职
        if (StringUtils.equalsIgnoreCase(userInfo.getWorkStatus(), WorkStatusEnum.DIMISSION.getCode())
                && param.getDimissionDate() != null) {
            Long dimissionDayId = Long.valueOf(DateUtil.format(param.getDimissionDate(), "yyyyMMdd"));
            // 离职时间大于当前时间
            if (dimissionDayId.compareTo(dimissionDayId) < 0) {
                return attendanceResult;
            }
            // 离职当天-特殊处理 如果是离职当天，前面加上LWD-
            if (dimissionDayId.compareTo(dayId) == 0) {
                flag.append("LWD-");
            }
        }
        // 该用户是否停用
        if (StringUtils.equalsIgnoreCase(userInfo.getStatus(), StatusEnum.DISABLED.getCode())
                && userInfo.getDisabledDate() != null) {
            Long disabledDayId = Long.valueOf(DateUtil.format(userInfo.getDisabledDate(), "yyyyMMdd"));
            if (disabledDayId.compareTo(dayId) < 0) {
                return attendanceResult;
            }
        }
        // 该用户入职之前的时间不生成记录
        if (StringUtils.equalsIgnoreCase(userInfo.getStatus(), StatusEnum.ACTIVE.getCode())
                && param.getEntryDate() != null) {
            Long entryRecordDayId = Long.valueOf(DateUtil.format(param.getEntryDate(), "yyyyMMdd"));
            // 如果该用户入职日期 > 当前日期，则为空
            if (entryRecordDayId.compareTo(dayId) > 0) {
                return attendanceResult;
            }
            // 入职当天，特殊处理。如果是入职当天，前面加上DOJ-【就算未来已经知道他离职了，入职当天也显示DOJ前缀】
            if (entryRecordDayId.compareTo(dayId) == 0) {
                flag.append("DOJ-");
            }
        }
        // 获取当天出勤情况
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailDOList = param.getAttendanceEmployeeDetailDOList();
        // 获取当天班次
        PunchClassConfigDTO punchClassConfigDTO = param.getPunchClassConfigDTO();
        // 获取当天打卡规则
        PunchConfigDO punchConfigDO = param.getPunchConfigDO();
        //获取当天的出勤时间
        BigDecimal defaultLegalWorkingHours = BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS;
        List<BigDecimal> defaultWorkingHours = attendanceEmployeeDetailDOList.stream()
                .filter(item -> item.getLegalWorkingHours() != null
                        && item.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > 0
                        && item.getAttendanceMinutes() != null
                        && item.getAttendanceMinutes().compareTo(BigDecimal.ZERO) > 0)
                .map(AttendanceEmployeeDetailDO::getLegalWorkingHours).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(defaultWorkingHours)) {
            defaultLegalWorkingHours = defaultWorkingHours.get(0);
        }
        if (Objects.nonNull(punchClassConfigDTO)
                && Objects.nonNull(punchClassConfigDTO.getLegalWorkingHours())
                && punchClassConfigDTO.getLegalWorkingHours().compareTo(BigDecimal.ZERO) > -1) {
            defaultLegalWorkingHours = punchClassConfigDTO.getLegalWorkingHours();
        }
        if (Objects.nonNull(punchConfigDO)
                && PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfigDO.getConfigType())) {
            defaultLegalWorkingHours = Objects.isNull(punchConfigDO.getPunchTimeInterval())
                    ? BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS : punchConfigDO.getPunchTimeInterval();
        }
        BigDecimal defaultLegalWorkingMinutes = defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES);
        //获取当天的假期信息/改月的请假信息
        StringBuilder dayLeaveInfo = new StringBuilder();
        // 获取该用户当天考勤数据的请假信息--->使用请假类型(后期记录为名称)分组
        Map<String, List<AttendanceEmployeeDetailDO>> leaveAttendanceMap = attendanceEmployeeDetailDOList.stream()
                .filter(item -> item.getLeaveMinutes() != null && StringUtils.isNotBlank(item.getLeaveType()))
                .collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getLeaveType));
        //存储该用户该请假类型的请假天数：key-请假类型，value-请假天数
        Map<String, String> leaveMap = new HashMap<>();
        // 遍历考勤请假信息
        for (Map.Entry<String, List<AttendanceEmployeeDetailDO>> entry : leaveAttendanceMap.entrySet()) {
            BigDecimal leaveDays = this.getLeaveDay(entry, defaultLegalWorkingHours);
            // 如果是一整天则省略前面的1，否则加上，比如0.75L
            if (leaveDays.compareTo(BigDecimal.ONE) == 0) {
                dayLeaveInfo.append(entry.getValue().get(0).getConcreteType()).append(" ");
            } else {
                dayLeaveInfo.append(leaveDays).append(entry.getValue().get(0).getConcreteType()).append(" ");
            }
            String existLeaveDays = leaveMap.get(entry.getKey());
            if (StringUtils.isBlank(existLeaveDays)) {
                leaveMap.put(entry.getKey(), leaveDays.toString());
            } else {
                leaveMap.put(entry.getKey(), leaveDays.add(new BigDecimal(existLeaveDays)).toString());
            }
        }

        //请假时长
        BigDecimal leaveMinutes = Objects.isNull(attendanceDayReportDO.getLeaveMinutes())
                ? BigDecimal.ZERO : attendanceDayReportDO.getLeaveMinutes();
        //外勤时长
        BigDecimal oooMinutes = Objects.isNull(attendanceDayReportDO.getOooMinutes())
                ? BigDecimal.ZERO : attendanceDayReportDO.getOooMinutes();
        //出勤时长
        BigDecimal attendanceMinutes = Objects.isNull(attendanceDayReportDO.getFinalWorkMinutes())
                ? BigDecimal.ZERO : attendanceDayReportDO.getFinalWorkMinutes();
        //免打卡
        if (Objects.equals(PunchConfigTypeEnum.NO_NEED_PUNCH_WORK.getCode(), attendanceDayReportDO.getPunchConfigType())) {
            //该用户当天完全请假
            if (leaveMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                // 去掉请假特殊逻辑：请假一整天：excel展示"L"。使用具体的类型替换，比如"AL"、"ML"等
                return flag.append(dayLeaveInfo).toString();
            }
            //该用户当天部分请假
            if (leaveMinutes.compareTo(BigDecimal.ZERO) > 0) {
                // 获取该用户该天实际出勤天数：【实际出勤时长 / 默认时长 = 实际出勤天数】【四舍五入，保留两位小数】
                BigDecimal dayActualAttendanceDays = (defaultLegalWorkingMinutes.subtract(leaveMinutes))
                        .divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
                // excel展示的该用户该天出勤情况：【实际出勤天数 + "P" + 请假信息】
                return flag.append(dayActualAttendanceDays).append("P").append(" ").append(dayLeaveInfo).toString();
            }
        }
        // 班次计划
        String dayShiftRule = attendanceDayReportDO.getDayShiftRule();
        // 排班
        UserShiftConfigDO userShiftConfig = param.getUserShiftConfigDO();
        //该用户当天未排班(按照上班算，即该天是应出勤的)
        if (Objects.isNull((userShiftConfig))) {
            //休息日/法假
            if (Objects.equals(DayShiftRuleEnum.OFF.getCode(), dayShiftRule)
                    || Objects.equals(DayShiftRuleEnum.H.getCode(), dayShiftRule)) {
                return flag.append(attendanceEmployeeDetailDOList.get(0).getConcreteType()).append(" ").append(dayLeaveInfo).toString();
            }
            //当天未排班异常处理为工作日或者异常还未处理，那么也是按照工作日计算
            // 该用户该天的请假分钟与出勤分钟都为0 则为缺勤
            if (leaveMinutes.compareTo(BigDecimal.ZERO) == 0
                    && attendanceMinutes.compareTo(BigDecimal.ZERO) == 0) {
                //该用户该天异常未处理
                return flag.append("A").toString();
            }
            // 该用户该天出勤分钟数大于0
            if (attendanceMinutes.compareTo(BigDecimal.ZERO) > 0) {
                // 如果该用户该天出勤分钟数 = 默认出勤分钟数，则为全天出勤
                if (attendanceMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
                    return flag.append("P").toString();
                }
                // 否则为部分出勤，计算实际出勤小时数
                BigDecimal dayActualAttendanceHours = attendanceMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
                // 计算实际出勤天数
                BigDecimal dayActualAttendanceDays = attendanceMinutes.divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
                // 拼接该用户该天出勤情况：【实际出勤天数 + "P"】
                attendanceResult = dayActualAttendanceDays + "P";
            }
            // excel展示的该用户该天出勤情况：【实际出勤天数 + "P" + 请假信息】
            return flag.append(attendanceResult).append(" ").append(dayLeaveInfo).toString();
        }
        //该用户当天排班如果是PH/OFF就无需调用
        if (Objects.equals(DayShiftRuleEnum.OFF.getCode(), dayShiftRule)
                || Objects.equals(DayShiftRuleEnum.H.getCode(), dayShiftRule)) {
            return flag.append(userShiftConfig.getDayShiftRule()).append(" ").append(dayLeaveInfo).toString();
        }
        /*
         *（ 没有排班 + 有排班但是是PH/OFF ）之后的就只剩下有排班不是PH/OFF的情况了。统计该用户应出勤天数
         * 1. 前置拦截：在根据打卡记录判断考勤时长之前，先判断该用户的出勤时长是否是满勤，也就是法定时长
         * 2. 兼容批量异常处理的情况。如果是批量异常处理：考勤表直接会生成p的记录，异常表的异常状态会是PASS
         */
        if (attendanceMinutes.compareTo(defaultLegalWorkingMinutes) == 0) {
            return flag.append("P").toString();
        }

        if (attendanceMinutes.compareTo(BigDecimal.ZERO) == 0
                && leaveMinutes.compareTo(BigDecimal.ZERO) == 0
                && oooMinutes.compareTo(BigDecimal.ZERO) == 0) {
            return flag.append("A").toString();
        }

        if (attendanceMinutes.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal dayActualAttendanceDays = attendanceMinutes.divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
            attendanceResult = dayActualAttendanceDays + "P";
        }
        return flag.append(attendanceResult).append(" ").append(dayLeaveInfo).toString();
    }

    @NotNull
    private static BigDecimal getLeaveDay(Map.Entry<String, List<AttendanceEmployeeDetailDO>> entry, BigDecimal defaultLegalWorkingHours) {
        BigDecimal leaveMinutes = BigDecimal.ZERO;
        // 遍历同一种类型的请假信息--->获取该用户的该请假类型的请假总分钟数
        for (AttendanceEmployeeDetailDO employeeDetailDO : entry.getValue()) {
            if (employeeDetailDO.getLeaveMinutes() != null && employeeDetailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                leaveMinutes = leaveMinutes.add(employeeDetailDO.getLeaveMinutes());
            }
        }
        // 将该用户该请假类型的总请假分钟转换为天数
        BigDecimal leaveDays = leaveMinutes.divide(BusinessConstant.MINUTES.multiply(defaultLegalWorkingHours), 4, RoundingMode.HALF_UP);
        return leaveDays;
    }

    private void selectReissueCardFormList(Long userId, Long attendanceDayId, List<Long> formIdList) {
        List<AttendanceFormDetailBO> attendanceFormDetailBOList = attendanceFormManage.listByUserIds(Collections.singletonList(userId),
                Collections.singletonList(FormStatusEnum.PASS.getCode()),
                Lists.newArrayList(FormTypeEnum.REISSUE_CARD.getCode()));
        // 获取时间范围内有效的审批单据
        List<AttendanceFormDO> effectFormList = attendanceFormManage.getEffectReissueCardFormList(attendanceDayId, attendanceFormDetailBOList);
        List<Long> effectFormIdList = effectFormList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(effectFormList)) {
            formIdList.addAll(effectFormIdList);
        }
    }

    /**
     * 批量插入 mysql默认值不生效 设置BigDecimal默认值
     *
     * @param addDayReportDOList
     */
    private void buildDefaultVal(List<AttendanceDayReportDO> addDayReportDOList) {
        if (CollectionUtils.isNotEmpty(addDayReportDOList)) {
            addDayReportDOList.stream().forEach(item -> {
                if (Objects.isNull(item.getLeaveMinutes())) {
                    item.setLateMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getOooMinutes())) {
                    item.setOooMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getOverTimeMinutes())) {
                    item.setOverTimeMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getDelayMinutes())) {
                    item.setDelayMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getLeaveEarlyMinutes())) {
                    item.setLeaveEarlyMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getLateMinutes())) {
                    item.setLateMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getActualAttendanceMinutes())) {
                    item.setActualAttendanceMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getFinalWorkMinutes())) {
                    item.setFinalWorkMinutes(BigDecimal.ZERO);
                }
                if (Objects.isNull(item.getAbnormalWorkMinutes())) {
                    item.setAbnormalWorkMinutes(BigDecimal.ZERO);
                }
            });
        }
    }
}
