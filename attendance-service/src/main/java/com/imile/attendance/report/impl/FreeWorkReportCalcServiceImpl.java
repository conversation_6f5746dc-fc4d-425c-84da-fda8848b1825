package com.imile.attendance.report.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.report.AttendanceReportCalcService;
import com.imile.attendance.report.dto.AttendanceReportCalcContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 自由打卡(灵活打卡两次)日月报时长计算
 *
 * <AUTHOR>
 * @menu 考勤日月报
 * @date 2025/6/12
 */
@Slf4j
@Service
@Strategy(value = AttendanceReportCalcService.class, implKey = "FreeWorkReportCalcServiceImpl")
public class FreeWorkReportCalcServiceImpl implements AttendanceReportCalcService {

    @Override
    public boolean isMatch(String punchConfigType) {
        return PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode().equals(punchConfigType);
    }

    @Override
    public BigDecimal execute(AttendanceReportCalcContext reportCalcContext) {
        BigDecimal availableMinutes = BigDecimal.ZERO;
        //获取当天的正常上班时间
        Date earliestPunchInTime = reportCalcContext.getDayClassItemConfigList().get(0).getEarliestPunchInTime();
        String earliestPunchInTimeString = DateUtil.format(earliestPunchInTime, "HH:mm:ss");
        String earliestPunchInTimeDayString = DateUtil.format(DateUtil.parse(reportCalcContext.getDayId().toString(), "yyyyMMdd"), "yyyy-MM-dd");
        Date actualAttendanceStartTime = DateUtil.parse(earliestPunchInTimeDayString + " " + earliestPunchInTimeString, "yyyy-MM-dd HH:mm:ss");
        Date actualAttendanceEndTime = DateUtil.offsetDay(actualAttendanceStartTime, 1);

        List<UserPunchRecordDTO> itemPunchRecordList = reportCalcContext.getUserPunchRecordList().stream()
                .filter(item -> item.getFormatPunchTime().compareTo(actualAttendanceStartTime) > -1
                        && item.getFormatPunchTime().compareTo(actualAttendanceEndTime) < 1)
                .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime))
                .collect(Collectors.toList());

        //么有打卡数据，直接上下班缺卡异常
        if (CollectionUtils.isEmpty(itemPunchRecordList)) {
            return availableMinutes;
        }
        //只有一个打卡
        if (itemPunchRecordList.size() == 1) {
            return availableMinutes;
        }
        //有多个打卡记录
        availableMinutes = BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime()
                , itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE));
        //需要把请假时间也加上，自由打卡规则需要修改下
        availableMinutes = availableMinutes.add(reportCalcContext.getFormMinutes());
        return availableMinutes;
    }
}
