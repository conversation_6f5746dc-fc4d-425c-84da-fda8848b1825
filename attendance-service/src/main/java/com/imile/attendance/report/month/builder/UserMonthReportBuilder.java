package com.imile.attendance.report.month.builder;

import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.report.mapstruct.UserMonthReportMapstruct;
import com.imile.attendance.report.month.dto.UserDayMetricsDTO;
import com.imile.attendance.report.month.dto.UserMonthDayAttendanceDTO;
import com.imile.attendance.report.month.dto.UserMonthReportMetricsDTO;
import com.imile.attendance.report.month.vo.UserMonthReportListVO;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户月报构建器
 * 负责构建用户月报的基本信息和考勤指标
 *
 * <AUTHOR> chen
 * @date 2025/6/23
 */
@Component
@Slf4j
public class UserMonthReportBuilder {

    /**
     * 设置用户基本信息
     *
     * @param userMonthReport 用户月报VO
     * @param userDayReportList 用户日报列表
     * @param userMap 用户信息映射
     * @param deptMap 部门信息映射
     * @param postMap 岗位信息映射
     * @param userEntryRecordMap 用户入职记录映射
     * @param userDimissionRecordMap 用户离职记录映射
     */
    public void buildUserBasicInfo(UserMonthReportListVO userMonthReport,
                                   List<AttendanceDayReportDO> userDayReportList,
                                   Map<Long, AttendanceUser> userMap,
                                   Map<Long, AttendanceDept> deptMap,
                                   Map<Long, AttendancePost> postMap,
                                   Map<Long, UserEntryRecordDO> userEntryRecordMap,
                                   Map<Long, UserDimissionRecordDO> userDimissionRecordMap) {

        Long userId = userMonthReport.getUserId();

        // 获取考勤周期最后一天的数据作为用户信息来源
        AttendanceDayReportDO endDayReport = getEndDayReport(userDayReportList, userMonthReport.getAttendanceEndCycle());

        // 设置入离职
        UserEntryRecordDO userEntryRecordDO = userEntryRecordMap.get(userId);
        if (Objects.nonNull(userEntryRecordDO) && Objects.nonNull(userEntryRecordDO.getConfirmDate())) {
            //设置用户实际入职日期
            userMonthReport.setEntryDate(DateHelper.formatDateWithSlash(userEntryRecordDO.getConfirmDate()));
        }
        //获取员工的实际离职日期
        UserDimissionRecordDO userDimissionRecordDO = userDimissionRecordMap.get(userId);
        if (Objects.nonNull(userDimissionRecordDO) && Objects.nonNull(userDimissionRecordDO.getActualDimissionDate())) {
            userMonthReport.setDimissionDate(DateHelper.formatDateWithSlash(userDimissionRecordDO.getActualDimissionDate()));
        }

        if (endDayReport != null) {
            // 使用周期结束日的数据
            setUserInfoFromDayReport(userMonthReport, endDayReport, deptMap, postMap);
        } else {
            // 使用用户基础信息
            setUserInfoFromUserData(userMonthReport, userMap.get(userId), deptMap, postMap);
        }
    }

    /**
     * 设置考勤指标
     *
     * @param userMonthReport 用户月报VO
     * @param metrics 考勤指标DTO
     */
    public void buildAttendanceMetrics(UserMonthReportListVO userMonthReport, UserMonthReportMetricsDTO metrics) {
        if (metrics == null || !metrics.isValid()) {
            log.warn("考勤指标无效，使用默认值。用户ID: {}", userMonthReport.getUserId());
            metrics = UserMonthReportMetricsDTO.empty();
        }

        userMonthReport.setShouldAttendanceDays(metrics.getShouldAttendanceDays());
        userMonthReport.setActualAttendanceDays(metrics.getActualAttendanceDays());
        userMonthReport.setActualAttendanceHours(metrics.getActualAttendanceHours());
        userMonthReport.setWorkHours(metrics.getWorkHours());
        userMonthReport.setActualAttendanceAverageHours(metrics.getActualAttendanceAverageHours());
        userMonthReport.setLeaveHours(metrics.getLeaveHours());
        userMonthReport.setOooHours(metrics.getOooHours());
        userMonthReport.setLeaveCount(metrics.getLeaveCount());
        userMonthReport.setOooCount(metrics.getOooCount());
        userMonthReport.setReissueCardFormCount(metrics.getReissueCardFormCount());
        userMonthReport.setDelayHours(metrics.getDelayHours());
        userMonthReport.setReissueCardCount(metrics.getReissueCardCount());
        userMonthReport.setOffDay(metrics.getOffDay());
        userMonthReport.setAbsentDays(metrics.getAbsentDays());
    }

    /**
     * 获取考勤周期结束日的日报数据
     *
     * @param userDayReportList 用户日报列表
     * @param endCycleDayId 周期结束日ID
     * @return 结束日的日报数据，如果不存在则返回null
     */
    private AttendanceDayReportDO getEndDayReport(List<AttendanceDayReportDO> userDayReportList, Long endCycleDayId) {
        if (userDayReportList == null || userDayReportList.isEmpty() || endCycleDayId == null) {
            return null;
        }

        return userDayReportList.stream()
                .filter(dayReport -> endCycleDayId.equals(dayReport.getDayId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 从日报数据设置用户信息
     *
     * @param userMonthReport 用户月报VO
     * @param endDayReport 结束日日报数据
     * @param deptMap 部门信息映射
     * @param postMap 岗位信息映射
     */
    private void setUserInfoFromDayReport(UserMonthReportListVO userMonthReport,
                                          AttendanceDayReportDO endDayReport,
                                          Map<Long, AttendanceDept> deptMap,
                                          Map<Long, AttendancePost> postMap) {

        // 设置用工类型
        String employeeType = endDayReport.getEmployeeType();
        if (StringUtils.isNotBlank(employeeType)) {
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(employeeType);
            if (employmentTypeEnum != null) {
                userMonthReport.setEmployeeType(employmentTypeEnum.getCode());
                userMonthReport.setEmployeeTypeDesc(RequestInfoHolder.isChinese() ?
                        employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
            }
        }

        // 设置部门信息
        Long deptId = endDayReport.getDeptId();
        AttendanceDept dept = deptMap.get(deptId);
        if (dept != null) {
            userMonthReport.setDeptName(dept.getLocalizeName());
        }

        // 设置岗位信息
        Long postId = endDayReport.getPostId();
        AttendancePost post = postMap.get(postId);
        if (post != null) {
            userMonthReport.setPostName(post.getLocalizeName());
        }

        // 设置常驻地信息
        userMonthReport.setLocationCountry(endDayReport.getLocationCountry());
        userMonthReport.setLocationProvince(endDayReport.getLocationProvince());
        userMonthReport.setLocationCity(endDayReport.getLocationCity());

        // 设置工作状态和账号状态
        userMonthReport.setWorkStatus(endDayReport.getWorkStatus());
        userMonthReport.setWorkStatusDesc(WorkStatusEnum.getLocalizedDesc(endDayReport.getWorkStatus()));

        userMonthReport.setAccountStatus(endDayReport.getStatus());
    }

    /**
     * 从用户基础数据设置用户信息
     *
     * @param userMonthReport 用户月报VO
     * @param user 用户信息
     * @param deptMap 部门信息映射
     * @param postMap 岗位信息映射
     */
    private void setUserInfoFromUserData(UserMonthReportListVO userMonthReport,
                                         AttendanceUser user,
                                         Map<Long, AttendanceDept> deptMap,
                                         Map<Long, AttendancePost> postMap) {

        if (user != null) {
            // 设置用工类型
            EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(user.getEmployeeType());
            if (employmentTypeEnum != null) {
                userMonthReport.setEmployeeType(employmentTypeEnum.getCode());
                userMonthReport.setEmployeeTypeDesc(RequestInfoHolder.isChinese() ?
                        employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
            }
            // 设置工作状态描述
            userMonthReport.setWorkStatusDesc(WorkStatusEnum.getLocalizedDesc(user.getWorkStatus()));
        }

        // 设置部门信息
        AttendanceDept dept = deptMap.get(userMonthReport.getDeptId());
        if (dept != null) {
            userMonthReport.setDeptName(dept.getLocalizeName());
        }

        // 设置岗位信息
        AttendancePost post = postMap.get(userMonthReport.getPostId());
        if (post != null) {
            userMonthReport.setPostName(post.getLocalizeName());
        }
    }

    /**
     * 构建每日考勤情况
     *
     * @param userMonthReportListVO 用户月报VO
     * @param userDayReportDOList 用户日报列表
     */
    public void buildDayAttendanceResult(UserMonthReportListVO userMonthReportListVO,
                                         List<AttendanceDayReportDO> userDayReportDOList,
                                         List<UserDayMetricsDTO> userDayMetricsDTOList) {
        // 设置每日的法定工作时长,每日考勤情况
        List<UserMonthDayAttendanceDTO> dayAttendanceDTOList = new ArrayList<>();

        Map<Long, UserDayMetricsDTO> userDayMetricsMap = userDayMetricsDTOList.stream()
                .collect(Collectors.toMap(UserDayMetricsDTO::getDayId, Function.identity()));

        userDayReportDOList.forEach(dayReport -> {
            UserMonthDayAttendanceDTO userMonthDayAttendanceDTO = new UserMonthDayAttendanceDTO();
            userMonthDayAttendanceDTO.setUserId(dayReport.getUserId());
            userMonthDayAttendanceDTO.setDayId(dayReport.getDayId());
            userMonthDayAttendanceDTO.setDayAttendanceResult(dayReport.getAttendanceResult());
            // 设置法定工作时长
            UserDayMetricsDTO userDayMetricsDTO = userDayMetricsMap.get(dayReport.getDayId());
            if (userDayMetricsDTO != null) {
                userMonthDayAttendanceDTO.setDefaultLegalWorkingHours(userDayMetricsDTO.getDefaultLegalWorkingHours());
            }
            dayAttendanceDTOList.add(userMonthDayAttendanceDTO);
        });
        userMonthReportListVO.setDayAttendanceResults(dayAttendanceDTOList);
    }
}
