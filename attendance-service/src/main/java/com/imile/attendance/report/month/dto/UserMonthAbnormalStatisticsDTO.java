package com.imile.attendance.report.month.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户月报异常统计DTO
 * 用于封装月报中的异常情况统计数据
 *
 * <AUTHOR> chen
 * @date 2025/6/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMonthAbnormalStatisticsDTO {

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 异常类型描述
     */
    private String abnormalTypeDesc;

    /**
     * 异常次数
     */
    private Integer abnormalCount;

    /**
     * 异常时间
     * 对于迟到、早退、时长异常显示具体时间（如"2.5小时"）
     * 对于其他异常类型显示"-"
     */
    private String abnormalTime;
}
