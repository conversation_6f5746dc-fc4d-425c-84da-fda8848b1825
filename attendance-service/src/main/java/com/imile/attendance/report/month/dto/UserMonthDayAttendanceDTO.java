package com.imile.attendance.report.month.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户月报每日考勤结果DTO
 * <p>
 * 该DTO用于封装月度考勤报表中每个员工每日的考勤结果信息，
 * 主要用于月报导出功能中的日考勤数据展示。
 * <p>
 * 数据来源：从 {@code AttendanceDayReportDO} 通过 MapStruct 映射转换而来，
 * 其中 {@code dayAttendanceResult} 字段对应数据库中的 {@code attendance_result} 字段。
 * <p>
 * 使用场景：月度考勤报表导出：在Excel中显示每日的考勤状态
 *
 * <AUTHOR> chen
 * @date 2025/6/25
 */
@Data
public class UserMonthDayAttendanceDTO {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 考勤日期ID
     */
    private Long dayId;

    /**
     * 当天法定工作时长
     */
    private BigDecimal defaultLegalWorkingHours;

    /**
     * 每日考勤结果
     * <p>
     * 表示员工在该日期的最终考勤状态，是考勤系统计算后的结果。
     * 数据处理说明：
     * <ul>
     *   <li>当该字段为 null 或空字符串时，在导出时会显示为 "-"</li>
     * </ul>
     */
    private String dayAttendanceResult;
}
