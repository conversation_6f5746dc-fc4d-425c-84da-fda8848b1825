package com.imile.attendance.report.day.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 员工日报详情VO
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
public class UserDayReportDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 工作状态
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.ACCOUNT_STATUS, ref = "workStatusDesc")
    private String workStatus;

    /**
     * 工作状态描述
     */
    private String workStatusDesc;

    /**
     * 是否司机
     */
    private Integer isDriver;

    /**
     * 是否派遣
     */
    private Integer isGlobalRelocation;

    /**
     * 状态 状态(ACTIVE 生效,DISABLED)
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.FMS_ACCOUNT_STATUS, ref = "statusDesc")
    private String status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * 入职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date entryDate;

    /**
     * 离职日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dimissionDate;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * 考勤日期
     */
    private String date;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型名称
     */
    private String employeeTypeDesc;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 打卡规则主键
     */
    private Long punchConfigId;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡类型
     */
    private String punchConfigType;

    /**
     * 补卡规则主键
     */
    private Long reissueConfigId;

    /**
     * 补卡规则名称
     */
    private String reissueConfigName;

    /**
     * 加班规则主键
     */
    private Long overTimeConfigId;

    /**
     * 加班规则名称
     */
    private String overTimeConfigName;

    /**
     * 日历规则主键
     */
    private Long calendarConfigId;

    /**
     * 日历规则名称
     */
    private String calendarConfigName;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型
     */
    private String classNature;

    /**
     * 班次时段信息
     */
    private List<UserDayReportClassItemDTO> userDayReportClassItemList;

    /**
     * 打卡记录
     */
    private List<UserDayReportPunchRecordDTO> punchCardRecordDTO;

    /**
     * 当日请假小时数
     */
    private BigDecimal leaveHours;
    /**
     * 当日外勤小时数
     */
    private BigDecimal outOfOfficeHours;
    /**
     * 当日加班小时数
     */
    private BigDecimal overTimeHours;

    /**
     * 当日单据信息
     */
    private List<UserDayReportFormDTO> formDTO;

    /**
     * 异常记录
     */
    private List<UserDayReportAbnormalDTO> abnormalDTO;

    /**
     * 初始考勤结果
     */
    private String initResult;

    /**
     * 最终考勤结果
     */
    private String finalResult;

    /**
     * 实际出勤小时数
     */
    private BigDecimal actualAttendanceHours;

    /**
     * 最终工作小时数
     */
    private BigDecimal finalWorkHours;
}
