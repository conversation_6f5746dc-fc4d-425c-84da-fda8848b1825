package com.imile.attendance.report.month.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 用户每日考勤指标DTO
 * 用于封装月报中的每日的默认法定工作时长
 *
 * <AUTHOR> chen
 * @date 2025/07/02
 */
@Data
public class UserDayMetricsDTO {

    /**
     * 日期
     */
    private Long dayId;
    /**
     * 法定工作时长
     */
    private BigDecimal defaultLegalWorkingHours;

    /**
     * 构建用户每日考勤指标DTO
     *
     * @param dayId 日期
     * @param defaultLegalWorkingHours 法定工作时长
     * @return 用户每日考勤指标DTO
     */
    public static UserDayMetricsDTO build(Long dayId, BigDecimal defaultLegalWorkingHours) {
        UserDayMetricsDTO userDayMetricsDTO = new UserDayMetricsDTO();
        userDayMetricsDTO.setDayId(dayId);
        userDayMetricsDTO.setDefaultLegalWorkingHours(defaultLegalWorkingHours);
        return userDayMetricsDTO;
    }
}
