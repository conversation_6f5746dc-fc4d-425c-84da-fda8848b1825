package com.imile.attendance.report.day.job.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/18
 * @Description 用户考勤日报历史数据迁移定时任务参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DayReportJobHistoryParam {
    /**
     * 所属国
     */
    private String countryList;

    /**
     * 用户编码
     */
    private String userCodes;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 定义当地时间年月日(YYYY-MM-dd)
     */
    private String startDay;

    /**
     * 定义当地时间年月日(YYYY-MM-dd)
     */
    private String endDay;
}