package com.imile.attendance.report.mapstruct;

import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.report.dto.UserMonthReportBaseDTO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.report.month.dto.UserMonthDayAttendanceDTO;
import com.imile.attendance.report.month.vo.UserMonthReportListVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface UserMonthReportMapstruct {

    UserMonthReportMapstruct INSTANCE = Mappers.getMapper(UserMonthReportMapstruct.class);


    @Mapping(target = "attendanceStartCycle", ignore = true)
    @Mapping(target = "attendanceEndCycle", ignore = true)
    @Mapping(target = "workHours", ignore = true)
    @Mapping(target = "shouldAttendanceDays", ignore = true)
    @Mapping(target = "postName", ignore = true)
    @Mapping(target = "employeeTypeDesc", ignore = true)
    @Mapping(target = "deptName", ignore = true)
    @Mapping(target = "attendanceMonth", ignore = true)
    @Mapping(target = "attendanceCycle", ignore = true)
    @Mapping(target = "actualAttendanceHours", ignore = true)
    @Mapping(target = "actualAttendanceDays", ignore = true)
    @Mapping(target = "actualAttendanceAverageHours", ignore = true)
    @Mapping(target = "abnormalStatisticsList", ignore = true)
    UserMonthReportListVO toMonthReportListVO(UserMonthReportBaseDTO userMonthReportBaseDTO);

    List<UserMonthReportListVO> toMonthReportListVO(List<UserMonthReportBaseDTO> userMonthReportBaseDTOList);



    @Mapping(target = "defaultLegalWorkingHours", ignore = true)
    @Mapping(target = "dayAttendanceResult", source = "attendanceResult")
    UserMonthDayAttendanceDTO toDayAttendance(AttendanceDayReportDO attendanceDayReportDO);

    List<UserMonthDayAttendanceDTO> toDayAttendance(List<AttendanceDayReportDO> attendanceDayReportDOList);
}
