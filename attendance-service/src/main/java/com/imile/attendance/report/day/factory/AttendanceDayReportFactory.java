package com.imile.attendance.report.day.factory;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportAbnormalDao;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportDao;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportFormDao;
import com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportAbnormalMapper;
import com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportFormMapper;
import com.imile.attendance.infrastructure.repository.report.mapper.AttendanceDayReportMapper;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.attendance.report.day.command.AttendanceDayReportAddCommand;
import com.imile.attendance.report.day.command.AttendanceDayReportDeleteCommand;
import com.imile.attendance.report.mapstruct.AttendanceReportMapstruct;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.common.enums.IsDeleteEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/4/15
 * @Description
 */
@Slf4j
@Service
public class AttendanceDayReportFactory {

    @Resource
    private AttendanceDayReportDao attendanceDayReportDao;
    @Resource
    private AttendanceDayReportAbnormalDao attendanceDayReportAbnormalDao;
    @Resource
    private AttendanceDayReportFormDao attendanceDayReportFormDao;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendanceDayReportMapper attendanceDayReportMapper;
    @Resource
    private AttendanceDayReportAbnormalMapper attendanceDayReportAbnormalMapper;
    @Resource
    private AttendanceDayReportFormMapper attendanceDayReportFormMapper;


    @Transactional
    public void add(AttendanceDayReportAddCommand addCommand) {
        // save main-table
        AttendanceDayReportDO dayReportDO = AttendanceReportMapstruct.INSTANCE.toDayReportDO(addCommand);
        dayReportDO.setId(defaultIdWorker.nextId());
        attendanceDayReportDao.save(dayReportDO);
        if (CollectionUtils.isNotEmpty(addCommand.getAbnormalAddCommandList())) {
            // save abnormal sub-table
            List<AttendanceDayReportAbnormalDO> dayReportItemDO = AttendanceReportMapstruct.INSTANCE.toAddDayReportAbnormalDO(addCommand.getAbnormalAddCommandList(), dayReportDO.getId());
            attendanceDayReportAbnormalDao.saveBatch(dayReportItemDO);
        }
        if (CollectionUtils.isNotEmpty(addCommand.getFormAddCommandList())) {
            // save form sub-table
            List<AttendanceDayReportFormDO> dayReportItemDO = AttendanceReportMapstruct.INSTANCE.toAddDayReportFormDO(addCommand.getFormAddCommandList(), dayReportDO.getId());
            attendanceDayReportFormDao.saveBatch(dayReportItemDO);
        }
    }

    @Transactional
    public void addBatch(List<AttendanceDayReportDO> addDOList,
                         List<AttendanceDayReportAbnormalDO> addAbnormalDOList,
                         List<AttendanceDayReportFormDO> addFormDOList) {
        if (CollectionUtils.isNotEmpty(addDOList)) {
            attendanceDayReportMapper.insertBatchSomeColumn(addDOList);
        }
        if (CollectionUtils.isNotEmpty(addAbnormalDOList)) {
            attendanceDayReportAbnormalMapper.insertBatchSomeColumn(addAbnormalDOList);
        }
        if (CollectionUtils.isNotEmpty(addFormDOList)) {
            attendanceDayReportFormMapper.insertBatchSomeColumn(addFormDOList);
        }
    }


//    @Transactional
//    public void update(AttendanceDayReportUpdateCommand updateCommand) {
//
//    }

    @Transactional
    public void delete(AttendanceDayReportDeleteCommand deleteCommand) {
        // report delete check
        Long reportId = deleteCommand.getId();
        AttendanceDayReportDO dayReportDO = attendanceDayReportDao.getById(reportId);
        if (Objects.isNull(dayReportDO)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // delete main-table
        dayReportDO.setIsDelete(IsDeleteEnum.YES.getCode());
        BaseDOUtil.fillDOUpdateByUserOrSystem(dayReportDO);
        attendanceDayReportDao.updateById(dayReportDO);
        // delete abnormal sub-table
        List<AttendanceDayReportAbnormalDO> abnormalDOList = attendanceDayReportAbnormalDao.selectByReportId(reportId);
        if (CollectionUtils.isNotEmpty(abnormalDOList)) {
            for (AttendanceDayReportAbnormalDO dayReportItemDO : abnormalDOList) {
                dayReportItemDO.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(dayReportItemDO);
            }
        }
        attendanceDayReportAbnormalDao.updateBatchById(abnormalDOList);
        // delete form sub-table
        List<AttendanceDayReportFormDO> formDOList = attendanceDayReportFormDao.selectByReportId(reportId);
        if (CollectionUtils.isNotEmpty(formDOList)) {
            for (AttendanceDayReportFormDO dayReportItemDO : formDOList) {
                dayReportItemDO.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdateByUserOrSystem(dayReportItemDO);
            }
        }
        attendanceDayReportFormDao.updateBatchById(formDOList);

    }

    @Transactional
    public void deleteBatch(List<AttendanceDayReportDO> deleteDOList,
                            List<AttendanceDayReportAbnormalDO> deleteAbnormalDOList,
                            List<AttendanceDayReportFormDO> deleteFormDOList) {
        if (CollectionUtils.isNotEmpty(deleteDOList)) {
            attendanceDayReportDao.updateBatchById(deleteDOList);
        }
        if (CollectionUtils.isNotEmpty(deleteAbnormalDOList)) {
            attendanceDayReportAbnormalDao.updateBatchById(deleteAbnormalDOList);
        }
        if (CollectionUtils.isNotEmpty(deleteFormDOList)) {
            attendanceDayReportFormDao.updateBatchById(deleteFormDOList);
        }
    }

}
