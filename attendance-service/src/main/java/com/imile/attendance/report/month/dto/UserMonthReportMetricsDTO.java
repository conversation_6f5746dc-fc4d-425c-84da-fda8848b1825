package com.imile.attendance.report.month.dto;

import com.imile.attendance.constants.BusinessConstant;
import io.swagger.models.auth.In;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 月报指标DTO
 * 封装月报中的各项考勤计算结果
 *
 * <AUTHOR> chen
 * @date 2025/6/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserMonthReportMetricsDTO {

    /**
     * 应出勤天数
     */
    private BigDecimal shouldAttendanceDays;

    /**
     * 实际出勤天数
     */
    private BigDecimal actualAttendanceDays;

    /**
     * 实际出勤时长（小时）
     */
    private BigDecimal actualAttendanceHours;

    /**
     * 工作时长（小时）
     */
    private BigDecimal workHours;

    /**
     * 实际出勤平均工时（小时）
     */
    private BigDecimal actualAttendanceAverageHours;

    /**
     * 请假总时长
     */
    private BigDecimal leaveHours;

    /**
     * 外勤总时长
     */
    private BigDecimal oooHours;

    /**
     * 请假次数
     */
    private Integer leaveCount;

    /**
     * 外勤次数
     */
    private Integer oooCount;

    /**
     * 补卡次数
     */
    private Integer reissueCardFormCount;

    /**
     * 延时时长
     */
    private BigDecimal delayHours;

    /**
     * 当前考勤周期内已使用的补卡次数
     */
    private Integer reissueCardCount;

    /**
     * 排休天数（H和OFF）
     */
    private BigDecimal offDay;

    /**
     * 实际缺勤天数
     */
    private BigDecimal absentDays;

    /**
     * 每天的法定工作时长
     */
    private List<UserDayMetricsDTO> userDayMetricsDTOList;


    /**
     * 创建空的考勤指标对象
     *
     * @return 所有指标为0的考勤指标DTO
     */
    public static UserMonthReportMetricsDTO empty() {
        return UserMonthReportMetricsDTO.builder()
                .shouldAttendanceDays(BigDecimal.ZERO)
                .actualAttendanceDays(BigDecimal.ZERO)
                .actualAttendanceHours(BigDecimal.ZERO)
                .workHours(BigDecimal.ZERO)
                .actualAttendanceAverageHours(BigDecimal.ZERO)
                .leaveHours(BigDecimal.ZERO)
                .oooHours(BigDecimal.ZERO)
                .leaveCount(BusinessConstant.ZERO)
                .oooCount(BusinessConstant.ZERO)
                .reissueCardFormCount(BusinessConstant.ZERO)
                .delayHours(BigDecimal.ZERO)
                .reissueCardCount(BusinessConstant.ZERO)
                .offDay(BigDecimal.ZERO)
                .absentDays(BigDecimal.ZERO)
                .userDayMetricsDTOList(Collections.emptyList())
                .build();
    }

    /**
     * 检查是否为有效的考勤指标
     *
     * @return true表示有效，false表示无效
     */
    public boolean isValid() {
        return shouldAttendanceDays != null && shouldAttendanceDays.compareTo(BigDecimal.ZERO) >= 0
                && actualAttendanceDays != null && actualAttendanceDays.compareTo(BigDecimal.ZERO) >= 0
                && actualAttendanceHours != null && actualAttendanceHours.compareTo(BigDecimal.ZERO) >= 0
                && workHours != null && workHours.compareTo(BigDecimal.ZERO) >= 0
                && actualAttendanceAverageHours != null && actualAttendanceAverageHours.compareTo(BigDecimal.ZERO) >= 0;
    }
}
