package com.imile.attendance.report.day.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工日报列表VO
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
public class UserDayReportListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 日报主键
     */
    private Long id;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * dayId
     */
    private Long dayId;

    /**
     * 考勤日期
     */
    private String date;

    /**
     * 打卡类型
     */
    private String punchType;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 初始考勤结果
     */
    private String initResult;

    /**
     * 最终考勤结果
     */
    private String finalResult;
}
