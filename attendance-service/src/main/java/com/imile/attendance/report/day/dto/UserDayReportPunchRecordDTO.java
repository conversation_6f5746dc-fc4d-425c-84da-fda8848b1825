package com.imile.attendance.report.day.dto;

import lombok.Data;

import java.util.Date;

/**
 * 员工日报详情打卡记录VO
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
public class UserDayReportPunchRecordDTO {

    private Long id;

    /**
     * 日期
     */
    private String dayId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 打卡时间
     */
    private Date punchTime;

    /**
     * 班次id
     */
    private Long classId;

    /**
     * 班次详情id
     */
    private Long classItemId;
}
