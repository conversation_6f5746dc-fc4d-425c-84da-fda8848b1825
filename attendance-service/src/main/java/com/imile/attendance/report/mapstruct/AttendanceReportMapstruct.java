package com.imile.attendance.report.mapstruct;


import com.imile.attendance.clock.dto.MobilePunchCardRecordDTO;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.form.dto.AttendanceFormDTO;
import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.abnormal.dto.UserAttendanceDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserBaseInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.attendance.infrastructure.repository.report.query.UserDayReportQuery;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.report.day.command.AttendanceDayReportAbnormalAddCommand;
import com.imile.attendance.report.day.command.AttendanceDayReportAddCommand;
import com.imile.attendance.report.day.command.AttendanceDayReportFormAddCommand;
import com.imile.attendance.report.day.dto.UserDayReportAbnormalDTO;
import com.imile.attendance.report.day.dto.UserDayReportAbnormalOperationDTO;
import com.imile.attendance.report.day.dto.UserDayReportClassItemDTO;
import com.imile.attendance.report.day.dto.UserDayReportDTO;
import com.imile.attendance.report.day.dto.UserDayReportExportDTO;
import com.imile.attendance.report.day.dto.UserDayReportFormDTO;
import com.imile.attendance.report.day.dto.UserDayReportListDTO;
import com.imile.attendance.report.day.dto.UserDayReportPunchRecordDTO;
import com.imile.attendance.report.day.query.DayReportListQuery;
import com.imile.attendance.report.day.vo.UserDayReportExportVO;
import com.imile.attendance.report.day.vo.UserDayReportListVO;
import com.imile.attendance.util.DateHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/9
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface AttendanceReportMapstruct {

    AttendanceReportMapstruct INSTANCE = Mappers.getMapper(AttendanceReportMapstruct.class);

    // 考勤月报
    UserAttendanceDTO toUserAttendanceDTO(UserBaseInfoDTO user);

    // 考勤日报
    UserDayReportQuery toDayReportQuery(DayReportListQuery listQuery);

    AttendanceDayReportDO toDayReportDO(AttendanceDayReportAddCommand addCommand);

    AttendanceDayReportAbnormalDO toAddDayReportAbnormalDO(AttendanceDayReportAbnormalAddCommand itemAddCommand);

    AttendanceDayReportFormDO toAddDayReportFormDO(AttendanceDayReportFormAddCommand itemAddCommand);

    default List<AttendanceDayReportAbnormalDO> toAddDayReportAbnormalDO(List<AttendanceDayReportAbnormalAddCommand> itemAddCommandList,
                                                                         Long reportId) {
        List<AttendanceDayReportAbnormalDO> itemDOList = new ArrayList<>();
        for (AttendanceDayReportAbnormalAddCommand itemAddCommand : itemAddCommandList) {
            AttendanceDayReportAbnormalDO dayReportItemDO = toAddDayReportAbnormalDO(itemAddCommand);
            dayReportItemDO.setDayReportId(reportId);
            itemDOList.add(dayReportItemDO);
        }
        return itemDOList;
    }

    default List<AttendanceDayReportFormDO> toAddDayReportFormDO(List<AttendanceDayReportFormAddCommand> itemAddCommandList,
                                                                 Long reportId) {
        List<AttendanceDayReportFormDO> itemDOList = new ArrayList<>();
        for (AttendanceDayReportFormAddCommand itemAddCommand : itemAddCommandList) {
            AttendanceDayReportFormDO dayReportItemDO = toAddDayReportFormDO(itemAddCommand);
            dayReportItemDO.setDayReportId(reportId);
            itemDOList.add(dayReportItemDO);
        }
        return itemDOList;
    }

    UserDayReportListDTO toDayReportListDTO(AttendanceDayReportDO dayReportDOList);

    default List<UserDayReportListDTO> toDayReportListDTO(List<AttendanceDayReportDO> dayReportDOList) {
        List<UserDayReportListDTO> result = new ArrayList<>();
        for (AttendanceDayReportDO dayReportDO : dayReportDOList) {
            UserDayReportListDTO dayReportDTO = toDayReportListDTO(dayReportDO);
            dayReportDTO.setDate(DateHelper.dayIdFormat(dayReportDO.getDayId()));
            dayReportDTO.setPunchType(dayReportDO.getPunchConfigType());
            result.add(dayReportDTO);
        }
        return result;
    }

    UserDayReportListVO toDayReportListVO(UserDayReportListDTO dayReportDTO);

    default List<UserDayReportListVO> toDayReportListVO(List<UserDayReportListDTO> dayReportDTOList) {
        List<UserDayReportListVO> result = new ArrayList<>();
        for (UserDayReportListDTO dayReportDTO : dayReportDTOList) {
            UserDayReportListVO dayReportVO = toDayReportListVO(dayReportDTO);
            if (StringUtils.isNotBlank(dayReportVO.getDayShiftRule())) {
                DayShiftRuleEnum dayShiftEnum = DayShiftRuleEnum.getByCode(dayReportVO.getDayShiftRule());
                dayReportVO.setDayShiftRule(RequestInfoHolder.isChinese()
                        ? dayShiftEnum.getDesc() : dayShiftEnum.getDescEn());
            }
            result.add(dayReportVO);
        }
        return result;
    }

    UserDayReportDTO toDayReportDTO(AttendanceDayReportDO dayReportDO);

    List<UserDayReportClassItemDTO> toDayReportClassItem(List<PunchClassItemConfigDO> classItemConfigList);

    List<UserDayReportPunchRecordDTO> toUserDayReportPunchRecordDTO(List<MobilePunchCardRecordDTO> punchCardRecordDTO);

    List<UserDayReportPunchRecordDTO> toUserDayReportPunchRecordDTOByDO(List<EmployeePunchRecordDO> punchRecordDO);

    List<UserDayReportAbnormalDTO> toUserDayReportAbnormalDTO(List<AttendanceDayReportAbnormalDO> abnormalList);

    @Mapping(target = "id", source = "abnormalId")
    @Mapping(target = "status", source = "abnormalStatus")
    @Mapping(target = "punchClassConfigId", source = "punchClassId")
    EmployeeAbnormalAttendanceDO toAbnormalDOByAbnormalReportDO(AttendanceDayReportAbnormalDO abnormalDO);

    List<EmployeeAbnormalAttendanceDO> toAbnormalDOByAbnormalReportDO(List<AttendanceDayReportAbnormalDO> abnormalDOList);

    @Mapping(target = "id", source = "abnormalId")
    @Mapping(target = "status", source = "abnormalStatus")
    @Mapping(target = "punchClassConfigId", source = "punchClassId")
    EmployeeAbnormalAttendanceDO toAbnormalDOByAbnormalDTO(UserDayReportAbnormalDTO abnormalDTO);

    List<EmployeeAbnormalAttendanceDO> toAbnormalDOByAbnormalDTO(List<UserDayReportAbnormalDTO> abnormalDTOList);

    List<UserDayReportAbnormalOperationDTO> toUserDayReportAbnormalOperationDTO(List<EmployeeAbnormalOperationRecordDO> operationRecordList);

    List<UserDayReportFormDTO> toUserDayReportFormDTO(List<AttendanceDayReportFormDO> dayReportFormList);

    default List<UserDayReportFormDTO> toUserDayReportFormDTOByForm(List<AttendanceFormDTO> effectFormList,
                                                                    Long dayReportId) {
        List<UserDayReportFormDTO> userDayReportFormDTOList = new ArrayList<>();
        for (AttendanceFormDTO formDTO : effectFormList) {
            UserDayReportFormDTO dto = UserDayReportFormDTO.builder()
                    .dayReportId(dayReportId)
                    .formId(formDTO.getId())
                    .formStatus(formDTO.getFormStatus())
                    .formType(formDTO.getFormType())
                    .applicationCode(formDTO.getApplicationCode())
                    .approvalId(formDTO.getApprovalId()).build();
            if (StringUtils.isNotBlank(formDTO.getLeaveStartDate())) {
                dto.setStartDate(DateHelper.parseYYYYMMDDHHMMSS(formDTO.getLeaveStartDate()));
            }
            if (StringUtils.isNotBlank(formDTO.getLeaveEndDate())) {
                dto.setEndDate(DateHelper.parseYYYYMMDDHHMMSS(formDTO.getLeaveEndDate()));
            }
            if (StringUtils.isNotBlank(formDTO.getOutOfOfficeStartDate())) {
                dto.setStartDate(DateHelper.parseYYYYMMDDHHMMSS(formDTO.getOutOfOfficeStartDate()));
            }
            if (StringUtils.isNotBlank(formDTO.getOutOfOfficeEndDate())) {
                dto.setEndDate(DateHelper.parseYYYYMMDDHHMMSS(formDTO.getOutOfOfficeEndDate()));
            }
            if (Objects.nonNull(formDTO.getReissueCardDayId())) {
                dto.setReissueCardDayId(formDTO.getReissueCardDayId());
            }
            userDayReportFormDTOList.add(dto);
        }
        return userDayReportFormDTOList;
    }

    default List<UserDayReportFormDTO> toUserDayReportFormDTOByOverTimeForm(List<OverTimeApprovalListDTO> overTimeFormList,
                                                                            Long dayReportId) {
        List<UserDayReportFormDTO> userDayReportFormDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(overTimeFormList)) {
            return userDayReportFormDTOList;
        }
        for (OverTimeApprovalListDTO formDTO : overTimeFormList) {
            UserDayReportFormDTO dto = UserDayReportFormDTO.builder()
                    .dayReportId(dayReportId)
                    .formId(formDTO.getId())
                    .formStatus(formDTO.getFormStatus())
                    .formType(formDTO.getFormType())
                    .applicationCode(formDTO.getApplicationCode())
                    .approvalId(formDTO.getApprovalId()).build();
            if (Objects.nonNull(formDTO.getStartDate())) {
                dto.setStartDate(formDTO.getStartDate());
            }
            if (Objects.nonNull(formDTO.getEndDate())) {
                dto.setEndDate(formDTO.getEndDate());
            }
            userDayReportFormDTOList.add(dto);
        }
        return userDayReportFormDTOList;
    }

    @Mapping(target = "userId", source = "id")
    UserDayReportExportDTO toUserDayReportExportDTO(UserDTO userDTO);

    UserDayReportExportVO toUserDayReportExportVO(UserDayReportExportDTO dayReportExportDTO);
}
