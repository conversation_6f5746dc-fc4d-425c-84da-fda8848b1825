package com.imile.attendance.report.day.dto;

import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 员工日报导出DTO
 *
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class UserDayReportExportDTO {

    /**
     * 员工主键
     */
    private Long userId;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 部门主键
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位主键
     */
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 考勤日期
     */
    private Long dayId;

    /**
     * 考勤日期
     */
    private String date;

    /**
     * 日历规则主键
     */
    private Long calendarId;

    /**
     * 日历规则名称
     */
    private String calendarConfigName;

    /**
     * 打卡规则id
     */
    private Long punchConfigId;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡类型
     */
    private String punchConfigType;

    /**
     * 上下班时间间隔
     */
    private BigDecimal punchTimeInterval;

    /**
     * 补卡规则id
     */
    private Long reissueConfigId;

    /**
     * 补卡规则名称
     */
    private String reissueConfigName;

    /**
     * 加班规则id
     */
    private Long overTimeConfigId;

    /**
     * 加班规则名称
     */
    private String overTimeConfigName;

    /**
     * 排班Id
     */
    private Long shiftId;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 班次类型
     */
    private String classNature;

    /**
     * 班次Id
     */
    private Long classId;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次详情
     */
    private PunchClassConfigDTO punchClassConfigDTO;

    /**
     * 初始考勤结果
     */
    private Integer initResult;

    /**
     * 实际出勤时长-不含休息(min)
     */
    private BigDecimal actualAttendanceMinutes;

    /**
     * 当日请假时长
     */
    private BigDecimal leaveMinutes;

    /**
     * 当日外勤时长
     */
    private BigDecimal oooMinutes;

    /**
     * 当日延时时长
     */
    private BigDecimal delayMinutes;

    /**
     * 当日迟到时长
     */
    private BigDecimal lateMinutes;

    /**
     * 当日早退时长
     */
    private BigDecimal leaveEarlyMinutes;

    /**
     * 当日异常及操作记录
     */
    private List<UserDayReportAbnormalDTO> userDayReportAbnormalList;

    /**
     * 当日单据信息
     */
    private List<UserDayReportFormDTO> userDayReportFormList;

    /**
     * 当日打卡记录(存在前后两天)
     */
    private List<UserDayReportPunchRecordDTO> userDayReportPunchRecordList;

    /**
     * 最终考勤结果
     */
    private Integer finalResult;

    /**
     * 最终工作时长-不含休息(h)
     */
    private BigDecimal finalWorkMinutes;
}
