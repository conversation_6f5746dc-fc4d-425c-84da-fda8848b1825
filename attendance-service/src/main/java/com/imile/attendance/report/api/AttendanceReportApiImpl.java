package com.imile.attendance.report.api;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import com.imile.attendance.report.AttendanceReportApi;
import com.imile.attendance.report.month.AttendanceMonthReportExportService;
import com.imile.attendance.report.month.AttendanceMonthReportService;
import com.imile.attendance.report.month.vo.UserMonthReportListVO;
import com.imile.attendance.report.param.AttendanceMonthReportParam;
import com.imile.rpc.common.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/6/30
 * @Description 考勤日月报外部依赖API实现
 */
@Slf4j
@Service(version = "1.0.0")
public class AttendanceReportApiImpl implements AttendanceReportApi {

    @Resource
    private AttendanceMonthReportService monthReportService;
    @Resource
    private AttendanceMonthReportExportService monthReportExportService;

    @Override
    public RpcResult<List<Map<String, String>>> selectAttendanceMonthReport(AttendanceMonthReportParam param) {
        // 1.checkParam
        if (!checkParam(param)) {
            return RpcResult.fail(ErrorCodeEnum.PARAM_NOT_NULL.getCode(), ErrorCodeEnum.PARAM_NOT_NULL.getDesc());
        }
        // 2.selectMonthReportList
        return RpcResult.ok(this.selectMonthReportList(param));
    }

    /**
     * 校验参数
     *
     * @param param
     * @return
     */
    private Boolean checkParam(AttendanceMonthReportParam param) {
        if (Objects.isNull(param)
                || Objects.isNull(param.getStartTime())
                || Objects.isNull(param.getEndTime())
                || CollectionUtils.isEmpty(param.getUserCodeList())) {
            return false;
        }
        return true;
    }

    /**
     * 批量查询月报
     *
     * @param param
     * @return
     */
    private List<Map<String, String>> selectMonthReportList(AttendanceMonthReportParam param) {
        Long startDayId = Long.valueOf(DateUtil.format(param.getStartTime(), "yyyyMMdd"));
        Long endDayId = Long.valueOf(DateUtil.format(param.getEndTime(), "yyyyMMdd"));
        MonthReportListQuery query = MonthReportListQuery.builder()
                .areExport(true)
                .userCodes(param.getUserCodeList())
                .build();
        List<UserMonthReportListVO> results = monthReportService.selectList(startDayId, endDayId, query);
        // 封装导出数据
        return monthReportExportService.buildExportResult(startDayId, endDayId, results, true);
    }
}
