package com.imile.attendance.report.month;

import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.cycleConfig.CycleConfigManage;
import com.imile.attendance.cycleConfig.enums.AttendanceCycleTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.excel.header.ExcelHeaderUtil;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.model.AttendanceCycleConfigDO;
import com.imile.attendance.infrastructure.repository.cycleConfig.query.AttendanceCycleConfigQuery;
import com.imile.attendance.infrastructure.repository.report.query.MonthReportListQuery;
import com.imile.attendance.report.excel.MonthReportExportHeaderEnum;
import com.imile.attendance.report.month.dto.AttendanceCycleDTO;
import com.imile.attendance.report.month.dto.UserMonthAbnormalStatisticsDTO;
import com.imile.attendance.report.month.dto.UserMonthDayAttendanceDTO;
import com.imile.attendance.report.month.vo.UserMonthReportListVO;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.StatusUtil;
import com.imile.common.enums.StatusEnum;
import com.imile.common.page.Pagination;
import com.imile.common.page.PaginationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 月度考勤报表导出服务
 * 该服务专门负责月度考勤报表的Excel导出功能，提供完整的导出解决方案。
 * 主要功能包括：动态表头生成,根据考勤周期动态生成包含每日列的Excel表头
 *
 * <AUTHOR> chen
 * @date 2025/6/24
 */
@Slf4j
@Service
public class AttendanceMonthReportExportService {

    @Resource
    private AttendanceMonthReportService monthReportService;
    @Resource
    private CycleConfigManage cycleConfigManage;
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;


    /**
     * 生成月报导出Excel的动态表头
     * <p>
     * 该方法根据查询条件动态生成Excel表头，包括固定的员工信息列和根据考勤周期生成的日期列。
     * <ol>
     *   <li><strong>固定表头</strong>：从 {@link MonthReportExportHeaderEnum} 获取员工基本信息、出勤统计、异常统计等固定列</li>
     *   <li><strong>动态日期列</strong>：根据考勤周期配置，为周期内的每一天生成对应的日期列</li>
     *   <li><strong>国际化处理</strong>：根据用户语言环境选择中文或英文表头</li>
     * </ol>
     * <p>
     * <strong>表头结构：</strong>
     * <pre>
     * [员工信息列] + [出勤统计列] + [异常统计列] + [day20250601] + [day20250602] + ... + [day20250630]
     * </pre>
     * <p>
     * <strong>日期列命名规则：</strong>
     * 日期列的标题格式为 "day" + dayId，例如：day20250625 表示2025年6月25日
     *
     * @param query 月报查询条件，必须包含以下字段：
     *              <ul>
     *                <li>locationCountry - 常驻国家（必填）</li>
     *                <li>attendanceYear - 考勤年份（必填）</li>
     *                <li>attendanceMonth - 考勤月份（必填）</li>
     *              </ul>
     * @return Excel表头列表，包含所有需要导出的列信息
     * @throws BusinessLogicException 当以下情况发生时抛出：
     *                                <ul>
     *                                  <li>locationCountry 为空</li>
     *                                  <li>指定国家没有配置月度考勤周期</li>
     *                                  <li>指定年月没有有效的考勤周期</li>
     *                                </ul>
     */
    public List<ExcelTitleExportDTO> titleExport(MonthReportListQuery query) {
        validateExportQuery(query);
        // 获取固定表头
        List<ExcelTitleExportDTO> result =
                ExcelHeaderUtil.convertToExportDTOs(MonthReportExportHeaderEnum.class, RequestInfoHolder.isChinese());

        // 添加日期表头
        AttendanceCycleConfigQuery cycleConfigQuery = AttendanceCycleConfigQuery.builder()
                .country(query.getLocationCountry())
                .cycleType(AttendanceCycleTypeEnum.MONTH.getType())
                .build();
        List<AttendanceCycleConfigDO> cycleConfigDOList = cycleConfigManage.selectByCondition(cycleConfigQuery);
        if (CollectionUtils.isEmpty(cycleConfigDOList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, "locationCountry:" + query.getLocationCountry() +
                    ",cycleType:" + AttendanceCycleTypeEnum.MONTH.getType() + "not have a attendance cycle");
        }
        AttendanceCycleConfigDO attendanceCycleConfigDO = cycleConfigDOList.get(0);

        AttendanceCycleDTO attendanceCycleDTO = monthReportService.getAttendanceCycleByYearAndMonth(
                attendanceCycleConfigDO.getCycleStart(),
                attendanceCycleConfigDO.getCycleEnd(),
                query.getAttendanceYear(),
                query.getAttendanceMonth());

        Long startDayId = attendanceCycleDTO.getAttendanceStartCycle();
        Long endDayId = attendanceCycleDTO.getAttendanceEndCycle();

        if (startDayId == null || endDayId == null) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, "locationCountry:" + query.getLocationCountry() +
                    ",attendanceYear:" + query.getAttendanceYear() + ",attendanceMonth:" + query.getAttendanceMonth() +
                    "not have a valid attendance cycle");
        }

        while (startDayId <= endDayId) {
            String dayFormat = this.formatExportDayTitle(startDayId);
            result.add(ExcelTitleExportDTO.of(dayFormat, dayFormat));
            startDayId = DateHelper.getNextDayId(startDayId);
        }
        return result;
    }

    /**
     * 月报数据导出处理
     *
     * @param query 月报查询条件，会自动设置为导出模式
     * @return 分页结果，包含转换后的Map格式数据，每个Map代表一行Excel数据
     */
    public PaginationResult<Map<String, String>> monthReportExport(MonthReportListQuery query) {
        // 设置为导出
        query.setAreExport(true);
        PaginationResult<UserMonthReportListVO> paginationResult = monthReportService.list(query);
        List<UserMonthReportListVO> results = paginationResult.getResults();
        if (CollectionUtils.isEmpty(results)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        Pagination pagination = paginationResult.getPagination();

        PaginationResult<Map<String, String>> resPaginationResult = new PaginationResult<>();
        resPaginationResult.setPagination(pagination);

        // 获取周期，同一个国家下的考勤周期都一致
        UserMonthReportListVO monthReportListVOItem = results.get(0);
        Long startDayId = monthReportListVOItem.getAttendanceStartCycle();
        Long endDayId = monthReportListVOItem.getAttendanceEndCycle();
        // 封装月报导出数据
        List<Map<String, String>> resList = this.buildExportResult(startDayId, endDayId, results, false);
        resPaginationResult.setResults(resList);
        return resPaginationResult;
    }

    /**
     * 封装导出数据
     *
     * @param startDayId
     * @param endDayId
     * @param results
     * @return
     */
    public List<Map<String, String>> buildExportResult(Long startDayId,
                                                       Long endDayId,
                                                       List<UserMonthReportListVO> results,
                                                       Boolean isFromSalaryApi) {
        // 获取周期的每一天并排序
        List<Long> dayIdList = DateHelper.getDayIdList(startDayId, endDayId)
                .stream()
                .sorted()
                .collect(Collectors.toList());

        List<Map<String, String>> resList = new ArrayList<>();
        Map<Long, List<AttendanceEmployeeDetailDO>> employeeLeaveDetailMap = new HashMap<>();
        if (isFromSalaryApi) {
            List<Long> userIdList = results.stream()
                    .map(UserMonthReportListVO::getUserId)
                    .distinct()
                    .collect(Collectors.toList());
            employeeLeaveDetailMap = attendanceEmployeeDetailManage.selectByUserIdListAndDayIdList(userIdList, dayIdList)
                            .stream()
                            .filter(item -> item.getLeaveMinutes() != null &&
                                    StringUtils.isNotBlank(item.getLeaveType()))
                            .collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getUserId));

        }
        for (UserMonthReportListVO userMonthReportListVO : results) {
            Map<String, String> map = new LinkedHashMap<>();

            // 填充员工基本信息
            fillEmployeeBasicInfo(map, userMonthReportListVO);

            // 填充出勤统计信息
            fillAttendanceStatistics(map, userMonthReportListVO);

            // 填充异常统计信息
            fillAbnormalStatistics(map, userMonthReportListVO);

            // 填充周期内每一天的考勤结果
            fillCycleDayAttendanceResult(map, userMonthReportListVO, dayIdList);

            if (isFromSalaryApi) {
                // 填充薪资相关字段
                fillSalaryRelatedFields(map, userMonthReportListVO, employeeLeaveDetailMap);
            }

            resList.add(map);
        }
        return resList;
    }

    private void fillSalaryRelatedFields(Map<String, String> map,
                                         UserMonthReportListVO userMonthReportListVO,
                                         Map<Long, List<AttendanceEmployeeDetailDO>> employeeLeaveDetailMap) {
        //实际缺勤天数absentDays
        map.put("absentDays", formatBigDecimalValue(userMonthReportListVO.getAbsentDays()));
        //排休天数offDay
        map.put("offDay", formatBigDecimalValue(userMonthReportListVO.getOffDay()));
        //外勤小时数outOfOfficeHour
        map.put("outOfOfficeHour", formatBigDecimalValue(userMonthReportListVO.getOooHours()));

        // 获取用户的考勤周期内的每一天的考勤结果
        List<UserMonthDayAttendanceDTO> dayAttendanceResults = userMonthReportListVO.getDayAttendanceResults();
        Map<Long, UserMonthDayAttendanceDTO> dayAttendanceResultMap = dayAttendanceResults.stream()
                .collect(Collectors.toMap(UserMonthDayAttendanceDTO::getDayId, Function.identity()));

        //存储该用户该请假类型的请假天数：key-请假类型，value-请假天数
        Map<String, String> leaveMap = new HashMap<>();

        List<AttendanceEmployeeDetailDO> userDayRangeLeaveList =
                employeeLeaveDetailMap.getOrDefault(userMonthReportListVO.getUserId(), Collections.emptyList());
        Map<String, List<AttendanceEmployeeDetailDO>> leaveAttendanceMap = userDayRangeLeaveList.stream()
                .collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getLeaveType));

        // 遍历考勤请假信息
        for (Map.Entry<String, List<AttendanceEmployeeDetailDO>> entry : leaveAttendanceMap.entrySet()) {
            BigDecimal leaveMinutes = BigDecimal.ZERO;
            BigDecimal defaultLegalWorkingMinutes = BigDecimal.ZERO;
            // 遍历同一种类型的请假信息--->获取该用户的该请假类型的请假总分钟数,并获取每一天的法定工作分钟数
            for (AttendanceEmployeeDetailDO employeeDetailDO : entry.getValue()) {
                // 获取请假分钟数
                if (employeeDetailDO.getLeaveMinutes() != null && employeeDetailDO.getLeaveMinutes().compareTo(BigDecimal.ZERO) > 0) {
                    leaveMinutes = leaveMinutes.add(employeeDetailDO.getLeaveMinutes());
                }
                // 获取每一天的法定工作分钟数
                UserMonthDayAttendanceDTO userMonthDayAttendanceDTO = dayAttendanceResultMap.get(employeeDetailDO.getDayId());
                BigDecimal defaultLegalWorkingHours = userMonthDayAttendanceDTO == null ?
                        BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS : userMonthDayAttendanceDTO.getDefaultLegalWorkingHours();

                defaultLegalWorkingMinutes = defaultLegalWorkingMinutes.add(
                        defaultLegalWorkingHours.multiply(BusinessConstant.MINUTES));
            }
            // 将该用户该请假类型的总请假分钟转换为天数
            BigDecimal leaveDays = leaveMinutes.divide(defaultLegalWorkingMinutes, 4, RoundingMode.HALF_UP);
            String existLeaveDays = leaveMap.get(entry.getKey());
            if (StringUtils.isBlank(existLeaveDays)) {
                leaveMap.put(entry.getKey(), leaveDays.toString());
            } else {
                leaveMap.put(entry.getKey(), leaveDays.add(new BigDecimal(existLeaveDays)).toString());
            }
        }

        // 将请假信息添加到导出数据中
        map.putAll(leaveMap);
    }

    /**
     * 填充考勤周期内每一天的考勤结果
     * 该方法负责将用户的每日考勤数据填充到导出Map中，确保考勤周期内的每一天都有对应的数据。
     *
     * @param map                   导出数据Map，方法会向其中添加日期列数据
     * @param userMonthReportListVO 用户月报数据，包含每日考勤结果列表
     * @param dayIdList             考勤周期内的完整日期列表，已排序
     */
    private void fillCycleDayAttendanceResult(Map<String, String> map,
                                              UserMonthReportListVO userMonthReportListVO,
                                              List<Long> dayIdList) {
        List<UserMonthDayAttendanceDTO> dayAttendanceResults = userMonthReportListVO.getDayAttendanceResults();
        if (CollectionUtils.isEmpty(dayAttendanceResults)) {
            return;
        }
        Map<Long, UserMonthDayAttendanceDTO> dayAttendanceResultMap = dayAttendanceResults.stream()
                .collect(Collectors.toMap(UserMonthDayAttendanceDTO::getDayId, Function.identity(), (a, b) -> a));

        // 防止日报的在周期内的数据不全，使用dayIdList循环构建每一天的考勤结果，没有则置为-
        for (Long dayId : dayIdList) {
            map.put(formatExportDayTitle(dayId), formatDayAttendanceResult(dayAttendanceResultMap.get(dayId)));
        }
    }

    /**
     * 格式化每日考勤结果为导出格式
     */
    private String formatDayAttendanceResult(UserMonthDayAttendanceDTO userMonthDayAttendanceDTO) {
        if (userMonthDayAttendanceDTO == null) {
            return "-";
        }
        return StringUtils.defaultIfBlank(userMonthDayAttendanceDTO.getDayAttendanceResult(), "-");
    }

    /**
     * 格式化导出Excel的日期列标题
     * <p>
     * 将dayId转换为Excel列标题格式，用作动态日期列的键名。
     * 标题格式为dayId，例如：20250625
     *
     * @param dayId 日期ID，格式为yyyyMMdd，如20250625表示2025年6月25日
     * @return 格式化后的日期标题
     */
    private String formatExportDayTitle(Long dayId) {
        if (dayId == null) {
            return null;
        }
        return dayId.toString();
    }

    /**
     * 填充员工基本信息到导出Map
     * 该方法负责将员工的基本信息填充到导出数据Map中，包括个人信息、组织信息、地理位置信息和时间信息。
     *
     * @param map                   导出数据Map，方法会向其中添加员工基本信息
     * @param userMonthReportListVO 用户月报数据，包含员工的各项基本信息
     */
    private void fillEmployeeBasicInfo(Map<String, String> map, UserMonthReportListVO userMonthReportListVO) {
        // 员工基本信息
        map.put(MonthReportExportHeaderEnum.EMPLOYEE_NAME.getTitleField(),
                formatStringValue(userMonthReportListVO.getUserName()));
        map.put(MonthReportExportHeaderEnum.EMPLOYEE_ID.getTitleField(),
                formatStringValue(userMonthReportListVO.getUserCode()));
        map.put(MonthReportExportHeaderEnum.WORK_STATUS.getTitleField(),
                formatStringValue(userMonthReportListVO.getWorkStatusDesc()));
        map.put(MonthReportExportHeaderEnum.ACCOUNT_STATUS.getTitleField(),
                formatStringValue(StatusUtil.getStatusDesc(userMonthReportListVO.getAccountStatus())));
        map.put(MonthReportExportHeaderEnum.EMPLOYEE_TYPE.getTitleField(),
                formatStringValue(userMonthReportListVO.getEmployeeTypeDesc()));

        // 部门和岗位信息
        map.put(MonthReportExportHeaderEnum.DEPARTMENT_NAME.getTitleField(),
                formatStringValue(userMonthReportListVO.getDeptName()));
        map.put(MonthReportExportHeaderEnum.POST_NAME.getTitleField(),
                formatStringValue(userMonthReportListVO.getPostName()));

        // 地理位置信息
        map.put(MonthReportExportHeaderEnum.LOCATION_COUNTRY.getTitleField(),
                formatStringValue(userMonthReportListVO.getLocationCountry()));
        map.put(MonthReportExportHeaderEnum.LOCATION_PROVINCE.getTitleField(),
                formatStringValue(userMonthReportListVO.getLocationProvince()));
        map.put(MonthReportExportHeaderEnum.LOCATION_CITY.getTitleField(),
                formatStringValue(userMonthReportListVO.getLocationCity()));

        // 日期信息
        map.put(MonthReportExportHeaderEnum.ENTRY_DATE.getTitleField(),
                formatStringValue(userMonthReportListVO.getEntryDate()));
        map.put(MonthReportExportHeaderEnum.DISMISSION_DATE.getTitleField(),
                formatStringValue(userMonthReportListVO.getDimissionDate()));
        map.put(MonthReportExportHeaderEnum.ATTENDANCE_MONTH.getTitleField(),
                formatStringValue(userMonthReportListVO.getAttendanceMonth()));
        map.put(MonthReportExportHeaderEnum.ATTENDANCE_CYCLE.getTitleField(),
                formatStringValue(userMonthReportListVO.getAttendanceCycle()));
    }

    /**
     * 填充出勤统计信息到导出Map
     *
     * @param map                   导出数据Map，方法会向其中添加出勤统计信息
     * @param userMonthReportListVO 用户月报数据，包含各项出勤统计数据
     */
    private void fillAttendanceStatistics(Map<String, String> map, UserMonthReportListVO userMonthReportListVO) {
        // 出勤统计相关字段
        map.put(MonthReportExportHeaderEnum.SHOULD_ATTENDANCE_DAYS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getShouldAttendanceDays()));
        map.put(MonthReportExportHeaderEnum.ACTUAL_ATTENDANCE_DAYS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getActualAttendanceDays()));
        map.put(MonthReportExportHeaderEnum.ACTUAL_ATTENDANCE_TOTAL_HOURS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getActualAttendanceHours()));
        map.put(MonthReportExportHeaderEnum.WORK_TOTAL_HOURS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getWorkHours()));
        map.put(MonthReportExportHeaderEnum.ACTUAL_ATTENDANCE_AVERAGE_HOURS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getActualAttendanceAverageHours()));

        // 延时工作相关字段
        map.put(MonthReportExportHeaderEnum.OVERTIME_TOTAL_HOURS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getDelayHours()));

        // 请假相关字段
        map.put(MonthReportExportHeaderEnum.LEAVE_TOTAL_HOURS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getLeaveHours()));
        map.put(MonthReportExportHeaderEnum.LEAVE_COUNT.getTitleField(),
                formatIntegerValue(userMonthReportListVO.getLeaveCount()));

        // 外勤相关字段
        map.put(MonthReportExportHeaderEnum.OUT_OF_OFFICE_TOTAL_HOURS.getTitleField(),
                formatBigDecimalValue(userMonthReportListVO.getOooHours()));
        map.put(MonthReportExportHeaderEnum.OUT_OF_OFFICE_COUNT.getTitleField(),
                formatIntegerValue(userMonthReportListVO.getOooCount()));
        //补卡次数
        map.put(MonthReportExportHeaderEnum.REISSUE_CARD_COUNT.getTitleField(),
                formatIntegerValue(userMonthReportListVO.getReissueCardCount()));
    }

    /**
     * 填充考勤异常统计信息到导出Map
     *
     * @param map                   导出数据Map，方法会向其中添加异常统计信息
     * @param userMonthReportListVO 用户月报数据，包含异常统计列表
     */
    private void fillAbnormalStatistics(Map<String, String> map, UserMonthReportListVO userMonthReportListVO) {
        List<UserMonthAbnormalStatisticsDTO> abnormalStatisticsList = userMonthReportListVO.getAbnormalStatisticsList();
        if (CollectionUtils.isEmpty(abnormalStatisticsList)) {
            // 如果没有异常统计数据，设置默认值
            setDefaultAbnormalValues(map);
            return;
        }

        // 考勤异常相关字段 - 迟到
        map.put(MonthReportExportHeaderEnum.LATE_COUNT.getTitleField(),
                getAbnormalCountByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.LATE.getCode()));
        map.put(MonthReportExportHeaderEnum.LATE_TOTAL_MINUTES.getTitleField(),
                getAbnormalTimeByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.LATE.getCode()));

        // 考勤异常相关字段 - 早退
        map.put(MonthReportExportHeaderEnum.EARLY_LEAVE_COUNT.getTitleField(),
                getAbnormalCountByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode()));
        map.put(MonthReportExportHeaderEnum.EARLY_LEAVE_TOTAL_MINUTES.getTitleField(),
                getAbnormalTimeByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode()));

        // 考勤异常相关字段 - 缺卡
        map.put(MonthReportExportHeaderEnum.PUNCH_IN_MISSING_COUNT.getTitleField(),
                getAbnormalCountByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode()));
        map.put(MonthReportExportHeaderEnum.PUNCH_OUT_MISSING_COUNT.getTitleField(),
                getAbnormalCountByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode()));

        // 考勤异常相关字段 - 其他异常
        map.put(MonthReportExportHeaderEnum.NO_PUNCH_COUNT.getTitleField(),
                getAbnormalCountByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.NO_PUNCH.getCode()));
        map.put(MonthReportExportHeaderEnum.DURATION_ABNORMAL_COUNT.getTitleField(),
                getAbnormalCountByType(abnormalStatisticsList, AttendanceAbnormalTypeEnum.ABNORMAL_DURATION.getCode()));
    }

    /**
     * 设置异常统计字段的默认值
     * <p>
     * 当员工没有任何异常统计数据时，为所有异常统计字段设置默认值"0"，
     * 确保导出Excel中的异常统计列都有数据，避免空白单元格。
     * <p>
     * <strong>设置默认值的字段：</strong>
     * <ul>
     *   <li>迟到次数和总时长</li>
     *   <li>早退次数和总时长</li>
     *   <li>上班和下班缺卡次数</li>
     *   <li>未打卡次数</li>
     *   <li>时长异常次数</li>
     *   <li>补卡次数</li>
     * </ul>
     *
     * @param map 导出数据Map，方法会向其中添加默认的异常统计值
     */
    private void setDefaultAbnormalValues(Map<String, String> map) {
        map.put(MonthReportExportHeaderEnum.LATE_COUNT.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.LATE_TOTAL_MINUTES.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.EARLY_LEAVE_COUNT.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.EARLY_LEAVE_TOTAL_MINUTES.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.PUNCH_IN_MISSING_COUNT.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.PUNCH_OUT_MISSING_COUNT.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.NO_PUNCH_COUNT.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.DURATION_ABNORMAL_COUNT.getTitleField(), "0");
        map.put(MonthReportExportHeaderEnum.REISSUE_CARD_COUNT.getTitleField(), "0");
    }

    /**
     * 根据异常类型获取异常次数
     */
    private String getAbnormalCountByType(List<UserMonthAbnormalStatisticsDTO> abnormalStatisticsList, String abnormalType) {
        return abnormalStatisticsList.stream()
                .filter(item -> abnormalType.equals(item.getAbnormalType()))
                .findFirst()
                .map(item -> item.getAbnormalCount() != null ? item.getAbnormalCount().toString() : "0")
                .orElse("0");
    }

    /**
     * 根据异常类型获取异常时间（分钟）
     * <p>
     * 从异常统计列表中查找指定类型的异常记录，并返回对应的异常时长。
     * 主要用于获取迟到和早退的时长统计。
     *
     * @param abnormalStatisticsList 异常统计数据列表
     * @param abnormalType           异常类型代码，通常是迟到或早退类型
     * @return 异常时长的字符串表示（分钟）：
     * <ul>
     *   <li>如果找到对应记录，通过 {@link #parseAbnormalTimeMinutes} 解析时长</li>
     *   <li>如果没有找到对应记录，返回"0"</li>
     * </ul>
     */
    private String getAbnormalTimeByType(List<UserMonthAbnormalStatisticsDTO> abnormalStatisticsList, String abnormalType) {
        return abnormalStatisticsList.stream()
                .filter(item -> abnormalType.equals(item.getAbnormalType()))
                .findFirst()
                .map(item -> parseAbnormalTimeMinutes(item.getAbnormalTime()))
                .orElse("0");
    }

    /**
     * 解析异常时间字符串，提取分钟数
     *
     * @param abnormalTime 异常时间字符串，如"120min"或"-"
     * @return 解析后的分钟数字符串，如"120"或"0"
     */
    private String parseAbnormalTimeMinutes(String abnormalTime) {
        if (StringUtils.isEmpty(abnormalTime) || "-".equals(abnormalTime)) {
            return "0";
        }

        // 提取数字部分，去掉 "min" 后缀
        if (abnormalTime.endsWith("min")) {
            return abnormalTime.substring(0, abnormalTime.length() - 3);
        }

        return "0";
    }

    /**
     * 格式化BigDecimal数值为Excel导出字符串
     *
     * @param value BigDecimal数值，可能为null
     * @return 格式化后的字符串，null值返回"0"
     */
    private String formatBigDecimalValue(BigDecimal value) {
        if (value == null) {
            return "0";
        }
        return value.toString();
    }

    /**
     * 格式化Integer数值为Excel导出字符串
     *
     * @param value Integer数值，可能为null
     * @return 格式化后的字符串，null值返回"0"
     */
    private String formatIntegerValue(Integer value) {
        if (value == null) {
            return "0";
        }
        return value.toString();
    }

    /**
     * 格式化字符串值为Excel导出格式
     */
    private String formatStringValue(String value) {
        if (StringUtils.isEmpty(value)) {
            return "-";
        }
        return value;
    }

    /**
     * 验证导出查询参数的有效性
     *
     * @param query 月报查询条件
     * @throws BusinessLogicException 当locationCountry为空时抛出参数验证错误
     */
    private void validateExportQuery(MonthReportListQuery query) {
        String locationCountry = query.getLocationCountry();
        if (StringUtils.isEmpty(locationCountry)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR, "locationCountry can not be null");
        }
    }


}
