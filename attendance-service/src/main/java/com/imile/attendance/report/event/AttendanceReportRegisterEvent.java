package com.imile.attendance.report.event;

import com.imile.attendance.report.day.job.param.DayReportJobParam;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/7/4
 * @Description 事件
 */
@Getter
public class AttendanceReportRegisterEvent extends ApplicationEvent {

    private final List<DayReportJobParam> data;

    public AttendanceReportRegisterEvent(Object source, List<DayReportJobParam> data) {
        super(source);
        this.data = data;
    }
}
