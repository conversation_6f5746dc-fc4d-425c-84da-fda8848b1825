package com.imile.attendance.report.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.abnormal.AttendanceMinuteCalculateService;
import com.imile.attendance.abnormal.dto.DayAttendanceHandlerFormDTO;
import com.imile.attendance.abnormal.dto.DayItemInfoDTO;
import com.imile.attendance.annon.Strategy;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.punch.mapstruct.AttendancePunchRecordMapstruct;
import com.imile.attendance.report.AttendanceReportCalcService;
import com.imile.attendance.report.dto.AttendanceReportCalcContext;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 固班/多班次日月报时长计算
 *
 * <AUTHOR>
 * @menu 考勤日月报
 * @date 2025/6/12
 */
@Slf4j
@Service
@Strategy(value = AttendanceReportCalcService.class, implKey = "FixedWorkReportCalcServiceImpl")
public class FixedWorkReportCalcServiceImpl implements AttendanceReportCalcService {

    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private AttendanceMinuteCalculateService attendanceMinuteCalculateService;

    @Override
    public boolean isMatch(String punchConfigType) {
        return PunchConfigTypeEnum.FIXED_WORK.getCode().equals(punchConfigType);
    }

    @Override
    public BigDecimal execute(AttendanceReportCalcContext reportCalcContext) {
        BigDecimal availableMinutes = BigDecimal.ZERO;

        List<DayAttendanceHandlerFormDTO> handlerFormDTOList = new ArrayList<>();
        // 计算单据时长
        this.dayFormInfoBuild(handlerFormDTOList, reportCalcContext.getPassAndInViewFormList(), reportCalcContext.getPassAndInViewFormAttrList());
        handlerFormDTOList = handlerFormDTOList.stream().sorted(Comparator.comparing(DayAttendanceHandlerFormDTO::getStartTime)).collect(Collectors.toList());
        List<PunchClassItemConfigDO> dayClassItemConfigList = reportCalcContext.getDayClassItemConfigList();
        for (PunchClassItemConfigDO itemConfigDO : dayClassItemConfigList) {
            //获取当前时刻的正常时间
            DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(reportCalcContext.getDayId()
                    , itemConfigDO.getId(), dayClassItemConfigList);
            if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                return availableMinutes;
            }
            //获取打卡时间的所有点
            //最早上班打卡时间
            Date earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
            //上班时间  默认和最早打卡时间同一天
            Date punchInTime = DateUtil.parse(DateUtil.format(earliestPunchInTime, "yyyy-MM-dd")
                    + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            //上班时间早于最早打卡时间，跨天
            if (itemConfigDO.getPunchInTime().before(itemConfigDO.getEarliestPunchInTime())) {
                punchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(earliestPunchInTime, 1), "yyyy-MM-dd")
                        + " " + DateUtil.format(itemConfigDO.getPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            }
            //最晚上班打卡时间 默认和上班时间同一天
            Date latestPunchInTime = DateUtil.parse(DateUtil.format(punchInTime, "yyyy-MM-dd")
                    + " " + DateUtil.format(itemConfigDO.getLatestPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            if (itemConfigDO.getLatestPunchInTime().before(itemConfigDO.getPunchInTime())) {
                latestPunchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(punchInTime, 1), "yyyy-MM-dd")
                        + " " + DateUtil.format(itemConfigDO.getLatestPunchInTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            }

            //休息开始时间
            Date restStartTime = null;
            Date restEndTime = null;
            if (itemConfigDO.getRestStartTime() != null) {
                //默认和开始时间同一天
                restStartTime = DateUtil.parse(DateUtil.format(punchInTime, "yyyy-MM-dd")
                        + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                if (itemConfigDO.getRestStartTime().before(itemConfigDO.getPunchInTime())) {
                    restStartTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(punchInTime, 1), "yyyy-MM-dd")
                            + " " + DateUtil.format(itemConfigDO.getRestStartTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                }
                restEndTime = DateUtil.parse(DateUtil.format(restStartTime, "yyyy-MM-dd")
                        + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                if (itemConfigDO.getRestEndTime().before(itemConfigDO.getRestStartTime())) {
                    restEndTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(restStartTime, 1), "yyyy-MM-dd")
                            + " " + DateUtil.format(itemConfigDO.getRestEndTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
                }
            }

            //最晚下班打卡时间
            Date latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();
            //下班时间  默认和最晚下班时间同一天
            Date punchOutTime = DateUtil.parse(DateUtil.format(latestPunchOutTime, "yyyy-MM-dd")
                    + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            if (itemConfigDO.getPunchOutTime().after(itemConfigDO.getLatestPunchOutTime())) {
                punchOutTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(latestPunchOutTime, -1), "yyyy-MM-dd")
                        + " " + DateUtil.format(itemConfigDO.getPunchOutTime(), "HH:mm:ss"), "yyyy-MM-dd HH:mm:ss");
            }

            BigDecimal elasticTime = BigDecimal.ZERO;
            if (itemConfigDO.getElasticTime() != null) {
                elasticTime = itemConfigDO.getElasticTime();
            }

            //这个时刻的应出勤分钟
            BigDecimal itemTotalMinutes = BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE));
            if (restStartTime != null) {
                itemTotalMinutes = itemTotalMinutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
            }

            // 获取该班次最早上班打卡时间和最晚上班打卡时间之间的打卡记录
            Date finalLatestPunchInTime = latestPunchInTime;
            List<UserPunchRecordDTO> userPunchRecordList = reportCalcContext.getUserPunchRecordList();
            List<UserPunchRecordDTO> punchRecordList = userPunchRecordList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(earliestPunchInTime) > -1
                            && item.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
            // 获取最新的一个打卡记录
            UserPunchRecordDTO userPunchRecordDTO = null;
            // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
            long betweenMinutes = 0;
            if (CollUtil.isNotEmpty(punchRecordList)) {
                userPunchRecordDTO = punchRecordList.get(0);
                // 如果最近的一条打卡记录是大于等于上班时间，小于等于最晚上班时间也就是弹性之后的，那么需要根据打卡时间进行弹性处理
                if (userPunchRecordDTO.getFormatPunchTime().compareTo(punchInTime) > -1 && userPunchRecordDTO.getFormatPunchTime().compareTo(finalLatestPunchInTime) < 1) {
                    // 获取两个时间相差分钟数
                    betweenMinutes = DateUtil.between(userPunchRecordDTO.getFormatPunchTime(), punchInTime, DateUnit.MINUTE);
                }
            }

            BigDecimal usedMinutes = BigDecimal.ZERO;
            //找出和该时刻相关的所有单据
            //先生成正常考勤，所有的请假/外勤落库
            //这里请假可能超出出勤时长了
            List<DayAttendanceHandlerFormDTO> filterFormDTOList = new ArrayList<>();
            for (DayAttendanceHandlerFormDTO handlerFormDTO : handlerFormDTOList) {
                BigDecimal leaveMinutes = BigDecimal.ZERO;
                //请假时间和本时刻一定有交集，看和时刻内的休息时间的关系
                //看看本次请假/外勤的起始时间还是结束时间有咩有落在上班的弹性时间中(会有一个假跨多个时段的情况出现)
                leaveMinutes = attendanceMinuteCalculateService.shiftDayLeaveMinuteHandler(handlerFormDTO
                        , punchInTime, punchOutTime, restStartTime, restEndTime, betweenMinutes
                        , filterFormDTOList);
                usedMinutes = usedMinutes.add(leaveMinutes);
            }

            //当前时刻完全请假
            if ((itemTotalMinutes.subtract(usedMinutes)).compareTo(BigDecimal.ZERO) < 1) {
                //当前时刻完全请假
                availableMinutes = availableMinutes.add(usedMinutes);
                continue;
            }
            //可能部分请假/外勤，需要加上
            availableMinutes = availableMinutes.add(usedMinutes);
            //没有全部请假，需要看打卡时间（可能打卡时间够，正常考勤，也可能不够，异常考勤）
            List<UserPunchRecordDTO> itemPunchRecordList = userPunchRecordList.stream()
                    .filter(item -> item.getFormatPunchTime().compareTo(earliestPunchInTime) > -1
                            && item.getFormatPunchTime().compareTo(latestPunchOutTime) < 1)
                    .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime))
                    .collect(Collectors.toList());
            //情况1:当天没有打卡时间
            if (CollectionUtils.isEmpty(itemPunchRecordList)) {
                continue;
            }
            //情况2:当天完全没请假，看打卡记录
            if (CollectionUtils.isEmpty(filterFormDTOList)) {
                BigDecimal minutes = normalPunchHandler(itemPunchRecordList, punchInTime, latestPunchInTime, punchOutTime, restStartTime, restEndTime, itemTotalMinutes);
                availableMinutes = availableMinutes.add(minutes);
                continue;
            }

            //情况3:当天存在请假  有一条打卡记录
            if (itemPunchRecordList.size() == 1) {
                continue;
            }
            //情况4: 当天存在请假 多条数据，但可能都是上班卡
            BigDecimal minutes = leaveDayBatchHandler(itemPunchRecordList, filterFormDTOList
                    , earliestPunchInTime, punchInTime, punchOutTime, latestPunchOutTime, restStartTime, restEndTime
                    , itemTotalMinutes.subtract(usedMinutes));
            availableMinutes = availableMinutes.add(minutes);
        }
        return availableMinutes;
    }

    /**
     * 计算当日单据时间(拷贝原有HRMS方法)
     * @param handlerFormDTOList
     * @param passFormDOList
     * @param passFormAttrDOList
     */
    private void dayFormInfoBuild(List<DayAttendanceHandlerFormDTO> handlerFormDTOList,
                                  List<AttendanceFormDO> passFormDOList,
                                  List<AttendanceFormAttrDO> passFormAttrDOList) {
        for (AttendanceFormDO formDO : passFormDOList) {
            //请假
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.LEAVE.getCode())
                    && FormStatusEnum.PASS.getCode().equals(formDO.getFormStatus())) {
                List<AttendanceFormAttrDO> leaveStartDateDO = passFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode())).collect(Collectors.toList());
                List<AttendanceFormAttrDO> leaveEndDateDO = passFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(leaveStartDateDO) || CollectionUtils.isEmpty(leaveEndDateDO)) {
                    continue;
                }
                Date leaveStartDate = DateUtil.parse(leaveStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Date leaveEndDate = DateUtil.parse(leaveEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long leaveStartDayId = Long.valueOf(DateUtil.format(leaveStartDate, "yyyyMMdd"));
                Long leaveEndDayId = Long.valueOf(DateUtil.format(leaveEndDate, "yyyyMMdd"));
                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(leaveStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(leaveStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(leaveEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(leaveEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
            //外勤
            if (StringUtils.equalsIgnoreCase(formDO.getFormType(), FormTypeEnum.OUT_OF_OFFICE.getCode())
                    && FormStatusEnum.PASS.getCode().equals(formDO.getFormStatus())) {
                List<AttendanceFormAttrDO> outOfOfficeStartDateDO = passFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode())).collect(Collectors.toList());
                List<AttendanceFormAttrDO> outOfOfficeEndDateDO = passFormAttrDOList.stream()
                        .filter(item -> item.getFormId().equals(formDO.getId())
                                && StringUtils.equalsIgnoreCase(item.getAttrKey(), ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(outOfOfficeStartDateDO) || CollectionUtils.isEmpty(outOfOfficeEndDateDO)) {
                    continue;
                }
                Date outOfOfficeStartDate = DateUtil.parse(outOfOfficeStartDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Date outOfOfficeEndDate = DateUtil.parse(outOfOfficeEndDateDO.get(0).getAttrValue(), "yyyy-MM-dd HH:mm:ss");
                Long outOfOfficeStartDayId = Long.valueOf(DateUtil.format(outOfOfficeStartDate, "yyyyMMdd"));
                Long outOfOfficeEndDayId = Long.valueOf(DateUtil.format(outOfOfficeEndDate, "yyyyMMdd"));
                DayAttendanceHandlerFormDTO dayAttendanceHandlerFormDTO = new DayAttendanceHandlerFormDTO();
                dayAttendanceHandlerFormDTO.setFormId(formDO.getId());
                dayAttendanceHandlerFormDTO.setFormType(formDO.getFormType());
                dayAttendanceHandlerFormDTO.setStartTime(outOfOfficeStartDate);
                dayAttendanceHandlerFormDTO.setStartDayId(outOfOfficeStartDayId);
                dayAttendanceHandlerFormDTO.setEndTime(outOfOfficeEndDate);
                dayAttendanceHandlerFormDTO.setEndDayId(outOfOfficeEndDayId);
                handlerFormDTOList.add(dayAttendanceHandlerFormDTO);
            }
        }
    }

    /**
     * 时刻没有请假，正常打卡
     */
    private BigDecimal normalPunchHandler(List<UserPunchRecordDTO> itemPunchRecordList,
                                          Date punchInTime,
                                          Date latestPunchInTime,
                                          Date punchOutTime,
                                          Date restStartTime,
                                          Date restEndTime,
                                          BigDecimal itemTotalMinutes) {
        BigDecimal availableMinutes = BigDecimal.ZERO;
        //一条打卡记录
        if (itemPunchRecordList.size() == 1) {
            return availableMinutes;
        }

        //多条打卡记录
        //下班未打卡
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
            return availableMinutes;
        }
        //上班未打卡
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchOutTime) > -1) {
            return availableMinutes;
        }

        //todo
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(punchInTime) < 1) {
            //早退  下班时间在正常上班之前
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchInTime) < 1) {
                return availableMinutes;
            }
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                //需要和休息时间比较
                availableMinutes = compareRestTimeHandler(punchInTime, itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
                return availableMinutes;
            }
            availableMinutes = compareRestTimeHandler(punchInTime, punchOutTime, restStartTime, restEndTime);
            return availableMinutes;
        }
        if (itemPunchRecordList.get(0).getFormatPunchTime().compareTo(latestPunchInTime) < 1) {
            //早退
            if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
                availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
                return availableMinutes;
            }
            //比较时间长短
            if (BigDecimal.valueOf(DateUtil.between(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), DateUnit.MINUTE)).compareTo(BigDecimal.valueOf(DateUtil.between(punchInTime, punchOutTime, DateUnit.MINUTE))) > -1) {
                availableMinutes = itemTotalMinutes;
                return availableMinutes;
            }
            availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
            return availableMinutes;
        }
        if (itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime().compareTo(punchOutTime) < 0) {
            availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), itemPunchRecordList.get(itemPunchRecordList.size() - 1).getFormatPunchTime(), restStartTime, restEndTime);
            return availableMinutes;
        }
        availableMinutes = compareRestTimeHandler(itemPunchRecordList.get(0).getFormatPunchTime(), punchOutTime, restStartTime, restEndTime);
        return availableMinutes;
    }

    private BigDecimal compareRestTimeHandler(Date startDate, Date endDate, Date restStartTime, Date restEndTime) {
        BigDecimal minutes = BigDecimal.valueOf(DateUtil.between(startDate, endDate, DateUnit.MINUTE));
        if (restStartTime == null) {
            return minutes;
        }
        //没有交集
        if (endDate.compareTo(restStartTime) < 1 || startDate.compareTo(restEndTime) > -1) {
            return minutes;
        }
        //被休息时间包含
        if (startDate.compareTo(restStartTime) > -1 && endDate.compareTo(restEndTime) < 1) {
            return BigDecimal.ZERO;
        }
        //休息时间被包含
        if (startDate.compareTo(restStartTime) < 1 && endDate.compareTo(restEndTime) > -1) {
            return minutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, restEndTime, DateUnit.MINUTE)));
        }
        if (startDate.compareTo(restStartTime) < 0) {
            return minutes.subtract(BigDecimal.valueOf(DateUtil.between(restStartTime, endDate, DateUnit.MINUTE)));
        }
        return minutes.subtract(BigDecimal.valueOf(DateUtil.between(startDate, restEndTime, DateUnit.MINUTE)));
    }

    /**
     * 时刻有请假，正常打卡
     */
    private BigDecimal leaveDayBatchHandler(List<UserPunchRecordDTO> itemPunchRecordList,
                                            List<DayAttendanceHandlerFormDTO> filterFormDTOList,
                                            Date earliestPunchInTime,
                                            Date punchInTime,
                                            Date punchOutTime,
                                            Date latestPunchOutTime,
                                            Date restStartTime,
                                            Date restEndTime,
                                            BigDecimal itemTotalMinutes) {
        Date finalPunchInTime = punchInTime;
        Date finalPunchOutTime = punchOutTime;
        List<UserPunchRecordDTO> punchBeforeCardList = itemPunchRecordList.stream().filter(o -> o.getFormatPunchTime().compareTo(earliestPunchInTime) > -1 && o.getFormatPunchTime().compareTo(finalPunchInTime) < 0).collect(Collectors.toList());
        List<UserPunchRecordDTO> punchBetweenCardList = itemPunchRecordList.stream().filter(o -> o.getFormatPunchTime().compareTo(finalPunchInTime) > -1 && o.getFormatPunchTime().compareTo(finalPunchOutTime) < 1).collect(Collectors.toList());
        List<UserPunchRecordDTO> punchAfterCardList = itemPunchRecordList.stream().filter(o -> o.getFormatPunchTime().compareTo(finalPunchOutTime) > 0 && o.getFormatPunchTime().compareTo(latestPunchOutTime) < 1).collect(Collectors.toList());
        List<DayItemInfoDTO> dayItemInfoDTOS = new ArrayList<>();
        BigDecimal presentMinutes = BigDecimal.ZERO;
        //打卡时间全部在正规上下班中
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            //整个时段内，除去请假外的打卡记录的区间
            List<UserPunchRecordBO> punchRecordBoList = AttendancePunchRecordMapstruct.INSTANCE.toPunchRecordBo(punchBetweenCardList);
            attendanceMinuteCalculateService.multiplePunchHandler(dayItemInfoDTOS, filterFormDTOList, punchRecordBoList);
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            return presentMinutes;
        }
        //开始时间和中间时间为空,直接是上班卡未打异常
        if (CollectionUtils.isEmpty(punchBeforeCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            return presentMinutes;
        }
        //结束时间和中间时间为空,直接是下班卡未打异常
        if (CollectionUtils.isEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList)) {
            return presentMinutes;
        }
        if (CollectionUtils.isNotEmpty(punchBeforeCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchAfterCardList)) {
            Date beginPunchTime = punchInTime;
            Date endPunchTime = punchBetweenCardList.get(punchBetweenCardList.size() - 1).getFormatPunchTime();
            for (int i = 0; i < filterFormDTOList.size(); i++) {
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoDTOS.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                dayItemInfoDTO.setEndItemTime(filterFormDTOList.get(i).getStartTime());
                dayItemInfoDTOS.add(dayItemInfoDTO);
                if (endPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    beginPunchTime = filterFormDTOList.get(i).getEndTime();
                    if (i == filterFormDTOList.size() - 1) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoDTOS.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }
            for (DayItemInfoDTO itemInfoDTO : dayItemInfoDTOS) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(itemInfoDTO.getBeginItemTime(), itemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            return presentMinutes;
        }
        if (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isEmpty(punchBeforeCardList)) {
            Date beginPunchTime = punchBetweenCardList.get(0).getFormatPunchTime();
            Date endPunchTime = punchOutTime;
            for (int i = filterFormDTOList.size() - 1; i >= 0; i--) {
                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getEndTime()) > -1) {
                    DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                    dayItemInfoDTO.setBeginItemTime(beginPunchTime);
                    dayItemInfoDTO.setEndItemTime(endPunchTime);
                    dayItemInfoDTOS.add(dayItemInfoDTO);
                    break;
                }
                DayItemInfoDTO dayItemInfoDTO = new DayItemInfoDTO();
                dayItemInfoDTO.setBeginItemTime(filterFormDTOList.get(i).getEndTime());
                dayItemInfoDTO.setEndItemTime(endPunchTime);
                dayItemInfoDTOS.add(dayItemInfoDTO);

                if (beginPunchTime.compareTo(filterFormDTOList.get(i).getStartTime()) < 1) {
                    endPunchTime = filterFormDTOList.get(i).getStartTime();
                    if (i == 0) {
                        DayItemInfoDTO dayItemInfoFinalDTO = new DayItemInfoDTO();
                        dayItemInfoFinalDTO.setBeginItemTime(beginPunchTime);
                        dayItemInfoFinalDTO.setEndItemTime(endPunchTime);
                        dayItemInfoDTOS.add(dayItemInfoFinalDTO);
                    }
                    continue;
                }
                break;
            }
            for (DayItemInfoDTO itemInfoDTO : dayItemInfoDTOS) {
                presentMinutes = presentMinutes.add(BigDecimal.valueOf(DateUtil.between(itemInfoDTO.getBeginItemTime(), itemInfoDTO.getEndItemTime(), DateUnit.MINUTE)));
            }
            //再去和休息时间比较
            presentMinutes = attendanceMinuteCalculateService.subtractRestTimeHandler(dayItemInfoDTOS, restStartTime, restEndTime);
            return presentMinutes;

        }
        //全部打卡,（或者前后都有打卡，就中间没打卡）没有异常，就看P的时间
        if ((CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isNotEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList)) || (CollectionUtils.isNotEmpty(punchAfterCardList) && CollectionUtils.isEmpty(punchBetweenCardList) && CollectionUtils.isNotEmpty(punchBeforeCardList))) {
            presentMinutes = itemTotalMinutes;
            return presentMinutes;
        }
        return presentMinutes;
    }



}
