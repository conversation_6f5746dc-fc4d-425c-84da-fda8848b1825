package com.imile.attendance.report.day.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 员工日报导出VO
 *
 * <AUTHOR>
 * @date 2025/6/21
 */
@Data
public class UserDayReportExportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 考勤日期
     */
    private String date;

    /**
     * 日历规则名称
     */
    private String calendarConfigName;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡类型
     */
    private String punchConfigType;

    /**
     * 补卡规则名称
     */
    private String reissueConfigName;

    /**
     * 加班规则名称
     */
    private String overTimeConfigName;

    /**
     * 排班计划
     */
    private String dayShiftRule;

    /**
     * 班次类型
     */
    private String classNature;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 上班1打卡时间
     */
    private String earliestPunchInTime1;

    /**
     * 上班1打卡时间
     */
    private String latestPunchOutTime1;

    /**
     * 上班2打卡时间
     */
    private String earliestPunchInTime2;

    /**
     * 上班2打卡时间
     */
    private String latestPunchOutTime2;

    /**
     * 上班3打卡时间
     */
    private String earliestPunchInTime3;

    /**
     * 上班3打卡时间
     */
    private String latestPunchOutTime3;

    /**
     * 初始考勤结果
     */
    private String initResult;

    /**
     * 实际出勤时长-不含休息(h)
     */
    private String actualAttendanceHour;

    /**
     * 延时时长(h)
     */
    private String delayHour;

    /**
     * 当日请假时长
     */
    private String leaveHour;

    /**
     * 当日外勤时长
     */
    private String oooHour;

    /**
     * 迟到时长(min)
     */
    private String lateMinutes;

    /**
     * 迟到处理方式
     */
    private String lateOperation;

    /**
     * 早退时长(min)
     */
    private String leaveEarlyMinutes;

    /**
     * 早退处理方式
     */
    private String leaveEarlyOperation;

    /**
     * 上班缺卡次数
     */
    private String beforeOfficeLackNum;

    /**
     * 上班缺卡处理方式
     */
    private String beforeOfficeLackOperation;

    /**
     * 下班缺卡次数
     */
    private String afterOfficeLackNum;

    /**
     * 下班缺卡处理方式
     */
    private String afterOfficeLackOperation;

    /**
     * 未打卡次数
     */
    private String noPunchNum;

    /**
     * 未打卡处理方式
     */
    private String noPunchOperation;

    /**
     * 工时异常时长(min)
     */
    private String abnormalWorkMinutes;

    /**
     * 工时异常处理方式
     */
    private String abnormalWorkOperation;

    /**
     * 最终考勤结果
     */
    private String finalResult;

    /**
     * 最终工作时长-不含休息(h)
     */
    private String finalWorkHour;
}
