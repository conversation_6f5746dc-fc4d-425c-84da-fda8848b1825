package com.imile.attendance.report.day.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Data
public class AttendanceDayReportDeleteCommand {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;
}
