package com.imile.attendance.report.day.dto;

import lombok.Data;

import java.util.List;

/**
 * 员工日报详情异常记录DTO
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
public class UserDayReportAbnormalDTO {

    /**
     * 日报主键
     */
    private Long dayReportId;

    /**
     * 异常主键
     */
    private Long abnormalId;

    /**
     * 异常类型
     */
    private String abnormalType;

    /**
     * 异常状态
     */
    private String abnormalStatus;

    /**
     * 班次id
     */
    private Long punchClassId;

    /**
     * 班次时段id
     */
    private Long punchClassItemConfigId;

    /**
     * 操作记录
     */
    private List<UserDayReportAbnormalOperationDTO> operationList;
}
