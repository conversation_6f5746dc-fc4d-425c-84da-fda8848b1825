package com.imile.attendance.report.day.job.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.report.AttendanceReportAbnormalHandlerService;
import com.imile.attendance.report.AttendanceReportQueryService;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.dto.AttendanceReportAbnormalHandlerDTO;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 考勤日报定时任务业务实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
@Slf4j
public class AttendanceDayReportJobService {
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private AttendanceCountryService attendanceCountryService;
    @Resource
    private AttendanceReportQueryService reportQueryService;
    @Resource
    private AttendanceReportAbnormalHandlerService abnormalHandlerService;
    @Resource
    private MigrationService migrationService;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;

    private static final Integer PAGE_SIZE_DEFAULT = 5000;

    public void attendanceDayReportInit(DayReportJobParam param) {
        // 1. 查询满足条件的用户数据
        XxlJobLogger.log("xxl-job【attendanceDayReportInit】开始执行，参数为：{}", JSON.toJSONString(param));
        List<String> countryList = param.getCountryArrayList();
        if (CollectionUtils.isEmpty(countryList)) {
            // 默认查询所有人员的常驻国
            countryList = userInfoManage.selectUserLocationCountry();
        }
        // 2. 获取对应国家及当地时间
        Map<String, Date> countryDateMap = attendanceCountryService.getCountryDateMap(countryList, param);
        // 3. 遍历国家 查询满足时刻为凌晨0点的国家
        for (String country : countryList) {
            Date localDate = countryDateMap.get(country);
            if (Objects.isNull(localDate)) {
                XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | 当前国家:{}, 不存在对应时区");
                continue;
            }
            if (!DateHelper.isMidNight(localDate)) {
                XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | 当前国家:{}, 时间未到凌晨0点，无需初始化考勤日报");
                continue;
            }
            XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | 当前国家:{}, 凌晨0点，初始化考勤日报");
            // 查询人员
            int currentPage = 1, pageSize = PAGE_SIZE_DEFAULT;
            Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
            UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                    .locationCountry(country)
                    .userIds(param.getUserIdList())
                    .userCodes(param.getUserCodeList())
                    .employeeTypeList(param.getEmployeeTypeList())
                    .isDelete(IsDeleteEnum.NO.getCode())
                    // 默认不查询司机
                    .isDriver(WhetherEnum.NO.getKey())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .status(StatusEnum.ACTIVE.getCode())
                    .build();
            PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoManage.listUsersByQuery(userDaoQuery));
            int userCount = BusinessConstant.ZERO.intValue();
            // 总记录数
            List<UserInfoDO> pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                userCount = pageUserInfoList.size();
                // 生成对应国家人员考勤日报
                this.initCountryDayReport(pageUserInfoList, localDate);
            }
            XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | country:{}, currentPage:{},pageSize:{},total:{},pages：{}"
                    , country, currentPage, pageSize, pageInfo.getTotal(), pageInfo.getPages());
            while (currentPage < pageInfo.getPages()) {
                XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | country:{},进入while循环", country);
                currentPage++;
                page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> userInfoManage.listUsersByQuery(userDaoQuery));
                pageUserInfoList = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                    XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | country:{}, while循环：pageUserInfoList size:{}，pageUserInfoList：{}"
                            , country, pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                    userCount = userCount + pageUserInfoList.size();
                    // 生成对应国家人员考勤日报
                    this.initCountryDayReport(pageUserInfoList, localDate);
                }
                XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | country:{}, while循环：currentPage:{},pageSize:{},total:{}"
                        , country, currentPage, pageSize, pageInfo.getTotal());
            }
            XxlJobLogger.log("xxl-job【attendanceDayReportInit】 | country:{} currentPage {}，userCount {} while循环结束"
                    , country, currentPage, userCount);
        }
    }

    public void attendanceBatchDayReportInit(List<DayReportJobParam> param) {
        log.info("attendanceBatchDayReportInit开始执行，参数为：{}", JSON.toJSONString(param));
        if (CollectionUtils.isEmpty(param)) {
            return;
        }
        for (DayReportJobParam dayReportJobParam : param) {
            this.attendanceDayReportInit(dayReportJobParam);
        }
    }

    /**
     * 初始化用户考勤日报
     *
     * @param userInfoList
     * @param localDate
     */
    private void initCountryDayReport(List<UserInfoDO> userInfoList,
                                      Date localDate) {
        String dayId = DateHelper.formatPureDate(localDate);
        // 1. 过滤未满足条件人员
        userInfoList = this.filterUserCondition(userInfoList, dayId);
        // 2. 获取聚合对象
        List<AttendanceReportAbnormalHandlerDTO> userReportDTOList = reportQueryService.getUserReportDTOList(userInfoList, Long.valueOf(dayId));
        // 3. 初始化考勤日报(适配历史数据迁移处理)
        abnormalHandlerService.batchExecute(userReportDTOList);
    }

    /**
     * 过滤未满足条件人员
     *
     * @param userInfoList
     * @return
     */
    private List<UserInfoDO> filterUserCondition(List<UserInfoDO> userInfoList,
                                                 String dayId) {
        List<UserInfoDO> filterUserList = new ArrayList<>();
        // 1. 查询考勤管理范围内管理劳务派遣的国家列表
        List<String> osCountry = migrationService.getAttendanceOSCountry();
        // 2. 查询仓内推广国家
        List<String> wareHouseCountry = migrationService.getWareHouseCountry();
        // 3. 查询用户入职日期
        List<Long> userIds = userInfoList
                .stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList());
        // 转换入职日期map
        Map<Long, AttendanceUserEntryRecord> userEntryMap = userEntryRecordService.selectUserEntryByUserIds(userIds)
                .stream()
                .collect(Collectors.toMap(AttendanceUserEntryRecord::getUserId,
                        Function.identity(), (a, b) -> a));

        for (UserInfoDO userInfo : userInfoList) {
            // 过滤掉不在管理国家范围内且是劳务派遣得人员
            if (CollectionUtils.isNotEmpty(osCountry)
                    && !osCountry.contains(userInfo.getLocationCountry())
                    && ObjectUtil.equal(userInfo.getEmployeeType(),
                    EmploymentTypeEnum.OS_FIXED_SALARY.getCode())) {
                continue;
            }
            // 过滤掉仓内推广国家劳务派遣、仓内人员
            if (CollectionUtils.isNotEmpty(wareHouseCountry)
                    && wareHouseCountry.contains(userInfo.getLocationCountry())
                    && ObjectUtil.equal(userInfo.getIsWarehouseStaff(), BusinessConstant.Y)
                    && ObjectUtil.equal(userInfo.getEmployeeType(),
                    EmploymentTypeEnum.OS_FIXED_SALARY.getCode())) {
                continue;
            }
            // 过滤掉未到入职日期人员
            AttendanceUserEntryRecord userEntryRecord = userEntryMap.get(userInfo.getId());
            Long dayIdLong = Long.valueOf(dayId);
            if (Objects.nonNull(userEntryRecord)
                    && Objects.nonNull(userEntryRecord.getConfirmDate())
                    && dayIdLong.compareTo(DateHelper.getDayId(userEntryRecord.getConfirmDate())) < 0) {
                continue;
            }
            filterUserList.add(userInfo);
        }
        return filterUserList;
    }

}
