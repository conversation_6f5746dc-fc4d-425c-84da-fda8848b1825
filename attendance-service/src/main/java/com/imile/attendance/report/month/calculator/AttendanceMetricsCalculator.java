package com.imile.attendance.report.month.calculator;

import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.form.model.UserCycleReissueCardCountDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.report.month.dto.UserDayMetricsDTO;
import com.imile.attendance.report.month.dto.UserMonthReportMetricsDTO;
import com.imile.attendance.util.DateHelper;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 考勤指标计算器
 * 负责计算月报中的各项考勤指标，包括应出勤天数、实际出勤天数、工作时长等
 *
 * <AUTHOR> chen
 * @date 2025/6/23
 */
@Component
@Slf4j
public class AttendanceMetricsCalculator {

    /**
     * 计算用户月度考勤指标
     *
     * @param userDayReportList   用户日报列表
     * @param punchClassConfigMap 班次配置映射
     * @return 考勤指标DTO
     */
    public UserMonthReportMetricsDTO calculateMonthlyMetrics(Long userId,
                                                             Long attendanceStartCycle,
                                                             Long attendanceEndCycle,
                                                             List<AttendanceDayReportDO> userDayReportList,
                                                             Map<Long, PunchConfigDO> punchConfigMap,
                                                             Map<Long, PunchClassConfigDO> punchClassConfigMap,
                                                             Map<Long, List<AttendanceDayReportFormDO>> dayReportFormMap,
                                                             Map<Long, List<UserCycleReissueCardCountDO>> userReissueCardCountMap,
                                                             Map<Long, List<UserShiftConfigDO>> userShiftConfigMap) {
        if (attendanceStartCycle == null || attendanceEndCycle == null ||
                userDayReportList == null || userDayReportList.isEmpty()) {
            return UserMonthReportMetricsDTO.empty();
        }

        // 计算排休天数和缺勤天数
        BigDecimal offDay = BigDecimal.ZERO;
        BigDecimal absentDays = BigDecimal.ZERO;

        for (AttendanceDayReportDO dayReport : userDayReportList) {
//            if (dayReport.areWorkDay()) {
//                shouldAttendanceDays = shouldAttendanceDays.add(BigDecimal.ONE);
//            }
            if (dayReport.areOffOrHDay()) {
                offDay = offDay.add(BigDecimal.ONE);
            }
            if (dayReport.getAbsentResult() == 1) {
                absentDays = absentDays.add(BigDecimal.ONE);
            }
        }

        // 计算应出勤天数
        List<UserShiftConfigDO> userShiftConfigDOList = userShiftConfigMap.getOrDefault(userId, Collections.emptyList());
        List<UserShiftConfigDO> workDayConfigList = userShiftConfigDOList.stream()
                .filter(UserShiftConfigDO::areWorkDay)
                .collect(Collectors.toList());

        BigDecimal shouldAttendanceDays = new BigDecimal(workDayConfigList.size());
        log.debug("计算应出勤天数和排休天数完成，出勤天数: {}, 排休天数: {}", shouldAttendanceDays, offDay);

        // 计算请假次数、外勤次数、补卡次数
        int leaveCount = 0;
        int oooCount = 0;
        for (AttendanceDayReportDO dayReport : userDayReportList) {
            // 请假次数,外勤次数,补卡次数
            List<AttendanceDayReportFormDO> dayReportFormDOList = dayReportFormMap.getOrDefault(dayReport.getId(), Collections.emptyList());
            if (CollectionUtils.isNotEmpty(dayReportFormDOList)) {
                for (AttendanceDayReportFormDO dayReportFormDO : dayReportFormDOList) {
                    if (FormTypeEnum.LEAVE.getCode().equals(dayReportFormDO.getFormType())) {
                        leaveCount++;
                    }
                    if (FormTypeEnum.OUT_OF_OFFICE.getCode().equals(dayReportFormDO.getFormType())) {
                        oooCount++;
                    }
                }
            }
        }

        // 计算实际出勤指标
        ActualAttendanceMetrics actualMetrics = calculateActualAttendanceMetrics(userDayReportList, punchConfigMap, punchClassConfigMap, dayReportFormMap);

        // 计算平均工时（实出勤总时长（不含休息）/实出勤天数）
        BigDecimal averageHours = calculateAverageHours(actualMetrics.getActualAttendanceMinutes(),
                actualMetrics.getActualAttendanceDays());

        // 设置补卡次数
        Integer reissueCardCount = calculateReissueCardCount(userId, attendanceStartCycle, attendanceEndCycle, userReissueCardCountMap);

        return UserMonthReportMetricsDTO.builder()
                .shouldAttendanceDays(shouldAttendanceDays)
                .offDay(offDay)
                .absentDays(absentDays)
                .actualAttendanceDays(actualMetrics.getActualAttendanceDays())
                .actualAttendanceHours(actualMetrics.getActualAttendanceHours())
                .workHours(actualMetrics.getWorkHours())
                .actualAttendanceAverageHours(averageHours)
                .leaveHours(actualMetrics.getLeaveHours())
                .oooHours(actualMetrics.getOooHours())
                .leaveCount(leaveCount)
                .oooCount(oooCount)
                .reissueCardFormCount(reissueCardCount)
                .delayHours(actualMetrics.getDelayHours())
                .reissueCardCount(reissueCardCount)
                .userDayMetricsDTOList(actualMetrics.getUserDayMetricsDTOList())
                .build();
    }

    /**
     * 计算补卡次数
     *
     * @param userId                  用户ID
     * @param attendanceStartCycle    考勤开始周期
     * @param attendanceEndCycle      考勤结束周期
     * @param userReissueCardCountMap 用户补卡次数映射
     * @return 补卡次数
     */
    private Integer calculateReissueCardCount(Long userId,
                                              Long attendanceStartCycle,
                                              Long attendanceEndCycle,
                                              Map<Long, List<UserCycleReissueCardCountDO>> userReissueCardCountMap) {
        if (MapUtils.isEmpty(userReissueCardCountMap)) {
            return 0;
        }
        List<UserCycleReissueCardCountDO> cycleReissueCardCountDOList = userReissueCardCountMap.getOrDefault(userId, Collections.emptyList());
        if (CollectionUtils.isEmpty(cycleReissueCardCountDOList)) {
            return 0;
        }
        String startCycleDate = DateHelper.formatPureDate(DateHelper.transferDayIdToDate(attendanceStartCycle));
        String endCycleDate = DateHelper.formatPureDate(DateHelper.transferDayIdToDate(attendanceEndCycle));
        UserCycleReissueCardCountDO userCycleReissueCardCountDO = cycleReissueCardCountDOList.stream()
                .filter(i -> DateHelper.formatPureDate(i.getCycleStartDate()).equals(startCycleDate) &&
                        DateHelper.formatPureDate(i.getCycleEndDate()).equals(endCycleDate))
                .findAny()
                .orElse(null);
        if (userCycleReissueCardCountDO == null) {
            return 0;
        }
        return userCycleReissueCardCountDO.getUsedReissueCardCount();
    }

    /**
     * 计算实际出勤指标
     * 包括实际出勤天数、实际出勤时长、工作时长、请假时长、外勤时长
     *
     * @param userDayReportList   用户日报列表
     * @param punchClassConfigMap 班次配置映射
     * @return 实际出勤指标
     */
    private ActualAttendanceMetrics calculateActualAttendanceMetrics(List<AttendanceDayReportDO> userDayReportList,
                                                                     Map<Long, PunchConfigDO> punchConfigMap,
                                                                     Map<Long, PunchClassConfigDO> punchClassConfigMap,
                                                                     Map<Long, List<AttendanceDayReportFormDO>> dayReportFormMap) {
        BigDecimal totalActualAttendanceDays = BigDecimal.ZERO;
        BigDecimal totalActualAttendanceMinutes = BigDecimal.ZERO;
        BigDecimal totalWorkMinutes = BigDecimal.ZERO;
        BigDecimal totalLeaveMinutes = BigDecimal.ZERO;
        BigDecimal totalOOOMinutes = BigDecimal.ZERO;
        BigDecimal totalDelayMinutes = BigDecimal.ZERO;
        List<UserDayMetricsDTO> userDayMetricsDTOList = new ArrayList<>();

        for (AttendanceDayReportDO dayReport : userDayReportList) {
            // 设置每日的法定工作时长(默认8小时，如果有班次则使用班次的法定工作时长)
            UserDayMetricsDTO userDayMetricsDTO = new UserDayMetricsDTO();
            userDayMetricsDTO.setDayId(dayReport.getDayId());
            userDayMetricsDTO.setDefaultLegalWorkingHours(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS);
            userDayMetricsDTOList.add(userDayMetricsDTO);

            Long punchClassConfigId = dayReport.getPunchClassConfigId();
            PunchClassConfigDO punchClassConfig = punchClassConfigMap.get(punchClassConfigId);

            Long punchConfigId = dayReport.getPunchConfigId();
            PunchConfigDO punchConfigDO = punchConfigMap.get(punchConfigId);
            // TODO 非休息日/节假日 看是否要加上实出勤天数、有效工时、实际工时
//            if (!DayShiftRuleEnum.isRestDay(dayReport.getDayShiftRule())) {
//
//            }
            if (punchClassConfig == null) {
                log.warn("班次配置不存在，跳过计算。用户ID: {}, 日期: {}, 班次ID: {}",
                        dayReport.getUserId(), dayReport.getDayId(), punchClassConfigId);
                continue;
            }

            // 班次总工作时长（小时）
            BigDecimal legalWorkingHours = (Objects.nonNull(punchConfigDO)
                    && PunchConfigTypeEnum.isFlexibleWorkTwice(punchConfigDO.getConfigType()))
                    ? punchConfigDO.getPunchTimeInterval() : punchClassConfig.getLegalWorkingHours();
            if (legalWorkingHours == null) {
                log.warn("班次总工作时长无效，跳过计算。班次ID: {}", punchClassConfigId);
                continue;
            }
            // 更新每日的法定工作时长
            userDayMetricsDTO.setDefaultLegalWorkingHours(legalWorkingHours);
            // 班次应出勤时长（小时）
            BigDecimal attendanceHours = punchClassConfig.getAttendanceHours();
            if (attendanceHours == null || attendanceHours.compareTo(BigDecimal.ZERO) <= 0) {
                log.warn("班次应出勤时长无效，跳过计算。班次ID: {}, 应出勤时长: {}",
                        punchClassConfigId, attendanceHours);
                continue;
            }

            // 实际出勤时长（分钟）
            BigDecimal actualAttendanceMinutes = dayReport.getActualAttendanceMinutes();
            if (actualAttendanceMinutes == null) {
                actualAttendanceMinutes = BigDecimal.ZERO;
            }

            // 工作时长(分钟)
            BigDecimal finalWorkMinutes = dayReport.getFinalWorkMinutes() == null ?
                    BigDecimal.ZERO : dayReport.getFinalWorkMinutes();

            // 实际出勤天数 = 最终工作时长 ÷ (班次总工作时长（小时） × 60)
            BigDecimal actualAttendanceDays = finalWorkMinutes.divide(
                    userDayMetricsDTO.getDefaultLegalWorkingHours().multiply(BusinessConstant.MINUTES),
                    2,
                    RoundingMode.HALF_UP
            );

            // 累加指标
            totalActualAttendanceDays = totalActualAttendanceDays.add(actualAttendanceDays);
            totalActualAttendanceMinutes = totalActualAttendanceMinutes.add(actualAttendanceMinutes);
            totalWorkMinutes = totalWorkMinutes.add(finalWorkMinutes);
            totalLeaveMinutes = totalLeaveMinutes.add(dayReport.getLeaveMinutes());
            totalOOOMinutes = totalOOOMinutes.add(dayReport.getOooMinutes());
            totalDelayMinutes = totalDelayMinutes.add(dayReport.getDelayMinutes());
        }

        // 转换为小时
        BigDecimal actualAttendanceHours = totalActualAttendanceMinutes.divide(
                BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        BigDecimal workHours = totalWorkMinutes.divide(
                BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        BigDecimal leaveHours = totalLeaveMinutes.divide(
                BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        BigDecimal oooHours = totalOOOMinutes.divide(
                BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);
        BigDecimal delayHours = totalDelayMinutes.divide(
                BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);

        log.debug("实际出勤指标计算完成。实际出勤天数: {}, 实际出勤时长: {}小时, 工作时长: {}小时",
                totalActualAttendanceDays, actualAttendanceHours, workHours);

        return new ActualAttendanceMetrics(
                totalActualAttendanceDays,
                actualAttendanceHours,
                workHours,
                totalActualAttendanceMinutes,
                leaveHours,
                oooHours,
                delayHours,
                userDayMetricsDTOList
        );
    }

    /**
     * 计算实出勤平均工时（h)
     *
     * @param totalMinutes 总时长（分钟）
     * @param totalDays    总天数
     * @return 平均工时（小时）
     */
    private BigDecimal calculateAverageHours(BigDecimal totalMinutes, BigDecimal totalDays) {
        if (totalDays == null || totalDays.compareTo(BigDecimal.ZERO) == 0) {
            log.debug("总天数为0，平均工时设为0");
            return BigDecimal.ZERO;
        }

        if (totalMinutes == null) {
            totalMinutes = BigDecimal.ZERO;
        }

        BigDecimal averageMinutes = totalMinutes.divide(totalDays, 2, RoundingMode.HALF_UP);
        BigDecimal averageHours = averageMinutes.divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP);

        log.debug("实出勤平均工时计算完成: {}小时", averageHours);
        return averageHours;
    }

    /**
     * 内部类，用于封装计算得出的实际出勤相关指标。
     */
    @Getter
    private static class ActualAttendanceMetrics {
        /**
         * 实际出勤天数
         * (工作总时长(不含休息)/班次要求总工作时长)
         */
        private final BigDecimal actualAttendanceDays;
        /**
         * 实际出勤小时数
         * 日报中每天的实际出勤时长累加。
         */
        private final BigDecimal actualAttendanceHours;
        /**
         * 工作总时长（不含休息）(h)
         * 日报中每天的工作出勤时长累加
         */
        private final BigDecimal workHours;
        /**
         * 实际出勤分钟数
         * 用于计算平均工时的原始数据。
         */
        private final BigDecimal actualAttendanceMinutes;
        /**
         * 请假小时数
         * 日报中每天请假时长累加
         */
        private final BigDecimal leaveHours;
        /**
         * 外勤（OOO）小时数
         * 日报中每天外勤时长累加
         */
        private final BigDecimal oooHours;
        /**
         * 迟到小时数
         * 日报中每天的迟到时长累加
         */
        private final BigDecimal delayHours;
        /**
         * 每天的法定工作时长
         */
        private final List<UserDayMetricsDTO> userDayMetricsDTOList;

        public ActualAttendanceMetrics(BigDecimal actualAttendanceDays,
                                       BigDecimal actualAttendanceHours,
                                       BigDecimal workHours,
                                       BigDecimal actualAttendanceMinutes,
                                       BigDecimal leaveHours,
                                       BigDecimal oooHours,
                                       BigDecimal delayHours,
                                       List<UserDayMetricsDTO> userDayMetricsDTOList) {
            this.actualAttendanceDays = actualAttendanceDays;
            this.actualAttendanceHours = actualAttendanceHours;
            this.workHours = workHours;
            this.actualAttendanceMinutes = actualAttendanceMinutes;
            this.leaveHours = leaveHours;
            this.oooHours = oooHours;
            this.delayHours = delayHours;
            this.userDayMetricsDTOList = userDayMetricsDTOList;
        }

    }
}
