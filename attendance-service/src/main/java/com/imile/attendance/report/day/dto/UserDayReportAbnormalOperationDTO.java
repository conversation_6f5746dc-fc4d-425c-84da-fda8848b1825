package com.imile.attendance.report.day.dto;

import lombok.Data;

/**
 * 员工日报详情异常操作记录VO
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
public class UserDayReportAbnormalOperationDTO {

    /**
     * 异常主键
     */
    private Long abnormalId;

    /**
     * 操作类型(请假/补卡/外勤/确认异常)
     * 详见com.imile.hrms.common.enums.approval.AttendanceAbnormalOperationTypeEnum
     */
    private String operationType;

    /**
     * 申请单据ID
     */
    private Long formId;

    /**
     * 原因说明
     */
    private String reason;
}
