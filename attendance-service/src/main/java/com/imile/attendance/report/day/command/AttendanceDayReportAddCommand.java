package com.imile.attendance.report.day.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/6/17
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceDayReportAddCommand {

    @ApiModelProperty(value = "考勤日期")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Long dayId;

    @ApiModelProperty(value = "员工主键")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Long userId;

    @ApiModelProperty(value = "员工姓名")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String userName;

    @ApiModelProperty(value = "员工帐号")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String userCode;

    @ApiModelProperty(value = "用工类型")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String employeeType;

    @ApiModelProperty(value = "部门ID")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Long deptId;

    @ApiModelProperty(value = "岗位ID")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Long postId;

    @ApiModelProperty(value = "常驻地国家")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String locationCountry;

    @ApiModelProperty(value = "常驻地省份")
    private String locationProvince;

    @ApiModelProperty(value = "常驻地城市")
    private String locationCity;

    @ApiModelProperty(value = "班次性质（FIXED_CLASS,MULTIPLE_CLASS）")
    private String classNature;

    @ApiModelProperty(value = "日历主键")
    private Long calendarId;

    @ApiModelProperty(value = "打卡规则主键")
    private Long punchConfigId;

    @ApiModelProperty(value = "打卡规则类型：1.免打卡 2.班次固定打卡 3灵活打卡一次 4.灵活打卡两次")
    private String punchConfigType;

    @ApiModelProperty(value = "班次计划 (OFF,H,CLASS,NO_CLASS)")
    private String dayShiftRule;

    @ApiModelProperty(value = "班次规则主键")
    private Long punchClassConfigId;

    @ApiModelProperty(value = "补卡规则主键")
    private Long reissueCardConfigId;

    @ApiModelProperty(value = "加班规则主键")
    private Long overTimeConfigId;

    @ApiModelProperty(value = "请假时长(分钟)")
    private BigDecimal leaveMinutes;

    @ApiModelProperty(value = "外勤时长(分钟)")
    private BigDecimal oooMinutes;

    @ApiModelProperty(value = "加班时长(分钟)")
    private BigDecimal overTimeMinutes;

    @ApiModelProperty(value = "初始考勤结果 0: 异常 1: 正常")
    private Integer initResult;

    @ApiModelProperty(value = "最终考勤结果 0: 异常 1: 正常")
    private Integer finalResult;

    @ApiModelProperty(value = "实际出勤时长(不含休息)-min")
    private BigDecimal actualAttendanceMinutes;

    @ApiModelProperty(value = "工时异常时长-min")
    private BigDecimal abnormalWorkMinutes;

    @ApiModelProperty(value = "最终工作时长(不含休息)-min")
    private BigDecimal finalWorkMinutes;

    @ApiModelProperty(value = "关联异常子表集合")
    private List<AttendanceDayReportAbnormalAddCommand> abnormalAddCommandList;

    @ApiModelProperty(value = "关联单据子表集合")
    private List<AttendanceDayReportFormAddCommand> formAddCommandList;
}
