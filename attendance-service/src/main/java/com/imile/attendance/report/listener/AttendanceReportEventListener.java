package com.imile.attendance.report.listener;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.imile.attendance.report.day.job.service.AttendanceDayReportJobService;
import com.imile.attendance.report.event.AttendanceReportRegisterEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2025/7/4
 * @Description
 */
@Slf4j
@Component
@Resource
public class AttendanceReportEventListener {

    @Resource
    private AttendanceDayReportJobService dayReportJobService;

    @Async("bizTaskThreadPool")
    @EventListener
    public void onApplicationEvent(AttendanceReportRegisterEvent event) {
        if (Objects.isNull(event.getData())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        List<DayReportJobParam> data = event.getData();
        long startTime = System.currentTimeMillis();
        log.info("AttendanceReportEventListener | startTime:{},param:{}",
                startTime, JSON.toJSON(data));
        // 生成/更新考勤日报
        dayReportJobService.attendanceBatchDayReportInit(data);
        log.info("AttendanceReportEventListener | 耗时:{},param:{}",
                System.currentTimeMillis() - startTime, JSON.toJSON(data));
    }
}
