package com.imile.attendance.report.month.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.report.month.dto.UserMonthAbnormalStatisticsDTO;
import com.imile.attendance.report.month.dto.UserMonthDayAttendanceDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/6/23 
 * @Description
 */
@Data
public class UserMonthReportListVO {

    /**
     * 员工id
     */
    private Long userId;

    /**
     * 员工姓名
     */
    private String userName;

    /**
     * 员工帐号
     */
    private String userCode;

    /**
     * 工作状态
     */
    private String workStatus;

    /**
     * 工作状态描述
     */
    private String workStatusDesc;

    /**
     * 账号状态
     */
    private String accountStatus;

    /**
     * 用工类型
     */
    @WithDict(typeCode = BusinessConstant.SysDictDataTypeConstant.EMPLOYMENT_TYPE, ref = "employeeTypeDesc")
    private String employeeType;

    /**
     * 用工类型描述
     */
    private String employeeTypeDesc;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 岗位id
     */
    private Long postId;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 常驻地
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 考勤月份
     */
    private String attendanceMonth;

    /**
     * 考勤周期
     */
    private String attendanceCycle;

    /**
     * 入职日期(详情有)
     */
    private String entryDate;

    /**
     * 离职日期(详情有)
     */
    private String dimissionDate;

    /**
     * 应出勤天数(员工当月日历的实际情况计算)
     */
    private BigDecimal shouldAttendanceDays;

    /**
     * 实出勤天数
     */
    private BigDecimal actualAttendanceDays;

    /**
     * 实出勤时长(不含休息)
     */
    private BigDecimal actualAttendanceHours;

    /**
     * 工作时长（不含休息）
     */
    private BigDecimal workHours;

    /**
     * 实际出勤平均工时
     */
    private BigDecimal actualAttendanceAverageHours;

    /**
     * 请假总时长
     */
    private BigDecimal leaveHours;

    /**
     * 外勤总时长
     */
    private BigDecimal oooHours;

    /**
     * 请假次数
     */
    private Integer leaveCount;

    /**
     * 外勤次数
     */
    private Integer oooCount;

    /**
     * 补卡次数
     */
    private Integer reissueCardFormCount;

    /**
     * 延时时长
     */
    private BigDecimal delayHours;

    /**
     * 当前考勤周期内已使用补卡次数
     */
    private Integer reissueCardCount;

    /**
     * 异常情况统计列表
     * 包含异常类型、异常次数、异常时间等信息
     */
    private List<UserMonthAbnormalStatisticsDTO> abnormalStatisticsList;


    // ===========导出用到的字段====================

    /**
     * 每日考勤情况
     */
    private List<UserMonthDayAttendanceDTO> dayAttendanceResults;

    /**
     * 排休天数（H和OFF）
     */
    private BigDecimal offDay;

    /**
     * 实际缺勤天数
     */
    private BigDecimal absentDays;


    // ===========内部字段====================

    /**
     * 周期起始时间
     */
    private Long attendanceStartCycle;

    /**
     * 周期截止时间
     */
    private Long attendanceEndCycle;

    /**
     * 周期开始
     */
    private String cycleStart;

    /**
     * 周期结束
     */
    private String cycleEnd;
}
