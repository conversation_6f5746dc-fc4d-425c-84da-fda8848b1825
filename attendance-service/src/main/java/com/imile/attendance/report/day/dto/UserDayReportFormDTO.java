package com.imile.attendance.report.day.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 员工日报单据详情DTO
 *
 * <AUTHOR>
 * @date 2025/6/20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDayReportFormDTO {
    /**
     * 日报主键
     */
    private Long dayReportId;

    /**
     * 单据id
     */
    private Long formId;

    /**
     * 单据编码
     */
    private String applicationCode;

    /**
     * 审批单id
     */
    private Long approvalId;

    /**
     * 单据类型
     */
    private String formType;

    /**
     * 单据状态
     */
    private String formStatus;

    /**
     * 请假/外勤 开始时间
     */
    private Date startDate;

    /**
     * 请假/外勤 结束时间
     */
    private Date endDate;

    /**
     * 补卡日期
     */
    private Long reissueCardDayId;
}
