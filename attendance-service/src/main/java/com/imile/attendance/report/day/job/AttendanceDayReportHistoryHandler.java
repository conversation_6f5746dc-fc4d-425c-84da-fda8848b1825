package com.imile.attendance.report.day.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.report.day.job.param.DayReportJobHistoryParam;
import com.imile.attendance.report.day.job.param.DayReportJobParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * 考勤日报定时任务
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/6/24
 */
@Slf4j
@Component
public class AttendanceDayReportHistoryHandler {

    @Resource
    AttendanceDayReportHandler attendanceDayReportHandler;

    @XxlJob(BusinessConstant.JobHandler.ATTENDANCE_DAY_REPORT_HISTORY_HANDLER)
    public ReturnT<String> attendanceDayReportHistoryHandler(String content) {
        DayReportJobHistoryParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, DayReportJobHistoryParam.class)
                : new DayReportJobHistoryParam();
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHistoryHandler】参数为空");
            return ReturnT.FAIL;
        }

        if (StringUtils.isBlank(param.getStartDay()) || StringUtils.isBlank(param.getEndDay())) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHistoryHandler】参数开始日期/结束日期未填写");
            return ReturnT.FAIL;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = null;
        LocalDate endDate = null;
        try {
            startDate = LocalDate.parse(param.getStartDay(), formatter);
            endDate = LocalDate.parse(param.getEndDay(), formatter);
        } catch (Exception e) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHistoryHandler】参数开始日期/结束日期格式有误");
            return ReturnT.FAIL;
        }
        // 遍历日期范围
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            String dayId = currentDate.format(formatter);
            XxlJobLogger.log("xxl-job【attendanceDayReportHistoryHandler】参数执行日期:{}" + dayId);
            DayReportJobParam dayReportJobParam = DayReportJobParam.builder()
                    .countryList(param.getCountryList())
                    .employeeType(param.getEmployeeType())
                    .userCodes(param.getUserCodes())
                    .localDate(dayId)
                    .isUseCustomLocalTime(true)
                    .build();
            attendanceDayReportHandler.attendanceDayReportHandler(JSON.toJSONString(dayReportJobParam));
            currentDate = currentDate.plusDays(1);  // 增加一天
        }
        return ReturnT.SUCCESS;
    }

    /**
     * 参数校验
     *
     * @param param
     * @return
     */
    private Boolean checkParam(DayReportJobParam param) {
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHandler】参数为空");
            return false;
        }

        if (param.getIsUseCustomLocalTime() && StringUtils.isBlank(param.getLocalDate())) {
            XxlJobLogger.log("xxl-job【attendanceDayReportHandler】参数自定义日期未填写");
            return false;
        }
        return true;
    }

    /**
     * 参数默认值设置
     *
     * @param param
     */
    private void buildDefaultParam(DayReportJobParam param) {
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            param.setUserCodeList(Arrays.asList(param.getUserCodes().split(",")));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            param.setCountryArrayList(Arrays.asList(param.getCountryList().split(",")));
        }
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            param.setEmployeeTypeList(Arrays.asList(param.getEmployeeType().split(",")));
        } else {
            param.setEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
        }
    }

}
