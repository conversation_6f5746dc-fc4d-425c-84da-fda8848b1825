package com.imile.attendance.report.day.dto;

import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 员工日报聚合DTO
 *
 * <AUTHOR>
 * @date 2025/6/9
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDayReportAggregationDTO {

    /**
     * 员工主键
     */
    private Long id;

    /**
     * 考勤日期
     */
    private Long dayId;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 帐号
     */
    private String userCode;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 部门主键
     */
    private Long deptId;

    /**
     * 岗位主键
     */
    private Long postId;

    /**
     * 常驻国
     */
    private String locationCountry;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 班次类型
     */
    private String classNature;

    /**
     * 员工当日日历
     */
    CalendarConfigDO calendarConfig;

    /**
     * 员工当日正常考勤
     */
    List<AttendanceEmployeeDetailDO> dayRecordList;

    /**
     * 员工当日异常考勤
     */
    List<EmployeeAbnormalAttendanceDO> dayAbnormalAttendanceList;

    /**
     * 员工当日打卡规则
     */
    PunchConfigDO punchConfig;

    /**
     * 员工当日补卡规则
     */
    ReissueCardConfigDO reissueCardConfig;

    /**
     * 员工当日加班规则
     */
    OverTimeConfigDO overTimeConfig;

    /**
     * 员工当日排班
     */
    UserShiftConfigDO dayShift;

    /**
     * 员工当日班次
     */
    List<PunchClassConfigDO> dayClassConfigList;

    List<PunchClassItemConfigDO> dayClassItemConfigDOList;

    /**
     * 员工当日班次（聚合）
     */
    List<PunchClassConfigDTO> dayClassConfigDTOList;

    /**
     * 员工当日请假单据
     */
    List<AttendanceFormDO> userLeaveForm;

    /**
     * 员工当日外勤单据
     */
    List<AttendanceFormDO> userOutOfOfficeForm;

    /**
     * 员工当日加班单据
     */
    List<OverTimeApprovalListDTO> userOverTimeForm;
}
