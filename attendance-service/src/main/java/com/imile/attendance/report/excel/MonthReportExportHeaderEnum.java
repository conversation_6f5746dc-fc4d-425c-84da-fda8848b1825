package com.imile.attendance.report.excel;

import com.imile.attendance.infrastructure.excel.header.ExcelHeaderBaseService;
import com.imile.attendance.infrastructure.excel.header.ExcelTitleExportDTO;
import lombok.Getter;

/**
 * 月度考勤报表导出Excel表头枚举
 * <p>
 * 该枚举定义了月度考勤报表导出时的所有表头字段，包括：
 * <ul>
 *   <li>员工基本信息：姓名、账号、工作状态等</li>
 *   <li>组织信息：部门、岗位、地理位置等</li>
 *   <li>时间信息：入职日期、离职日期、考勤周期等</li>
 *   <li>出勤统计：应出勤天数、实出勤天数、工作时长等</li>
 *   <li>异常统计：迟到、早退、缺卡、补卡等各类异常情况</li>
 *   <li>请假外勤：请假时长、外勤时长等</li>
 * </ul>
 * <p>
 * 每个枚举值包含英文标题和中文标题，支持国际化显示。
 * 在实际导出时，会根据用户语言环境自动选择对应的标题。
 *
 * <AUTHOR> chen
 * @date 2025/6/24
 */
@Getter
public enum MonthReportExportHeaderEnum implements ExcelHeaderBaseService {

    // ==================== 员工基本信息 ====================

    /** 员工姓名 - 显示员工的真实姓名 */
    EMPLOYEE_NAME("employeeName", "Employee Name", "姓名"),

    /** 员工账号 - 员工在系统中的唯一标识账号 */
    EMPLOYEE_ID("employeeId", "Employee ID", "账号"),

    /** 工作状态 - 员工当前的工作状态（如：在职、离职等） */
    WORK_STATUS("workStatus", "Work Status", "工作状态"),

    /** 账号状态 - 员工账号的状态（如：正常、冻结等） */
    ACCOUNT_STATUS("accountStatus", "Account Status", "账号状态"),

    /** 用工类型 - 员工的用工性质（如：正式员工、实习生、外包等） */
    EMPLOYEE_TYPE("employeeType", "Employee Type", "用工类型"),

    // ==================== 组织架构信息 ====================

    /** 部门名称 - 员工所属的部门 */
    DEPARTMENT_NAME("deptName", "Department Name", "部门"),

    /** 岗位名称 - 员工的具体岗位职务 */
    POST_NAME("postName", "Post Name", "岗位"),

    // ==================== 地理位置信息 ====================

    /** 常驻国家 - 员工工作的常驻国家 */
    LOCATION_COUNTRY("country", "Location Country", "常驻国"),

    /** 常驻省份 - 员工工作的常驻省份 */
    LOCATION_PROVINCE("locationProvince", "Location Province", "常驻省"),

    /** 常驻城市 - 员工工作的常驻城市 */
    LOCATION_CITY("locationCity", "Location City", "常驻市"),

    // ==================== 时间信息 ====================

    /** 入职日期 - 员工正式入职的日期 */
    ENTRY_DATE("entryDate", "Entry Date", "入职日期"),

    /** 离职日期 - 员工离职的日期（如果已离职） */
    DISMISSION_DATE("dimissionDate", "Dimission Date", "离职日期"),

    /** 考勤月份 - 当前统计的考勤月份 */
    ATTENDANCE_MONTH("attendanceMonth", "Attendance Month", "考勤月"),

    /** 考勤周期 - 具体的考勤周期范围（如：2025-06-01 至 2025-06-30） */
    ATTENDANCE_CYCLE("attendanceCycle", "Attendance Cycle", "考勤周期"),

    // ==================== 出勤统计相关字段 ====================

    /** 应出勤天数 - 根据工作日历计算的应该出勤的天数 */
    SHOULD_ATTENDANCE_DAYS("attendanceDays", "Should Attendance Days", "应出勤天数"),

    /** 实出勤天数 - 员工实际出勤的天数 */
    ACTUAL_ATTENDANCE_DAYS("present", "Actual Attendance Days", "实出勤天数"),

    /** 实出勤总时长 - 员工实际出勤的总时长，不包含休息时间，单位：小时 */
    ACTUAL_ATTENDANCE_TOTAL_HOURS("presentHour", "Actual Attendance Total Hours", "实际工时"),

    /** 工作总时长 - 员工的工作总时长，不包含休息时间，单位：小时 */
    WORK_TOTAL_HOURS("workTotalHours", "Work Total Hours", "有效工时"),

    /** 实出勤平均工时 - 员工实际出勤的平均每日工作时长，单位：小时 */
    ACTUAL_ATTENDANCE_AVERAGE_HOURS("actualAttendanceAverageHours", "Actual Attendance Average Hours", "平均工时"),

    // ==================== 延时工作相关字段 ====================

    /** 延时总时长 - 员工超出正常工作时间的延时工作总时长 */
    OVERTIME_TOTAL_HOURS("overtimeTotalHours", "Overtime Total Hours", "延时总时长"),

    // ==================== 请假相关字段 ====================

    /** 请假次数 - 员工在统计周期内的请假次数 */
    LEAVE_COUNT("leaveCount", "Leave Count", "请假次数"),

    /** 请假总时长 - 员工请假的总时长，单位：小时 */
    LEAVE_TOTAL_HOURS("leaveTotalHours", "Leave Total Hours", "请假总时长（h）"),

    // ==================== 外勤相关字段 ====================

    /** 外勤次数 - 员工外出办公的次数 */
    OUT_OF_OFFICE_COUNT("outOfOfficeCount", "Out of Office Count", "外勤次数"),

    /** 外勤总时长 - 员工外勤工作的总时长，单位：小时 */
    OUT_OF_OFFICE_TOTAL_HOURS("outOfOfficeHours", "Out of Office Total Hours", "外勤总时长（h）"),

    // ==================== 考勤异常相关字段 ====================

    /** 迟到次数 - 员工迟到的次数 */
    LATE_COUNT("lateCount", "Late Count", "迟到次数"),

    /** 迟到总时长 - 员工迟到的累计时长，单位：分钟 */
    LATE_TOTAL_MINUTES("lateTotalMinutes", "Late Total Minutes", "迟到总时长（min）"),

    /** 早退次数 - 员工早退的次数 */
    EARLY_LEAVE_COUNT("earlyLeaveCount", "Early Leave Count", "早退次数"),

    /** 早退总时长 - 员工早退的累计时长，单位：分钟 */
    EARLY_LEAVE_TOTAL_MINUTES("earlyLeaveTotalMinutes", "Early Leave Total Minutes", "早退总时长（min）"),

    /** 上班缺卡次数 - 员工上班时忘记打卡的次数 */
    PUNCH_IN_MISSING_COUNT("beforeOfficeLackCount", "Punch In Missing Count", "上班缺卡次数"),

    /** 下班缺卡次数 - 员工下班时忘记打卡的次数 */
    PUNCH_OUT_MISSING_COUNT("afterOfficeLackCount", "Punch Out Missing Count", "下班缺卡次数"),

    /** 未打卡次数 - 员工完全未打卡的次数 */
    NO_PUNCH_COUNT("noPunchCount", "No Punch Count", "未打卡次数"),

    /** 时长异常次数 - 工作时长异常（过长或过短）的次数 */
    DURATION_ABNORMAL_COUNT("abnormalDurationCount", "Duration Abnormal Count", "时长异常次数"),

    /** 补卡次数 - 员工申请补卡的次数 */
    REISSUE_CARD_COUNT("reissueCardCount", "Reissue Card Count", "补卡次数")

    ;

    /**
     * 英文标题
     * 用于英文环境下的Excel表头显示
     */
    private final String englishTitle;

    /**
     * 中文标题
     * 用于中文环境下的Excel表头显示
     */
    private final String chineseTitle;

    /**
     * 对应的字段名
     * 用于从数据库查询结果中获取对应的值
     */
    private final String titleField;


    MonthReportExportHeaderEnum(String titleField, String englishTitle, String chineseTitle) {
        this.titleField = titleField;
        this.englishTitle = englishTitle;
        this.chineseTitle = chineseTitle;
    }

    /**
     * 获取英文标题
     *
     * @return 英文标题
     */
    @Override
    public String getEnglishTitle() {
        return englishTitle;
    }

    /**
     * 获取中文标题
     *
     * @return 中文标题
     */
    @Override
    public String getChineseTitle() {
        return chineseTitle;
    }

    /**
     * 获取标题字段
     *
     * @return 标题字段（驼峰命名法）
     */
    public String getTitleField() {
        return titleField;
    }

    /**
     * 转换为导出DTO(使用titleField)
     */
    public ExcelTitleExportDTO toExportDTO(boolean isChinese) {
        return ExcelTitleExportDTO.of(
                getTitleField(),
                isChinese ? getChineseTitle() : getEnglishTitle()
        );
    }
}
