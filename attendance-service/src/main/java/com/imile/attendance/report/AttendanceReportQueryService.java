package com.imile.attendance.report;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.abnormal.AttendanceEmployeeDetailManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceManage;
import com.imile.attendance.abnormal.EmployeeAbnormalAttendanceService;
import com.imile.attendance.attendance.EmployeeDetailQueryService;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.clock.MobilePunchDetailQueryService;
import com.imile.attendance.clock.dto.MobilePunchCardRecordDTO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.form.ApplicationFormAttrKeyEnum;
import com.imile.attendance.enums.form.FormStatusEnum;
import com.imile.attendance.enums.form.FormTypeEnum;
import com.imile.attendance.form.AttendanceApprovalFormManage;
import com.imile.attendance.form.AttendanceFormManage;
import com.imile.attendance.form.bo.AttendanceFormDetailBO;
import com.imile.attendance.form.dto.AttendanceFormDTO;
import com.imile.attendance.infrastructure.repository.abnormal.dao.EmployeeAbnormalOperationRecordDao;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.AttendanceEmployeeDetailSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalAttendanceSnapshotDO;
import com.imile.attendance.infrastructure.repository.abnormal.model.EmployeeAbnormalOperationRecordDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendancePostService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendancePost;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dto.UserBaseInfoDTO;
import com.imile.attendance.infrastructure.repository.employee.dto.UserDTO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.form.dto.OverTimeApprovalListDTO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceApprovalFormUserInfoDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.form.query.ApplicationFormQuery;
import com.imile.attendance.infrastructure.repository.form.query.OverTimeListQuery;
import com.imile.attendance.infrastructure.repository.punch.model.EmployeePunchRecordDO;
import com.imile.attendance.infrastructure.repository.punch.query.EmployeePunchCardRecordQuery;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportAbnormalDao;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportFormDao;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportAbnormalDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportFormDO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.UserDayConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.OverTimeConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchConfigDO;
import com.imile.attendance.infrastructure.repository.rule.model.ReissueCardConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.punch.EmployeePunchRecordManage;
import com.imile.attendance.punch.EmployeePunchRecordService;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.report.day.dto.UserDayReportAbnormalDTO;
import com.imile.attendance.report.day.dto.UserDayReportAbnormalOperationDTO;
import com.imile.attendance.report.day.dto.UserDayReportClassItemDTO;
import com.imile.attendance.report.day.dto.UserDayReportDTO;
import com.imile.attendance.report.day.dto.UserDayReportExportDTO;
import com.imile.attendance.report.day.dto.UserDayReportFormDTO;
import com.imile.attendance.report.day.dto.UserDayReportListDTO;
import com.imile.attendance.report.day.dto.UserDayReportPunchRecordDTO;
import com.imile.attendance.report.dto.AttendanceReportAbnormalHandlerDTO;
import com.imile.attendance.report.mapstruct.AttendanceReportMapstruct;
import com.imile.attendance.report.month.dto.UserReportAggregationDTO;
import com.imile.attendance.rule.OverTimeConfigManage;
import com.imile.attendance.rule.PunchClassConfigManage;
import com.imile.attendance.rule.PunchConfigManage;
import com.imile.attendance.rule.ReissueCardConfigManage;
import com.imile.attendance.rule.mapstruct.PunchClassConfigApiMapstruct;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户日月包聚合查询
 *
 * <AUTHOR>
 * @menu 考勤日月报
 * @date 2025/6/9
 */
@Service
@Slf4j
public class AttendanceReportQueryService {
    @Resource
    private EmployeeDetailQueryService employeeDetailQueryService;
    @Resource
    private EmployeePunchRecordService employeePunchRecordService;
    @Resource
    private AttendanceFormManage formManage;
    @Resource
    private AttendanceApprovalFormManage overTimeFormManage;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private EmployeeAbnormalAttendanceManage employeeAbnormalAttendanceManage;
    @Resource
    private UserShiftConfigManage userShiftConfigManage;
    @Resource
    private PunchConfigManage punchConfigManage;
    @Resource
    private PunchClassConfigManage punchClassConfigManage;
    @Resource
    private ReissueCardConfigManage reissueCardConfigManage;
    @Resource
    private OverTimeConfigManage overTimeConfigManage;
    @Resource
    private AttendanceEmployeeDetailManage attendanceEmployeeDetailManage;
    @Resource
    private EmployeeAbnormalAttendanceManage abnormalAttendanceManage;
    @Resource
    private AttendanceDayReportAbnormalDao dayReportAbnormalDao;
    @Resource
    private EmployeeAbnormalOperationRecordDao employeeAbnormalOperationRecordDao;
    @Resource
    private AttendanceDayReportFormDao dayReportFormDao;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendancePostService postService;
    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private MobilePunchDetailQueryService mobilePunchDetailQueryService;
    @Resource
    private EmployeePunchRecordManage employeePunchRecordManage;
    @Resource
    private EmployeeAbnormalAttendanceService abnormalAttendanceService;

    /**
     * 查询用户报表
     *
     * @param userBaseInfo
     * @param year
     * @param month
     * @return
     */
    public UserReportAggregationDTO getUserReportDTO(UserBaseInfoDTO userBaseInfo,
                                                     Long year, Long month) {
        //传递月份 则按月查询
        Date monthTime = null;
        if (Objects.nonNull(month) && month > 0) {
            StringBuilder append = new StringBuilder(String.valueOf(year)).append("/").append(month).append("/").append("1");
            monthTime = DateUtil.parse(append, DateFormatterUtil.SLASH_YYYYMMDD);
        }
        // 1.查询员工考勤明细
        Long userId = userBaseInfo.getId();
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailList = employeeDetailQueryService.selectByYearAndMonth(userId, year, month);
        // 2.查询表单
        List<Long> formIdList = attendanceEmployeeDetailList.stream()
                .filter(item -> item.getFormId() != null)
                .map(item -> item.getFormId()).collect(Collectors.toList());
        List<AttendanceFormAttrDO> formAttrDOList = formManage.selectFormAttrByFormIdList(formIdList);
        // 3.查询日历
        List<CalendarConfigDetailDO> calendarDetailDOList = Objects.isNull(monthTime)
                ? calendarManage.getCalendarRangeRecords(userId, year, userBaseInfo.getLocationCountry())
                : calendarManage.getCalendarRangeRecords(userId, year, month, userBaseInfo.getLocationCountry());
        if (CollectionUtils.isEmpty(calendarDetailDOList)) {
            int yearInt = Math.toIntExact(year);
            throw BusinessException.get(ErrorCodeEnum.ATTENDANCE_CALENDAR_NOT_EXIST.getDesc(),
                    I18nUtils.getMessage(ErrorCodeEnum.ATTENDANCE_CALENDAR_NOT_EXIST.getDesc(), yearInt, yearInt));
        }
        //判断该考勤方案是默认的还是部门/个人的
        CalendarConfigDO calendarConfigDO = calendarManage.getActiveCalendarConfigById(calendarDetailDOList.get(0).getAttendanceConfigId());

        // 4.获取用户改年/月的所有的打卡记录
        Date date = DateUtil.parse(year.toString(), "yyyy");
        EmployeePunchCardRecordQuery employeePunchCardRecordQuery = EmployeePunchCardRecordQuery.builder()
                .userCode(userBaseInfo.getUserCode())
                .startTime(Objects.isNull(monthTime)
                        ? DateUtil.beginOfYear(date)
                        : DateUtil.offsetDay(DateUtil.beginOfMonth(monthTime), BusinessConstant.DEFAULT_OFFSET))
                .endTime(Objects.isNull(monthTime)
                        ? DateUtil.endOfYear(date)
                        : DateUtil.offsetDay(DateUtil.endOfMonth(monthTime), -BusinessConstant.DEFAULT_OFFSET))
                .build();
        List<EmployeePunchRecordDO> allPunchRecordList = employeePunchRecordService.listRecords(employeePunchCardRecordQuery);
        List<UserPunchRecordDTO> userPunchRecordDTOList = new ArrayList<>();
        for (EmployeePunchRecordDO employeePunchRecordDO : allPunchRecordList) {
            String punchTimeString = DateUtil.format(employeePunchRecordDO.getPunchTime(), "yyyy-MM-dd HH:mm");
            userPunchRecordDTOList.add(UserPunchRecordDTO
                    .builder()
                    .id(employeePunchRecordDO.getId())
                    .userCode(employeePunchRecordDO.getUserCode())
                    .formId(employeePunchRecordDO.getFormId())
                    .formatPunchTime(DateUtil.parse(punchTimeString + ":00", "yyyy-MM-dd HH:mm:ss"))
                    .build());
        }
        // 5.获取用户改年的所有审批通过的请假/外勤单据
        ApplicationFormQuery applicationFormQuery = ApplicationFormQuery.builder()
                .userId(userId)
                .statusList(FormStatusEnum.getAttendanceCodeList())
                .fromTypeList(FormTypeEnum.getAttendanceCodeList())
                .build();
        List<AttendanceFormDO> passAndInViewFormList = formManage.selectForm(applicationFormQuery);
        List<Long> passAndInViewFormIdList = passAndInViewFormList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<AttendanceFormAttrDO> passAndInViewFormAttrList = formManage.selectFormAttrByFormIdList(passAndInViewFormIdList);

        // 6.查询员工改年/月的所有异常(包含处理和未处理的)
        Long startDate = Objects.isNull(monthTime)
                ? Long.valueOf(DateUtil.format(DateUtil.beginOfYear(date), "yyyyMMdd"))
                : Long.valueOf(DateUtil.format(DateUtil.beginOfMonth(monthTime), "yyyyMMdd"));
        Long endDate = Objects.isNull(monthTime)
                ? Long.valueOf(DateUtil.format(DateUtil.endOfYear(date), "yyyyMMdd"))
                : Long.valueOf(DateUtil.format(DateUtil.endOfMonth(monthTime), "yyyyMMdd"));
        List<EmployeeAbnormalAttendanceDO> abnormalAttendanceDOList = employeeAbnormalAttendanceManage.selectAbnormalByUserId(userId, startDate, endDate);
        List<Long> abnormalIdList = abnormalAttendanceDOList.stream().map(item -> item.getId()).collect(Collectors.toList());
        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordDOList = employeeAbnormalAttendanceManage.selectRecordByAbnormalList(abnormalIdList);

        // 7.查询员工改年/月的所有排班，然后查出打卡规则（需要获取当天的法定工作时长）
        List<UserShiftConfigDO> userShiftConfigList = userShiftConfigManage.selectRecordByUserIdList(Arrays.asList(userId), startDate, endDate);
        List<Long> classIdList = userShiftConfigList.stream().filter(item -> item.getPunchClassConfigId() != null)
                .map(item -> item.getPunchClassConfigId()).collect(Collectors.toList());
        List<PunchClassConfigDO> classConfigList = punchClassConfigManage.selectByClassIds(classIdList);
        List<PunchClassItemConfigDO> classItemConfigList = punchClassConfigManage.selectClassItemByClassIds(classIdList);

        // 8.查询员工该年/月所有打卡规则
        List<UserDayConfigDTO> UserDayConfigDTOList = punchConfigManage.selectDayConfigByUserIdList(Arrays.asList(userId),
                Objects.isNull(monthTime) ? DateUtil.beginOfYear(date) : DateUtil.beginOfMonth(monthTime),
                Objects.isNull(monthTime) ? DateUtil.endOfYear(date) : DateUtil.endOfMonth(monthTime));

        return UserReportAggregationDTO.builder()
                .id(userId)
                .userCode(userBaseInfo.getUserCode())
                .userName(userBaseInfo.getUserName())
                .attendanceEmployeeDetailList(attendanceEmployeeDetailList)
                .formAttrList(formAttrDOList)
                .passAndInViewFormList(passAndInViewFormList)
                .passAndInViewFormAttrList(passAndInViewFormAttrList)
                .calendarConfig(calendarConfigDO)
                .calendarConfigDetailList(calendarDetailDOList)
                .userPunchRecordList(userPunchRecordDTOList)
                .abnormalAttendanceList(abnormalAttendanceDOList)
                .abnormalOperationRecordList(abnormalOperationRecordDOList)
                .userShiftConfigList(userShiftConfigList)
                .classConfigList(classConfigList)
                .classItemConfigList(classItemConfigList)
                .userDayConfigDTOList(UserDayConfigDTOList)
                .build();
    }

    /**
     * 查询用户日报集合
     *
     * @param userInfo
     * @param dayId
     * @return
     */
    public List<AttendanceReportAbnormalHandlerDTO> getUserReportDTOList(List<UserInfoDO> userInfo,
                                                                         Long dayId) {
        List<AttendanceReportAbnormalHandlerDTO> userReportAggregationList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(userInfo) || Objects.isNull(dayId)) {
            return userReportAggregationList;
        }
        // 1.查询员工考勤明细
        List<Long> userIds = userInfo.stream().map(UserInfoDO::getId).collect(Collectors.toList());
        List<AttendanceEmployeeDetailDO> attendanceEmployeeDetailList = employeeDetailQueryService.selectByUserId(userIds, dayId);
        Map<Long, List<AttendanceEmployeeDetailDO>> userDetailMap = attendanceEmployeeDetailList.stream().collect(Collectors.groupingBy(AttendanceEmployeeDetailDO::getUserId));

        // 2.查询日历
        Date finalDateNow = DateUtil.endOfDay(DateUtil.parse(dayId.toString(), DatePattern.PURE_DATE_PATTERN));
        Map<Long, List<CalendarConfigDO>> userCalendarConfigMap = calendarManage.mapByUserIds(userIds, finalDateNow);

        // 3.查询打卡规则
        Map<Long, PunchConfigDO> punchConfigMap = punchConfigManage.mapByUserIds(userIds, finalDateNow);

        // 4.查询用户排班
        Map<Long, List<UserShiftConfigDO>> userShiftConfigMap = userShiftConfigManage.getConfigByUserIdsAndDayId(userIds, dayId);

        // 5.查询用户班次规则
        List<Long> punchClassIds = userShiftConfigMap.values()
                .stream()
                .flatMap(List::stream)
                .filter(item -> Objects.nonNull(item.getPunchClassConfigId()))
                .map(item -> item.getPunchClassConfigId())
                .distinct()
                .collect(Collectors.toList());
        Map<Long, PunchClassConfigDTO> punchClassConfigMap = punchClassConfigManage.selectByIds(punchClassIds);

        // 6.获取用户所有审批通过的请假/外勤单据
        List<AttendanceFormDetailBO> attendanceFormDetailBOList = formManage.listByUserIds(userIds,
                Collections.singletonList(FormStatusEnum.PASS.getCode()),
                FormTypeEnum.getLeaveAndOutOfOfficeCodeList());
        // 获取时间范围内有效的审批单据
        List<AttendanceFormDO> effectFormList = formManage.getEffectFormList(dayId, dayId, attendanceFormDetailBOList);
        List<Long> effectFormIdList = effectFormList.stream().map(AttendanceFormDO::getId).collect(Collectors.toList());
        List<AttendanceFormDetailBO> passFormBOList = attendanceFormDetailBOList.stream()
                .filter(formDetail -> Objects.nonNull(formDetail.getFormDO())
                        && effectFormIdList.contains(formDetail.getFormDO().getId()))
                .collect(Collectors.toList());
//        Map<Long, List<AttendanceFormDO>> userLeaveFormMap = effectFormList.stream()
//                .filter(item -> FormTypeEnum.LEAVE.getCode().equals(item.getFormType()))
//                .collect(Collectors.groupingBy(AttendanceFormDO::getUserId));
//        Map<Long, List<AttendanceFormDO>> userOutOfOfficeFormMap = effectFormList.stream()
//                .filter(item -> FormTypeEnum.OUT_OF_OFFICE.getCode().equals(item.getFormType()))
//                .collect(Collectors.groupingBy(AttendanceFormDO::getUserId));
//
//        // 7.查询用户当日加班单据
//        List<String> userCodes = userInfo.stream().map(UserInfoDO::getUserCode).collect(Collectors.toList());
//        OverTimeListQuery overTimeQuery = OverTimeListQuery.builder()
//                .userCodeList(userCodes)
//                .formTypeList(Collections.singletonList(FormTypeEnum.OVER_TIME.getCode()))
//                .dayId(dayId)
//                .formStatus(FormStatusEnum.PASS.getCode())
//                .build();
//        Map<String, List<OverTimeApprovalListDTO>> userOverTimeMap = overTimeFormManage.selectListByCondition(overTimeQuery)
//                .stream().collect(Collectors.groupingBy(OverTimeApprovalListDTO::getUserCode));
//
//      // 8.查询考勤当天的异常数据表
        Map<Long, List<EmployeeAbnormalAttendanceDO>> userAbnormalMap = abnormalAttendanceManage.mapAllByUserIdsAndDayIds(userIds, Collections.singletonList(dayId));

        // 9.查询考勤当天得正常快照数据表
        Map<Long, List<AttendanceEmployeeDetailSnapshotDO>> userSnapShotDetailMap = attendanceEmployeeDetailManage.mapSnapShotByUserIdsAndDayIds(userIds, Collections.singletonList(dayId));

        // 10.查询考勤当天得异常快照数据表
        Map<Long, List<EmployeeAbnormalAttendanceSnapshotDO>> userSnapShotAbnormalMap = abnormalAttendanceManage.mapSnapShotByUserIdsAndDayIds(userIds, Collections.singletonList(dayId));

        // 11.查询用户考勤当天补卡规则
        Map<Long, ReissueCardConfigDO> userReissueCardMap = reissueCardConfigManage.mapByUserIds(userIds, finalDateNow);

        // 12.查询用户考勤当天加班规则
        Map<Long, OverTimeConfigDO> userOverTimeConfigMap = overTimeConfigManage.mapByUserIds(userIds, finalDateNow);

        // 13. 查询用户打卡记录
        Long startDayId = DateHelper.getPreviousDayId(dayId);
        Long endDayId = DateHelper.getNextDayId(dayId);
        List<String> userCodes = userInfo.stream().map(UserInfoDO::getUserCode).collect(Collectors.toList());
        Map<String, List<UserPunchRecordBO>> userPunchCardGroup = employeePunchRecordManage.mapByUserCodesAndTimeRange(DateHelper.beginOfDay(DateHelper.transferDayIdToDate(startDayId))
                , DateHelper.endOfDay(DateHelper.transferDayIdToDate(endDayId)), userCodes);

        // 14. 封装聚合对象
        for (UserInfoDO user : userInfo) {
            Long userId = user.getId();
            String userCode = user.getUserCode();
            List<CalendarConfigDO> userCalendarConfig = userCalendarConfigMap.get(userId);
            List<UserShiftConfigDO> userShiftConfigList = userShiftConfigMap.get(userId);
            if (Objects.isNull(userId) || StringUtils.isBlank(userCode)) {
                continue;
            }
            UserShiftConfigDO userShiftConfig = CollectionUtils.isEmpty(userShiftConfigList)
                    ? null : userShiftConfigList.get(0);
            // 查询用户排班对应班次
            PunchClassConfigDTO punchClassConfigDTO = null;
            if (Objects.nonNull(userShiftConfig)
                    && Objects.nonNull(userShiftConfig.getPunchClassConfigId())) {
                punchClassConfigDTO = punchClassConfigMap.get(userShiftConfig.getPunchClassConfigId());
            }
            // 查询用户对应单据
            List<AttendanceFormDetailBO> userPassFormBOList = passFormBOList.stream().filter(item -> Objects.nonNull(item.getFormDO())
                    && userId.equals(item.getFormDO().getUserId())).collect(Collectors.toList());
            userReportAggregationList.add(AttendanceReportAbnormalHandlerDTO.builder()
                    .attendanceDayId(dayId)
                    .userInfoDO(user)
                    .calendarConfigDO(CollectionUtils.isEmpty(userCalendarConfig)
                            ? null : userCalendarConfig.get(0))
                    .punchConfigDO(punchConfigMap.get(userId))
                    .userShiftConfigDO(userShiftConfig)
                    .punchClassConfigDTO(punchClassConfigDTO)
                    .reissueCardConfigDO(userReissueCardMap.get(userId))
                    .overTimeConfigDO(userOverTimeConfigMap.get(userId))
                    .attendanceEmployeeDetailDOList(userDetailMap.getOrDefault(userId, Collections.emptyList()))
                    .employeeAbnormalAttendanceDOList(userAbnormalMap.getOrDefault(userId, Collections.emptyList()))
                    .attendanceEmployeeDetailSnapshotDOList(userSnapShotDetailMap.getOrDefault(userId, Collections.emptyList()))
                    .employeeAbnormalAttendanceSnapshotDOList(userSnapShotAbnormalMap.getOrDefault(userId, Collections.emptyList()))
                    .punchRecordDOList(userPunchCardGroup.getOrDefault(user.getUserCode(), Collections.emptyList()))
                    .userPassFormBOList(userPassFormBOList)
                    .build());
        }
        return userReportAggregationList;
    }

    /**
     * 查询用户日报列表聚合信息
     *
     * @param dayReportDOList
     * @return
     */
    public List<UserDayReportListDTO> getDayReportListDTO(List<AttendanceDayReportDO> dayReportDOList) {
        List<UserDayReportListDTO> dayReportListDTO = AttendanceReportMapstruct.INSTANCE.toDayReportListDTO(dayReportDOList);
        // 1.批量查询当日部门名称
        List<Long> deptIds = dayReportListDTO.stream()
                .filter(item -> Objects.nonNull(item.getDeptId()))
                .map(item -> item.getDeptId()).distinct().collect(Collectors.toList());
        Map<Long, List<AttendanceDept>> deptMap = Optional.ofNullable(deptService.listByDeptIds(deptIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(AttendanceDept::getId)))
                .orElse(Collections.emptyMap());
        // 2.批量查询当日岗位名称
        List<Long> postIds = dayReportListDTO.stream()
                .filter(item -> Objects.nonNull(item.getPostId()))
                .map(item -> item.getPostId()).distinct().collect(Collectors.toList());
        Map<Long, List<AttendancePost>> postMap = Optional.ofNullable(postService.listByPostList(postIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(AttendancePost::getId)))
                .orElse(Collections.emptyMap());
        // 3.查询当日班次
        List<Long> classIds = dayReportListDTO.stream().map(item -> item.getPunchClassConfigId()).distinct().collect(Collectors.toList());
        Map<Long, PunchClassConfigDTO> classMap = punchClassConfigManage.selectByIds(classIds);
        for (UserDayReportListDTO userDayReportListDTO : dayReportListDTO) {
            Long deptId = userDayReportListDTO.getDeptId();
            Long postId = userDayReportListDTO.getPostId();
            Long classId = userDayReportListDTO.getPunchClassConfigId();
            if (Objects.nonNull(deptId) && CollectionUtils.isNotEmpty(deptMap.get(deptId))) {
                userDayReportListDTO.setDeptName(RequestInfoHolder.isChinese()
                        ? deptMap.get(deptId).get(0).getDeptNameCn()
                        : deptMap.get(deptId).get(0).getDeptNameEn());
            }
            if (Objects.nonNull(postId) && CollectionUtils.isNotEmpty(postMap.get(postId))) {
                userDayReportListDTO.setPostName(RequestInfoHolder.isChinese()
                        ? postMap.get(postId).get(0).getPostNameCn()
                        : postMap.get(postId).get(0).getPostNameEn());
            }
            if (Objects.nonNull(classId) && Objects.nonNull(classMap.get(classId))) {
                userDayReportListDTO.setClassName(classMap.get(classId).getClassName());
            }
            // 列表初始结果为空，则最终结果也不展示
            if (Objects.isNull(userDayReportListDTO.getInitResult())) {
                userDayReportListDTO.setFinalResult(userDayReportListDTO.getInitResult());
            }
        }
        return dayReportListDTO;
    }

    /**
     * 查询用户日报详情聚合信息
     *
     * @param dayReportDO
     * @return
     */
    public UserDayReportDTO getDayReportDetail(AttendanceDayReportDO dayReportDO,
                                               Date dateTime) {
        UserDayReportDTO dayReportDTO = AttendanceReportMapstruct.INSTANCE.toDayReportDTO(dayReportDO);
        dayReportDTO.setDate(DateHelper.dayIdFormat(dayReportDO.getDayId()));
        // 查询员工信息
        UserDTO userDTO = Optional.ofNullable(userInfoDao.getUserByCode(dayReportDTO.getUserCode()))
                .orElseThrow(() -> BusinessException.get(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getCode()
                        , I18nUtils.getMessage(ErrorCodeEnum.ACCOUNT_NOT_EXITS.getDesc())));
        dayReportDTO.setEntryDate(userDTO.getEntryDate());
        dayReportDTO.setDimissionDate(userDTO.getActualDimissionDate());
        dayReportDTO.setStatus(userDTO.getStatus());
        dayReportDTO.setWorkStatus(userDTO.getWorkStatus());
        dayReportDTO.setIsDriver(userDTO.getIsDriver());
        dayReportDTO.setIsGlobalRelocation(userDTO.getIsGlobalRelocation());
        // 查询当日部门名称
        if (Objects.nonNull(dayReportDO.getDeptId())) {
            AttendanceDept deptInfo = deptService.getByDeptId(dayReportDO.getDeptId());
            dayReportDTO.setDeptName(Objects.isNull(deptInfo)
                    ? "" : RequestInfoHolder.isChinese()
                    ? deptInfo.getDeptNameCn() : deptInfo.getDeptNameEn());
        }
        // 查询当日岗位名称
        if (Objects.nonNull(dayReportDO.getPostId())) {
            AttendancePost postInfo = postService.getByPostId(dayReportDO.getPostId());
            dayReportDTO.setPostName(Objects.isNull(postInfo)
                    ? "" : RequestInfoHolder.isChinese()
                    ? postInfo.getPostNameCn() : postInfo.getPostNameEn());
        }
        // 查询打卡规则
        if (Objects.nonNull(dayReportDO.getPunchConfigId())) {
            PunchConfigDO punchConfig = punchConfigManage.getPunchConfigById(dayReportDO.getPunchConfigId());
            dayReportDTO.setPunchConfigId(punchConfig.getId());
            dayReportDTO.setPunchConfigName(Objects.isNull(punchConfig)
                    ? "" : punchConfig.getConfigName());
            dayReportDTO.setPunchConfigType(punchConfig.getConfigType());
        }
        // 查询补卡规则
        if (Objects.nonNull(dayReportDO.getReissueCardConfigId())) {
            ReissueCardConfigDO reissueCardConfig = reissueCardConfigManage.getReissueCardConfigById(dayReportDO.getReissueCardConfigId());
            dayReportDTO.setReissueConfigId(reissueCardConfig.getId());
            dayReportDTO.setReissueConfigName(Objects.isNull(reissueCardConfig)
                    ? "" : reissueCardConfig.getConfigName());
        }
        // 查询加班规则
        if (Objects.nonNull(dayReportDO.getOverTimeConfigId())) {
            OverTimeConfigDO overTimeConfig = overTimeConfigManage.getOverTimeConfigById(dayReportDO.getOverTimeConfigId());
            dayReportDTO.setOverTimeConfigId(overTimeConfig.getId());
            dayReportDTO.setOverTimeConfigName(Objects.isNull(overTimeConfig)
                    ? "" : overTimeConfig.getConfigName());
        }
        // 查询日历规则
        if (Objects.nonNull(dayReportDO.getCalendarId())) {
            CalendarConfigDO calendarConfig = calendarManage.getCalendarConfigById(dayReportDO.getCalendarId());
            dayReportDTO.setCalendarConfigId(calendarConfig.getId());
            dayReportDTO.setCalendarConfigName(Objects.isNull(calendarConfig)
                    ? "" : calendarConfig.getAttendanceConfigName());
        }
        // 查询班次及班次时段下的打卡记录
        Long punchClassConfigId = dayReportDO.getPunchClassConfigId();
        List<PunchClassItemConfigDO> classItemConfigList = new ArrayList<>();
        if (Objects.isNull(punchClassConfigId)) {
            // 获取用户打卡记录
            List<EmployeePunchRecordDO> userPunchRecords = employeePunchRecordManage.getUserPunchRecords(
                    dayReportDO.getUserCode(), Collections.singletonList(dayReportDO.getDayId()));
            List<UserDayReportPunchRecordDTO> userDayReportPunchRecordDTO = AttendanceReportMapstruct.INSTANCE.toUserDayReportPunchRecordDTOByDO(userPunchRecords);
            dayReportDTO.setPunchCardRecordDTO(userDayReportPunchRecordDTO);
        } else {
            PunchClassConfigDO punchClassConfig = punchClassConfigManage.getPunchClassConfigById(punchClassConfigId);
            classItemConfigList =
                    punchClassConfigManage.selectClassItemByClassIds(Collections.singletonList(punchClassConfigId));
            dayReportDTO.setClassName(Objects.isNull(punchClassConfig)
                    ? "" : punchClassConfig.getClassName());
            // 查询当日打卡记录 转换班次时段时间为具体的年月日+时分秒
            punchClassConfigQueryService.transferItemConfigTimeFormat(classItemConfigList, dayReportDO.getDayId());
            List<UserDayReportClassItemDTO> dayReportClassItem = AttendanceReportMapstruct.INSTANCE.toDayReportClassItem(classItemConfigList);
            dayReportDTO.setUserDayReportClassItemList(dayReportClassItem);
            // 转转班次时间配置
            List<PunchClassItemConfigDTO> punchClassItemConfigDTOList =
                    PunchClassConfigApiMapstruct.INSTANCE.toPunchClassItemConfigDTO(classItemConfigList);
            List<MobilePunchCardRecordDTO> punchCardRecordDTO = mobilePunchDetailQueryService.getPunchCardRecordDTOByClass(dayReportDTO.getUserCode()
                    , Collections.singletonList(dayReportDO.getDayId())
                    , punchClassConfig.getId(), punchClassItemConfigDTOList);
            List<UserDayReportPunchRecordDTO> userDayReportPunchRecordDTO = AttendanceReportMapstruct.INSTANCE.toUserDayReportPunchRecordDTO(punchCardRecordDTO);
            dayReportDTO.setPunchCardRecordDTO(userDayReportPunchRecordDTO);
        }
        // 查询当日异常记录
        List<UserDayReportAbnormalDTO> abnormalDTOList = this.selectDayReportAbnormalList(Collections.singletonList(dayReportDO.getId()));
        // 过滤异常
        List<EmployeeAbnormalAttendanceDO> abnormalDOList = AttendanceReportMapstruct.INSTANCE.toAbnormalDOByAbnormalDTO(abnormalDTOList);
        abnormalDOList = abnormalAttendanceService.filterAbnormalAttendance(abnormalDOList, classItemConfigList, dateTime);
        if (CollectionUtils.isEmpty(abnormalDOList)) {
            dayReportDTO.setAbnormalDTO(Collections.emptyList());
        } else {
            List<Long> abnormalIds = abnormalDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
            abnormalDTOList = abnormalDTOList.stream()
                    .filter(item -> abnormalIds.contains(item.getAbnormalId()))
                    .collect(Collectors.toList());
            dayReportDTO.setAbnormalDTO(abnormalDTOList);
        }

        // 查询当日单据信息
        List<AttendanceFormDTO> effectForm = formManage.getEffectForm(dayReportDO.getUserId(), dayReportDO.getDayId());
        List<UserDayReportFormDTO> dayReportFormList = AttendanceReportMapstruct.INSTANCE.toUserDayReportFormDTOByForm(effectForm, dayReportDO.getId());
        OverTimeListQuery overTimeQuery = OverTimeListQuery.builder()
                .userCode(dayReportDO.getUserCode())
                .dayId(dayReportDO.getDayId())
                .formStatusList(FormStatusEnum.getAttendanceCodeList())
                .build();
        List<OverTimeApprovalListDTO> overTimeForm = overTimeFormManage.selectListByCondition(overTimeQuery);
        List<UserDayReportFormDTO> userOverTimeForm = AttendanceReportMapstruct.INSTANCE.toUserDayReportFormDTOByOverTimeForm(overTimeForm, dayReportDO.getId());
        dayReportFormList.addAll(userOverTimeForm);
        dayReportDTO.setFormDTO(dayReportFormList);
//        List<AttendanceDayReportFormDO> dayReportFormList = dayReportFormDao.selectByReportId(dayReportDO.getId());
//        if (CollectionUtils.isNotEmpty(dayReportFormList)) {
//            // 查询单据详情
//            List<UserDayReportFormDTO> userDayReportFormDTOList = AttendanceReportMapstruct.INSTANCE.toUserDayReportFormDTO(dayReportFormList);
//            List<Long> formIds = dayReportFormList.stream().map(AttendanceDayReportFormDO::getFormId).collect(Collectors.toList());
//            this.buildFormAttrInfo(formIds, userDayReportFormDTOList);
//            dayReportDTO.setFormDTO(userDayReportFormDTOList);
//        }
        dayReportDTO.setLeaveHours(Objects.isNull(dayReportDO.getLeaveMinutes()) ? null
                : dayReportDO.getLeaveMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        dayReportDTO.setOutOfOfficeHours(Objects.isNull(dayReportDO.getOooMinutes()) ? null
                : dayReportDO.getOooMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        dayReportDTO.setOverTimeHours(Objects.isNull(dayReportDO.getOverTimeMinutes()) ? null
                : dayReportDO.getOverTimeMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        dayReportDTO.setActualAttendanceHours(Objects.isNull(dayReportDO.getActualAttendanceMinutes()) ? null
                : dayReportDO.getActualAttendanceMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        dayReportDTO.setFinalWorkHours(Objects.isNull(dayReportDO.getFinalWorkMinutes()) ? null
                : dayReportDO.getFinalWorkMinutes().divide(BusinessConstant.MINUTES, 2, RoundingMode.HALF_UP));
        return dayReportDTO;
    }

    /**
     * 查询用户日报导出聚合信息
     *
     * @param dayReportList
     * @return
     */
    public List<UserDayReportExportDTO> getDayReportExportDetail(List<AttendanceDayReportDO> dayReportList,
                                                                 Date dateTime) {
        List<UserDayReportExportDTO> userDayReportExportDTOList = new ArrayList<>();
        // 1.批量查询员工信息（含入离职信息）
        List<String> userCodes = dayReportList.stream().filter(item -> StringUtils.isNotBlank(item.getUserCode()))
                .map(AttendanceDayReportDO::getUserCode).distinct().collect(Collectors.toList());
        List<UserDTO> userByCodes = userInfoDao.getUserByCodes(userCodes);
        Map<Long, List<UserDTO>> userMap = Optional.ofNullable(userByCodes)
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(UserDTO::getId)))
                .orElse(Collections.emptyMap());
        // 2.批量查询当日部门名称
        List<Long> deptIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getDeptId()))
                .map(AttendanceDayReportDO::getDeptId).distinct().collect(Collectors.toList());
        Map<Long, List<AttendanceDept>> deptMap = Optional.ofNullable(deptService.listByDeptIds(deptIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(AttendanceDept::getId)))
                .orElse(Collections.emptyMap());
        // 3.批量查询当日岗位名称
        List<Long> postIds = dayReportList
                .stream().filter(item -> Objects.nonNull(item.getPostId()))
                .map(AttendanceDayReportDO::getPostId).distinct().collect(Collectors.toList());
        Map<Long, List<AttendancePost>> postMap = Optional.ofNullable(postService.listByPostList(postIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(AttendancePost::getId)))
                .orElse(Collections.emptyMap());
        // 4.批量查询当日打卡规则
        List<Long> punchConfigIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getPunchConfigId()))
                .map(AttendanceDayReportDO::getPunchConfigId).distinct().collect(Collectors.toList());
        Map<Long, List<PunchConfigDO>> punchConfigMap = Optional.ofNullable(punchConfigManage.getPunchConfigByIds(punchConfigIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(PunchConfigDO::getId)))
                .orElse(Collections.emptyMap());
        // 5.批量查询当日补卡规则
        List<Long> reissueCardConfigIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getReissueCardConfigId()))
                .map(AttendanceDayReportDO::getReissueCardConfigId).distinct().collect(Collectors.toList());
        Map<Long, List<ReissueCardConfigDO>> reissueCardConfigMap = Optional.ofNullable(reissueCardConfigManage.getReissueCardConfigByIds(reissueCardConfigIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(ReissueCardConfigDO::getId)))
                .orElse(Collections.emptyMap());
        // 6.批量查询当日加班规则
        List<Long> overTimeConfigIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getOverTimeConfigId()))
                .map(AttendanceDayReportDO::getOverTimeConfigId).distinct().collect(Collectors.toList());
        Map<Long, List<OverTimeConfigDO>> overTimeConfigMap = Optional.ofNullable(overTimeConfigManage.getOverTimeConfigByIds(overTimeConfigIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(OverTimeConfigDO::getId)))
                .orElse(Collections.emptyMap());
        // 7.批量查询当日日历规则
        List<Long> calendarIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getCalendarId()))
                .map(AttendanceDayReportDO::getCalendarId).distinct().collect(Collectors.toList());
        Map<Long, List<CalendarConfigDO>> calendarConfigMap = Optional.ofNullable(calendarManage.getByCalendarConfigIds(calendarIds))
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(CalendarConfigDO::getId)))
                .orElse(Collections.emptyMap());
        // 8.批量查询当日班次
        List<Long> classIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getPunchClassConfigId()))
                .map(AttendanceDayReportDO::getPunchClassConfigId).distinct().collect(Collectors.toList());
        Map<Long, PunchClassConfigDTO> classMap = punchClassConfigManage.selectByIds(classIds);
        // 9.批量查询当日异常及操作记录
        List<Long> reportIds = dayReportList.stream().filter(item -> Objects.nonNull(item.getId()))
                .map(AttendanceDayReportDO::getId).distinct().collect(Collectors.toList());
        List<UserDayReportAbnormalDTO> abnormalDTOList = this.selectDayReportAbnormalList(reportIds);
        Map<Long, List<UserDayReportAbnormalDTO>> abnormalMap = Optional.ofNullable(abnormalDTOList)
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(UserDayReportAbnormalDTO::getDayReportId)))
                .orElse(Collections.emptyMap());
        // 10.查询当日单据信息
        List<AttendanceDayReportFormDO> dayReportFormList = dayReportFormDao.selectByReportIds(reportIds);
        List<UserDayReportFormDTO> userDayReportFormDTO = AttendanceReportMapstruct.INSTANCE.toUserDayReportFormDTO(dayReportFormList);
        Map<Long, List<UserDayReportFormDTO>> formMap = Optional.ofNullable(userDayReportFormDTO)
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(UserDayReportFormDTO::getDayReportId)))
                .orElse(Collections.emptyMap());
        // 11.查询打卡信息
        List<String> dayIds = dayReportList.stream()
                .filter(item -> Objects.nonNull(item.getDayId()))
                .map(item -> String.valueOf(item.getDayId())).distinct().collect(Collectors.toList());
        Map<String, List<UserDayReportPunchRecordDTO>> userRecordMap = selectUserDayReportPunchCode(userCodes, dayIds);
        // 12.封装导出DTO
        for (AttendanceDayReportDO dayReportDO : dayReportList) {
            List<UserDTO> userDTOList = userMap.get(dayReportDO.getUserId());
            if (CollectionUtils.isEmpty(userDTOList)) {
                continue;
            }
            UserDayReportExportDTO dayReportExportDTO = AttendanceReportMapstruct.INSTANCE.toUserDayReportExportDTO(userDTOList.get(0));
            // 部门
            dayReportExportDTO.setDeptId(dayReportDO.getDeptId());
            List<AttendanceDept> deptInfoList = deptMap.get(dayReportDO.getDeptId());
            if (CollectionUtils.isNotEmpty(deptInfoList)) {
                dayReportExportDTO.setDeptName(RequestInfoHolder.isChinese()
                        ? deptInfoList.get(0).getDeptNameCn()
                        : deptInfoList.get(0).getDeptNameEn());
            }
            // 岗位
            dayReportExportDTO.setPostId(dayReportDO.getPostId());
            List<AttendancePost> postInfoList = postMap.get(dayReportDO.getPostId());
            if (CollectionUtils.isNotEmpty(postInfoList)) {
                dayReportExportDTO.setPostName(RequestInfoHolder.isChinese()
                        ? postInfoList.get(0).getPostNameCn()
                        : postInfoList.get(0).getPostNameEn());
            }
            // 考勤日期
            dayReportExportDTO.setDayId(dayReportDO.getDayId());
            dayReportExportDTO.setDate(DateHelper.getString(dayReportDO.getDayId()));
            // 日历
            dayReportExportDTO.setCalendarId(dayReportDO.getCalendarId());
            List<CalendarConfigDO> calendarConfigList = calendarConfigMap.get(dayReportDO.getCalendarId());
            if (CollectionUtils.isNotEmpty(calendarConfigList)) {
                dayReportExportDTO.setCalendarConfigName(calendarConfigList.get(0).getAttendanceConfigName());
            }
            // 打卡规则
            dayReportExportDTO.setPunchConfigId(dayReportDO.getPunchConfigId());
            List<PunchConfigDO> punchConfigList = punchConfigMap.get(dayReportDO.getPunchConfigId());
            if (CollectionUtils.isNotEmpty(punchConfigList)) {
                PunchConfigDO punchConfigDO = punchConfigList.get(0);
                dayReportExportDTO.setPunchConfigName(punchConfigDO.getConfigName());
                dayReportExportDTO.setPunchConfigType(punchConfigDO.getConfigType());
                dayReportExportDTO.setPunchTimeInterval(punchConfigDO.getPunchTimeInterval());
            }
            // 补卡规则
            dayReportExportDTO.setReissueConfigId(dayReportDO.getReissueCardConfigId());
            List<ReissueCardConfigDO> reissueCardConfigList = reissueCardConfigMap.get(dayReportDO.getReissueCardConfigId());
            if (CollectionUtils.isNotEmpty(reissueCardConfigList)) {
                dayReportExportDTO.setReissueConfigName(reissueCardConfigList.get(0).getConfigName());
            }
            // 加班规则
            dayReportExportDTO.setOverTimeConfigId(dayReportDO.getOverTimeConfigId());
            List<OverTimeConfigDO> overTimeConfigList = overTimeConfigMap.get(dayReportDO.getOverTimeConfigId());
            if (CollectionUtils.isNotEmpty(overTimeConfigList)) {
                dayReportExportDTO.setOverTimeConfigName(overTimeConfigList.get(0).getConfigName());
            }
            // 排班计划
            dayReportExportDTO.setDayShiftRule(dayReportDO.getDayShiftRule());
            // 班次
            dayReportExportDTO.setClassId(dayReportDO.getPunchClassConfigId());
            PunchClassConfigDTO punchClassConfigDTO = classMap.get(dayReportDO.getPunchClassConfigId());
            if (Objects.nonNull(punchClassConfigDTO)) {
                dayReportExportDTO.setPunchClassConfigDTO(punchClassConfigDTO);
                dayReportExportDTO.setClassName(punchClassConfigDTO.getClassName());
            }
            // 初始考勤结果
            dayReportExportDTO.setInitResult(dayReportDO.getInitResult());
            // 实际出勤时长-不含休息(min)
            dayReportExportDTO.setActualAttendanceMinutes(dayReportDO.getActualAttendanceMinutes());
            // 审批信息
            dayReportExportDTO.setLeaveMinutes(dayReportDO.getLeaveMinutes());
            dayReportExportDTO.setOooMinutes(dayReportDO.getOooMinutes());
            // 当日异常
            List<UserDayReportAbnormalDTO> userDayReportAbnormalList = abnormalMap.get(dayReportDO.getId());
            if (CollectionUtils.isNotEmpty(userDayReportAbnormalList)) {
                Long dayId = DateHelper.getDayId(dateTime);
                if (Objects.nonNull(dayId)) {
                    Long previousDayId = DateHelper.getPreviousDayId(dayId);
                    Long nextDayId = DateHelper.getNextDayId(dayId);
                    List<Long> dayIdList = Arrays.asList(dayId, previousDayId, nextDayId);
                    // 过滤异常
                    if (dayIdList.contains(dayReportDO.getDayId()) && Objects.nonNull(punchClassConfigDTO)) {
                        List<EmployeeAbnormalAttendanceDO> abnormalDOList = AttendanceReportMapstruct.INSTANCE.toAbnormalDOByAbnormalDTO(userDayReportAbnormalList);
                        List<PunchClassItemConfigDTO> classItemConfigList = punchClassConfigDTO.getClassItemConfigList();
                        List<PunchClassItemConfigDO> classItemDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigList);
                        punchClassConfigQueryService.transferItemConfigTimeFormat(classItemDOList, dayReportDO.getDayId());
                        abnormalDOList = abnormalAttendanceService.filterAbnormalAttendance(abnormalDOList, classItemDOList, dateTime);
                        if (CollectionUtils.isNotEmpty(abnormalDOList)) {
                            List<Long> abnormalIds = abnormalDOList.stream().map(EmployeeAbnormalAttendanceDO::getId).collect(Collectors.toList());
                            userDayReportAbnormalList = userDayReportAbnormalList.stream()
                                    .filter(item -> abnormalIds.contains(item.getAbnormalId()))
                                    .collect(Collectors.toList());
                        }
                        if (CollectionUtils.isEmpty(abnormalDOList)) {
                            userDayReportAbnormalList = Collections.emptyList();
                        }
                    }
                }
                dayReportExportDTO.setUserDayReportAbnormalList(userDayReportAbnormalList);
            }
            // 当日单据信息
            List<UserDayReportFormDTO> userDayReportFormList = formMap.get(dayReportDO.getId());
            if (CollectionUtils.isNotEmpty(userDayReportFormList)) {
                dayReportExportDTO.setUserDayReportFormList(userDayReportFormList);
            }
            // 打卡记录
            List<UserDayReportPunchRecordDTO> userDayReportPunchRecordList = userRecordMap.get(dayReportDO.getUserCode());
            Long dayId = dayReportDO.getDayId();
            List<String> dayIdStrList = Arrays.asList(dayId.toString());
            if (CollectionUtils.isNotEmpty(userDayReportPunchRecordList)) {
                dayReportExportDTO.setUserDayReportPunchRecordList(userDayReportPunchRecordList
                        .stream()
                        .filter(item -> dayIdStrList.contains(item.getDayId()))
                        .collect(Collectors.toList()));
            }
            // 异常结果
            if (Objects.nonNull(dayReportExportDTO.getInitResult())) {
                dayReportExportDTO.setFinalResult(dayReportDO.getFinalResult());
            }
            dayReportExportDTO.setLateMinutes(dayReportDO.getLateMinutes());
            dayReportExportDTO.setLeaveEarlyMinutes(dayReportDO.getLeaveEarlyMinutes());
            dayReportExportDTO.setDelayMinutes(dayReportDO.getDelayMinutes());
            // 工时
            dayReportExportDTO.setFinalWorkMinutes(dayReportDO.getFinalWorkMinutes());
            userDayReportExportDTOList.add(dayReportExportDTO);
        }
        return userDayReportExportDTOList;
    }

    /**
     * 查询异常操作记录
     *
     * @return
     */
    private List<UserDayReportAbnormalDTO> selectDayReportAbnormalList(List<Long> reportIds) {
        List<AttendanceDayReportAbnormalDO> abnormalList = dayReportAbnormalDao.selectByReportIds(reportIds);
        if (CollectionUtils.isEmpty(abnormalList)) {
            return Collections.emptyList();
        }
        List<UserDayReportAbnormalDTO> abnormalDTOList = AttendanceReportMapstruct.INSTANCE.toUserDayReportAbnormalDTO(abnormalList);
        // 查询异常操作记录
        List<Long> abnormalIds = abnormalList.stream().map(item -> item.getAbnormalId()).collect(Collectors.toList());
        List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecordList = employeeAbnormalOperationRecordDao.selectByAbnormalList(abnormalIds);
        Map<Long, List<EmployeeAbnormalOperationRecordDO>> operationMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(abnormalOperationRecordList)) {
            operationMap = abnormalOperationRecordList.stream().collect(Collectors.groupingBy(EmployeeAbnormalOperationRecordDO::getAbnormalId));
        }
        for (UserDayReportAbnormalDTO dayReportAbnormalDTO : abnormalDTOList) {
            List<EmployeeAbnormalOperationRecordDO> abnormalOperationRecord = operationMap.get(dayReportAbnormalDTO.getAbnormalId());
            if (CollectionUtils.isNotEmpty(abnormalOperationRecord)) {
                List<UserDayReportAbnormalOperationDTO> userDayReportAbnormalOperationDTO = AttendanceReportMapstruct.INSTANCE.toUserDayReportAbnormalOperationDTO(abnormalOperationRecord);
                dayReportAbnormalDTO.setOperationList(userDayReportAbnormalOperationDTO);
            }
        }
        return abnormalDTOList;
    }

    /**
     * 封装单据详情信息
     *
     * @param formIds
     * @param userDayReportFormDTOList
     * @return
     */
    private void buildFormAttrInfo(List<Long> formIds,
                                   List<UserDayReportFormDTO> userDayReportFormDTOList) {
        if (CollectionUtils.isEmpty(formIds)
                || CollectionUtils.isEmpty(userDayReportFormDTOList)) {
            return;
        }
        List<AttendanceFormAttrDO> formAttrDOList = formManage.selectFormAttrByFormIdList(formIds);
        Map<Long, List<AttendanceFormAttrDO>> formAttrMap = Optional.ofNullable(formAttrDOList)
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(AttendanceFormAttrDO::getFormId)))
                .orElse(Collections.emptyMap());
        for (UserDayReportFormDTO userDayReportFormDTO : userDayReportFormDTOList) {
            List<AttendanceFormAttrDO> attendanceFormAttrList = formAttrMap.get(userDayReportFormDTO.getFormId());
            if (CollectionUtils.isEmpty(attendanceFormAttrList)) {
                continue;
            }
            Map<String, AttendanceFormAttrDO> formAttrFieldMap = attendanceFormAttrList.stream()
                    .collect(Collectors.toMap(AttendanceFormAttrDO::getAttrKey, o -> o, (v1, v2) -> v1));
            AttendanceFormAttrDO leaveStartDate = formAttrFieldMap.get(ApplicationFormAttrKeyEnum.leaveStartDate.getLowerCode());
            if (leaveStartDate != null) {
                userDayReportFormDTO.setStartDate(DateHelper.parseYYYYMMDDHHMMSS(leaveStartDate.getAttrValue()));
            }
            AttendanceFormAttrDO leaveEndDate = formAttrFieldMap.get(ApplicationFormAttrKeyEnum.leaveEndDate.getLowerCode());
            if (leaveEndDate != null) {
                userDayReportFormDTO.setEndDate(DateHelper.parseYYYYMMDDHHMMSS(leaveEndDate.getAttrValue()));
            }
            AttendanceFormAttrDO outOfOfficeStartDate = formAttrFieldMap.get(ApplicationFormAttrKeyEnum.outOfOfficeStartDate.getLowerCode());
            if (outOfOfficeStartDate != null) {
                userDayReportFormDTO.setStartDate(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeStartDate.getAttrValue()));
            }
            AttendanceFormAttrDO outOfOfficeEndDate = formAttrFieldMap.get(ApplicationFormAttrKeyEnum.outOfOfficeEndDate.getLowerCode());
            if (outOfOfficeEndDate != null) {
                userDayReportFormDTO.setEndDate(DateHelper.parseYYYYMMDDHHMMSS(outOfOfficeEndDate.getAttrValue()));
            }
        }
    }

    /**
     * 批量查询日月报打卡记录
     *
     * @param userCodes
     * @param dayIds
     */
    private Map<String, List<UserDayReportPunchRecordDTO>> selectUserDayReportPunchCode(List<String> userCodes,
                                                                                        List<String> dayIds) {
//        List<String> dayIdAll = new ArrayList<>();
//        dayIdAll.addAll(dayIds);
//        for (String dayId : dayIds) {
//            String preDayId = DateHelper.formatPureDate(DateUtil.offsetDay(DateHelper.transferDayIdToDate(dayId), -1));
//            if (StringUtils.isNotBlank(preDayId)) {
//                dayIdAll.add(preDayId);
//            }
//            String nextDayId = DateHelper.formatPureDate(DateUtil.offsetDay(DateHelper.transferDayIdToDate(dayId), 1));
//            if (StringUtils.isNotBlank(nextDayId)) {
//                dayIdAll.add(nextDayId);
//            }
//        }
//        dayIds = dayIdAll.stream().distinct().collect(Collectors.toList());
        List<EmployeePunchRecordDO> usersPunchRecordsInTimeRange = employeePunchRecordManage.getUsersPunchRecordsInTimeRange(userCodes, dayIds);
        List<UserDayReportPunchRecordDTO> userDayReportPunchRecordDTO = AttendanceReportMapstruct.INSTANCE.toUserDayReportPunchRecordDTOByDO(usersPunchRecordsInTimeRange);
        return Optional.ofNullable(userDayReportPunchRecordDTO)
                .filter(item -> !item.isEmpty())
                .map(item -> item.stream().collect(Collectors.groupingBy(UserDayReportPunchRecordDTO::getUserCode)))
                .orElse(Collections.emptyMap());
    }

}
