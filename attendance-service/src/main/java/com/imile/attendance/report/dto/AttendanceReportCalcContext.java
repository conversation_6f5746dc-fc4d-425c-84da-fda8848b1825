package com.imile.attendance.report.dto;

import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormAttrDO;
import com.imile.attendance.infrastructure.repository.form.model.AttendanceFormDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户日月报上下文
 *
 * <AUTHOR>
 * @menu 考勤日月报
 * @date 2025/6/12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendanceReportCalcContext {
    /**
     * 出勤日期
     */
    private Long dayId;

    /**
     * 班次时段信息
     */
    private List<PunchClassItemConfigDO> dayClassItemConfigList;

    /**
     * 员工时间范围内打卡记录
     */
    private List<UserPunchRecordDTO> userPunchRecordList;

    /**
     * 法定出勤时长(分钟)
     */
    private BigDecimal defaultMinutes;

    /**
     * 出勤时长(分钟)
     */
    private BigDecimal attendanceMinutes;

    /**
     * 请假时长(分钟)
     */
    private BigDecimal leaveMinutes;

    /**
     * 单据时长,含外勤(分钟)
     */
    private BigDecimal formMinutes;

    /**
     * 员工时间范围内申请单信息
     */
    private List<AttendanceFormDO> passAndInViewFormList;
    private List<AttendanceFormAttrDO> passAndInViewFormAttrList;
}
