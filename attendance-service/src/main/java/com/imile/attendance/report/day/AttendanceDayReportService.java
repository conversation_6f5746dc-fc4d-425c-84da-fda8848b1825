package com.imile.attendance.report.day;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.enums.AttendanceReportStatusEnum;
import com.imile.attendance.enums.ClassNatureEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.abnormal.AbnormalOperationTypeEnum;
import com.imile.attendance.enums.abnormal.AttendanceAbnormalTypeEnum;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.convert.ConverterService;
import com.imile.attendance.infrastructure.repository.common.vo.PermissionCountryDeptVO;
import com.imile.attendance.infrastructure.repository.report.dao.AttendanceDayReportDao;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.report.query.UserDayReportQuery;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.migration.constants.HrPunchConfigTypeEnum;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.punch.bo.UserPunchRecordBO;
import com.imile.attendance.report.AttendanceReportQueryService;
import com.imile.attendance.report.day.dto.UserDayReportAbnormalDTO;
import com.imile.attendance.report.day.dto.UserDayReportAbnormalOperationDTO;
import com.imile.attendance.report.day.dto.UserDayReportDTO;
import com.imile.attendance.report.day.dto.UserDayReportExportDTO;
import com.imile.attendance.report.day.dto.UserDayReportListDTO;
import com.imile.attendance.report.day.dto.UserDayReportPunchRecordDTO;
import com.imile.attendance.report.day.query.DayReportDetailQuery;
import com.imile.attendance.report.day.query.DayReportListQuery;
import com.imile.attendance.report.day.vo.UserDayReportExportVO;
import com.imile.attendance.report.day.vo.UserDayReportListVO;
import com.imile.attendance.report.mapstruct.AttendanceReportMapstruct;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.mapstruct.PunchClassItemConfigMapstruct;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import com.imile.attendance.util.DateHelper;
import com.imile.attendance.util.PageUtil;
import com.imile.common.page.PaginationResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 用户日报服务
 *
 * <AUTHOR>
 * @menu 考勤日报
 * @date 2025/6/14
 */
@Service
@Slf4j
public class AttendanceDayReportService {
    @Resource
    private AttendanceDayReportDao dayReportDao;
    @Resource
    private AttendanceReportQueryService attendanceReportQueryService;
    @Resource
    protected PunchClassConfigQueryService punchClassConfigQueryService;
    @Resource
    private ConverterService converterService;
    @Resource
    private AttendancePermissionService attendancePermissionService;

    /**
     * 日报列表
     *
     * @param query
     * @return
     */
    public PaginationResult<UserDayReportListVO> list(DayReportListQuery query) {
        // 1.分页查询考勤日报
        PageInfo<AttendanceDayReportDO> pageInfo = this.listQuery(query);
        List<AttendanceDayReportDO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 2.封装列表信息
        List<UserDayReportListDTO> dayReportDTOList = attendanceReportQueryService.getDayReportListDTO(list);
        List<UserDayReportListVO> dayReportListVOList = AttendanceReportMapstruct.INSTANCE.toDayReportListVO(dayReportDTOList);
        return PageUtil.getPageResult(dayReportListVOList, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 考勤日报详情
     *
     * @param query
     * @return
     */
    public UserDayReportDTO detail(DayReportDetailQuery query) {
        // 1.查询日报详情
        AttendanceDayReportDO dayReportDO = dayReportDao.getById(query.getDayReportId());
        if (Objects.isNull(dayReportDO)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        // 2.封装日报信息
        UserDayReportDTO dayReportDTO = attendanceReportQueryService.getDayReportDetail(dayReportDO, query.getDateTime());
        converterService.withAnnotationForSingle(dayReportDTO);
        return dayReportDTO;
    }

    /**
     * 考勤日报导出
     *
     * @param query
     * @return
     */
    public PaginationResult<UserDayReportExportVO> export(DayReportListQuery query) {
        // 1.列表查询
        PageInfo<AttendanceDayReportDO> pageInfo = this.listQuery(query);
        List<AttendanceDayReportDO> dayReportlist = pageInfo.getList();
        if (CollectionUtils.isEmpty(dayReportlist)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 2.封装日报导出信息
        List<UserDayReportExportDTO> dayReportExportDTOS = attendanceReportQueryService.getDayReportExportDetail(dayReportlist, query.getDateTime());
        List<UserDayReportExportVO> dayReportExportVOS = this.buildUserDayExportVO(dayReportExportDTOS);
        return PageUtil.getPageResult(dayReportExportVOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 列表查询
     *
     * @param query
     * @return
     */
    private PageInfo<AttendanceDayReportDO> listQuery(DayReportListQuery query) {
        // 1.拼接查询权限
        UserDayReportQuery dayReportQuery = AttendanceReportMapstruct.INSTANCE.toDayReportQuery(query);
        if (!this.buildAuthQuery(dayReportQuery)) {
            return PageInfo.of(Collections.emptyList());
        }
        // 2.查询日报
        PageInfo<AttendanceDayReportDO> pageInfo = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> dayReportDao.selectDayReportList(dayReportQuery));
        return pageInfo;
    }

    /**
     * 拼接考勤日报查询权限
     *
     * @param query
     */
    private Boolean buildAuthQuery(UserDayReportQuery query) {
        // 默认员工类型查询
        if (CollectionUtils.isEmpty(query.getEmployeeTypeList())) {
            query.setEmployeeTypeList(EmploymentTypeEnum.SPECIAL_COUNTRY_EMPLOYEE_TYPE_FOR_PAGE_SHIFT);
        }
        List<String> authLocationCountryList = attendancePermissionService.getUserLocationCountryPermission();
        List<Long> authDeptIdList = attendancePermissionService.getUserDeptPermission();

        if ((CollectionUtils.isEmpty(authLocationCountryList)
                && CollectionUtils.isEmpty(authDeptIdList))) {
            return false;
        }
        query.setAuthLocationCountryList(authLocationCountryList);
        query.setAuthDeptIdList(authDeptIdList);
        return true;
    }

    /**
     * 封装导出VO信息
     *
     * @param dayReportExportDTOList
     * @return
     */
    private List<UserDayReportExportVO> buildUserDayExportVO(List<UserDayReportExportDTO> dayReportExportDTOList) {
        List<UserDayReportExportVO> dayReportExportVOList = new ArrayList<>();
        for (UserDayReportExportDTO dayReportExportDTO : dayReportExportDTOList) {
            UserDayReportExportVO dayReportExportVO = AttendanceReportMapstruct.INSTANCE.toUserDayReportExportVO(dayReportExportDTO);

            // 员工类型翻译
            String employeeType = dayReportExportVO.getEmployeeType();
            if (StringUtils.isNotBlank(employeeType)
                    && Objects.nonNull(EmploymentTypeEnum.getByCode(employeeType))) {
                EmploymentTypeEnum employmentTypeEnum = EmploymentTypeEnum.getByCode(employeeType);
                dayReportExportVO.setEmployeeType(RequestInfoHolder.isChinese()
                        ? employmentTypeEnum.getDesc() : employmentTypeEnum.getDescEn());
            }

            // 封装排班计划翻译
            String dayShiftRule = dayReportExportDTO.getDayShiftRule();
            if (StringUtils.isNotBlank(dayShiftRule)
                    && Objects.nonNull(DayShiftRuleEnum.getByCode(dayShiftRule))) {
                DayShiftRuleEnum dayShiftEnum = DayShiftRuleEnum.getByCode(dayShiftRule);
                dayReportExportVO.setDayShiftRule(RequestInfoHolder.isChinese()
                        ? dayShiftEnum.getDesc() : dayShiftEnum.getDescEn());
            }
            // 转换打卡时间
            setPunchTime(dayReportExportDTO, dayReportExportVO);
            // 转换打卡类型
            String punchConfigType = dayReportExportDTO.getPunchConfigType();
            if (StringUtils.isNotBlank(punchConfigType) && Objects.nonNull(PunchConfigTypeEnum.getInstance(punchConfigType))) {
                PunchConfigTypeEnum punchConfigTypeEnum = PunchConfigTypeEnum.getInstance(punchConfigType);
                dayReportExportVO.setPunchConfigType(RequestInfoHolder.isChinese()
                        ? punchConfigTypeEnum.getDesc() : punchConfigTypeEnum.getDescEn());
            }
            // 转换班次类型
            String classNature = dayReportExportDTO.getClassNature();
            if (StringUtils.isNotBlank(classNature) && Objects.nonNull(ClassNatureEnum.getByCode(classNature))) {
                ClassNatureEnum classNatureEnum = ClassNatureEnum.getByCode(classNature);
                dayReportExportVO.setClassNature(RequestInfoHolder.isChinese()
                        ? classNatureEnum.getDesc() : classNatureEnum.getDescEn());
            }
            // 实际出勤时长
            BigDecimal actualAttendanceMinutes = dayReportExportDTO.getActualAttendanceMinutes();
            if (Objects.nonNull(actualAttendanceMinutes)) {
                dayReportExportVO.setActualAttendanceHour(String.valueOf(actualAttendanceMinutes.divide(BusinessConstant.MINUTES
                        , 2, RoundingMode.HALF_UP)));
            }
            // 请假时长
            BigDecimal leaveMinutes = dayReportExportDTO.getLeaveMinutes();
            if (Objects.nonNull(leaveMinutes)) {
                dayReportExportVO.setLeaveHour(String.valueOf(leaveMinutes.divide(BusinessConstant.MINUTES
                        , 2, RoundingMode.HALF_UP)));
            }
            // 外勤时长
            BigDecimal oooMinutes = dayReportExportDTO.getOooMinutes();
            if (Objects.nonNull(oooMinutes)) {
                dayReportExportVO.setOooHour(String.valueOf(oooMinutes.divide(BusinessConstant.MINUTES
                        , 2, RoundingMode.HALF_UP)));
            }
            // 延时时长
            BigDecimal delayMinutes = dayReportExportDTO.getDelayMinutes();
            if (Objects.nonNull(delayMinutes)) {
                dayReportExportVO.setDelayHour(String.valueOf(delayMinutes.divide(BusinessConstant.MINUTES
                        , 2, RoundingMode.HALF_UP)));
            }
            // 迟到时长
            BigDecimal lateMinutes = dayReportExportDTO.getLateMinutes();
            if (Objects.nonNull(lateMinutes)) {
                dayReportExportVO.setLateMinutes(String.valueOf(lateMinutes));
            }
            // 早退时长
            BigDecimal leaveEarlyMinutes = dayReportExportDTO.getLeaveEarlyMinutes();
            if (Objects.nonNull(leaveEarlyMinutes)) {
                dayReportExportVO.setLeaveEarlyMinutes(String.valueOf(leaveEarlyMinutes));
            }
            // 最终工作时长
            BigDecimal finalWorkMinutes = dayReportExportDTO.getFinalWorkMinutes();
            if (Objects.nonNull(finalWorkMinutes)) {
                dayReportExportVO.setFinalWorkHour(String.valueOf(finalWorkMinutes.divide(BusinessConstant.MINUTES
                        , 2, RoundingMode.HALF_UP)));
            }
            // 初始结果
            Integer initResult = dayReportExportDTO.getInitResult();
            if (Objects.nonNull(initResult)) {
                AttendanceReportStatusEnum resultEnum = AttendanceReportStatusEnum.getCode(initResult);
                dayReportExportVO.setInitResult(RequestInfoHolder.isChinese()
                        ? resultEnum.getDesc() : resultEnum.getDescEn());
            }
            // 最终结果
            Integer finalResult = dayReportExportDTO.getFinalResult();
            if (Objects.nonNull(finalResult)) {
                AttendanceReportStatusEnum resultEnum = AttendanceReportStatusEnum.getCode(finalResult);
                dayReportExportVO.setFinalResult(RequestInfoHolder.isChinese()
                        ? resultEnum.getDesc() : resultEnum.getDescEn());
            }
            // 异常统计
            List<UserDayReportAbnormalDTO> userDayReportAbnormalList = dayReportExportDTO.getUserDayReportAbnormalList();
            if (CollectionUtils.isEmpty(userDayReportAbnormalList)) {
                dayReportExportVOList.add(dayReportExportVO);
                continue;
            }
            // 迟到
            List<UserDayReportAbnormalDTO> lateDTOList = userDayReportAbnormalList.stream()
                    .filter(item -> AttendanceAbnormalTypeEnum.LATE.getCode().equals(item.getAbnormalType()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(lateDTOList)) {
                // 封装迟到异常操作方式
                String operationType = buildAbnormalOperation(lateDTOList);
                dayReportExportVO.setLateOperation(operationType);
            }
            // 早退
            List<UserDayReportAbnormalDTO> leaveEarlyDTOList = userDayReportAbnormalList.stream()
                    .filter(item -> AttendanceAbnormalTypeEnum.LEAVE_EARLY.getCode().equals(item.getAbnormalType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(leaveEarlyDTOList)) {
                // 封装早退异常操作方式
                String operationType = buildAbnormalOperation(leaveEarlyDTOList);
                dayReportExportVO.setLeaveEarlyOperation(operationType);
            }
            // 上班缺卡
            List<UserDayReportAbnormalDTO> beforeOfficeLackDTOList = userDayReportAbnormalList.stream()
                    .filter(item -> AttendanceAbnormalTypeEnum.BEFORE_OFFICE_LACK.getCode().equals(item.getAbnormalType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(beforeOfficeLackDTOList)) {
                dayReportExportVO.setBeforeOfficeLackNum(String.valueOf(beforeOfficeLackDTOList.size()));
                // 封装上班缺卡异常操作方式
                String operationType = buildAbnormalOperation(beforeOfficeLackDTOList);
                dayReportExportVO.setBeforeOfficeLackOperation(operationType);
            }
            // 下班缺卡
            List<UserDayReportAbnormalDTO> afterOfficeLackDTOList = userDayReportAbnormalList.stream()
                    .filter(item -> AttendanceAbnormalTypeEnum.AFTER_OFFICE_LACK.getCode().equals(item.getAbnormalType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(afterOfficeLackDTOList)) {
                dayReportExportVO.setAfterOfficeLackNum(String.valueOf(afterOfficeLackDTOList.size()));
                // 封装下班缺卡异常操作方式
                String operationType = buildAbnormalOperation(afterOfficeLackDTOList);
                dayReportExportVO.setAfterOfficeLackOperation(operationType);
            }
            // 未打卡
            List<UserDayReportAbnormalDTO> noPunchDTOList = userDayReportAbnormalList.stream()
                    .filter(item -> AttendanceAbnormalTypeEnum.NO_PUNCH.getCode().equals(item.getAbnormalType()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(noPunchDTOList)) {
                dayReportExportVO.setNoPunchNum(String.valueOf(noPunchDTOList.size()));
                // 封装未打卡异常操作方式
                String operationType = buildAbnormalOperation(noPunchDTOList);
                dayReportExportVO.setNoPunchOperation(operationType);
            }

            dayReportExportVOList.add(dayReportExportVO);
        }
        return dayReportExportVOList;
    }

    /**
     * 处理打卡时间
     *
     * @param dayReportExportDTO
     * @param dayReportExportVO
     */
    private void setPunchTime(UserDayReportExportDTO dayReportExportDTO,
                              UserDayReportExportVO dayReportExportVO) {
        List<UserDayReportPunchRecordDTO> userDayReportPunchRecordList = dayReportExportDTO.getUserDayReportPunchRecordList();
        if (CollectionUtils.isEmpty(userDayReportPunchRecordList)) {
            return;
        }
        for (UserDayReportPunchRecordDTO userDayReportPunchRecordDTO : userDayReportPunchRecordList) {
            String punchTimeString = DateUtil.format(userDayReportPunchRecordDTO.getPunchTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN);
            userDayReportPunchRecordDTO.setPunchTime(DateUtil.parse(punchTimeString + ":00", DatePattern.NORM_DATETIME_PATTERN));
        }
        PunchClassConfigDTO punchClassConfigDTO = dayReportExportDTO.getPunchClassConfigDTO();
        if (Objects.isNull(punchClassConfigDTO) 
                || CollectionUtils.isEmpty(punchClassConfigDTO.getClassItemConfigList())) {
            return;
        }
        String punchConfigType = dayReportExportDTO.getPunchConfigType();
        List<PunchClassItemConfigDTO> classItemConfigList = punchClassConfigDTO.getClassItemConfigList();
        for (PunchClassItemConfigDTO punchClassItemConfigDTO : classItemConfigList) {
            //获取当日最早最晚打卡时间
            DayPunchTimeDTO dayPunchTimeDTO;
            if (PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode().equals(punchConfigType)) {
                PunchClassItemConfigDO itemClassDO = PunchClassItemConfigMapstruct.INSTANCE.toDO(punchClassItemConfigDTO);
                dayPunchTimeDTO = punchClassConfigQueryService.getUserFreeWorkPunchClassItemDayTime(dayReportExportDTO.getDayId(), itemClassDO);
            } else {
                List<PunchClassItemConfigDO> itemClassDOList = PunchClassItemConfigMapstruct.INSTANCE.toDOList(classItemConfigList);
                dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(dayReportExportDTO.getDayId(), punchClassItemConfigDTO.getId(), itemClassDOList);
            }
            // 获取打卡时长
            List<UserDayReportPunchRecordDTO> itemPunchRecord = userDayReportPunchRecordList.stream()
                    .filter(item -> item.getPunchTime().compareTo(dayPunchTimeDTO.getDayPunchStartTime()) > -1
                            && item.getPunchTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) < 1)
                    .sorted(Comparator.comparing(UserDayReportPunchRecordDTO::getPunchTime))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(itemPunchRecord)) {
                continue;
            }
            Date earliestPunchInTime = itemPunchRecord.get(0).getPunchTime();
            String earliestFormatPunchTime = DateUtil.format(earliestPunchInTime, "HH:mm");
            // 灵活一次
            if (PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode().equals(punchConfigType)) {
                setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), earliestFormatPunchTime, BusinessConstant.EMPTY_STR, dayReportExportVO);
                continue;
            }
            // 灵活两次
            if (PunchConfigTypeEnum.FLEXIBLE_WORK_TWICE.getCode().equals(punchConfigType)) {
                BigDecimal punchTimeInterval = dayReportExportDTO.getPunchTimeInterval();
                if (Objects.isNull(punchTimeInterval)) {
                    continue;
                }
                DateTime midDate = DateUtil.offsetHour(dayPunchTimeDTO.getDayPunchEndTime(), punchTimeInterval.negate().intValue());
                if (itemPunchRecord.size() == BusinessConstant.ONE
                        && earliestPunchInTime.compareTo(midDate) >= BusinessConstant.ZERO) {
                    setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), BusinessConstant.EMPTY_STR, earliestFormatPunchTime, dayReportExportVO);
                }
                if (itemPunchRecord.size() == BusinessConstant.ONE
                        && earliestPunchInTime.compareTo(midDate) < BusinessConstant.ZERO) {
                    setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), earliestFormatPunchTime, BusinessConstant.EMPTY_STR, dayReportExportVO);
                }
                if (itemPunchRecord.size() > BusinessConstant.ONE) {
                    Date latestPunchOutTime = itemPunchRecord.get(itemPunchRecord.size() - 1).getPunchTime();
                    setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), earliestFormatPunchTime, DateUtil.format(latestPunchOutTime, "HH:mm"), dayReportExportVO);
                }
                continue;
            }
            // 固定/多班次打卡
            //上班时间  默认和最早打卡时间同一天
            Date punchInTime = DateUtil.parse(DateUtil.format(dayPunchTimeDTO.getDayPunchStartTime(), DatePattern.NORM_DATE_PATTERN)
                    + " " + DateUtil.format(punchClassItemConfigDTO.getPunchInTime(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
            //上班时间早于最早打卡时间，跨天
            if (punchClassItemConfigDTO.getPunchInTime().before(punchClassItemConfigDTO.getEarliestPunchInTime())) {
                punchInTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(dayPunchTimeDTO.getDayPunchStartTime(), 1), DatePattern.NORM_DATE_PATTERN)
                        + " " + DateUtil.format(punchClassItemConfigDTO.getPunchInTime(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
            }
            //下班时间  默认和最晚下班时间同一天
            Date punchOutTime = DateUtil.parse(DateUtil.format(dayPunchTimeDTO.getDayPunchEndTime(), DatePattern.NORM_DATE_PATTERN)
                    + " " + DateUtil.format(punchClassItemConfigDTO.getPunchOutTime(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
            if (punchClassItemConfigDTO.getPunchOutTime().after(punchClassItemConfigDTO.getLatestPunchOutTime())) {
                punchOutTime = DateUtil.parse(DateUtil.format(DateUtil.offsetDay(dayPunchTimeDTO.getDayPunchEndTime(), -1), DatePattern.NORM_DATE_PATTERN)
                        + " " + DateUtil.format(punchClassItemConfigDTO.getPunchOutTime(), DatePattern.NORM_TIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
            }
            //获取中间时间
            Date midDate = DateHelper.getMidDate(punchInTime, punchOutTime);
            if (itemPunchRecord.size() == BusinessConstant.ONE
                    && earliestPunchInTime.compareTo(midDate) >= BusinessConstant.ZERO) {
                setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), BusinessConstant.EMPTY_STR, earliestFormatPunchTime, dayReportExportVO);
            }
            if (itemPunchRecord.size() == BusinessConstant.ONE
                    && earliestPunchInTime.compareTo(midDate) < BusinessConstant.ZERO) {
                setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), earliestFormatPunchTime, BusinessConstant.EMPTY_STR, dayReportExportVO);
            }
            if (itemPunchRecord.size() > BusinessConstant.ONE
                    && earliestPunchInTime.compareTo(punchOutTime) < BusinessConstant.ZERO) {
                Date latestPunchOutTime = itemPunchRecord.get(itemPunchRecord.size() - 1).getPunchTime();
                setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), earliestFormatPunchTime, DateUtil.format(latestPunchOutTime, "HH:mm"), dayReportExportVO);
            }
            //如果最早时间已经大于下班时间了，则不设置最早打卡时间
            if (itemPunchRecord.size() > BusinessConstant.ONE
                    && earliestPunchInTime.compareTo(punchOutTime) >= BusinessConstant.ZERO) {
                Date latestPunchOutTime = itemPunchRecord.get(itemPunchRecord.size() - 1).getPunchTime();
                setFormatPunchTime(punchClassItemConfigDTO.getSortNo(), BusinessConstant.EMPTY_STR, DateUtil.format(latestPunchOutTime, "HH:mm"), dayReportExportVO);
            }
        }
    }

    /**
     * 打卡时间对应时间段
     * @param sortNo
     * @param punchInTime
     * @param punchOutTime
     * @param dayReportExportVO
     */
    private void setFormatPunchTime(Integer sortNo,
                                    String punchInTime, String punchOutTime,
                                    UserDayReportExportVO dayReportExportVO) {
        if (Objects.isNull(sortNo)) {
            return;
        }

        if (sortNo == BusinessConstant.ONE) {
            dayReportExportVO.setEarliestPunchInTime1(punchInTime);
            dayReportExportVO.setLatestPunchOutTime1(punchOutTime);
        }

        if (sortNo == BusinessConstant.TWO) {
            dayReportExportVO.setEarliestPunchInTime2(punchInTime);
            dayReportExportVO.setLatestPunchOutTime2(punchOutTime);
        }

        if (sortNo == BusinessConstant.THREE) {
            dayReportExportVO.setEarliestPunchInTime3(punchInTime);
            dayReportExportVO.setLatestPunchOutTime3(punchOutTime);
        }
    }

    /**
     *
     * @param abnormalDTOList
     * @return
     */
    private String buildAbnormalOperation(List<UserDayReportAbnormalDTO> abnormalDTOList) {
        StringBuilder operationTypeStr = new StringBuilder(BusinessConstant.EMPTY_STR);
        for (int i = 0; i < abnormalDTOList.size(); i++) {
            UserDayReportAbnormalDTO userDayReportAbnormalDTO =  abnormalDTOList.get(i);
            List<UserDayReportAbnormalOperationDTO> operationList = userDayReportAbnormalDTO.getOperationList();
            if (CollectionUtils.isEmpty(operationList) || StringUtils.isBlank(operationList.get(0).getOperationType())) {
                continue;
            }
            String operationType = operationList.get(0).getOperationType();
            AbnormalOperationTypeEnum operationTypeEnum = AbnormalOperationTypeEnum.getInstance(operationType);
            if (Objects.isNull(operationTypeEnum)) {
                continue;
            }
            String desc = operationTypeEnum.getDesc();
            String descEn = operationTypeEnum.getDescEn();
            if (AbnormalOperationTypeEnum.getNormalConfirmList().contains(operationType)) {
                desc = "确认正常";
                descEn = "normal confirm";
            }
            if (BusinessConstant.EMPTY_STR.equals(operationTypeStr.toString())) {
                operationTypeStr.append(RequestInfoHolder.isChinese()
                        ? desc : descEn);
                continue;
            }
            operationTypeStr.append(",");
            operationTypeStr.append(RequestInfoHolder.isChinese()
                    ? desc : descEn);

        }
        return operationTypeStr.toString();
    }

}
