package com.imile.attendance.report.impl;

import com.imile.attendance.annon.Strategy;
import com.imile.attendance.enums.rule.PunchConfigTypeEnum;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassItemConfigDO;
import com.imile.attendance.punch.dto.UserPunchRecordDTO;
import com.imile.attendance.report.AttendanceReportCalcService;
import com.imile.attendance.report.dto.AttendanceReportCalcContext;
import com.imile.attendance.rule.dto.DayPunchTimeDTO;
import com.imile.attendance.rule.service.PunchClassConfigQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 灵活打卡一次日月报时长计算
 *
 * <AUTHOR>
 * @menu 考勤日月报
 * @date 2025/6/12
 */
@Slf4j
@Service
@Strategy(value = AttendanceReportCalcService.class, implKey = "OnceWorkReportCalcServiceImpl")
public class OnceWorkReportCalcServiceImpl implements AttendanceReportCalcService {

    @Resource
    private PunchClassConfigQueryService punchClassConfigQueryService;

    @Override
    public boolean isMatch(String punchConfigType) {
        return PunchConfigTypeEnum.FLEXIBLE_WORK_ONCE.getCode().equals(punchConfigType);
    }

    @Override
    public BigDecimal execute(AttendanceReportCalcContext reportCalcContext) {
        BigDecimal availableMinutes = BigDecimal.ZERO;
        Date earliestPunchInTime = null;
        Date latestPunchOutTime = null;
        List<PunchClassItemConfigDO> dayClassItemConfigList = reportCalcContext.getDayClassItemConfigList();
        for (PunchClassItemConfigDO itemConfigDO : dayClassItemConfigList) {
            //获取当前时刻的正常时间
            DayPunchTimeDTO dayPunchTimeDTO = punchClassConfigQueryService.getUserPunchClassItemDayTime(reportCalcContext.getDayId()
                    , itemConfigDO.getId()
                    , dayClassItemConfigList);
            if (dayPunchTimeDTO == null || dayPunchTimeDTO.getDayPunchStartTime().compareTo(dayPunchTimeDTO.getDayPunchEndTime()) > -1) {
                return availableMinutes;
            }
            //获取打卡时间的所有点
            //最早上班打卡时间
            if (Objects.isNull(earliestPunchInTime) || dayPunchTimeDTO.getDayPunchStartTime().compareTo(earliestPunchInTime) < 0) {
                earliestPunchInTime = dayPunchTimeDTO.getDayPunchStartTime();
            }
            //最晚下班打卡时间
            if (Objects.isNull(latestPunchOutTime) || dayPunchTimeDTO.getDayPunchEndTime().compareTo(latestPunchOutTime) > 0) {
                latestPunchOutTime = dayPunchTimeDTO.getDayPunchEndTime();
            }
        }
        // 获取该班次最早上班打卡时间和最晚下班打卡时间之间的打卡记录
        Date finalEarliestPunchInTime = earliestPunchInTime;
        Date finalLatestPunchOutTime = latestPunchOutTime;
        List<UserPunchRecordDTO> punchRecordList = reportCalcContext.getUserPunchRecordList().stream()
                .filter(item -> item.getFormatPunchTime().compareTo(finalEarliestPunchInTime) > -1
                        && item.getFormatPunchTime().compareTo(finalLatestPunchOutTime) < 1)
                .sorted(Comparator.comparing(UserPunchRecordDTO::getFormatPunchTime)).collect(Collectors.toList());
        //当天不存在打卡记录
        BigDecimal leaveMinutes = reportCalcContext.getLeaveMinutes();
        BigDecimal defaultMinutes = reportCalcContext.getDefaultMinutes();
        if (CollectionUtils.isEmpty(punchRecordList)) {
            //当天完全请假
            if (leaveMinutes.compareTo(defaultMinutes) == 0) {
                return availableMinutes.add(reportCalcContext.getLeaveMinutes());
            }
            //当天没有请假或部分请假 需要加上外勤
            return availableMinutes.add(reportCalcContext.getAttendanceMinutes());
        }
        //当天存在打卡记录
        if (CollectionUtils.isNotEmpty(punchRecordList) && leaveMinutes.compareTo(BigDecimal.ZERO) == 0) {
            //当天没有请假
            return defaultMinutes;
        }
        //当天完全请假
        if (leaveMinutes.compareTo(defaultMinutes) == 0) {
            return availableMinutes.add(leaveMinutes);
        }
        //部分请假
        availableMinutes = availableMinutes.add(defaultMinutes);
        return availableMinutes;
    }
}
