package com.imile.attendance.vacation.dto;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-15
 * @version: 1.0
 */
@Data
public class CompanyLeaveConfigDetailDTO {

    private Long id;
    /**
     * 所属国
     */
    private String country;
    /**
     * 已绑定员工数
     */
    private Integer bindUserCount;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 创建日期
     */
    private Date createDate;
    /**
     * 修改人
     */
    private String lastUpdateUser;
    /**
     * 修改日期
     */
    private Date lastUpdateDate;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 是否派遣假
     */
    private Integer isDispatch;
    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 假期简称
     */
    private String leaveShortName;
    /**
     * 节假日是否消耗假期：0-否：表示消耗工作日，1-是：表示消耗自然日
     */
    private String consumeLeaveType;
}
