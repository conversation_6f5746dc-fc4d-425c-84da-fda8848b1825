package com.imile.attendance.vacation;

import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveTypeDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveTypeDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveTypeQuery;
import com.imile.attendance.vacation.dto.CompanyLeaveTypeDTO;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigTypeMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 假期类型业务层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/2/17
 */
@Service
@Slf4j
public class CompanyLeaveTypeService {

    @Resource
    private CompanyLeaveTypeDao companyLeaveTypeDao;

    /**
     * 假期类型条件查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public List<CompanyLeaveTypeDTO> queryByCondition(CompanyLeaveTypeQuery query) {
        List<CompanyLeaveTypeDO> leaveTypeDOList = companyLeaveTypeDao.queryByCondition(query);
        return CompanyLeaveConfigTypeMapstruct.INSTANCE.toConfigTypeDTO(leaveTypeDOList);
    }
}
