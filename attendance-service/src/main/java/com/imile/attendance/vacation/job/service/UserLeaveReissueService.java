package com.imile.attendance.vacation.job.service;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.common.AttendanceCountryService;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.WhetherEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIssueFrequencyEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIssueTimeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIssueTypeEnum;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigIssueRuleQuery;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigRangQuery;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.BigDecimalUtil;
import com.imile.attendance.util.DateFormatterUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigCarryOverService;
import com.imile.attendance.vacation.CompanyLeaveConfigIssueRuleService;
import com.imile.attendance.vacation.CompanyLeaveConfigRangService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.UserLeaveDetailManage;
import com.imile.attendance.vacation.UserLeaveDetailService;
import com.imile.attendance.vacation.UserLeaveRecordService;
import com.imile.attendance.vacation.UserLeaveStageDetailService;
import com.imile.attendance.vacation.param.UserLeaveReissueParam;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.util.date.DateUtils;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Year;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 假期补发业务实现
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
@Slf4j
public class UserLeaveReissueService {
    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private CompanyLeaveConfigRangService companyLeaveConfigRangService;
    @Resource
    private CompanyLeaveConfigIssueRuleService companyLeaveConfigIssueRuleService;
    @Resource
    private UserLeaveDetailService userLeaveDetailService;
    @Resource
    private UserLeaveDetailManage userLeaveDetailManage;
    @Resource
    private UserLeaveStageDetailService userLeaveStageDetailService;
    @Resource
    private UserLeaveInspectionService userLeaveInspectionService;
    @Resource
    private UserLeaveRecordService userLeaveRecordService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private AttendanceCountryService attendanceCountryService;
    @Resource
    private MigrationService migrationService;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;

    private static final Integer PAGE_SIZE_DEFAULT = 5000;

    public void userLeaveReissueHandler(UserLeaveReissueParam param) {
        // 查询满足条件的假期规则数据
        XxlJobLogger.log("xxl-job【userLeaveReissueHandler】开始执行，参数为：{}", JSON.toJSONString(param));
        LeaveConfigIssueRuleQuery issueRuleQuery = LeaveConfigIssueRuleQuery.builder()
                .issueType("age".equals(param.getReissueType())
                        ? LeaveConfigIssueTypeEnum.INCREASING_WITH_AGE.getType()
                        : LeaveConfigIssueTypeEnum.INCREASING_WITH_LENGTH_OF_SERVICE.getType())
                .isRecalculate(BusinessConstant.Y)
                .build();
        List<CompanyLeaveConfigIssueRuleDO> leaveConfigIssueRuleList = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleList(issueRuleQuery);
        if (CollectionUtils.isEmpty(leaveConfigIssueRuleList)) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】未查询到有效得假期发放规则");
            return;
        }
        List<Long> configIds = leaveConfigIssueRuleList.stream().map(item -> item.getLeaveId()).collect(Collectors.toList());
        Map<Long, List<CompanyLeaveConfigIssueRuleDO>> issueRuleMap = leaveConfigIssueRuleList
                .stream()
                .collect(Collectors.groupingBy(CompanyLeaveConfigIssueRuleDO::getLeaveId));
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .status(StatusEnum.ACTIVE.getCode())
                .idList(configIds)
                .leaveNameList(param.getLeaveNameList())
                .countryList(param.getCountryArrayList())
                .build();
        List<CompanyLeaveConfigDO> companyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(companyLeaveQuery);
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】companyLeaveConfigList is Empty");
            return;
        }
        List<Long> leaveIds = companyLeaveConfigList.stream().map(CompanyLeaveConfigDO::getId).collect(Collectors.toList());
        List<CompanyLeaveConfigCarryOverDO> companyLeaveConfigCarryOverList = Optional.ofNullable(companyLeaveConfigCarryOverService.selectByLeaveId(leaveIds))
                .orElse(Collections.emptyList());
        Map<Long, List<CompanyLeaveConfigCarryOverDO>> carryOverMap = companyLeaveConfigCarryOverList
                .stream()
                .filter(item -> Objects.nonNull(item.getLeaveId()))
                .collect(Collectors.groupingBy(CompanyLeaveConfigCarryOverDO::getLeaveId));
        List<Long> carryOverIds = companyLeaveConfigCarryOverList.stream().map(CompanyLeaveConfigCarryOverDO::getId).collect(Collectors.toList());
        Map<Long, List<CompanyLeaveConfigCarryOverRangeDO>> carryOverRangMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(carryOverIds)) {
            List<CompanyLeaveConfigCarryOverRangeDO> configCarryOverRangeList = Optional.ofNullable(companyLeaveConfigCarryOverService.selectRangeByCarryOverId(carryOverIds))
                    .orElse(Collections.emptyList());
            carryOverRangMap = configCarryOverRangeList
                    .stream()
                    .filter(item -> Objects.nonNull(item.getCarryOverId()))
                    .collect(Collectors.groupingBy(CompanyLeaveConfigCarryOverRangeDO::getCarryOverId));
        }
        // 获取对应国家及当地时间
        List<String> countryList = companyLeaveConfigList.stream().map(CompanyLeaveConfigDO::getCountry).collect(Collectors.toList());
        Map<String, Date> countryDateMap = attendanceCountryService.getCountryDateMap(countryList, param);
        // 遍历假期规则
        for (CompanyLeaveConfigDO companyLeaveConfig : companyLeaveConfigList) {
            List<CompanyLeaveConfigIssueRuleDO> issueRuleList = issueRuleMap.get(companyLeaveConfig.getId());
            if (CollectionUtils.isEmpty(issueRuleList)) {
                XxlJobLogger.log("xxl-job【userLeaveReissueHandler】issueRuleList is Empty");
                continue;
            }
            CompanyLeaveConfigIssueRuleDO issueRule = issueRuleList.get(0);
            // 查询对应发放规则的年龄/工龄范围数据
            List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList
                    = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleRangeList(issueRule.getId());
            if (CollectionUtils.isEmpty(issueRuleRangeList)) {
                XxlJobLogger.log("xxl-job【userLeaveReissueHandler】issueRuleRangeList is Empty");
                continue;
            }
            List<CompanyLeaveConfigCarryOverDO> configCarryOverList = carryOverMap.get(companyLeaveConfig.getId());
            if (CollectionUtils.isEmpty(issueRuleRangeList)) {
                XxlJobLogger.log("xxl-job【userLeaveReissueHandler】configCarryOverList is Empty");
                continue;
            }
            CompanyLeaveConfigCarryOverDO carryOverConfig = configCarryOverList.get(0);
            List<CompanyLeaveConfigCarryOverRangeDO> carryOverRangeList = Optional.ofNullable(carryOverRangMap.get(carryOverConfig.getId()))
                    .orElse(Collections.emptyList());
            LeaveConfigRangQuery leaveConfigRangQuery = LeaveConfigRangQuery.builder()
                    .leaveId(companyLeaveConfig.getId())
                    .build();
            // 查询假期范围
            List<CompanyLeaveConfigRangDO> leaveConfigRangList
                    = companyLeaveConfigRangService.getLeaveConfigRangList(leaveConfigRangQuery);
            List<String> userCodeList = leaveConfigRangList.stream()
                    .map(item -> item.getUserCode())
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(param.getUserCodeList())) {
                userCodeList = userCodeList.stream()
                        .filter(item -> param.getUserCodeList().contains(item))
                        .collect(Collectors.toList());
                // 范围内不存在这个人员 则不补发放假期
                if (CollectionUtils.isEmpty(userCodeList)) {
                    continue;
                }
            }
            // 查询人员
            int currentPage = 1, pageSize = PAGE_SIZE_DEFAULT;
            Page<UserInfoDO> page = PageHelper.startPage(currentPage, pageSize, true);
            UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                    .userCodes(userCodeList)
                    .employeeTypeList(param.getEmployeeTypeList())
                    .status(StatusEnum.ACTIVE.getCode())
                    .workStatus(WorkStatusEnum.ON_JOB.getCode())
                    .isDelete(IsDeleteEnum.NO.getCode())
                    .build();
            PageInfo<UserInfoDO> pageInfo = page.doSelectPageInfo(() -> userInfoManage.listUsersByQuery(userDaoQuery));
            int userCount = BusinessConstant.ZERO.intValue();
            // 总记录数
            List<UserInfoDO> pageUserInfoList = pageInfo.getList();
            if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                userCount = pageUserInfoList.size();
                handleUserReissue(pageUserInfoList, countryDateMap, companyLeaveConfig, carryOverConfig, carryOverRangeList
                        , issueRule, issueRuleRangeList, param.getReissueType());
            }
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】 | currentPage:{},pageSize:{},total:{},pages：{}"
                    , currentPage, pageSize, pageInfo.getTotal(), pageInfo.getPages());
            while (currentPage < pageInfo.getPages()) {
                XxlJobLogger.log("xxl-job【userLeaveReissueHandler】 | 进入while循环");
                currentPage++;
                page = PageHelper.startPage(currentPage, pageSize, true);
                pageInfo = page.doSelectPageInfo(() -> userInfoManage.listUsersByQuery(userDaoQuery));
                pageUserInfoList = pageInfo.getList();
                if (CollectionUtils.isNotEmpty(pageUserInfoList)) {
                    XxlJobLogger.log("xxl-job【userLeaveReissueHandler】 | while循环：pageUserInfoList size:{}，pageUserInfoList：{}"
                            , pageUserInfoList.size(), JSON.toJSONString(pageUserInfoList));
                    userCount = userCount + pageUserInfoList.size();
                    handleUserReissue(pageUserInfoList, countryDateMap, companyLeaveConfig, carryOverConfig, carryOverRangeList
                            , issueRule, issueRuleRangeList, param.getReissueType());
                }
                XxlJobLogger.log("xxl-job【userLeaveReissueHandler】 | while循环：currentPage:{},pageSize:{},total:{}"
                        , currentPage, pageSize, pageInfo.getTotal());
            }
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】 | currentPage {}，userCount {} while循环结束"
                    , currentPage, userCount);
        }
    }

    private void handleUserReissue(List<UserInfoDO> userInfoList, Map<String, Date> countryDateMap,
                                   CompanyLeaveConfigDO companyLeaveConfig,
                                   CompanyLeaveConfigCarryOverDO carryOverConfig,
                                   List<CompanyLeaveConfigCarryOverRangeDO> carryOverRangeList,
                                   CompanyLeaveConfigIssueRuleDO issueRule,
                                   List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList,
                                   String reissueType) {
        Date localDate = countryDateMap.get(companyLeaveConfig.getCountry());
        // 回国年份
        Integer localYear = DateUtil.year(localDate);
        Integer issueMonth = issueRule.getIssueMonth();
        Integer issueDay = issueRule.getIssueDay();

        if (Objects.isNull(localDate)) {
            XxlJobLogger.log("该国家:{},不存在国家时区", companyLeaveConfig.getCountry());
            return;
        }
        // 只有周期性发放且按年发放才需要补发
        if (!LeaveConfigIssueFrequencyEnum.PERIODICAL_ISSUANCE.getType().equals(issueRule.getIssueFrequency())
                || !LeaveConfigIssueTimeEnum.FIXED_DAY_PER_YEAR.getType().equals(issueRule.getIssueTime())) {
            XxlJobLogger.log("该假期规则:{},不是周期性发放且按年发放数据", companyLeaveConfig.getId());
            return;
        }
        // 校验数据
        if (Objects.isNull(issueRule.getIssueMonth()) || Objects.isNull(issueRule.getIssueDay())) {
            XxlJobLogger.log("该假期规则:{}, 配置错误", companyLeaveConfig.getId());
            return;
        }
        // 获取员工入职信息
        List<Long> userIds = userInfoList.stream().map(item -> item.getId()).collect(Collectors.toList());
        // 查询在灰度名单的人员
        Map<Long, Boolean> userMigrationMap;
        try {
            userMigrationMap = migrationService.verifyUsersIsEnableNewAttendance(userIds);
        } catch (Exception e) {
            log.error("批量验证用户迁移状态异常", e);
            XxlJobLogger.log("迁移验证异常，跳过处理: {}", e.getMessage());
            return;
        }
        // 统计启用新系统的用户
        List<Long> enableNewAttendanceUserIdList = userMigrationMap.entrySet()
                .stream()
                .filter(Map.Entry::getValue)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        int enableNewAttendanceUserSize = enableNewAttendanceUserIdList.size();
        XxlJobLogger.log("用户迁移状态统计 - 总数: {}, 启用新系统: {}, 未启用: {}",
                userIds.size(), enableNewAttendanceUserSize, (userIds.size() - enableNewAttendanceUserSize));

        if (enableNewAttendanceUserSize == 0) {
            XxlJobLogger.log("没有启用新考勤系统的用户，跳过处理");
            return;
        }
        userIds = enableNewAttendanceUserIdList;
        // 过滤启用新系统的用户的异常考勤
        userInfoList = userInfoList.stream()
                .filter(i -> enableNewAttendanceUserIdList.contains(i.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userInfoList)) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】 |，当前人员全部都在灰度人员里，无异常考勤要处理");
            return;
        }
        // 将员工信息以userId为key转化为map
        Map<Long, String> userInfoMap = userInfoList
                .stream()
                .filter(item -> Objects.nonNull(item.getUserCode()))
                .collect(Collectors.toMap(UserInfoDO::getId
                        , UserInfoDO::getUserCode
                        , (existing, replacement) -> existing));
        List<AttendanceUserEntryRecord> userEntryRecordList = userEntryRecordService.selectUserEntryByUserIds(userIds);
        // 将员工入职信息以userCode为key转化为map
        Map<String, Date> userEntryDateMap = new HashMap<>();
        userEntryRecordList.stream().forEach(item -> {
            if (Objects.nonNull(item.getConfirmDate())
                    && Objects.nonNull(userInfoMap.get(item.getUserId()))) {
                userEntryDateMap.put(userInfoMap.get(item.getUserId()), item.getConfirmDate());
            }
        });
        // 员工入职信息
        Map<Long, List<AttendanceUserEntryRecord>> userEntryMap = userEntryRecordList.stream().collect(Collectors.groupingBy(AttendanceUserEntryRecord::getUserId));
        // 根据当前时间获取假期规则配置的开始日期和结束日期范围
        List<UserLeaveRecordDO> allLeaveRecordList = userLeaveRecordService.selectReissueRecord(userIds, companyLeaveConfig.getId(),
                issueRule.getIssueMonth(), issueRule.getIssueDay(),
                localDate);
        Map<Long, List<UserLeaveRecordDO>> userLeaveRecordMap = Optional.ofNullable(allLeaveRecordList)
                .orElse(new ArrayList<>())
                .stream()
                .collect(Collectors.groupingBy(UserLeaveRecordDO::getUserId));
        // 所在假期规则的发放日
        DateTime targetDate = DateUtil.parse(localYear
                + "-" + issueMonth + "-"
                + issueDay + " 00:00:00", DatePattern.NORM_DATETIME_PATTERN);
        // 工龄查询
        Map<String, String> userMapForInitWorkSeniority = new HashMap<>();
        // 当前日期对应工龄
        Map<String, String> userMapForWorkSeniority = new HashMap<>();
        // 发放日期对应工龄
        Map<String, String> userMapForWorkSeniorityIssue = new HashMap<>();
        if ("workSeniority".equals(reissueType)) {
            List<String> userCodes = userInfoList.stream().map(item -> item.getUserCode()).collect(Collectors.toList());
            userMapForInitWorkSeniority = userLeaveInspectionService.selectInitialWorkSeniority(userCodes);
            userMapForWorkSeniority = userLeaveInspectionService.selectWorkSeniority(userCodes, userEntryDateMap, localDate);
            userMapForWorkSeniorityIssue = userLeaveInspectionService.selectWorkSeniority(userCodes, userEntryDateMap, targetDate);
        }
        // 补发员工
        List<UserInfoDO> reissueUserList = new ArrayList<>();
        // 补发余额
        Map<Long, BigDecimal> userReissueBalanceMap = new HashMap<>();
        // 遍历找出需要补发的员工
        for (UserInfoDO userInfo : userInfoList) {
            List<UserLeaveRecordDO> userLeaveRecordList = userLeaveRecordMap.get(userInfo.getId());
            if (CollectionUtils.isNotEmpty(userLeaveRecordList)) {
                // 获取最近的发放记录
                UserLeaveRecordDO userLeaveRecord = userLeaveRecordList.stream()
                        .sorted(Comparator.comparing(UserLeaveRecordDO::getCreateDate)
                                .reversed())
                        .collect(Collectors.toList()).get(0);
                // 如果是补发过的记录 则不继续发放(同一年最多补发一次)
                if (LeaveTypeEnum.RECALCULATE.getCode().equals(userLeaveRecord.getType())) {
                    XxlJobLogger.log("该用户 userCode:{},已存在补发记录,不继续发放"
                            , userInfo.getUserCode());
                    continue;
                }
            }
            // 查询符合条件的规则及应发余额
            BigDecimal reissueQuoteByRule = this.selectIssueQuote(reissueType, localDate, userInfo,
                    issueRuleRangeList, userMapForWorkSeniority.get(userInfo.getUserCode()));
            if (BigDecimal.ZERO.equals(reissueQuoteByRule)) {
                XxlJobLogger.log("该用户 userCode:{},未获取到对应的出生日期或发放规则,不继续发放", userInfo.getUserCode());
                continue;
            }
            // 获取确认入职日期
            Date confirmEntryDate = userEntryDateMap.get(userInfo.getUserCode());
            if (Objects.isNull(confirmEntryDate)) {
                XxlJobLogger.log("该用户 userCode:{}, 没有入职日期", userInfo.getUserCode());
                continue;
            }
            // 补发余额通过当前日期到年底进行折算
            BigDecimal betweenDays = BigDecimal.valueOf(DateUtil.between(localDate, DateUtil.endOfYear(localDate)
                    , DateUnit.DAY)).add(BigDecimal.ONE);
            int year = localDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate().getYear();
            BigDecimal percent = BigDecimalUtil.divide(betweenDays
                    , BigDecimal.valueOf(Year.of(year).isLeap() ? 366 : 365), 4);
            // 应发分钟数
            BigDecimal leaveMinutesByRule = reissueQuoteByRule
                    .multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS)
                    .multiply(BusinessConstant.MINUTES);
            BigDecimal targetQuoteDayByRule;
            // 判断入职日期是否是当年
            if (DateUtil.year(localDate) == DateUtil.year(confirmEntryDate)) {
                // 获取入职时应发的层级对应天数
                targetQuoteDayByRule = this.selectIssueQuote(reissueType, confirmEntryDate, userInfo,
                        issueRuleRangeList, userMapForInitWorkSeniority.get(userInfo.getUserCode()));
            } else {
                // 获取假期发放时应发的层级对应天数
                targetQuoteDayByRule = this.selectIssueQuote(reissueType, targetDate, userInfo,
                        issueRuleRangeList, userMapForWorkSeniorityIssue.get(userInfo.getUserCode()));
            }
            if (reissueQuoteByRule.compareTo(targetQuoteDayByRule) <= 0) {
                XxlJobLogger.log("该用户 userCode:{},不存在跨层级,无需发放", userInfo.getUserCode());
                continue;
            }
            // 计算补发余额
            BigDecimal targetMinutesByRule = targetQuoteDayByRule
                    .multiply(BusinessConstant.DEFAULT_LEGAL_WORKING_HOURS)
                    .multiply(BusinessConstant.MINUTES);
            BigDecimal reissueBalance = BigDecimalUtil.multiply(leaveMinutesByRule.subtract(targetMinutesByRule), percent);
            userReissueBalanceMap.put(userInfo.getId(), reissueBalance);
            reissueUserList.add(userInfo);
        }
        if (CollectionUtils.isEmpty(reissueUserList)) {
            XxlJobLogger.log("日期:{},该假期:{},不存在补发假期人员"
                    , localDate, companyLeaveConfig.getLeaveName());
            return;
        }
        XxlJobLogger.log("日期:{},该假期:{},补发假期人员:{}"
                , localDate, companyLeaveConfig.getLeaveName(), reissueUserList);
        // 补发假期
        reissueUserLeave(reissueUserList, userReissueBalanceMap, companyLeaveConfig, carryOverConfig, carryOverRangeList,
                localDate, userEntryMap);
    }

    /**
     * 补发人员假期
     *
     * @param reissueUserList
     * @param userReissueBalanceMap
     * @param companyLeaveConfig
     * @param localDate
     */
    private void reissueUserLeave(List<UserInfoDO> reissueUserList,
                                  Map<Long, BigDecimal> userReissueBalanceMap,
                                  CompanyLeaveConfigDO companyLeaveConfig,
                                  CompanyLeaveConfigCarryOverDO carryOverConfig,
                                  List<CompanyLeaveConfigCarryOverRangeDO> carryOverRangeList,
                                  Date localDate,
                                  Map<Long, List<AttendanceUserEntryRecord>> userEntryMap) {
        // 查询用户假期详情
        List<Long> reissueUserIds = reissueUserList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
        UserLeaveDetailQuery userLeaveDetailQuery = UserLeaveDetailQuery.builder()
                .userIds(reissueUserIds)
                .configId(companyLeaveConfig.getId())
                .build();
        // 获取所有补发员工拥有的假期
        List<UserLeaveDetailDO> userLeaveDetailList = userLeaveDetailService.selectUserLeaveDetail(userLeaveDetailQuery);
        if (CollectionUtils.isEmpty(userLeaveDetailList)) {
            XxlJobLogger.log("日期:{},该假期:{},没有获取到补发员工拥有的假期详情", localDate, companyLeaveConfig.getLeaveName());
            return;
        }
        // 获取所有补发员工拥有的假期的主键
        List<Long> leaveIdList = userLeaveDetailList.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
        Map<Long, List<UserLeaveDetailDO>> userLeaveDetailMap = userLeaveDetailList
                .stream()
                .collect(Collectors.groupingBy(UserLeaveDetailDO::getUserId));
        // 获取所有补发员工拥有的生效的并且未结转假期详情数据
        UserLeaveStageDetailQuery userLeaveStageDetailQuery = UserLeaveStageDetailQuery.builder()
                .isInvalid(WhetherEnum.NO.getKey())
                .leaveMark(WhetherEnum.NO.getKey())
                .leaveIdList(leaveIdList)
                .build();
        List<UserLeaveStageDetailDO> userLeaveStageDetailList = Optional.ofNullable(userLeaveStageDetailService.selectByCondition(userLeaveStageDetailQuery))
                .orElse(Collections.emptyList());
//        if (CollectionUtils.isEmpty(userLeaveStageDetailList)) {
//            XxlJobLogger.log("日期:{},该假期:{},没有获取到补发员工拥有的假期余额"
//                    , localDate, companyLeaveConfig.getLeaveName());
//            return;
//        }
        // 更新用户假期详情集合
        List<UserLeaveStageDetailDO> updateUserLeaveStageDetailList = Lists.newArrayList();
        // 新增用户假期详情集合
        List<UserLeaveStageDetailDO> addUserLeaveStageDetailList = Lists.newArrayList();
        // 新增用户假期操作记录集合
        List<UserLeaveRecordDO> addUserLeaveRecordList = Lists.newArrayList();
        // 遍历用户补发假期
        for (UserInfoDO reissueUser : reissueUserList) {
            BigDecimal reissueBalance = userReissueBalanceMap.get(reissueUser.getId());
            if (Objects.isNull(reissueBalance)) {
                XxlJobLogger.log("该假期:{},没有获取到补发员工 userCode:{} 拥有的补发余额"
                        , companyLeaveConfig.getLeaveName(), reissueUser.getUserCode());
                continue;
            }
            List<UserLeaveDetailDO> userLeaveDetail = userLeaveDetailMap.get(reissueUser.getId());
            if (CollectionUtils.isEmpty(userLeaveDetail)) {
                XxlJobLogger.log("该假期:{},没有获取到补发员工 userCode:{} 拥有的假期"
                        , companyLeaveConfig.getLeaveName(), reissueUser.getUserCode());
                continue;
            }
            List<Long> leaveIds = userLeaveDetail.stream().map(UserLeaveDetailDO::getId).collect(Collectors.toList());
            List<UserLeaveStageDetailDO> userStageList = userLeaveStageDetailList.stream()
                    .filter(item -> leaveIds.contains(item.getLeaveId()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(userStageList)) {
                XxlJobLogger.log("该假期:{},没有获取到补发员工 userCode:{} 拥有的假期发放余额, 新增假期发放余额"
                        , companyLeaveConfig.getLeaveName(), reissueUser.getUserCode());
                List<AttendanceUserEntryRecord> userEntryRecord = userEntryMap.get(reissueUser.getId());
                if (CollectionUtils.isEmpty(userEntryRecord)) {
                    XxlJobLogger.log("该假期:{},没有获取到补发员工 userCode:{} 的入职记录"
                            , companyLeaveConfig.getLeaveName(), reissueUser.getUserCode());
                }
                this.addLeaveStageDetail(companyLeaveConfig, userLeaveDetail.get(0), reissueBalance,
                        userEntryRecord.get(0), carryOverConfig, carryOverRangeList, localDate, addUserLeaveStageDetailList);
            } else {
                XxlJobLogger.log("该假期:{}, userCode:{} 更新对应假期发放余额"
                        , companyLeaveConfig.getLeaveName(), reissueUser.getUserCode());
                // 更新原有假期余额
                for (UserLeaveStageDetailDO leaveStageDetail : userStageList) {
                    BigDecimal leaveResidueMinutes = leaveStageDetail.getLeaveResidueMinutes();
                    // 补发假期
                    leaveStageDetail.setLeaveResidueMinutes(leaveResidueMinutes.add(reissueBalance));
                    BaseDOUtil.fillDOUpdateByUserOrSystem(leaveStageDetail);
                    leaveStageDetail.setLastUpdUserCode("xxl-job");
                    leaveStageDetail.setLastUpdUserName("xxl-job");
                    updateUserLeaveStageDetailList.add(leaveStageDetail);
                }
            }
            XxlJobLogger.log("该假期:{},用户 userCode:{} 补发成功！"
                    , companyLeaveConfig.getLeaveName(), reissueUser.getUserCode());
            // 新增假期补发记录
            this.addLeaveRecord(reissueUser, reissueBalance, localDate, companyLeaveConfig, addUserLeaveRecordList);
        }
        // 落库
        userLeaveDetailManage.userLeaveBalanceDaysUpdate(null, addUserLeaveStageDetailList, addUserLeaveRecordList, updateUserLeaveStageDetailList, null);
    }

    private BigDecimal selectIssueQuote(String reissueType, Date date, UserInfoDO userInfo,
                                        List<CompanyLeaveConfigIssueRuleRangeDO> issueRuleRangeList,
                                        String workSeniority) {
        BigDecimal reissueQuoteByRule;
        switch (reissueType) {
            case "age":
                reissueQuoteByRule = userLeaveInspectionService.selectLeaveDayByRule(date, userInfo, issueRuleRangeList);
                break;
            case "workSeniority":
                reissueQuoteByRule = userLeaveInspectionService.selectWorkSeniorityByRule(workSeniority, issueRuleRangeList);
                break;
            default:
                reissueQuoteByRule = BigDecimal.ZERO;
                break;
        }
        return reissueQuoteByRule;
    }


    /**
     * 增加假期调整操作记录
     *
     * @param userInfo               用户信息
     * @param totalMinutes           假期总分钟数
     * @param addUserLeaveRecordList 假期操作记录集合
     * @param date                   本地当前时间
     * @param companyLeaveConfig     假期规则
     */
    private void addLeaveRecord(UserInfoDO userInfo, BigDecimal totalMinutes,
                                Date date, CompanyLeaveConfigDO companyLeaveConfig,
                                List<UserLeaveRecordDO> addUserLeaveRecordList) {
        UserLeaveRecordDO recordDO = new UserLeaveRecordDO();
        recordDO.setId(defaultIdWorker.nextId());
        recordDO.setUserId(userInfo.getId());
        recordDO.setUserCode(userInfo.getUserCode());
        recordDO.setDate(date);
        recordDO.setDayId(Long.parseLong(DateUtil.format(date, DatePattern.PURE_DATE_PATTERN)));
        recordDO.setConfigId(companyLeaveConfig.getId());
        recordDO.setLeaveName(companyLeaveConfig.getLeaveName());
        recordDO.setLeaveType(companyLeaveConfig.getLeaveType());
        recordDO.setType(LeaveTypeEnum.RECALCULATE.getCode());
        recordDO.setLeaveMinutes(totalMinutes);
        recordDO.setRemark("符合跨层级新规则时重新计算补发假期/Replacement leave is recalculated when the new cross-tier rules are met");
        BaseDOUtil.fillDOInsertByUsrOrSystem(recordDO);
        recordDO.setOperationUserCode("xxl-job");
        recordDO.setOperationUserName("xxl-job");
        addUserLeaveRecordList.add(recordDO);
    }

    /**
     * 添加司龄/年龄/工龄递增假期阶段详情
     *
     * @param companyLeaveConfig
     * @param userLeaveDetail
     * @param leaveResidueMinutes
     * @param addUserLeaveStageDetailList
     */
    private void addLeaveStageDetail(CompanyLeaveConfigDO companyLeaveConfig,
                                     UserLeaveDetailDO userLeaveDetail,
                                     BigDecimal leaveResidueMinutes,
                                     AttendanceUserEntryRecord userEntryRecord,
                                     CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                     List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeInfoList,
                                     Date date,
                                     List<UserLeaveStageDetailDO> addUserLeaveStageDetailList) {
        UserLeaveStageDetailDO detailDO = new UserLeaveStageDetailDO();
        detailDO.setLeaveId(userLeaveDetail.getId());
        detailDO.setLeaveResidueMinutes(leaveResidueMinutes);
        detailDO.setLeaveUsedMinutes(BigDecimal.ZERO);
        detailDO.setPercentSalary(BigDecimal.ONE);
        detailDO.setStage(BusinessConstant.ONE);
        detailDO.setStatus(companyLeaveConfig.getStatus());
        // 设置假期详情为未结转，有效状态
        detailDO.setLeaveMark(WhetherEnum.NO.getKey());
        detailDO.setIsInvalid(WhetherEnum.NO.getKey());
        // 设置假期发放日期
        detailDO.setIssueDate(DateUtils.date2Str(date, DateFormatterUtil.FORMAT_YYYYMMDD));
        // 设置失效日期
        detailDO.setInvalidDate(companyLeaveConfigCarryOverService.getUserInvalidDate(date,
                userEntryRecord, leaveConfigCarryOver, leaveConfigCarryOverRangeInfoList));
        BaseDOUtil.fillDOUpdateByUserOrSystem(detailDO);
        addUserLeaveStageDetailList.add(detailDO);
    }


}
