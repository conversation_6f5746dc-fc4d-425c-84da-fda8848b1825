package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} LegalLeaveConfigDetailVO
 * {@code @since:} 2024-03-11 11:24
 * {@code @description:}
 */
@Data
public class LegalLeaveConfigDetailVO implements Serializable {

    private static final long serialVersionUID = -4864630083792819243L;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 指定国家+年度下的法定假期信息
     */
    private List<LegalLeaveConfigDetailInfoVO> legalLeaveConfigDetailInfoList;

}
