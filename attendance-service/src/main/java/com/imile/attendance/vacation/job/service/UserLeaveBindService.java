package com.imile.attendance.vacation.job.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.vacation.LeaveConfigRangRangeTypeEnum;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.vacation.CompanyLeaveConfigRangService;
import com.imile.attendance.vacation.CompanyLeaveConfigService;
import com.imile.attendance.vacation.command.CompanyLeaveConfigRangeCommand;
import com.imile.attendance.vacation.factory.CompanyLeaveConfigRangeFactory;
import com.imile.attendance.vacation.param.UserLeaveBindParam;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 假期绑定service实现
 *
 * <AUTHOR>
 * @since 2025/04/27
 */
@Slf4j
@Service
public class UserLeaveBindService {

    @Resource
    private UserInfoManage userInfoManage;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;
    @Resource
    private CompanyLeaveConfigService companyLeaveConfigService;
    @Resource
    private CompanyLeaveConfigRangService companyLeaveConfigRangService;
    @Resource
    private CompanyLeaveConfigRangeFactory companyLeaveConfigRangeFactory;
    @Resource
    private DefaultIdWorker defaultIdWorker;

    public void userLeaveBindHandler(UserLeaveBindParam param) {
        List<String> userCodeList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            userCodeList = Arrays.asList(param.getUserCodes().split(","));
        }
        List<String> countryList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getCountryList())) {
            countryList = Arrays.asList(param.getCountryList().split(","));
        }
        List<String> leaveNameList = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getLeaveNameList())) {
            leaveNameList = Arrays.asList(param.getLeaveNameList().split(","));
        }

        if (CollUtil.isEmpty(countryList)) {
            log.info("userLeaveBindHandler countryList is empty");
            return;
        }
        List<String> leaveEmployeeType = Arrays.asList(EmploymentTypeEnum.EMPLOYEE.getCode()
                , EmploymentTypeEnum.SUB_EMPLOYEE.getCode(), EmploymentTypeEnum.INTERN.getCode()
                , EmploymentTypeEnum.PART_TIMER.getCode(), EmploymentTypeEnum.CONSULTANT.getCode());

        // 获取用户信息
        UserDaoQuery.UserDaoQueryBuilder userDaoQueryBuilder = UserDaoQuery.builder().locationCountryList(countryList).employeeTypeList(leaveEmployeeType);
        if (CollUtil.isNotEmpty(userCodeList)) {
            userDaoQueryBuilder.userCodes(userCodeList);
        }
        UserDaoQuery query = userDaoQueryBuilder.build();
        log.info("userLeaveBindHandler query:{}", JSON.toJSONString(query));

        List<UserInfoDO> userInfoList = userInfoManage.listUsersByQuery(query);
        if (CollUtil.isEmpty(userInfoList)) {
            log.info("userLeaveBindHandler hrms userInfoList is empty");
            return;
        }
        List<Long> userIdList = userInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
        // 获取入职记录
        List<AttendanceUserEntryRecord> userEntryRecordList = userEntryRecordService.selectUserEntryByUserIds(userIdList);
        // 将入职记录按照用户id分组
        Map<Long, List<AttendanceUserEntryRecord>> userEntryRecordMap = userEntryRecordList.stream().collect(Collectors.groupingBy(AttendanceUserEntryRecord::getUserId));

        List<UserInfoDO> failUserInfoList = new ArrayList<>();
        List<UserInfoDO> successUserInfoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(userInfoList) && userInfoList.size() > 10) {
            log.info("userInfoList size is large");
            return;
        }
        log.info("xxl-job【userLeaveBindHandler】start:{}", JSON.toJSONString(userInfoList));
        for (UserInfoDO userInfo : userInfoList) {
            //查询用户信息
            if (ObjectUtil.isNull(userInfo)) {
                log.info("xxl-job【userLeaveBindHandler】---> userInfo is null");
                return;
            }
            //已经离职的不处理
            if (WorkStatusEnum.DIMISSION.getCode().equals(userInfo.getWorkStatus())) {
                log.info("xxl-job【userLeaveBindHandler】---> userCode：{},已经离职", userInfo.getUserCode());
                return;
            }

            Long id = userInfo.getId();
            String userCode = userInfo.getUserCode();
            String locationCountry = userInfo.getLocationCountry();

            //查询用户入职时间
            List<AttendanceUserEntryRecord> userEntryRecordDOList = userEntryRecordMap.get(id);
            if (CollectionUtils.isEmpty(userEntryRecordDOList)) {
                log.info("xxl-job【userLeaveBindHandler】---> userCode：{},没有入职记录", userCode);
                return;
            }
            // 查询国家假期配置
            List<CompanyLeaveConfigDO> companyLeaveConfigList;
            if (BusinessConstant.Y.equals(userInfo.getIsGlobalRelocation())) {
                // 派遣人员根据国籍查询对应假期
                String countryCode = userInfo.getCountryCode();
                if (StringUtils.isBlank(countryCode)) {
                    log.info("xxl-job【userLeaveBindHandler】 userCode：{},countryCode：{},No countryCode", userInfo.getUserCode(), userInfo.getCountryCode());
                    return;
                }
                String locationCountryCode = CountryCodeEnum.convert2InternalCountryCode(countryCode);
                if (StringUtils.isBlank(locationCountryCode)) {
                    log.info("xxl-job【userLeaveBindHandler】 userCode：{},locationCountry：{},No locationCountryCode", userInfo.getUserCode(), locationCountryCode);
                    return;
                }
                CompanyLeaveQuery configQuery = CompanyLeaveQuery.builder()
                        .country(locationCountryCode)
                        .isDispatch(BusinessConstant.Y)
                        .status(StatusEnum.ACTIVE.getCode())
                        .build();
                companyLeaveConfigList = companyLeaveConfigService.selectCompanyLeave(configQuery);
            } else {
                // 非派遣人员根据常驻国查询对应假期
                companyLeaveConfigList = companyLeaveConfigService.selectLeaveConfigByCountryList(Collections.singletonList(userInfo.getLocationCountry()));
                if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
                    log.info("handlerCompanyLeaveConfigRang userCode：{},locationCountry：{},No national holidays configured", userInfo.getUserCode(), userInfo.getLocationCountry());
                    return;
                }
            }
            // 过滤假期名称
            if (CollUtil.isNotEmpty(leaveNameList)) {
                List<String> finalLeaveNameList = leaveNameList;
                companyLeaveConfigList = companyLeaveConfigList.stream()
                        .filter(item -> finalLeaveNameList.contains(item.getLeaveName()))
                        .collect(Collectors.toList());
            }
            //默认有事假
            if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
                log.info("xxl-job【userLeaveBindHandler】---> userCode：{},locationCountry：{},没有配置国家假期", userCode, locationCountry);
                return;
            }

            // 判断是够使用新逻辑
            List<CompanyLeaveConfigRangeCommand> targetListRange = new ArrayList<>();
            List<Long> leaveIdList = companyLeaveConfigList.stream().map(item -> item.getId()).collect(Collectors.toList());
            // 获取用户假期绑定范围
            List<CompanyLeaveConfigRangDO> companyLeaveConfigRang = companyLeaveConfigRangService.selectByLeaveId(leaveIdList);
            // 处理该假期配置的范围
            for (CompanyLeaveConfigDO leaveConfig : companyLeaveConfigList) {
                Long leaveId = leaveConfig.getId();
                String leaveType = leaveConfig.getLeaveType();

                log.info("假期id：{}，假期类型：{}", leaveId, leaveType);

                List<CompanyLeaveConfigRangDO> rangList = companyLeaveConfigRang.stream()
                        .filter(e -> ObjectUtil.equal(e.getLeaveId(), leaveId)
                                && ObjectUtil.equal(e.getUserCode(), userCode))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(rangList)) {
                    log.info("user_code:{} leave_id:{} has bind rang", userCode, leaveId);
                    continue;
                }
                if (companyLeaveConfigService.checkInCondition(leaveConfig, userInfo)) {
                    targetListRange.add(this.buildConfigRange(leaveConfig, userInfo.getUserCode()));
                }
            }
            if (CollUtil.isEmpty(targetListRange)) {
                log.info("该用户：{}，不满足该国家：{}，下所有假期的条件，未绑定假期范围表", userInfo.getUserCode(), userInfo.getLocationCountry());
                return;
            }
            log.info("用户：{}，绑定假期范围表：{}", userInfo.getUserCode(), targetListRange);
            if (Boolean.TRUE.equals(param.getFlag())) {
                // 保存用户假期范围
                companyLeaveConfigRangeFactory.saveBatch(targetListRange);
            }
        }
    }

    private CompanyLeaveConfigRangeCommand buildConfigRange(CompanyLeaveConfigDO leaveConfig,
                                                            String userCode) {
        // 给用户设置假期范围
        CompanyLeaveConfigRangeCommand leaveConfigRang = new CompanyLeaveConfigRangeCommand();
        leaveConfigRang.setId(defaultIdWorker.nextId());
        leaveConfigRang.setLeaveId(leaveConfig.getId());
        leaveConfigRang.setRangeType(LeaveConfigRangRangeTypeEnum.DEPT.getType());
        leaveConfigRang.setUserCode(userCode);
        return leaveConfigRang;
    }
}
