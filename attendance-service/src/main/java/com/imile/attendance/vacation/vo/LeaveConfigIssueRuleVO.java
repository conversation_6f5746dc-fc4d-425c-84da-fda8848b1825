package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIssueRuleVO
 * {@code @since:} 2024-04-13 17:44
 * {@code @description:}
 */
@Data
public class LeaveConfigIssueRuleVO implements Serializable {
    private static final long serialVersionUID = -2753467191127699171L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家假期配置id
     */
    private Long leaveId;

    /**
     * 发放频次：1：周期性发放，2:一次性发放
     */
    private Integer issueFrequency;

    /**
     * 发放时间：1：每年固定日（issueFrequency = 1的时候），2:每月固定日（issueFrequency = 1的时候），3:员工入职日：（issueFrequency = 2的时候）
     */
    private Integer issueTime;

    /**
     * 发放日期：月份：发放频次为周期性发放，发放时间是每年的时候月份不为0，发放频次为周期性发放，发放时间是每月的时候月份为0
     */
    private Integer issueMonth;

    /**
     * 发放日期：日
     */
    private Integer issueDay;

    /**
     * 发放类型：1:固定额度 2:司领递增 3:不限定额度 4:无初始额度 5:按派遣国远近 6:随年龄增加 7:随工龄增加 8:按常驻省市配置
     */
    private Integer issueType;

    /**
     * 发放额度
     */
    private BigDecimal issueQuota;

    /**
     * 循环单位(每满xx年/月)
     */
    private Integer cycleUnit;

    /**
     * 循环数值(每满xx年/月)
     */
    private Integer cycleNumber;

    /**
     * 是否折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    private Long isConvert;

    /**
     * 是否按入职日到派遣日折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    private Integer isConvertDispatch;

    /**
     * 假期发放规则是司龄的时候，范围数据必填，其他发放类型不需要该字段
     */
    private List<LeaveConfigIssueRuleRangeVO> leaveConfigIssueRuleRangeList;

    /**
     * 路途假范围数据
     */
    private List<LeaveJourneyConfigVO> leaveJourneyConfigList;

    /**
     * 假期发放时取整规则：0:表示无该选项 ，1:表示不取整（四舍五入两位小数），2:表示向下取整（1.23-->1），3:表示向上取整（1.23-->2）:当is_convert == 1的时候，该字段才不为0，其余为0
     */
    private Integer issueRoundingRule;

    /**
     * 是否符合跨层级新规则时重新计算发放假期 0:否 1:是
     */
    private Integer isRecalculate;
}
