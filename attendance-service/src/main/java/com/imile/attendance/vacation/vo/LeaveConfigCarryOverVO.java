package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigCarryOverVO
 * {@code @since:} 2024-04-13 17:44
 * {@code @description:}
 */
@Data
public class LeaveConfigCarryOverVO implements Serializable {
    private static final long serialVersionUID = 1241929702408863814L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家假期配置id
     */
    private Long leaveId;

    /**
     * 是否永久有效：1：是 2：否
     */
    private Integer isInvalid;

    /**
     * 是否可结转：1：可结转 2：不可结转
     */
    private Integer isCarryOver;

    /**
     * 最大结转天数
     */
    private Integer maxCarryOverDay;

    /**
     * 结转失效类型：0：固定日期 1：按年失效 2：按入职日期设置
     */
    private Integer invalidType;

    /**
     * 结转失效年：0：次年 1：第三年 2：第四年
     */
    private Integer invalidYear;

    /**
     * 失效日期：invalid_date 示例：0124
     */
    private Long invalidDate;

    /**
     * 假期失效类型是按入职日期设置的时候对应的范围数据
     */
    private List<LeaveConfigCarryOverRangeVO> leaveConfigCarryOverRangeList;
}
