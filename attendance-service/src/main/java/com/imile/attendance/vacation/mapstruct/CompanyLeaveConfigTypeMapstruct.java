package com.imile.attendance.vacation.mapstruct;


import com.imile.attendance.infrastructure.config.MapperConfiguration;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveTypeDO;
import com.imile.attendance.vacation.dto.CompanyLeaveTypeDTO;
import com.imile.attendance.vacation.vo.CompanyLeaveTypeVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/26
 * @Description
 */
@Mapper(config = MapperConfiguration.class)
public interface CompanyLeaveConfigTypeMapstruct {

    CompanyLeaveConfigTypeMapstruct INSTANCE = Mappers.getMapper(CompanyLeaveConfigTypeMapstruct.class);

    List<CompanyLeaveTypeDTO> toConfigTypeDTO(List<CompanyLeaveTypeDO> configDO);

    // VO转换
    List<CompanyLeaveTypeVO> toConfigTypeVO(List<CompanyLeaveTypeDTO> companyLeaveConfigDTO);

}
