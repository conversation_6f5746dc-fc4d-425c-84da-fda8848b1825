package com.imile.attendance.vacation.job;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.vacation.job.service.UserLeaveCarryOverService;
import com.imile.attendance.vacation.param.UserLeaveCarryOverParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * {@code @author:} allen
 * {@code @className:} UserLeaveCarryOverHandler
 * {@code @since:} 2024-04-29 16:22
 * {@code @description:} 用户假期结转处理
 */
@Slf4j
@Component
public class UserLeaveCarryOverHandler {
    @Resource
    private UserLeaveCarryOverService userLeaveCarryOverService;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_CARRY_OVER_HANDLER)
    public ReturnT<String> userLeaveCarryOverHandler(String content) {
        UserLeaveCarryOverParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, UserLeaveCarryOverParam.class)
                : new UserLeaveCarryOverParam();
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】参数为空");
            return ReturnT.FAIL;
        }
        // 定时任务执行默认参数
        this.buildDefaultParam(param);
        // 执行job
        userLeaveCarryOverService.userLeaveCarryOverHandler(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 参数默认值设置
     *
     * @param param
     */
    private void buildDefaultParam(UserLeaveCarryOverParam param) {
        if (ObjectUtil.isNull(param.getLocalYear())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】年份为空，设置默认值为当年");
            // 获取当前年份
            int year = DateUtil.year(DateUtil.date());
            param.setLocalYear(year);
        }
        if (ObjectUtil.isNull(param.getLocalMonth())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】年份为空，设置默认值12");
            param.setLocalMonth(BusinessConstant.MONTH_YEAR);
        }
        if (ObjectUtil.isNull(param.getLocalDay())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】日为空，设置默认值31");
            param.setLocalDay(BusinessConstant.MONTH_DAY);
        }
        if (ObjectUtil.isNull(param.getLocalHour())) {
            XxlJobLogger.log("xxl-job【userLeaveCarryOverHandler】时为空，设置默认值23");
            param.setLocalHour(BusinessConstant.HOUR_DAY_LAST);
        }
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            param.setEmployeeTypeList(Arrays.asList(param.getEmployeeType().split(",")));
        }
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            param.setUserCodeList(Arrays.asList(param.getUserCodes().split(",")));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            param.setCountryArrayList(Arrays.asList(param.getCountryList().split(",")));
        }
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            param.setLeaveNameList(Arrays.asList(param.getLeaveName().split(",")));
        }
    }
}
