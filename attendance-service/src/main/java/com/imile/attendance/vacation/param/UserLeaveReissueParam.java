package com.imile.attendance.vacation.param;

import com.imile.attendance.common.param.CountryDateParam;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/2/17
 * @Description 用户假期补发处理参数
 */
@Data
public class UserLeaveReissueParam extends CountryDateParam {
    /**
     * 所属国
     */
    private String countryList;

    /**
     * 用户编码
     */
    private String userCodes;

    /**
     * 假期类型
     */
    private String leaveName;

    /**
     * 用工类型
     */
    private String employeeType;

    /**
     * 补发类型(工龄：workSeniority/年龄：age)
     */
    private String reissueType;

    /**
     * 程序逻辑转换
     */
    @NotNull
    private List<String> employeeTypeList = new ArrayList<>();
    @NotNull
    private List<String> userCodeList = new ArrayList<>();
    @NotNull
    private List<String> countryArrayList = new ArrayList<>();
    @NotNull
    private List<String> leaveNameList = new ArrayList<>();
}