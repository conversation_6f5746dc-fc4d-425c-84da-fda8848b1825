package com.imile.attendance.vacation.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期：假期详情表保存入参")
public class CompanyLeaveItemConfigAddCommand implements Serializable {

    /**
     * 公司假期配置id
     */
    @ApiModelProperty(value = "公司假期配置id")
    private Long leaveId;

    /**
     * 阶段
     */
    @ApiModelProperty(value = "阶段")
    private Integer stage;

    /**
     * 假期长度
     */
    @ApiModelProperty(value = "假期长度")
    private BigDecimal leaveDay;

    /**
     * 百分比日薪
     */
    @ApiModelProperty(value = "百分比日薪")
    private BigDecimal percentSalary;
}
