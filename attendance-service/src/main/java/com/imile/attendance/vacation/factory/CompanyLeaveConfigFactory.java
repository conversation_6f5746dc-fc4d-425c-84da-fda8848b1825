package com.imile.attendance.vacation.factory;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CountryCodeEnum;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.WorkStatusEnum;
import com.imile.attendance.enums.vacation.LeaveConfigInvalidTypeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigIssueTypeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigRangRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserDaoQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveDetailQuery;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigRangDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.vacation.CompanyLeaveConfigManage;
import com.imile.attendance.vacation.command.CompanyLeaveConfigAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigCarryOverAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigIssueRuleAddCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigStatusUpdateCommand;
import com.imile.attendance.vacation.command.CompanyLeaveConfigUpdateCommand;
import com.imile.attendance.vacation.command.CompanyLeaveItemConfigAddCommand;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigCarryOverMapstruct;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigIssueRuleMapstruct;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigMapstruct;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.BeanUtils;
import com.imile.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Slf4j
@Service
public class CompanyLeaveConfigFactory {

    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;
    @Resource
    private CompanyLeaveConfigDao companyLeaveConfigDao;
    @Resource
    private CompanyLeaveConfigRangDao companyLeaveConfigRangDao;
    @Resource
    private UserLeaveDetailDao userLeaveDetailDao;
    @Resource
    private CompanyLeaveConfigManage companyLeaveConfigManage;
    @Resource
    private LogRecordService logRecordService;


    @Transactional
    public boolean add(CompanyLeaveConfigAddCommand param) {
        log.info("新增福利假期配置入参：{}", param);
        // config param build
        buildAddParam(param);
        // config add check
        checkCompanyLeaveConfigParam(param);

        // 1. 构建国家假期主表信息
        CompanyLeaveConfigDO leaveConfig = this.buildCompanyLeaveConfig(param);

        // 2. 构建福利假期使用范围表
        List<CompanyLeaveConfigRangDO> leaveConfigRangList = Lists.newArrayList();
        setLeaveConfigRange(param, leaveConfig, leaveConfigRangList);

        // 3. 构建福利假发放规则数据
        CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule = this.buildCompanyLeaveConfigIssueRule(leaveConfig, param.getLeaveConfigIssueRuleSaveParam());

        // 4. 如果发放类型是司龄递增/年龄递增/工龄递增，那么需要构建发放范围数据
        List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = this.buildCompanyLeaveConfigIssueRuleRange(leaveConfigIssueRule
                , param.getLeaveConfigIssueRuleSaveParam());

        // 5. 如果发放类型是派遣国远近，那么需要构建路途假数据
        List<CompanyLeaveJourneyConfigDO> leaveJourneyConfigList = this.buildCompanyLeaveJourneyConfigRange(leaveConfigIssueRule
                , param.getLeaveConfigIssueRuleSaveParam());

        // 6. 其他情况，需要构建假期详情数据
        List<CompanyLeaveItemConfigDO> leaveItemConfigList = this.buildCompanyLeaveItemConfig(leaveConfig, param.getLeaveItemConfigSaveParamList());

        // 7. 构建结转规则数据
        CompanyLeaveConfigCarryOverDO leaveConfigCarryOver = this.buildCompanyLeaveConfigCarryOver(leaveConfig,
                param.getLeaveConfigCarryOverSaveParam());

        // 8. 构建结转规则失效范围数据
        List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList = this.buildCompanyLeaveConfigCarryOverRange(leaveConfigCarryOver, param.getLeaveConfigCarryOverSaveParam());

        // 9. save
        companyLeaveConfigManage.addWelfareLeaveConfig(leaveConfig, leaveConfigRangList, leaveConfigIssueRule
                , leaveConfigIssueRuleRangeList, leaveItemConfigList, leaveConfigCarryOver
                , leaveConfigCarryOverRangeList, leaveJourneyConfigList);
        // 10. operation log
        logRecordService.recordOperation(leaveConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.LEAVE_CONFIG_ADD.getCode())
                        .country(leaveConfig.getCountry())
                        .bizName(leaveConfig.getLeaveName())
                        .build());
        return true;
    }

    @Transactional
    public Boolean update(CompanyLeaveConfigUpdateCommand updateCommand) {
        // 1. 查询假期信息
        CompanyLeaveConfigDO leaveConfig = companyLeaveConfigDao.getById(updateCommand.getId());
        if (ObjectUtil.isNull(leaveConfig)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.NO_COMPANY_LEAVE_EXISTS_ERROR);
        }
        // 2.更新假期表
        updateLeaveConfig(leaveConfig, updateCommand);
        // 3. 新增范围表 删除旧范围数据
        List<CompanyLeaveConfigRangDO> leaveConfigRangList = Lists.newArrayList();
        // 需要删除的范围数据
        List<CompanyLeaveConfigRangDO> leaveConfigRangDeleteList = Lists.newArrayList();
        List<CompanyLeaveConfigRangDO> hrmsCompanyLeaveConfigRangDOS = companyLeaveConfigRangDao.selectByLeaveId(Arrays.asList(leaveConfig.getId()));
        if (CollectionUtils.isNotEmpty(hrmsCompanyLeaveConfigRangDOS)) {
            for (CompanyLeaveConfigRangDO leaveConfigRangDO : hrmsCompanyLeaveConfigRangDOS) {
                leaveConfigRangDO.setIsDelete(IsDeleteEnum.YES.getCode());
                BaseDOUtil.fillDOUpdate(leaveConfigRangDO);
                leaveConfigRangDeleteList.add(leaveConfigRangDO);
            }
        }
        // 需要新增的范围数据
        CompanyLeaveConfigAddCommand saveParam = BeanUtils.convert(updateCommand, CompanyLeaveConfigAddCommand.class);
        setLeaveConfigRange(saveParam, leaveConfig, leaveConfigRangList);
        // 4. update
        companyLeaveConfigManage.updateWelfareLeaveConfig(leaveConfig, leaveConfigRangDeleteList, leaveConfigRangList);
        // 5. operation log
        logRecordService.recordOperation(leaveConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.UPDATE)
                        .operationType(OperationTypeEnum.LEAVE_CONFIG_UPDATE.getCode())
                        .country(leaveConfig.getCountry())
                        .bizName(leaveConfig.getLeaveName())
                        .build());
        return true;
    }

    @Transactional
    public boolean updateStatus(CompanyLeaveConfigStatusUpdateCommand updateCommand) {
        log.info("修改国家假期状态入参：{}", updateCommand);
        // 1. 查询国家假期信息
        CompanyLeaveConfigDO leaveConfig = companyLeaveConfigDao.getById(updateCommand.getId());
        if (ObjectUtil.isNull(leaveConfig)) {
            throw BusinessException.get(ErrorCodeEnum.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc(), "no company leave exists");
        }
        if (StringUtils.equals(leaveConfig.getStatus(), updateCommand.getStatus())) {
            return true;
        }
        leaveConfig.setStatus(updateCommand.getStatus());
        BaseDOUtil.fillDOUpdate(leaveConfig);

        // 2. 修改国家假期状态，需要修改用户假期表状态
        UserDaoQuery userDaoQuery = UserDaoQuery.builder().locationCountry(leaveConfig.getCountry()).build();
        List<UserInfoDO> userList = userInfoDao.userList(userDaoQuery);
        List<Long> userIdList = userList.stream().map(UserInfoDO::getId).collect(Collectors.toList());
        UserLeaveDetailQuery query = UserLeaveDetailQuery.builder()
                .userIds(userIdList)
                .configId(leaveConfig.getId())
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        if (StringUtils.equalsIgnoreCase(StatusEnum.ACTIVE.getCode(), updateCommand.getStatus())) {
            query.setStatus(StatusEnum.DISABLED.getCode());
        }
        List<UserLeaveDetailDO> userLeaveDetailDOList = userLeaveDetailDao.selectUserLeaveDetail(query);
        userLeaveDetailDOList.forEach(item -> {
            item.setStatus(leaveConfig.getStatus());
            BaseDOUtil.fillDOUpdate(item);
        });
        // 3. updateStatus
        companyLeaveConfigManage.updateStatus(leaveConfig, userLeaveDetailDOList);
        // 4. operation log
        logRecordService.recordOperation(leaveConfig,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.UPDATE)
                        .operationType(OperationTypeEnum.LEAVE_CONFIG_UPDATE.getCode())
                        .country(leaveConfig.getCountry())
                        .bizName(leaveConfig.getLeaveName())
                        .build());
        return true;
    }

    /**
     * 校验入参
     *
     * @param param 入参
     */
    private void checkCompanyLeaveConfigParam(CompanyLeaveConfigAddCommand param) {
        if (ObjectUtil.isNull(param)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        // 主表字段校验
        if (ObjectUtil.equal(param.getIsUploadAttachment(), 1)) {
            if (ObjectUtil.isNull(param.getUploadAttachmentCondition())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_WELFARE_LEAVE_CONFIG_LEAVE_CONDITION_ERROR);
            }
        }
        // 发放范围校验
        if (BusinessConstant.Y.equals(param.getIsDispatch()) && CollectionUtils.isEmpty(param.getDispatchCountry())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ADD_WELFARE_LEAVE_CONFIG_LEAVE_DISPATCH_COUNTRY_ERROR);
        }

        // 发放规则校验
        if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        // 如果发放频次是一次性发放，那么发放时间必须是员工入职日，并且没有发放日期
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueFrequency(), 2)) {
            if (!ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueTime(), 3)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
            }
            if (ObjectUtil.isNotNull(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth())
                    && param.getLeaveConfigIssueRuleSaveParam().getIssueMonth() != 0) {
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
            }
            if (ObjectUtil.isNotNull(param.getLeaveConfigIssueRuleSaveParam().getIssueDay())
                    && param.getLeaveConfigIssueRuleSaveParam().getIssueDay() != 0) {
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
            }
        }
        // 发放频次是周期性发放,如果发放时间是每年固定日，那么发放日期的月份不能为null，发放日期的日不能为null，如果发放时间是每月固定日，那么发放日期的月份必须为null，发放日期的日不能为null
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueFrequency(), 1)) {
            if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueTime(), 1)) {
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth())
                        || Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
                }
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueDay())
                        || Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueDay(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
                }
            }
            if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueTime(), 2)) {
                if (ObjectUtil.isNotNull(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth())
                        && !Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueMonth(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
                }
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueDay())
                        || Objects.equals(param.getLeaveConfigIssueRuleSaveParam().getIssueDay(), BusinessConstant.ZERO)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
                }
            }
        }

        // 如果额度类型是司领递增，则范围数据必填。其他的额度类型，阶段信息必填，如果是固定额度，那么发放额度必填。
        if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueType(), 2)) {
            if (CollectionUtils.isEmpty(param.getLeaveConfigIssueRuleSaveParam().getLeaveConfigIssueRuleRangeList())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
            }
        } else {
            if (ObjectUtil.equal(param.getLeaveConfigIssueRuleSaveParam().getIssueType(), 1)) {
                if (ObjectUtil.isNull(param.getLeaveConfigIssueRuleSaveParam().getIssueQuota())) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
                }
            }
        }

        // 结转规则校验
        if (ObjectUtil.isNull(param.getLeaveConfigCarryOverSaveParam())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
        // 如果假期是否永久有效为否时，那么有效期必填，更新周期必填
        if (ObjectUtil.equal(param.getLeaveConfigCarryOverSaveParam().getIsInvalid(), 2)) {
            if (ObjectUtil.isNull(param.getUseCycle()) || ObjectUtil.isNull(param.getUseStartDate()) || ObjectUtil.isNull(param.getUseEndDate())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
            }
        }

        // 校验同国家下假期名称不能重复
        CompanyLeaveQuery query = CompanyLeaveQuery.builder()
                .leaveName(param.getLeaveName())
                .country(param.getCountry())
                .build();
        List<CompanyLeaveConfigDO> leaveConfigDOList = companyLeaveConfigDao.selectLeaveConfig(query);
        if (CollectionUtils.isNotEmpty(leaveConfigDOList)) {
            throw BusinessException.get(ErrorCodeEnum.COMPANY_LEAVE_REPEAT_ERROR.getDesc(), "company leave repeat");
        }
    }

    /**
     * 封装入参
     */
    private void buildAddParam(CompanyLeaveConfigAddCommand param) {
        param.setLeaveName(param.getLeaveName().trim());
    }

    /**
     * 构建主表信息
     */
    private CompanyLeaveConfigDO buildCompanyLeaveConfig(CompanyLeaveConfigAddCommand param) {
        CompanyLeaveConfigDO leaveConfig = CompanyLeaveConfigMapstruct.INSTANCE.mapToCompanyLeaveConfigDO(param);

        leaveConfig.setId(defaultIdWorker.nextId());
        String employeeType = String.join(",", param.getEmployeeTypeList());
        String deptId = param.getDeptIdList().stream().map(Convert::toStr).collect(Collectors.joining(","));
        leaveConfig.setEmployeeType(employeeType);
        leaveConfig.setDeptIds(deptId);
        // 设置派遣国
        if (CollectionUtils.isNotEmpty(param.getDispatchCountry())) {
            String dispatchCountry = StringUtils.join(param.getDispatchCountry(), ",");
            leaveConfig.setDispatchCountry(dispatchCountry);
        }
        BaseDOUtil.fillDOInsert(leaveConfig);

        return leaveConfig;
    }

    /**
     * 构建范围表信息
     */
    private void setLeaveConfigRange(CompanyLeaveConfigAddCommand param,
                                     CompanyLeaveConfigDO leaveConfig,
                                     List<CompanyLeaveConfigRangDO> leaveConfigRangList) {
        // 根据userIdList查询用户信息
        UserDaoQuery userDaoQuery = buildUserQuery();
        if (CollectionUtils.isNotEmpty(param.getEmployeeTypeList())) {
            userDaoQuery.setEmployeeTypeList(param.getEmployeeTypeList());
        }
        if (Objects.nonNull(param.getUseSex())
                && !BusinessConstant.ZERO.equals(param.getUseSex())) {
            userDaoQuery.setSex(param.getUseSex());
        }
        // 如果有员工，只查询员工
        UserDaoQuery userOnlyQuery = buildUserQuery();
        List<UserInfoDO> userInfoList = Lists.newArrayList();
        List<UserInfoDO> deptUserInfoList;
        // 设置派遣人员查询条件
        setDispatchUserQuery(userDaoQuery, param);
        setDispatchUserQuery(userOnlyQuery, param);
        // 改动点：如果部门为空，员工不为空，那就只查询员工,并且和用工类型、性别、地区查出来得数据取并集
        if (CollectionUtils.isNotEmpty(param.getUserCodeList()) && CollectionUtils.isEmpty(param.getDeptIdList())) {
            userOnlyQuery.setUserCodes(param.getUserCodeList());
            userInfoList = userInfoDao.userList(userOnlyQuery);
            //如果只选了用工类型、性别、地区，也录成dept类型得，这样返回展示员工不会多
            deptUserInfoList = userInfoDao.userList(userDaoQuery);
        } else if (CollectionUtils.isNotEmpty(param.getDeptIdList()) && CollectionUtils.isEmpty(param.getUserCodeList())) {
            //如果部门不为空，员工为空，那就只交集部门
            userDaoQuery.setDeptIds(param.getDeptIdList());
            deptUserInfoList = userInfoDao.userList(userDaoQuery);
        } else if (CollectionUtils.isEmpty(param.getUserCodeList()) && CollectionUtils.isEmpty(param.getDeptIdList())) {
            //如果部门和员工都为空，那就取所有交集的员工
            deptUserInfoList = userInfoDao.userList(userDaoQuery);
        } else {
            //如果部门和员工都不为空，需要取员工和部门的并集
            userDaoQuery.setDeptIds(param.getDeptIdList());
            deptUserInfoList = userInfoDao.userList(userDaoQuery);

            userOnlyQuery.setUserCodes(param.getUserCodeList());
            userInfoList = userInfoDao.userList(userOnlyQuery);
        }
        // 获取targetDeptUserInfoList的主键id
        List<Long> targetUserInfoIdList = userInfoList.stream().map(UserInfoDO::getId).collect(Collectors.toList());

        List<UserInfoDO> targetDeptUserInfoList = Lists.newArrayList();
        // 遍历部门信息，与查出来的员工取并集
        for (UserInfoDO deptUserInfo : deptUserInfoList) {
            // 如果已经有用户级别的，那么不再添加为部门级别的
            if (targetUserInfoIdList.contains(deptUserInfo.getId())) {
                continue;
            }
            targetDeptUserInfoList.add(deptUserInfo);
        }
        // 过滤不满足入职日期范围人员
        targetUserInfoIdList.addAll(targetDeptUserInfoList
                .stream()
                .map(UserInfoDO::getId)
                .collect(Collectors.toList()));
        this.filterUserEntryDate(param, targetDeptUserInfoList, userInfoList, targetUserInfoIdList);
        // 构造假期范围数据
        targetDeptUserInfoList.forEach(deptUser -> buildLeaveConfigRang(leaveConfig, LeaveConfigRangRangeTypeEnum.DEPT.getType(), deptUser, leaveConfigRangList));
        userInfoList.forEach(user -> buildLeaveConfigRang(leaveConfig, LeaveConfigRangRangeTypeEnum.USER.getType(), user, leaveConfigRangList));
    }

    /**
     * 构建福利假发放规则信息
     */
    private CompanyLeaveConfigIssueRuleDO buildCompanyLeaveConfigIssueRule(CompanyLeaveConfigDO leaveConfig,
                                                                           CompanyLeaveConfigIssueRuleAddCommand param) {
        CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule = CompanyLeaveConfigIssueRuleMapstruct.INSTANCE.mapToCompanyLeaveIssueRuleConfigDO(param);
        leaveConfigIssueRule.setId(defaultIdWorker.nextId());
        leaveConfigIssueRule.setLeaveId(leaveConfig.getId());
        BaseDOUtil.fillDOInsert(leaveConfigIssueRule);
        return leaveConfigIssueRule;
    }

    /**
     * 构建福利假发放规则范围信息
     */
    private List<CompanyLeaveConfigIssueRuleRangeDO> buildCompanyLeaveConfigIssueRuleRange(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                                                           CompanyLeaveConfigIssueRuleAddCommand param) {
        // 构建发放规则阶梯范围数据（含司龄递增、年龄递增、工龄递增、常驻省市）
        List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = Lists.newArrayList();
        if (LeaveConfigIssueTypeEnum.getRangeTypeList().contains(param.getIssueType())) {
            leaveConfigIssueRuleRangeList = CompanyLeaveConfigIssueRuleMapstruct.INSTANCE.mapToCompanyLeaveIssueRuleRangeConfigDO(param.getLeaveConfigIssueRuleRangeList());
            leaveConfigIssueRuleRangeList.forEach(leaveConfigIssueRuleRange -> {
                leaveConfigIssueRuleRange.setId(defaultIdWorker.nextId());
                leaveConfigIssueRuleRange.setIssueRuleId(leaveConfigIssueRule.getId());
                BaseDOUtil.fillDOInsert(leaveConfigIssueRuleRange);
            });
        }
        return leaveConfigIssueRuleRangeList;
    }

    /**
     * 构建路途假范围信息
     */
    private List<CompanyLeaveJourneyConfigDO> buildCompanyLeaveJourneyConfigRange(CompanyLeaveConfigIssueRuleDO leaveConfigIssueRule,
                                                                                  CompanyLeaveConfigIssueRuleAddCommand param) {
        // 构建发放规则阶梯范围数据（含司龄递增、年龄递增、工龄递增、常驻省市）
        List<CompanyLeaveJourneyConfigDO> leaveJourneyConfigList = Lists.newArrayList();
        if (ObjectUtil.equal(param.getIssueType()
                , LeaveConfigIssueTypeEnum.DISPATCH_COUNTRY_DISTANCE.getType())) {
            leaveJourneyConfigList = CompanyLeaveConfigIssueRuleMapstruct.INSTANCE.mapToCompanyLeaveJourneyConfigDO(param.getLeaveJourneyConfigList());
            leaveJourneyConfigList.forEach(leaveJourneyConfig -> {
                leaveJourneyConfig.setId(defaultIdWorker.nextId());
                leaveJourneyConfig.setIssueRuleId(leaveConfigIssueRule.getId());
                BaseDOUtil.fillDOInsert(leaveJourneyConfig);
            });
        }
        return leaveJourneyConfigList;
    }

    /**
     * 构建假期阶段信息
     */
    private List<CompanyLeaveItemConfigDO> buildCompanyLeaveItemConfig(CompanyLeaveConfigDO leaveConfig,
                                                                       List<CompanyLeaveItemConfigAddCommand> leaveItemConfigSaveParamList) {
        List<CompanyLeaveItemConfigDO> leaveItemConfigList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(leaveItemConfigSaveParamList)) {
            // 假期百分比处理
            // 根据阶段从低到高排序
            List<CompanyLeaveItemConfigAddCommand> itemList = leaveItemConfigSaveParamList
                    .stream()
                    .sorted(Comparator.comparing(CompanyLeaveItemConfigAddCommand::getStage))
                    .collect(Collectors.toList());
            itemList.forEach(item -> {
                // 百分比不为0的时候，需要除以100
                if (item.getPercentSalary().compareTo(BigDecimal.ZERO) > 0) {
                    item.setPercentSalary(item.getPercentSalary().divide(new BigDecimal(100)));
                }
            });

            BigDecimal startDayTemp = BigDecimal.ONE;
            BigDecimal endDayTemp = BigDecimal.ZERO;
            for (CompanyLeaveItemConfigAddCommand itemConfigParan : itemList) {
                endDayTemp = endDayTemp.add(itemConfigParan.getLeaveDay());
                CompanyLeaveItemConfigDO itemConfigDO = new CompanyLeaveItemConfigDO();
                itemConfigDO.setId(defaultIdWorker.nextId());
                itemConfigDO.setLeaveId(leaveConfig.getId());
                itemConfigDO.setStage(itemConfigParan.getStage());
                itemConfigDO.setPercentSalary(itemConfigParan.getPercentSalary());
                itemConfigDO.setStartDay(startDayTemp);
                itemConfigDO.setEndDay(endDayTemp);
                startDayTemp = startDayTemp.add(itemConfigParan.getLeaveDay());
                BaseDOUtil.fillDOInsert(itemConfigDO);
                leaveItemConfigList.add(itemConfigDO);
            }
        }
        return leaveItemConfigList;
    }

    /**
     * 构建福利假结转规则信息
     */
    private CompanyLeaveConfigCarryOverDO buildCompanyLeaveConfigCarryOver(CompanyLeaveConfigDO leaveConfig,
                                                                           CompanyLeaveConfigCarryOverAddCommand param) {
        CompanyLeaveConfigCarryOverDO leaveConfigCarryOver = CompanyLeaveConfigCarryOverMapstruct.INSTANCE.mapToCompanyLeaveConfigCarryOverDO(param);
        leaveConfigCarryOver.setId(defaultIdWorker.nextId());
        leaveConfigCarryOver.setLeaveId(leaveConfig.getId());
        BaseDOUtil.fillDOInsert(leaveConfigCarryOver);
        return leaveConfigCarryOver;
    }

    /**
     * 构建结转规则范围信息
     */
    private List<CompanyLeaveConfigCarryOverRangeDO> buildCompanyLeaveConfigCarryOverRange(CompanyLeaveConfigCarryOverDO leaveConfigCarryOver,
                                                                                           CompanyLeaveConfigCarryOverAddCommand param) {
        List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList = Lists.newArrayList();
        // 如果结转失效类型是按入职日期设置，那么需要构建失效范围数据
        if (LeaveConfigInvalidTypeEnum.EXPIRES_ON_THE_DATE_OF_ENTRY.getType()
                .equals(param.getInvalidType())) {
            leaveConfigCarryOverRangeList = CompanyLeaveConfigCarryOverMapstruct.INSTANCE.mapToCompanyLeaveCarryOverRangeConfigDO(param.getLeaveConfigCarryOverRangeList());
            leaveConfigCarryOverRangeList.forEach(leaveConfigCarryOverRange -> {
                leaveConfigCarryOverRange.setId(defaultIdWorker.nextId());
                leaveConfigCarryOverRange.setCarryOverId(leaveConfigCarryOver.getId());
                BaseDOUtil.fillDOInsert(leaveConfigCarryOverRange);
            });
        }
        return leaveConfigCarryOverRangeList;
    }

    private UserDaoQuery buildUserQuery() {
        UserDaoQuery userDaoQuery = UserDaoQuery.builder()
                .isDelete(IsDeleteEnum.NO.getCode())
                .status(StatusEnum.ACTIVE.getCode())
                // 用工类型默认只有员工、挂靠、实习生、兼职、顾问才发放假期
                .employeeTypeList(EmploymentTypeEnum.TYPE_OF_DEFAULT_WELFARE_LEAVE)
                .workStatusList(Arrays.asList(WorkStatusEnum.ON_JOB.getCode(), WorkStatusEnum.TRANSFER.getCode()))
                .build();
        return userDaoQuery;
    }

    private void setDispatchUserQuery(UserDaoQuery userDaoQuery, CompanyLeaveConfigAddCommand param) {
        //如果是派遣假，则通过是否派遣标识+派遣地+国籍 找员工
        if (BusinessConstant.Y.equals(param.getIsDispatch())) {
            userDaoQuery.setIsGlobalRelocation(BusinessConstant.Y);
            // 添加国籍查询条件
            String countryCode = CountryCodeEnum.convert2StandardCountryCode(param.getCountry());
            if (StringUtils.isBlank(countryCode)) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_WELFARE_LEAVE_CONFIG_NATIONALITY_ERROR);
            }
            // CHN特殊判断 (人员中国籍存在TW)
            if (CountryCodeEnum.CHN.getStandardCode().equals(countryCode)) {
                userDaoQuery.setCountryCodeList(Arrays.asList(countryCode, "TW", "HK", "MO"));
            } else {
                userDaoQuery.setCountryCodeList(Arrays.asList(countryCode));
            }
            if (CollectionUtils.isNotEmpty(param.getDispatchCountry())
                    && !param.getDispatchCountry().contains(BusinessConstant.GLOBAL_FLAG)) {
                //需要派遣地条件
                userDaoQuery.setLocationCountryList(param.getDispatchCountry());
            }
        } else {
            userDaoQuery.setLocationCountry(param.getCountry());
            userDaoQuery.setIsGlobalRelocation(BusinessConstant.N);
        }
    }

    /**
     * 筛选入职日期范围数据
     *
     * @param param
     * @param deptUserInfoList
     * @param userInfoList
     * @param userIdList
     */
    private void filterUserEntryDate(CompanyLeaveConfigAddCommand param
            , List<UserInfoDO> deptUserInfoList, List<UserInfoDO> userInfoList
            , List<Long> userIdList) {
        // 过滤入职日期范围为空及人员范围为空的情况
        if (StringUtils.isBlank(param.getStartEntryDate()) || StringUtils.isBlank(param.getEndEntryDate())) {
            return;
        }
        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        Date startEntryDate = DateUtils.str2Date(param.getStartEntryDate());
        Date endEntryDate = DateUtils.str2Date(param.getEndEntryDate());
        // 查询人员入职日期
        List<AttendanceUserEntryRecord> userEntryRecordList = userEntryRecordService.selectUserEntryByUserIds(userIdList);
        if (CollectionUtils.isEmpty(userEntryRecordList)) {
            return;
        }
        // 过滤出入职日期范围内的员工
        List<Long> userIdsForCondition = userEntryRecordList.stream()
                .filter(item -> Objects.nonNull(item.getUserId())
                        && Objects.nonNull(item.getConfirmDate())
                        && item.getConfirmDate().compareTo(startEntryDate) >= 0
                        && item.getConfirmDate().compareTo(endEntryDate) <= 0)
                .map(item -> item.getUserId())
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIdsForCondition)) {
            deptUserInfoList.clear();
            userInfoList.clear();
            return;
        }
        deptUserInfoList.removeIf(item -> !userIdsForCondition.contains(item.getId()));
        userInfoList.removeIf(item -> !userIdsForCondition.contains(item.getId()));
    }

    /**
     * 构建福利假期配置范围表
     *
     * @param leaveConfig         福利假期配置
     * @param rangeType           范围类型：1-用户级别，2-部门级别
     * @param userInfo            部门用户
     * @param leaveConfigRangList 福利假期配置范围表
     */
    private void buildLeaveConfigRang(CompanyLeaveConfigDO leaveConfig,
                                      int rangeType, UserInfoDO userInfo,
                                      List<CompanyLeaveConfigRangDO> leaveConfigRangList) {
        CompanyLeaveConfigRangDO leaveConfigRang = new CompanyLeaveConfigRangDO();
        leaveConfigRang.setId(defaultIdWorker.nextId());
        leaveConfigRang.setLeaveId(leaveConfig.getId());
        leaveConfigRang.setRangeType(rangeType);
        leaveConfigRang.setUserCode(userInfo.getUserCode());
        BaseDOUtil.fillDOInsert(leaveConfigRang);
        leaveConfigRangList.add(leaveConfigRang);
    }

    /**
     * 更新假期绑定范围
     *
     * @param leaveConfig
     * @param updateCommand
     */
    private void updateLeaveConfig(CompanyLeaveConfigDO leaveConfig, CompanyLeaveConfigUpdateCommand updateCommand) {
        // 判断员工类型
        List<String> employeeTypeList = updateCommand.getEmployeeTypeList();
        if (CollectionUtils.isNotEmpty(employeeTypeList)) {
            for (String type : employeeTypeList) {
                if (!EmploymentTypeEnum.TYPE_OF_DEFAULT_WELFARE_LEAVE.contains(type)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.EMPLOYEE_TYPE_ERROR);
                }
            }
            String employeeType_Str = StringUtils.join(employeeTypeList, ",");
            leaveConfig.setEmployeeType(employeeType_Str);
        } else {
            leaveConfig.setEmployeeType(StringUtils.EMPTY);
        }
        // 更新性别
        if (Objects.nonNull(updateCommand.getUseSex())) {
            leaveConfig.setUseSex(updateCommand.getUseSex());
        } else {
            leaveConfig.setUseSex(BusinessConstant.ZERO);
        }
        // 更新部门
        List<Long> deptIdList = updateCommand.getDeptIdList();
        if (CollectionUtils.isNotEmpty(deptIdList)) {
            String deptId_Str = StringUtils.join(deptIdList, ",");
            leaveConfig.setDeptIds(deptId_Str);
        } else {
            leaveConfig.setDeptIds(StringUtils.EMPTY);
        }
        BaseDOUtil.fillDOUpdate(leaveConfig);
    }
}
