package com.imile.attendance.vacation.factory;

import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigRangDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.vacation.command.CompanyLeaveConfigRangeCommand;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigRangeMapstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Slf4j
@Service
public class CompanyLeaveConfigRangeFactory {
    @Resource
    private CompanyLeaveConfigRangDao companyLeaveConfigRangDao;


    @Transactional
    public boolean save(CompanyLeaveConfigRangeCommand addCommand) {
        // save
        CompanyLeaveConfigRangDO companyLeaveConfigRangDO = CompanyLeaveConfigRangeMapstruct.INSTANCE.mapToCompanyLeaveRangeConfigDO(addCommand);
        companyLeaveConfigRangDao.save(companyLeaveConfigRangDO);
        return true;
    }

    @Transactional
    public boolean saveBatch(List<CompanyLeaveConfigRangeCommand> addCommand) {
        // saveBatch
        List<CompanyLeaveConfigRangDO> companyLeaveConfigRangList = CompanyLeaveConfigRangeMapstruct.INSTANCE.toAddRangeConfigList(addCommand);
        companyLeaveConfigRangDao.saveBatch(companyLeaveConfigRangList);
        return true;
    }
}
