package com.imile.attendance.vacation;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.employee.UserInfoManage;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.vacation.dao.DispatchUserRecordDao;
import com.imile.attendance.infrastructure.repository.vacation.model.DispatchUserRecordDO;
import com.imile.attendance.infrastructure.repository.vacation.query.DispatchUserRecordQuery;
import com.imile.attendance.user.UserLeaveDispatchService;
import com.imile.attendance.user.UserLeaveService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.RpcResultProcessor;
import com.imile.attendance.vacation.dto.DispatchUserRecordDTO;
import com.imile.hrms.api.transform.api.UserTransformApi;
import com.imile.hrms.api.transform.dto.TransformContentDTO;
import com.imile.hrms.api.transform.dto.TransformDTO;
import com.imile.hrms.api.transform.enums.TransformSceneEnum;
import com.imile.hrms.api.transform.enums.TransformTypeEnum;
import com.imile.hrms.api.user.param.UserEventParam;
import com.imile.util.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 派遣人员记录
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-13
 */
@Service
@Slf4j
public class DispatchUserRecordService {

    @Resource
    private DispatchUserRecordDao dispatchUserRecordDao;
    @Resource
    private UserLeaveDispatchService userLeaveDispatchService;
    @Resource
    private AttendanceUserService userService;
    @Reference(version = "1.0.0", check = false, timeout = 6000)
    private UserTransformApi userTransformApi;

    /**
     * 根据条件获取人员派遣记录
     *
     * @param query
     * @return
     */
    public List<DispatchUserRecordDO> selectDispatchInfo(DispatchUserRecordQuery query) {
        return dispatchUserRecordDao.selectDispatchInfo(query);
    }

    /**
     * 根据用户编码获取人员派遣记录
     *
     * @param userCodeList
     * @return
     */
    public List<DispatchUserRecordDO> selectDispatchInfoByUserCode(List<String> userCodeList) {
        return dispatchUserRecordDao.selectDispatchInfoByUserCode(userCodeList);
    }

    /**
     * 接受调动消息后置处理事件
     *
     * @param event
     * @return
     */
    public void handleDispatchNotice(UserEventParam.TransformNotice event) {
        // 调用人员RPC获取调动信息
        DispatchUserRecordDTO param = selectDispatchRecord(event);
        if (Objects.isNull(param)) {
            return;
        }
        // 校验参数
        checkDispatchParam(param);
        // 处理派遣开始/结束通知事件
        if (BusinessConstant.Y.equals(param.getEndFlag())) {
            // 派遣结束，逻辑删除原派遣记录状态，新增派遣结束记录
            doEndDispatch(param);
        } else {
            // 派遣开始，新增派遣记录
            doStartDispatch(param);
        }
    }

    public void save(DispatchUserRecordDTO param) {
        DispatchUserRecordDO dispatchUserRecordDO = BeanUtils.convert(param, DispatchUserRecordDO.class);
        BaseDOUtil.fillDOInsert(dispatchUserRecordDO);
        dispatchUserRecordDao.save(dispatchUserRecordDO);
    }

    public void saveBatch(List<DispatchUserRecordDTO> dispatchUserRecordDOList) {
        if (CollectionUtils.isEmpty(dispatchUserRecordDOList)) {
            return;
        }
        List<DispatchUserRecordDO> dispatchUserRecordList = BeanUtils.convert(DispatchUserRecordDO.class, dispatchUserRecordDOList);
        dispatchUserRecordList.stream().forEach(item -> BaseDOUtil.fillDOInsert(item));
        dispatchUserRecordDao.saveBatch(dispatchUserRecordList);
    }

    public void delete(List<String> userCodeList, Integer endFlag) {
        // 删除原先派遣记录
        DispatchUserRecordQuery query = DispatchUserRecordQuery.builder()
                .userCodeList(userCodeList)
                .endFlag(endFlag)
                .build();
        List<DispatchUserRecordDO> deleteList = dispatchUserRecordDao.selectDispatchInfo(query);
        if (CollectionUtils.isNotEmpty(deleteList)) {
            for (DispatchUserRecordDO dispatchUserRecordDO : deleteList) {
                dispatchUserRecordDO.setIsDelete(BusinessConstant.Y);
                BaseDOUtil.fillDOUpdate(dispatchUserRecordDO);
            }
            dispatchUserRecordDao.updateBatchById(deleteList);
        }
    }

    /**
     * 处理派遣开始事件
     *
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void doStartDispatch(DispatchUserRecordDTO param) {
        // 折算非派遣假期余额
        userLeaveDispatchService.conversationUserDispatchLeave(param);
        // 删除派遣结束记录
        this.delete(Arrays.asList(param.getUserCode()), BusinessConstant.Y);
        // 新增派遣开始记录
        this.save(param);
    }

    /**
     * 处理派遣结束事件
     *
     * @param param
     */
    @Transactional(rollbackFor = Exception.class)
    public void doEndDispatch(DispatchUserRecordDTO param) {
        // 折算+返还派遣假期余额
        userLeaveDispatchService.conversationUserDispatchLeave(param);
        // 删除派遣开始记录
        this.delete(Arrays.asList(param.getUserCode()), BusinessConstant.N);
        // 新增派遣结束记录
        this.save(param);
    }

    /**
     * 查询调动信息
     */
    private DispatchUserRecordDTO selectDispatchRecord(UserEventParam.TransformNotice event) {
        // 查询调动单信息
        if (Objects.isNull(event.getTransformId())) {
            log.info("派遣事件接收调动记录缺少参数,event:{}", event);
            throw BusinessLogicException.getException(ErrorCodeEnum.TRANSFORM_OBJECT_DATA_NOT_EXIST, "调动记录");
        }
        // 调动消息
        TransformDTO transformInfo = RpcResultProcessor.process(userTransformApi.getTransformInfo(event.getTransformId()));
        // 调动单参数校验
        if (!checkTransformInfo(transformInfo)) {
            return null;
        }
        AttendanceUser userInfo = userService.getByUserId(transformInfo.getUserId());
        if (Objects.isNull(userInfo)) {
            log.info("派遣事件没有获取到员工,event:{}", event);
            return null;
        }
        DispatchUserRecordDTO dispatchUserRecord = DispatchUserRecordDTO.builder()
                .userCode(userInfo.getUserCode())
                .dispatchDate(transformInfo.getTransformContent().getDispatchStartDate())
                .dispatchCountry(Objects.isNull(transformInfo.getTransformContent().getLocationCountry())
                        ? userInfo.getLocationCountry() : transformInfo.getTransformContent().getLocationCountry())
                .countryCode(userInfo.getCountryCode())
                .transformType(transformInfo.getTransformType())
                .build();
        // 员工调动根据派遣和回流事件标记是否结束
        Integer endFlag = BusinessConstant.N;
        if (TransformTypeEnum.EMPLOYEE_TRANSFER.getType().equals(transformInfo.getTransformType())
                && BusinessConstant.Y.equals(transformInfo.getTransformSceneMap().get(TransformSceneEnum.BACK_FLOW.getKey()))) {
            log.info("派遣事件员工调动类型获取到回流标志,transformInfo:{}", JSON.toJSONString(transformInfo));
            endFlag = BusinessConstant.Y;
            dispatchUserRecord.setDispatchDate(transformInfo.getTransformContent().getReturnStartDate());

        }
        if (!TransformTypeEnum.EMPLOYEE_TRANSFER.getType().equals(transformInfo.getTransformType())
                && BusinessConstant.N.equals(userInfo.getIsGlobalRelocation())) {
            log.info("派遣事件非员工调动类型获取到人员派遣标志为否,transformInfo:{}", JSON.toJSONString(transformInfo));
            endFlag = BusinessConstant.Y;
            dispatchUserRecord.setDispatchDate(transformInfo.getTransformContent().getReturnStartDate());
        }
        dispatchUserRecord.setEndFlag(endFlag);
        return dispatchUserRecord;
    }

    /**
     * 参数校验
     *
     * @param param
     */
    private void checkDispatchParam(DispatchUserRecordDTO param) {
        //派遣参数校验
        if (StringUtils.isBlank(param.getUserCode()) || Objects.isNull(param.getEndFlag())
                || Objects.isNull(param.getTransformType())) {
            log.info("checkDispatchParam | param is null. AttendanceDispatchUserRecordParam:{}", JSON.toJSONString(param));
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_NOT_NULL);
        }
    }

    /**
     * 调动单参数校验
     *
     * @param transformInfo
     */
    private Boolean checkTransformInfo(TransformDTO transformInfo) {
        if (Objects.isNull(transformInfo) || Objects.isNull(transformInfo.getTransformContent())) {
            log.info("派遣事件接收调动记录不存在或已删除,transformInfo:{}", transformInfo);
            throw BusinessLogicException.getException(ErrorCodeEnum.TRANSFORM_OBJECT_DATA_NOT_EXIST, "调动记录");
        }
        // 判断派遣类型是否包含员工调动和特殊导入
        if (!TransformTypeEnum.EMPLOYEE_TRANSFER.getType().equals(transformInfo.getTransformType())
                && !TransformTypeEnum.SPECIAL_TRANSFER.getType().equals(transformInfo.getTransformType())) {
            log.info("派遣事件接收调动记录不包含员工调动和特殊导入,无需处理 transformId:{}", transformInfo.getId());
            return false;
        }
        // 获取调动信息
        TransformContentDTO transformContent = transformInfo.getTransformContent();
        if (Objects.isNull(transformInfo.getUserId())) {
            log.info("派遣事件缺少必填参数 transformInfo:{}", JSON.toJSONString(transformInfo));
            return false;
        }
        // 员工调动只需要关注派遣+回流场景
        if (TransformTypeEnum.EMPLOYEE_TRANSFER.getType().equals(transformInfo.getTransformType())) {
            Map<String, Integer> transformSceneMap = transformInfo.getTransformSceneMap();
            if (Objects.isNull(transformSceneMap)) {
                log.info("派遣事件员工调动类型不包含场景map transformInfo:{}", JSON.toJSONString(transformInfo));
                return false;
            }
            if (!transformSceneMap.containsKey(TransformSceneEnum.DISPATCH.getKey())
                    && !transformSceneMap.containsKey(TransformSceneEnum.BACK_FLOW.getKey())) {
                log.info("派遣事件员工调动类型不包含派遣和回流场景 transformInfo:{}", JSON.toJSONString(transformInfo));
                return false;
            }
            if (!BusinessConstant.Y.equals(transformSceneMap.get(TransformSceneEnum.DISPATCH.getKey()))
                    && !BusinessConstant.Y.equals(transformSceneMap.get(TransformSceneEnum.BACK_FLOW.getKey()))) {
                log.info("派遣事件员工调动类型没有勾选派遣和回流场景 transformInfo:{}", JSON.toJSONString(transformInfo));
                return false;
            }
            if (StringUtils.isBlank(transformContent.getLocationCountry()) || Objects.isNull(transformInfo.getUserId())) {
                log.info("派遣事件员工调动内容缺少必填参数 transformInfo:{}", JSON.toJSONString(transformInfo));
                return false;
            }
            if (Objects.isNull(transformContent.getDispatchStartDate()) && Objects.isNull(transformContent.getReturnStartDate())) {
                log.info("派遣事件缺少员工派遣开始/结束日期 transformInfo:{}", JSON.toJSONString(transformInfo));
                return false;
            }
        }

        return true;
    }
}
