package com.imile.attendance.vacation;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.EmploymentTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.vacation.CompanyLeaveTypeEnum;
import com.imile.attendance.enums.vacation.LeaveConfigRangRangeTypeEnum;
import com.imile.attendance.enums.vacation.LeaveConsumeTypeEnum;
import com.imile.attendance.enums.vacation.PunchDayTypeEnum;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserEntryRecordService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUserEntryRecord;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.dao.CompanyLeaveItemConfigDao;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigCarryOverRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigIssueRuleRangeDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveConfigRangDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveItemConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.model.CompanyLeaveJourneyConfigDO;
import com.imile.attendance.infrastructure.repository.vacation.query.CompanyLeaveQuery;
import com.imile.attendance.infrastructure.repository.vacation.query.LeaveConfigRangQuery;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.util.PageUtil;
import com.imile.attendance.vacation.dto.CompanyLeaveDetailDTO;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigCarryOverMapstruct;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigIssueRuleMapstruct;
import com.imile.attendance.vacation.mapstruct.CompanyLeaveConfigMapstruct;
import com.imile.attendance.vacation.query.CompanyLeaveConfigDetailQuery;
import com.imile.attendance.vacation.query.CompanyLeaveConfigQuery;
import com.imile.attendance.vacation.vo.CompanyLeaveConfigVO;
import com.imile.attendance.vacation.vo.LeaveConfigCarryOverRangeVO;
import com.imile.attendance.vacation.vo.LeaveConfigCarryOverVO;
import com.imile.attendance.vacation.vo.LeaveConfigIssueRuleRangeVO;
import com.imile.attendance.vacation.vo.LeaveConfigIssueRuleVO;
import com.imile.attendance.vacation.vo.LeaveItemConfigVO;
import com.imile.attendance.vacation.vo.LeaveJourneyConfigVO;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.util.date.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 假期规则配置业务类
 *
 * <AUTHOR>
 * @menu 假期规则
 * @date 2025/4/24
 */
@Slf4j
@Service
public class CompanyLeaveConfigService {
    @Resource
    private CompanyLeaveConfigDao companyLeaveConfigDao;
    @Resource
    private CompanyLeaveConfigRangService companyLeaveConfigRangService;
    @Resource
    private CompanyLeaveConfigIssueRuleService companyLeaveConfigIssueRuleService;
    @Resource
    private CompanyLeaveConfigCarryOverService companyLeaveConfigCarryOverService;
    @Resource
    private CompanyLeaveItemConfigDao companyLeaveItemConfigDao;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private AttendanceUserService attendanceUserService;
    @Resource
    private AttendanceDeptService attendanceDeptService;
    @Resource
    private AttendanceUserEntryRecordService userEntryRecordService;

    /**
     * 福利假期列表
     *
     * @param query 查询参数
     * @return 分页结果
     */
    public PaginationResult<CompanyLeaveDetailDTO> list(CompanyLeaveConfigQuery query) {
        // 校验常驻国权限
        List<String> countryList = attendancePermissionService.filterUserCountryAuth(query.getCountry(), null);
        if (CollectionUtils.isEmpty(countryList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        // 查询列表数据
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .countryList(countryList)
                .leaveName(query.getLeaveName())
                .leaveType(query.getLeaveType())
                .status(query.getStatus())
                .isDispatch(query.getIsDispatch())
                .build();
        Page<CompanyLeaveConfigDO> page = PageHelper.startPage(query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);
        PageInfo<CompanyLeaveConfigDO> pageInfo = page.doSelectPageInfo(() -> this.selectCompanyLeave(companyLeaveQuery));
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        List<CompanyLeaveDetailDTO> configDetailDTO = CompanyLeaveConfigMapstruct.INSTANCE.toConfigDetailDTO(pageInfo.getList());
        return PageUtil.getPageResult(configDetailDTO, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 福利假期详情
     *
     * @param param 入参
     * @return 详情
     */
    public CompanyLeaveConfigVO detail(CompanyLeaveConfigDetailQuery param) {
        log.info("福利假期详情入参：{}", param);
        Long leaveId = param.getId();
        // 1. 获取福利假期主表信息
        CompanyLeaveConfigDO leaveConfig = this.getById(leaveId);
        if (ObjectUtil.isNull(leaveConfig)) {
            throw BusinessException.get(ErrorCodeEnum.NO_COMPANY_LEAVE_EXISTS_ERROR.getDesc());
        }
        // 2. 获取福利假期使用范围表 查询用户级别的数据
        LeaveConfigRangQuery leaveConfigRangQuery = LeaveConfigRangQuery.builder()
                .leaveId(leaveId)
                .rangeType(LeaveConfigRangRangeTypeEnum.USER.getType())
                .build();
        List<CompanyLeaveConfigRangDO> leaveConfigRangList = companyLeaveConfigRangService.getLeaveConfigRangList(leaveConfigRangQuery);
        // 3. 福利假发放规则数据
        List<CompanyLeaveConfigIssueRuleDO> leaveConfigIssueRule = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleListById(leaveId);
        List<CompanyLeaveConfigIssueRuleRangeDO> leaveConfigIssueRuleRangeList = Lists.newArrayList();
        List<CompanyLeaveJourneyConfigDO> leaveJourneyConfigList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(leaveConfigIssueRule)) {
            Long issueRuleId = leaveConfigIssueRule.get(0).getId();
            // 4. 福利假发放规则：范围表
            leaveConfigIssueRuleRangeList = companyLeaveConfigIssueRuleService.getLeaveConfigIssueRuleRangeList(issueRuleId);
            // 5. 福利假发放规则：路途假规则表
            leaveJourneyConfigList = companyLeaveConfigIssueRuleService.getLeaveJourneyConfigList(issueRuleId);
        }
        // 6. 获取阶段比例信息
        List<CompanyLeaveItemConfigDO> itemConfigDOList = this.selectItemByConfigId(Collections.singletonList(leaveId));
        List<LeaveItemConfigVO> leaveItemConfiList = this.getLeaveItemConfigVO(itemConfigDOList);
        // 7. 获取发放规则数据
        LeaveConfigIssueRuleVO targetLeaveConfigIssueRule = null;
        if (CollectionUtils.isNotEmpty(leaveConfigIssueRule)) {
            targetLeaveConfigIssueRule = CompanyLeaveConfigIssueRuleMapstruct.INSTANCE.mapToCompanyLeaveIssueRuleConfigVO(leaveConfigIssueRule.get(0));
            List<LeaveConfigIssueRuleRangeVO> targetLeaveConfigIssueRuleRange =
                    CompanyLeaveConfigIssueRuleMapstruct.INSTANCE.mapToCompanyLeaveConfigIssueRuleRangeVO(leaveConfigIssueRuleRangeList);
            targetLeaveConfigIssueRule.setLeaveConfigIssueRuleRangeList(targetLeaveConfigIssueRuleRange);
            List<LeaveJourneyConfigVO> targetLeaveJourneyConfig =
                    CompanyLeaveConfigIssueRuleMapstruct.INSTANCE.mapToCompanyLeaveJourneyConfigVO(leaveJourneyConfigList);
            targetLeaveConfigIssueRule.setLeaveJourneyConfigList(targetLeaveJourneyConfig);
        }

        // 8. 获取结转规则数据
        List<CompanyLeaveConfigCarryOverDO> leaveConfigCarryOverList = companyLeaveConfigCarryOverService.getCarryOverByConfigId(leaveId);
        LeaveConfigCarryOverVO targetLeaveConfigCarryOver = null;
        if (CollUtil.isNotEmpty(leaveConfigCarryOverList)) {
            CompanyLeaveConfigCarryOverDO leaveConfigCarryOver = leaveConfigCarryOverList.get(0);
            targetLeaveConfigCarryOver =
                    CompanyLeaveConfigCarryOverMapstruct.INSTANCE.mapToCompanyLeaveConfigCarryOverVO(leaveConfigCarryOver);
            List<CompanyLeaveConfigCarryOverRangeDO> leaveConfigCarryOverRangeList = companyLeaveConfigCarryOverService.getLeaveConfigCarryOverRangeList(leaveConfigCarryOver.getId());
            List<LeaveConfigCarryOverRangeVO> targetLeaveConfigCarryOverRangeConfig =
                    CompanyLeaveConfigCarryOverMapstruct.INSTANCE.mapToCompanyLeaveConfigCarryOverRangeVO(leaveConfigCarryOverRangeList);
            targetLeaveConfigCarryOver.setLeaveConfigCarryOverRangeList(targetLeaveConfigCarryOverRangeConfig);
        }

        // 9. 组装展示信息
        CompanyLeaveConfigVO leaveConfigVO =
                CompanyLeaveConfigMapstruct.INSTANCE.mapToCompanyLeaveConfigVO(leaveConfig);
        // 获取leaveConfigRangList的userId
        List<String> userCodeList = leaveConfigRangList.stream()
                .map(CompanyLeaveConfigRangDO::getUserCode)
                .collect(Collectors.toList());
        leaveConfigVO.setUserCodeList(userCodeList);
        List<String> userNameList = attendanceUserService.listUsersByUserCodes(userCodeList).stream()
                .map(RequestInfoHolder.isChinese() ? AttendanceUser::getUserName : AttendanceUser::getUserNameEn)
                .collect(Collectors.toList());
        leaveConfigVO.setUserNameList(userNameList);
        List<Long> deptIdList = Lists.newArrayList();
        if (StringUtils.isNotBlank(leaveConfig.getDeptIds())) {
            deptIdList = Arrays.stream(leaveConfig.getDeptIds().split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        List<String> deptNameList = attendanceDeptService.selectDeptByIds(deptIdList).stream()
                .map(RequestInfoHolder.isChinese() ? AttendanceDept::getDeptNameCn : AttendanceDept::getDeptNameEn)
                .collect(Collectors.toList());
        leaveConfigVO.setDeptIdList(deptIdList);
        leaveConfigVO.setDeptNameList(deptNameList);
        leaveConfigVO.setEmployeeTypeList(Arrays.asList(leaveConfig.getEmployeeType().split(",")));
        // 发放规则数据，包含发放规则范围数据
        leaveConfigVO.setLeaveConfigIssueRule(targetLeaveConfigIssueRule);
        // 阶梯假期数据
        leaveConfigVO.setLeaveItemConfigList(leaveItemConfiList);
        // 结转规则数据
        leaveConfigVO.setLeaveConfigCarryOver(targetLeaveConfigCarryOver);

        return leaveConfigVO;
    }

    private List<LeaveItemConfigVO> getLeaveItemConfigVO(List<CompanyLeaveItemConfigDO> itemConfigDOList) {
        List<LeaveItemConfigVO> leaveItemConfiList = Lists.newArrayList();
        BigDecimal lastLeaveDay = BigDecimal.ZERO;
        for (CompanyLeaveItemConfigDO itemConfigDO : itemConfigDOList) {
            LeaveItemConfigVO leaveItemConfig = new LeaveItemConfigVO();
            leaveItemConfig.setId(itemConfigDO.getId());
            leaveItemConfig.setLeaveDay(itemConfigDO.getEndDay().subtract(lastLeaveDay));
            leaveItemConfig.setPercentSalary(itemConfigDO.getPercentSalary().multiply(new BigDecimal("100")));
            leaveItemConfig.setStage(itemConfigDO.getStage());
            leaveItemConfiList.add(leaveItemConfig);
            lastLeaveDay = itemConfigDO.getEndDay();
        }
        return leaveItemConfiList;
    }

    // 假期查询 拷贝原有方法

    /**
     * 根据条件查询公司假期配置数据
     *
     * @param companyLeaveQuery
     * @return
     */
    public List<CompanyLeaveConfigDO> selectCompanyLeave(CompanyLeaveQuery companyLeaveQuery) {
        return companyLeaveConfigDao.selectLeaveConfig(companyLeaveQuery);
    }

    /**
     * 根据国家查询假期类型
     *
     * @param countryList 根据公司查询假期类型
     * @return
     */
    public List<CompanyLeaveConfigDO> selectLeaveConfigByCountryList(List<String> countryList) {
        if (CollectionUtils.isEmpty(countryList)) {
            return new ArrayList<>();
        }
        return companyLeaveConfigDao.selectLeaveConfigByCountry(countryList);
    }

    /**
     * 根据id查询公司假期信息
     *
     * @param id
     * @return
     */
    public CompanyLeaveConfigDO getById(Long id) {
        return companyLeaveConfigDao.getById(id);
    }

    /**
     * 根据id查询公司假期信息
     *
     * @param ids
     * @return
     */
    public List<CompanyLeaveConfigDO> getByIdList(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return companyLeaveConfigDao.listByIds(ids);
    }

    /**
     * 查询公司假期阶段信息
     *
     * @param leaveConfigIds
     * @return
     */
    public List<CompanyLeaveItemConfigDO> selectItemByConfigId(List<Long> leaveConfigIds) {
        return companyLeaveItemConfigDao.selectItemByConfigId(leaveConfigIds);
    }

    /**
     * 根据用户假期范围及其他条件查询公司假期配置数据
     *
     * @param userCodeList
     * @return
     */
    public List<CompanyLeaveConfigDO> selectConfigByUserCodeAndQueryList(List<String> userCodeList,
                                                                         CompanyLeaveQuery companyLeaveQuery) {
        List<CompanyLeaveConfigRangDO> companyLeaveConfigRang = companyLeaveConfigRangService.selectRangByUserCode(userCodeList);
        if (CollectionUtils.isEmpty(companyLeaveConfigRang)) {
            log.info("userLeaveDetail companyLeaveConfigRang is empty. userCodeList：{}", userCodeList);
            return new ArrayList<>();
        }
        if (Objects.isNull(companyLeaveQuery)) {
            return new ArrayList<>();
        }
        List<Long> configIdList = companyLeaveConfigRang.stream()
                .map(CompanyLeaveConfigRangDO::getLeaveId)
                .collect(Collectors.toList());
        companyLeaveQuery.setIdList(configIdList);
        List<CompanyLeaveConfigDO> companyLeaveConfigDOList = this.selectCompanyLeave(companyLeaveQuery);
        return companyLeaveConfigDOList;
    }

    /**
     * 根据用户假期范围查询公司假期配置数据
     *
     * @param userCodeList
     * @return
     */
    public List<CompanyLeaveConfigDO> selectConfigByUserCodeList(List<String> userCodeList) {
        List<CompanyLeaveConfigRangDO> companyLeaveConfigRang = companyLeaveConfigRangService.selectRangByUserCode(userCodeList);
        if (CollectionUtils.isEmpty(companyLeaveConfigRang)) {
            log.info("userLeaveDetail companyLeaveConfigRang is empty. userCodeList：{}", userCodeList);
            return Lists.newArrayList();
        }
        List<Long> configIdList = companyLeaveConfigRang.stream().map(CompanyLeaveConfigRangDO::getLeaveId).collect(Collectors.toList());
        CompanyLeaveQuery companyLeaveQuery = CompanyLeaveQuery.builder()
                .idList(configIdList)
                .status(StatusEnum.ACTIVE.getCode())
                .build();
        List<CompanyLeaveConfigDO> companyLeaveConfigDOList = this.selectCompanyLeave(companyLeaveQuery);
        return companyLeaveConfigDOList;
    }

    /**
     * 过滤假期范围内满足条件的规则
     *
     * @param companyLeaveConfigList
     * @param userInfo
     * @return
     */
    public List<CompanyLeaveConfigDO> filterUserCompanyConfig(List<CompanyLeaveConfigDO> companyLeaveConfigList
            , AttendanceUser userInfo) {
        if (CollectionUtils.isEmpty(companyLeaveConfigList)) {
            return new ArrayList<>();
        }
        List<CompanyLeaveConfigDO> companyLeaveConfigListByRange = this.selectConfigByUserCodeList(Arrays.asList(userInfo.getUserCode()));
        List<Long> configIdListByRange = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(companyLeaveConfigListByRange)) {
            configIdListByRange = companyLeaveConfigListByRange.stream().map(item -> item.getId()).collect(Collectors.toList());
        }
        // 过滤假期范围内 或者 类型是年假+调休假类型假期
        List<Long> finalConfigIdListByRange = configIdListByRange;
        companyLeaveConfigList = companyLeaveConfigList.stream()
                .filter(item -> finalConfigIdListByRange.contains(item.getId())
                        || CompanyLeaveTypeEnum.ANNUAL_LEAVE.getCode().equals(item.getLeaveType())
                        || CompanyLeaveTypeEnum.COMPENSATORY_LEAVE.getCode().equals(item.getLeaveType()))
                .collect(Collectors.toList());

        return companyLeaveConfigList;
    }

    /**
     * 判断是否消耗假期
     *
     * @param consumeTypeStr 入参
     * @return 是否成功
     */
    public boolean isConsumeLeave(String consumeTypeStr, String dayPunchType) {
        if (StringUtils.isEmpty(consumeTypeStr) || StringUtils.isEmpty(dayPunchType)) {
            return true;
        }
        List<String> consumeType = Arrays.asList(consumeTypeStr.split(BusinessConstant.DEFAULT_DELIMITER));
        if (PunchDayTypeEnum.H.getCode().equals(dayPunchType)
                && !consumeType.contains(LeaveConsumeTypeEnum.PH.getType())) {
            return false;
        }
        if (PunchDayTypeEnum.OFF.getCode().equals(dayPunchType)
                && !consumeType.contains(LeaveConsumeTypeEnum.OFF.getType())) {
            return false;
        }
        return true;
    }

    /**
     * 判断用户是否满足绑定条件
     *
     * @param leaveConfig 假期规则
     * @param userInfo    用户
     * @return
     */
    public boolean checkInCondition(CompanyLeaveConfigDO leaveConfig, UserInfoDO userInfo) {
        // 重构拷贝保留原先判断逻辑，代码未做更改
        Integer useSex = leaveConfig.getUseSex();
        String deptIds = leaveConfig.getDeptIds();
        String employeeType = leaveConfig.getEmployeeType();
        String startEntryDate = leaveConfig.getStartEntryDate();
        String endEntryDate = leaveConfig.getEndEntryDate();
        List<String> employeeTypeList = Arrays.asList(employeeType.split(","));

        // 默认发放的五个用户类型：如果假期没有选择用工类型，就是全部的用工类型，就是下面的五个
        List<String> defaultEmployeeType = Arrays.asList(EmploymentTypeEnum.EMPLOYEE.getCode(),
                EmploymentTypeEnum.SUB_EMPLOYEE.getCode(),
                EmploymentTypeEnum.INTERN.getCode(),
                EmploymentTypeEnum.PART_TIMER.getCode(),
                EmploymentTypeEnum.CONSULTANT.getCode());
        // 如果假期绑定的用工类型为空，就是全部的用户类型
        List<String> targetEmployeeTypeList = StringUtils.isBlank(employeeType)
                ? defaultEmployeeType : employeeTypeList;
        log.info("国家假期配置：性别：{}，部门：{}，员工类型：{}", useSex, deptIds, employeeTypeList);
        if (StringUtils.isBlank(deptIds)
                && targetEmployeeTypeList.contains(userInfo.getEmployeeType())
                && (ObjectUtil.isNotNull(useSex)
                && (useSex == 0 || useSex.equals(userInfo.getSex())))
                && (this.checkInEntryDate(startEntryDate, endEntryDate, userInfo))) {
            log.info("部门范围为空，用户：{}，满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户绑定该假期范围表",
                    userInfo.getUserCode(), userInfo.getLocationCountry(),
                    leaveConfig.getId(), leaveConfig.getLeaveType());
            return true;
        }
        if (StringUtils.isNotBlank(deptIds)) {
            String[] split = deptIds.split(",");
            if (split.length > 0) {
                log.info("假期id：{}，假期类型：{}，部门范围：{}",
                        leaveConfig.getId(), leaveConfig.getLeaveType(), deptIds);
                List<Long> deptIdList = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
                if (deptIdList.contains(userInfo.getDeptId())
                        && targetEmployeeTypeList.contains(userInfo.getEmployeeType())
                        && (ObjectUtil.isNotNull(useSex)
                        && (useSex == 0 || useSex.equals(userInfo.getSex())))
                        && (this.checkInEntryDate(startEntryDate, endEntryDate, userInfo))) {
                    log.info("部门范围不为空，用户：{}，满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户绑定该假期范围表",
                            userInfo.getUserCode(), userInfo.getLocationCountry(), leaveConfig.getId(), leaveConfig.getLeaveType());
                    return true;
                }
            }
        }
        log.info("部门范围不为空，用户：{}，不满足该国家：{}，下假期id：{}，假期类型：{}的条件，该用户不绑定该假期范围表",
                userInfo.getUserCode(), userInfo.getLocationCountry(), leaveConfig.getId(), leaveConfig.getLeaveType());
        return false;
    }

    /**
     * 校验人员入职日期是否满足条件
     *
     * @param startEntryDateStr
     * @param endEntryDateStr
     * @param userInfo
     * @return
     */
    private Boolean checkInEntryDate(String startEntryDateStr,
                                     String endEntryDateStr,
                                     UserInfoDO userInfo) {
        if (StringUtils.isBlank(startEntryDateStr) || StringUtils.isBlank(endEntryDateStr)) {
            return true;
        }
        // 获取人员入职信息
        AttendanceUserEntryRecord userEntryInfo = userEntryRecordService.selectUserEntryByUserId(userInfo.getId());
        if (Objects.isNull(userEntryInfo) || Objects.isNull(userEntryInfo.getConfirmDate())) {
            log.info("userCode：{}，未获取到人员入职信息", userInfo.getUserCode());
            return false;
        }
        Date startEntryDate = DateUtils.str2Date(startEntryDateStr);
        Date endEntryDate = DateUtils.str2Date(endEntryDateStr);
        if (userEntryInfo.getConfirmDate().compareTo(startEntryDate) >= 0
                && userEntryInfo.getConfirmDate().compareTo(endEntryDate) <= 0) {
            return true;
        }
        return false;
    }

}
