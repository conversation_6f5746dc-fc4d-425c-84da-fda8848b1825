package com.imile.attendance.vacation.job;

import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.vacation.job.service.UserLeaveInspectionService;
import com.imile.attendance.vacation.param.UserLeaveInspectionParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * {@code @author:} allen
 * {@code @className:} userLeaveInspectionNewHandler
 * {@code @since:} 2023-12-21 17:53
 * {@code @description:} 人员假期巡检处理器
 */
@Slf4j
@Component
public class UserLeaveInspectionHandler {
    @Resource
    private UserLeaveInspectionService userLeaveInspectionService;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_INSPECTION_NEW_HANDLER)
    public ReturnT<String> UserLeaveInspectionHandler(String content) {
        UserLeaveInspectionParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, UserLeaveInspectionParam.class)
                : new UserLeaveInspectionParam();
        userLeaveInspectionService.userLeaveInspectionHandler(param);
        return ReturnT.SUCCESS;
    }
}
