package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveItemConfigVO
 * {@code @since:} 2024-04-13 17:45
 * {@code @description:}
 */
@Data
public class LeaveItemConfigVO implements Serializable {
    private static final long serialVersionUID = -269605260581828000L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 公司假期配置id
     */
    private Long leaveId;
    /**
     * 阶段
     */
    private Integer stage;
    /**
     * 假期长度
     */
    private BigDecimal leaveDay;
    /**
     * 百分比日薪
     */
    private BigDecimal percentSalary;
}
