package com.imile.attendance.vacation.command;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期范围表保存入参")
public class CompanyLeaveConfigRangeCommand {
    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 国家假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long leaveId;

    /**
     * 范围类型：1：表示用户 2：表示部门
     */
    @ApiModelProperty(value = "范围类型：1：表示用户 2：表示部门")
    private Integer rangeType;

    /**
     * 用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号
     */
    @ApiModelProperty(value = "用户编码/账号 Ucenter里面的用户编码，也是本系统中的账号")
    private String userCode;
}
