package com.imile.attendance.vacation.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-15
 * @version: 1.0
 */
@Data
public class CompanyLeaveDetailDTO {
    private Long id;
    /**
     * 假期类型
     */
    private String leaveType;
    /**
     * 假期名称
     */
    private String leaveName;
    /**
     * 是否派遣假
     */
    private Integer isDispatch;
    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;
    /**
     * 派遣地国家
     */
    private String dispatchCountry;
    /**
     * 所属国
     */
    private String country;
    /**
     * 适用性别
     */
    private Integer useSex;
    /**
     * 适用周期
     */
    private String useCycle;
    /**
     * 假期可用的开始时间
     */
    private String useStartDate;
    /**
     * 假期可用的结束时间
     */
    private String useEndDate;
    /**
     * 假期类型
     */
    List<CompanyLeaveItemDetailDTO> itemDetailVOList;

    /**
     * 假期简称
     */
    private String leaveShortName;

    /**
     * 节假日是否消耗假期
     */
    private String consumeLeaveType;

    /**
     * 是否上传附件
     */
    private Integer isUploadAttachment;

    /**
     * 请假单位
     */
    private String leaveUnit;
    /**
     * 最小请假时长
     */
    private Integer miniLeaveDuration;

    /**
     * 审计信息页面展示
     */

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserName;



}
