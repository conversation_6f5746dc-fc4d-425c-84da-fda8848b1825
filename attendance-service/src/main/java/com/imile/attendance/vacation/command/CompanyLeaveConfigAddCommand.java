package com.imile.attendance.vacation.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期记录表保存入参")
public class CompanyLeaveConfigAddCommand {

    /**
     * 适用国家
     */
    @ApiModelProperty(value = "适用国家")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String country;

    /**
     * 假期名称
     */
    @ApiModelProperty(value = "假期名称")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String leaveName;

    /**
     * 假期简称
     */
    @ApiModelProperty(value = "假期简称")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String leaveShortName;

    /**
     * 假期类型
     */
    @ApiModelProperty(value = "假期类型")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String leaveType;

    /**
     * 是否派遣假
     */
    @ApiModelProperty(value = "是否派遣假")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer isDispatch;
    /**
     * 派遣地国家
     */
    @ApiModelProperty(value = "派遣地国家")
    private List<String> dispatchCountry;


    /**
     * 适用性别
     */
    @ApiModelProperty(value = "适用性别")
    private Integer useSex;

    /**
     * 部门id集合
     */
    @ApiModelProperty(value = "部门id集合")
    private List<Long> deptIdList;

    /**
     * 用户id集合
     */
    @ApiModelProperty(value = "用户id集合")
    private List<String> userCodeList;

    /**
     * 用工类型集合
     */
    @ApiModelProperty(value = "用工类型集合")
    private List<String> employeeTypeList;

    /**
     * 入职日期范围(开始时间)
     */
    @ApiModelProperty(value = "入职日期范围(开始时间)")
    private String startEntryDate;

    /**
     * 入职日期范围(结束时间)
     */
    @ApiModelProperty(value = "入职日期范围(结束时间)")
    private String endEntryDate;

    /**
     * 使用限制: 1：入职后，2：试用期转正后， 3：入职一年后，4:入职两年后
     */
    @ApiModelProperty(value = "使用限制: 1：入职后，2：试用期转正后， 3：入职一年后，4:入职两年后")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer leaveUsageRestrictions;

    /**
     * 请假单位
     */
    @ApiModelProperty(value = "请假单位")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String leaveUnit;

    /**
     * 最小请假时长
     */
    @ApiModelProperty(value = "最小请假时长")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer miniLeaveDuration;

    /**
     * 节假日是否消耗假期：0-否：表示消耗工作日的时间，1-是：表示自然日的时间
     */
    @ApiModelProperty(value = "节假日是否消耗假期：0-否：表示消耗工作日的时间，1-是：表示自然日的时间")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String consumeLeaveType;

    /**
     * 是否上传附件：0-否，1-是
     */
    @ApiModelProperty(value = "是否上传附件：0-否，1-是")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer isUploadAttachment;

    /**
     * 上传附件条件：当isUploadAttachment为1时，此字段必填
     */
    @ApiModelProperty(value = "上传附件条件")
    private Long uploadAttachmentCondition;

    /**
     * 上传附件单位：DAYS-天 HOURS-小时 MINUTES-分钟
     */
    @ApiModelProperty(value = "上传附件单位：DAYS-天 HOURS-小时 MINUTES-分钟")
    private String attachmentUnit;

    /**
     * 更新周期：结转规则中，是否永久有效 无论是否，必填 为year
     */
    @ApiModelProperty(value = "更新周期：结转规则中，是否永久有效 无论是否，必填 为year")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String useCycle;

    /**
     * 有效期预览：假期可用的开始时间，结转规则中，是否永久有效为否时，必填
     */
    @ApiModelProperty(value = "假期可用的开始时间")
    private String useStartDate;

    /**
     * 有效期预览：假期可用的结束时间，结转规则中，是否永久有效为否时，必填
     */
    @ApiModelProperty(value = "假期可用的结束时间")
    private String useEndDate;

    /**
     * 状态 ACTIVE/DISABLED
     */
    @ApiModelProperty(value = "状态 ACTIVE/DISABLED")
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Add.class})
    private String status;

    /**
     * 是否带薪: 1：全薪假 2：无薪假 3：阶梯假
     */
    @ApiModelProperty(value = "是否带薪: 1：全薪假 2：无薪假 3：阶梯假")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer isSalary;


    /**
     * 发放假期规则数据
     */
    @ApiModelProperty(value = "发放假期规则数据")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private CompanyLeaveConfigIssueRuleAddCommand leaveConfigIssueRuleSaveParam;

    /**
     * 结转规则
     */
    @ApiModelProperty(value = "结转规则")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private CompanyLeaveConfigCarryOverAddCommand leaveConfigCarryOverSaveParam;

    /**
     * 国家假期主表明细表：阶段信息。【额度类型是：固定总额度、不限定额度、初始额度为0才会必填，随司龄递增类型，这张表没数据，数据在hrms_company_leave_config_issue_rule_range表】
     */
    @ApiModelProperty(value = "国家假期主表明细表")
    List<CompanyLeaveItemConfigAddCommand> leaveItemConfigSaveParamList;
}
