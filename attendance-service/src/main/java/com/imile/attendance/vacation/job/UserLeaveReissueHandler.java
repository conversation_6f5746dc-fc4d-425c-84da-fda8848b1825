package com.imile.attendance.vacation.job;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.vacation.job.service.UserLeaveReissueService;
import com.imile.attendance.vacation.param.UserLeaveReissueParam;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @Date 2025-03-10 15:37
 * @Description 用户假期配置按年龄/工龄发放并符合跨层级新规则时重新计算发放假期
 */
@Slf4j
@Component
public class UserLeaveReissueHandler {

    @Resource
    UserLeaveReissueService userLeaveReissueService;

    @XxlJob(BusinessConstant.JobHandler.USER_LEAVE_REISSUE_HANDLER)
    public ReturnT<String> userLeaveReissueHandler(String content) {
        UserLeaveReissueParam param = StringUtils.isNotBlank(content)
                ? JSON.parseObject(content, UserLeaveReissueParam.class)
                : new UserLeaveReissueParam();
        // 校验参数
        if (!this.checkParam(param)) {
            return ReturnT.SUCCESS;
        }
        // 定时任务执行默认参数
        this.buildDefaultParam(param);
        userLeaveReissueService.userLeaveReissueHandler(param);
        return ReturnT.SUCCESS;
    }

    /**
     * 参数校验
     *
     * @param param
     * @return
     */
    private Boolean checkParam(UserLeaveReissueParam param) {
        if (ObjectUtil.isNull(param)) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】参数为空");
            return false;
        }

        if (StringUtils.isBlank(param.getReissueType())) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】补发类型未填写");
            return false;
        }

        if (!"workSeniority".equals(param.getReissueType()) && !"age".equals(param.getReissueType())) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】补发类型填写错误");
            return false;
        }

        if (param.getIsUseCustomLocalTime() && StringUtils.isBlank(param.getLocalDate())) {
            XxlJobLogger.log("xxl-job【userLeaveReissueHandler】参数自定义日期未填写");
            return false;
        }
        return true;
    }

    /**
     * 参数默认值设置
     *
     * @param param
     */
    private void buildDefaultParam(UserLeaveReissueParam param) {
        if (StringUtils.isNotBlank(param.getEmployeeType())) {
            param.setEmployeeTypeList(Arrays.asList(param.getEmployeeType().split(",")));
        }
        if (StringUtils.isNotBlank(param.getUserCodes())) {
            param.setUserCodeList(Arrays.asList(param.getUserCodes().split(",")));
        }
        if (StringUtils.isNotBlank(param.getCountryList())) {
            param.setCountryArrayList(Arrays.asList(param.getCountryList().split(",")));
        }
        if (StringUtils.isNotBlank(param.getLeaveName())) {
            param.setLeaveNameList(Arrays.asList(param.getLeaveName().split(",")));
        }
    }

}
