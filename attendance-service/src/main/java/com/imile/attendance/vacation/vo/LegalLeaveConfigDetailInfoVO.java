package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} LegalLeaveConfigDetailInfoVO
 * {@code @since:} 2024-03-11 11:31
 * {@code @description:}
 */
@Data
public class LegalLeaveConfigDetailInfoVO implements Serializable {

    private static final long serialVersionUID = -4958380085243422490L;
    /**
     * 法定假期主键id：这边国家+年度+假期名称是一条数据的，国家+年度是多条数据，所以主键id放到假期名称中返回
     */
    private Long id;

    private String legalLeaveName;

    private Long legalLeaveStartDayId;

    private Long legalLeaveEndDayId;

    private Integer legalLeaveDuration;
}
