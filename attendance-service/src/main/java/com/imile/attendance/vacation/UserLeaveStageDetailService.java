package com.imile.attendance.vacation;

import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveStageDetailDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveStageDetailDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveStageDetailQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 用户假期余额业务层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
@Slf4j
public class UserLeaveStageDetailService {

    @Resource
    private UserLeaveStageDetailDao userLeaveStageDetailDao;

    /**
     * 根据用户详情表id查询该类型假期余额信息
     *
     * @param leaveIdList
     * @return
     */
    public List<UserLeaveStageDetailDO> selectByLeaveId(List<Long> leaveIdList) {
        return userLeaveStageDetailDao.selectByLeaveId(leaveIdList);
    }

    /**
     * 根据id查询该假期余额信息
     *
     * @param idList
     * @return
     */
    public List<UserLeaveStageDetailDO> selectById(List<Long> idList) {
        return userLeaveStageDetailDao.selectById(idList);
    }

    /**
     * 通过条件查询假期余额信息
     *
     * @param userLeaveStageDetailQuery 查询条件
     * @return 查询结果
     */
    public List<UserLeaveStageDetailDO> selectByCondition(UserLeaveStageDetailQuery userLeaveStageDetailQuery) {
        return userLeaveStageDetailDao.selectByCondition(userLeaveStageDetailQuery);
    }

    /**
     * 根据用户详情主键批量查询余额信息
     *
     * @param leaveIds
     * @return
     */
    public List<UserLeaveStageDetailDO> selectUserLeaveStageByLeaveIds(List<Long> leaveIds) {
        return userLeaveStageDetailDao.selectByLeaveId(leaveIds);
    }
}
