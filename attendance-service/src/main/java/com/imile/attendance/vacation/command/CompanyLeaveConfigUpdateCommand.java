package com.imile.attendance.vacation.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
public class CompanyLeaveConfigUpdateCommand {

    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 适用国家
     */
    @NotBlank(message = ValidCodeConstant.NOT_BLANK, groups = {Groups.Update.class})
    private String country;

    /**
     * 适用性别
     */
    private Integer useSex;

    /**
     * 部门id集合
     */
    private List<Long> deptIdList;

    /**
     * 用户id集合
     */
    private List<String> userCodeList;

    /**
     * 用工类型集合
     */
    private List<String> employeeTypeList;

    /**
     * 入职日期范围(开始时间)
     */
    private String startEntryDate;

    /**
     * 入职日期范围(结束时间)
     */
    private String endEntryDate;
}
