package com.imile.attendance.vacation.command;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期：发放规则范围保存入参")
public class CompanyLeaveConfigIssueRuleRangeAddCommand {

    /**
     * 国家发放规则表主键id
     */
    @ApiModelProperty(value = "国家发放规则表主键id")
    private Long issueRuleId;

    /**
     * 左边符号：1：表示大于 2：表示大于等于
     */
    @ApiModelProperty(value = "左边符号：1：表示大于 2：表示大于等于")
    private Integer symbolLeft;

    /**
     * 左边年份
     */
    @ApiModelProperty(value = "左边年份")
    private Integer yearLeft;

    /**
     * 右边符号：1：表示小于 2：表示小于等于
     */
    @ApiModelProperty(value = "右边符号：1：表示小于 2：表示小于等于")
    private Integer symbolRight;

    /**
     * 右边年份
     */
    @ApiModelProperty(value = "右边年份")
    private Integer yearRight;

    /**
     * 常驻省
     */
    @ApiModelProperty(value = "常驻省")
    private String locationProvince;

    /**
     * 常驻市
     */
    @ApiModelProperty(value = "常驻市")
    private String locationCity;

    /**
     * 不同条件的发放额度
     */
    @ApiModelProperty(value = "不同条件的发放额度")
    private BigDecimal issueQuota;
}
