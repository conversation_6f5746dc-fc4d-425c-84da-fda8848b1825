package com.imile.attendance.vacation.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description:
 * @author: taokang
 * @createDate: 2022-6-16
 * @version: 1.0
 */
@Data
public class CompanyLeaveItemDetailDTO {
    /**
     * 具体假期阶段的ID
     */
    private Long id;
    /**
     * 阶段
     */
    private Integer stage;
    /**
     * 假期长度
     */
    private BigDecimal leaveDay;
    /**
     * 发薪比例
     */
    private BigDecimal percentSalary;
}
