package com.imile.attendance.vacation.query;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * {@code @author:} allen
 * {@code @className:} WelfareLeaveConfigDetailParam
 * {@code @since:} 2024-04-13 17:33
 * {@code @description:}
 */
@Data
@ApiModel(description = "国家福利假期记录表详情入参")
public class CompanyLeaveConfigDetailQuery implements Serializable {

    private static final long serialVersionUID = -7211987924276429797L;
    /**
     * 国家假期配置ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;
}
