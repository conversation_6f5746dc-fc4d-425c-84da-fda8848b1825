package com.imile.attendance.vacation;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.enums.vacation.LeaveTypeEnum;
import com.imile.attendance.infrastructure.repository.employee.dao.UserLeaveRecordDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserLeaveRecordDO;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordConditionQuery;
import com.imile.attendance.infrastructure.repository.employee.query.UserLeaveRecordQuery;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 用户假期记录业务层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025/4/27
 */
@Service
@Slf4j
public class UserLeaveRecordService {

    @Resource
    private UserLeaveRecordDao userLeaveRecordDao;

    /**
     * 查询用户在该天的所有请假记录
     *
     * @param userId
     * @param usrCode
     * @param dayId
     * @return
     */
    public List<UserLeaveRecordDO> selectRecordByDayId(Long userId, String usrCode, Long dayId) {
        return userLeaveRecordDao.selectRecordByDayId(userId, usrCode, dayId);
    }

    /**
     * 查询用户请假流水表
     *
     * @param query
     * @return
     */
    public List<UserLeaveRecordDO> selectUserLeaveDetail(UserLeaveRecordQuery query) {
        return userLeaveRecordDao.selectUserLeaveDetail(query);
    }

    /**
     * 根据用户id查询用户指定假期操作记录
     *
     * @param userId
     * @param leaveType
     * @return
     */
    public List<UserLeaveRecordDO> selectRecordByUserId(Long userId, String leaveType) {
        return userLeaveRecordDao.selectRecordByUserId(userId, leaveType);
    }

    public List<UserLeaveRecordDO> selectRecordByUserIdList(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        return userLeaveRecordDao.selectRecordByUserIdList(userIdList);
    }

    public List<UserLeaveRecordDO> selectUserLeaveRecordByCondition(UserLeaveRecordConditionQuery query) {
        return userLeaveRecordDao.selectUserLeaveRecordByCondition(query);
    }

    /**
     * 获取员工对应得假期补发记录
     * @param userIds
     * @param configId
     * @param issueMonth
     * @param issueDay
     * @param localDate
     * @return
     */
    public List<UserLeaveRecordDO> selectReissueRecord(List<Long> userIds, Long configId,
                                                    Integer issueMonth, Integer issueDay,
                                                    Date localDate) {
        // 根据当前时间获取假期规则配置的开始日期和结束日期范围
        LocalDate parseLocalDate = LocalDate.parse(DateUtil.format(localDate, DatePattern.NORM_DATE_PATTERN));
        LocalDate beginDate = LocalDate.of(parseLocalDate.getYear(), issueMonth, issueDay);
        LocalDate endDate;
        if (beginDate.isAfter(parseLocalDate)) {
            endDate = beginDate;
            beginDate = beginDate.minusYears(1);
        } else {
            endDate = beginDate.plusYears(1);
        }
        // 判断是否符合跨层级新规则
        // 查询当年的发放记录
        UserLeaveRecordQuery query = UserLeaveRecordQuery.builder()
                .configId(configId)
                .typeList(Arrays.asList(LeaveTypeEnum.ADMIN_OPERATION_CANCEL.getCode()
                        , LeaveTypeEnum.RECALCULATE.getCode()))
                .beginDate(Date.from(beginDate.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .endDate(Date.from(endDate.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .userIds(userIds)
                .operationUserCode("xxl-job")
                .build();
        List<UserLeaveRecordDO> userLeaveRecordList = this.selectUserLeaveDetail(query);
        return userLeaveRecordList;
    }
}
