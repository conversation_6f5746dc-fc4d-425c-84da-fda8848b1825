package com.imile.attendance.vacation.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * {@code @author:} allen
 * {@code @className:} LeaveConfigIssueRuleRangeVO
 * {@code @since:} 2024-04-13 17:47
 * {@code @description:}
 */
@Data
public class LeaveConfigIssueRuleRangeVO implements Serializable {
    private static final long serialVersionUID = 1505347024173785186L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 国家发放规则表主键id
     */
    private Long issueRuleId;

    /**
     * 左边符号：1：表示大于 2：表示大于等于
     */
    private Integer symbolLeft;

    /**
     * 左边年份
     */
    private Integer yearLeft;

    /**
     * 右边符号：1：表示小于 2：表示小于等于
     */
    private Integer symbolRight;

    /**
     * 左边年份
     */
    private Integer yearRight;

    /**
     * 常驻省
     */
    private String locationProvince;

    /**
     * 常驻市
     */
    private String locationCity;

    /**
     * 不同条件的发放额度
     */
    private BigDecimal issueQuota;
}
