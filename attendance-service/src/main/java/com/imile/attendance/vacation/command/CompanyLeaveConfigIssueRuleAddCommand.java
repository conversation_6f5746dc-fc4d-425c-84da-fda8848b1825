package com.imile.attendance.vacation.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/4/24
 * @Description
 */
@Data
@ApiModel(description = "国家福利假期：发放规则数据保存入参")
public class CompanyLeaveConfigIssueRuleAddCommand implements Serializable {

    /**
     * 国家假期配置id
     */
    @ApiModelProperty(value = "国家假期配置id")
    private Long leaveId;

    /**
     * 发放频次：1：周期性发放，2:一次性发放
     */
    @ApiModelProperty(value = "发放频次：1：周期性发放，2:一次性发放")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer issueFrequency;

    /**
     * 发放时间：1：每年固定日（issueFrequency = 1的时候），2:每月固定日（issueFrequency = 1的时候），3:员工入职日：（issueFrequency = 2的时候）
     */
    @ApiModelProperty(value = "发放时间：1：每年固定日，2:每月固定日，3:员工入职日")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer issueTime;

    /**
     * 发放日期：月份：发放频次为周期性发放，发放时间是每年的时候月份不为0，发放频次为周期性发放，发放时间是每月的时候月份为0
     */
    @ApiModelProperty(value = "发放日期：月份")
    private Integer issueMonth;

    /**
     * 发放日期：日
     */
    @ApiModelProperty(value = "发放日期：日")
    private Integer issueDay;

    /**
     * 发放类型：1:固定额度 2:司领递增 3:不限定额度 4:无初始额度 5:按派遣国远近 6:随年龄增加 7:随工龄增加 8:按常驻省市配置
     */
    @ApiModelProperty(value = "发放类型：1:固定额度 2:司领递增 3:不限定额度 4:无初始额度 5:按派遣国远近 6:随年龄增加 7:随工龄增加 8:按常驻省市配置")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer issueType;

    /**
     * 发放额度
     */
    @ApiModelProperty(value = "发放额度")
    private BigDecimal issueQuota;

    /**
     * 循环单位(每满xx年/月)
     */
    @ApiModelProperty(value = "循环单位")
    private Integer cycleUnit;

    /**
     * 循环数值(每满xx年/月)
     */
    @ApiModelProperty(value = "循环数值")
    private Integer cycleNumber;

    /**
     * 是否折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    @ApiModelProperty(value = "是否折扣")
    private Long isConvert;

    /**
     * 是否按入职日到派遣日折扣：0:表示无该选项 ，1:表示折扣，2:表示无需折扣
     */
    @ApiModelProperty(value = "是否按入职日到派遣日折扣")
    private Integer isConvertDispatch;

    /**
     * 假期发放规则是司龄/按年龄发放的时候，范围数据必填，其他发放类型不需要该字段
     */
    @ApiModelProperty(value = "假期发放规则按司龄/按年龄范围")
    private List<CompanyLeaveConfigIssueRuleRangeAddCommand> leaveConfigIssueRuleRangeList;

    /**
     * 假期发放规则是按派遣国远近的时候，路途假数据必填，其他发放类型不需要该字段
     */
    @ApiModelProperty(value = "假期发放规则按派遣国远近范围")
    private List<CompanyLeaveConfigJourneyAddCommand> leaveJourneyConfigList;

    /**
     * 假期发放时取整规则：0:表示无该选项 ，1:表示不取整（四舍五入两位小数），2:表示向下取整（1.23-->1），3:表示向上取整（1.23-->2）:当is_convert == 1的时候，该字段才不为0，其余为0
     */
    @ApiModelProperty(value = "假期发放时取整规则")
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private Integer issueRoundingRule;

    /**
     * 是否符合跨层级新规则时重新计算发放假期 0:否 1:是
     */
    @ApiModelProperty(value = "是否符合跨层级新规则时重新计算发放假期 0:否 1:是")
    private Integer isRecalculate;
}
