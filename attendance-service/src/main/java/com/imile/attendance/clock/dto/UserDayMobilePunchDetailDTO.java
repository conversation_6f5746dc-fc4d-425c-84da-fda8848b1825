package com.imile.attendance.clock.dto;

import com.imile.attendance.base.Operator;
import com.imile.attendance.clock.bo.UserPunchMissingRuleType;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.form.dto.AttendanceFormDTO;
import com.imile.attendance.infrastructure.repository.rule.dto.PunchClassItemConfigDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserDayMobilePunchDetailDTO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户英文姓名
     */
    private String userNameEn;

    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 用户国籍
     */
    private String countryCode;

    /**
     * 用户常驻国
     */
    private String locationCountry;

    /**
     * 考勤日
     */
    private Long dayId;

    /**
     * 员工手机打卡等规则配置
     */
    private UserMobileRuleConfigDTO ruleConfigDTO;

    /**
     * 班次配置
     */
    private MobilePunchClassConfigDTO mobilePunchClassConfigDTO;

    /**
     * 班次详情配置
     */
    private List<PunchClassItemConfigDTO> punchClassItemConfigDTO;

    /**
     * 打卡记录
     */
    private List<MobilePunchCardRecordDTO> punchCardRecordDTO;

    /**
     * 请假/外勤记录
     */
    private List<AttendanceFormDTO> attendanceFormDTOList;

    /**
     * 考勤异常类型(枚举值)
     */
    private List<MobileAbnormalDTO> abnormalPunchList;

    /**
     * 用户打卡缺失规则类型
     */
    private UserPunchMissingRuleType userPunchMissingRuleType;

    /**
     * 用户上级部门hr列表
     */
    private List<Operator> superiorDeptHrList;


    /**
     * 获取本地化用户名
     * @return String
     */
    public String getLocaleUserName() {
        if (RequestInfoHolder.isChinese()) {
            return userName;
        }
        return userNameEn;
    }
}
