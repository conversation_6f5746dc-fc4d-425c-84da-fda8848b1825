package com.imile.attendance.clock.vo;

import com.imile.attendance.annon.WithDict;
import com.imile.attendance.constants.BusinessConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/5/23 
 * @Description 员工手机打卡规则配置VO
 */
@Data
public class UserMobileRuleConfigVO {

    //============打卡规则===============

    /**
     * 打卡规则编码
     */
    private String punchConfigNo;

    /**
     * 打卡规则名称
     */
    private String punchConfigName;

    /**
     * 打卡规则类型 PunchConfigTypeEnum
     */
    @WithDict(typeCode = BusinessConstant.HermesDict.PUNCH_RULE_TYPE, ref = "punchConfigTypeDesc")
    private String punchConfigType;

    /**
     * 打卡规则类型描述
     */
    private String punchConfigTypeDesc;

    /**
     * 打卡规则上下班时间间隔
     */
    private BigDecimal punchTimeInterval;


    //============补卡规则===============

    /**
     * 补卡次数
     */
    private Integer maxRepunchNumber;


    private List<PunchClassRule> punchClassRuleList;


    @Data
    public static class PunchClassRule {
        /**
         * 班次规则编码
         */
        private String configNo;

        /**
         * 班次名称
         */
        private String className;
    }
}
