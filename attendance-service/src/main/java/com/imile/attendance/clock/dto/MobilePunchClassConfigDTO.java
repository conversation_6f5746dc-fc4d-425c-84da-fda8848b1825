package com.imile.attendance.clock.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Data
public class MobilePunchClassConfigDTO {

    /**
     * classId
     */
    private Long id;


    /**
     * 班次性质
     */
    private String classNature;

    /**
     * 班次规则编码
     */
    private String configNo;

    /**
     * 班次名称
     */
    private String className;

    /**
     * 班次类型: 0表示未选择，1:早班,2:晚班
     */
    private Integer classType;

    /**
     * 法定工作时长（不包含休息时间）
     */
    private BigDecimal legalWorkingHours;

    /**
     * 出勤时长（包含休息时间）
     */
    private BigDecimal attendanceHours;

    /**
     * 时段数
     */
    private Integer itemNum;

    /**
     * 状态 ACTIVE、DISABLED
     */
    private String status;

    /**
     * 适用国家
     */
    private String country;

    /**
     * 适用部门
     */
    private String deptIds;

    /**
     * 是否最新
     */
    private Integer isLatest;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date expireTime;

    /**
     * 是否为国家级别规则
     */
    private Integer isCountryLevel;
}
