package com.imile.attendance.clock.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/5/19 
 * @Description
 */
@Data
public class MobilePunchDetailQuery implements Serializable {

    /**
     * 用户ID
     */
    @NotNull(message = "userId can not be null")
    private Long userId;

    /**
     * 当前日期(yyyy-MM-dd HH:mm:ss)
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "dateTime can not be null")
    private Date dateTime;
}
