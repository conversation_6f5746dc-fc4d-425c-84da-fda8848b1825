package com.imile.attendance.bizMq;

import com.imile.attendance.calendar.CalendarConfigRangeService;
import com.imile.attendance.enums.DimissionStatusEnum;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.mq.helper.OperatorHelper;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserDimissionRecordDO;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.rule.RuleConfigRangeService;
import com.imile.attendance.bizMq.sync.SyncHrToAttendanceTableService;
import com.imile.attendance.infrastructure.mq.BaseRocketMQListener;
import com.imile.attendance.rule.application.PunchClassConfigApplicationService;
import com.imile.attendance.shift.UserShiftService;
import com.imile.attendance.user.UserEntryAndDimissionService;
import com.imile.attendance.user.UserService;
import com.imile.attendance.util.CommonUtil;
import com.imile.attendance.util.DateHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26
 * @Description 监听用户离职消息
 */
@Slf4j
@Component
@RocketMQMessageListener(
        nameServer = "${rocketmq.nameServer}",
        topic = "${rocket.mq.hr.user.topic}",
        consumerGroup = "${rocket.mq.hr.user.dimission.group}",
        selectorExpression = "${rocket.mq.hr.user.dimission.tag}",
        consumeThreadMax = 4)
public class HrUserDimissionListener extends BaseRocketMQListener {

    @Resource
    private SyncHrToAttendanceTableService syncHrToAttendanceTableService;
    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private HrmsUserDimissionRecordDao hrmsUserDimissionRecordDao;
    @Resource
    private CalendarConfigRangeService calendarConfigRangeService;
    @Resource
    private RuleConfigRangeService ruleConfigRangeService;
    @Resource
    private PunchClassConfigApplicationService classConfigApplicationService;
    @Resource
    private UserShiftService userShiftService;
    @Resource
    private CountryService countryService;
    @Resource
    private UserService userService;
    @Resource
    private UserEntryAndDimissionService userEntryAndDimissionService;


    @Override
    public void doOnMessage(MessageExt messageExt) {
        String tags = messageExt.getTags();
        String body = new String(messageExt.getBody());
        log.info("HrUserDimissionListener 收到人员离职消息id：{},tags:{},body:{}", messageExt.getMsgId(), tags, body);
        Long userId = Long.valueOf(messageExt.getKeys());
        log.info("收到人员离职消息，userId:{}", userId);
        //新增离职表数据和更新用户表
        syncHrToAttendanceTableService.syncUserDimissionRecord(userId);
        //todo dimissionAttendanceHandler 员工确认离职考勤处理
        if (!userService.checkValidDimissionUserAttendanceRange(userId)) {
            log.info("该用户不在考勤有效用户范围中: {}", userId);
            return;
        }
        dimissionAttendanceHandler(userId);
    }

    private void dimissionAttendanceHandler(Long userId) {
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        OperatorHelper.putOperatorInfo(userInfoDO.getUserCode(), userInfoDO.getUserName());
        HrmsUserDimissionRecordDO userDimissionRecordDO = hrmsUserDimissionRecordDao.getByUserId(userId, DimissionStatusEnum.DIMISSION.getCode());
        if (!Objects.equals(DimissionStatusEnum.DIMISSION.getCode(), userDimissionRecordDO.getDimissionStatus())) {
            log.info("员工{}当前状态未确认离职", userInfoDO.getUserCode());
            return;
        }

        Date actualDimissionDate = userDimissionRecordDO.getActualDimissionDate();
        Long actualDimissionDayId = DateHelper.getDayId(actualDimissionDate);

        CountryDTO countryDTO = countryService.queryCountry(userInfoDO.getLocationCountry());
        Date hrOperateDimissionDate = CommonUtil.convertDateByTimeZonePlus(countryDTO.getTimeZone(), userDimissionRecordDO.getCreateDate());
        Long hrOperateDimissionDayId = DateHelper.getDayId(hrOperateDimissionDate);
        //是否延后确认离职
        boolean isDelayConfirm = hrOperateDimissionDayId.compareTo(actualDimissionDayId) > 0;
        log.info("员工{}是否延后离职:{}", userInfoDO.getUserCode(), isDelayConfirm);

        //清理用户日历适用范围
        calendarConfigRangeService.updateAttendanceConfigRange(userInfoDO, actualDimissionDate, isDelayConfirm);

        //清理用户关联的考勤规则适用范围
        ruleConfigRangeService.updateRuleConfigRange(userInfoDO, actualDimissionDate, isDelayConfirm);

        //清理班次适用范围
        classConfigApplicationService.updatePunchClassConfigRange(userInfoDO, actualDimissionDate, isDelayConfirm);

        //清理排班
        userShiftService.userDimissionCleanShift(userId, actualDimissionDate);

        //清理人脸信息 TODO

        //清理考勤
        userEntryAndDimissionService.dimissionAttendanceHandler(userId);
    }


}
