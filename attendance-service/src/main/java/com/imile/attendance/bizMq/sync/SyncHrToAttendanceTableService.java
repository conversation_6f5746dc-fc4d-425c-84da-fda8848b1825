package com.imile.attendance.bizMq.sync;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.imile.attendance.bizMq.mapstruct.UserInfoMapstruct;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.employee.modle.UserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.employee.modle.UserInfoDO;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserDimissionRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserEntryRecordDO;
import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsUserInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 同步HRMS人员信息到考勤系统相关表的服务类
 * <p>
 * 该服务负责将HRMS系统中的人员信息同步到考勤系统的相关表中，包括：
 * - 用户基本信息表（UserInfoDO）
 * - 用户入职记录表（UserEntryRecordDO）
 * - 用户离职记录表（UserDimissionRecordDO）
 * <p>
 * 同步过程中，会根据用户ID或用户编码查询HRMS系统中的数据，然后更新或新增考勤系统中的对应记录。
 * 使用MapStruct进行对象映射，确保数据的正确转换。
 * <p>
 * 注：HRMS数据源仅用于查询，事务仅控制考勤系统的数据更新。
 *
 * <AUTHOR> chen
 * @date 2025/3/26
 */
@Slf4j
@Component
public class SyncHrToAttendanceTableService {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Resource
    private HrmsUserDimissionRecordDao hrmsUserDimissionRecordDao;

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;

    /**
     * 根据HRMS系统中的最新用户信息，同步更新考勤系统中的用户信息
     *
     * @param userInfoDO 考勤系统中的用户信息
     */
    public void syncUserInfoRecordWithHrUser(UserInfoDO userInfoDO) {
        if (null == userInfoDO) {
            return;
        }
        String userCode = userInfoDO.getUserCode();
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoDao.getByCode(userCode);
        if (null == hrmsUserInfoDO) {
            return;
        }
        // 使用MapStruct更新考勤系统中的用户信息
        UserInfoMapstruct.INSTANCE.updateAttendanceUserInfo(userInfoDO, hrmsUserInfoDO);
        userInfoDao.updateById(userInfoDO);
    }

    @DSTransactional
    public void syncUserEntryRecord(String userCode) {
        HrmsUserInfoDO hrmsUserInfoDO = hrmsUserInfoDao.getByCode(userCode);
        if (null == hrmsUserInfoDO) {
            return;
        }
        ((SyncHrToAttendanceTableService) AopContext.currentProxy()).syncUserEntryRecord(hrmsUserInfoDO);
    }

    /**
     * 同步HRMS系统中的用户信息和入职记录到考勤系统
     * <p>
     * 该方法执行两个主要操作：
     * 1. 同步用户基本信息：根据用户ID查询考勤系统中是否存在该用户，如果存在则更新，不存在则新增
     * 2. 同步用户入职记录：根据用户ID查询HRMS系统中的入职记录，然后在考勤系统中更新或新增对应的入职记录
     * <p>
     * 如果HRMS系统中没有该用户的入职记录，则记录错误日志并中断同步
     *
     * @param hrmsUserInfoDO HRMS系统中的用户信息对象
     */
    @DSTransactional
    public void syncUserEntryRecord(HrmsUserInfoDO hrmsUserInfoDO) {
        Long userId = hrmsUserInfoDO.getId();
        // 第一步：新增或更新考勤人员表
        UserInfoDO attendanceUserInfo = userInfoDao.getByUserId(userId);
        if (attendanceUserInfo != null) {
            // 考勤系统中已存在该用户，使用MapStruct更新对象
            UserInfoMapstruct.INSTANCE.updateAttendanceUserInfo(attendanceUserInfo, hrmsUserInfoDO);
            userInfoDao.updateById(attendanceUserInfo);
        } else {
            // 考勤系统中不存在该用户，新增用户信息
            UserInfoDO userInfoDO = UserInfoMapstruct.INSTANCE.mapToAttendanceUserInfoDO(hrmsUserInfoDO);
            userInfoDao.save(userInfoDO);
        }

        // 第二步：新增或更新考勤人员入职表
        HrmsUserEntryRecordDO hrmsUserEntryRecordDO = hrmsUserEntryRecordDao.getByUserId(userId);
        if (hrmsUserEntryRecordDO == null) {
            log.info("userId:{} 员工在hr入职表不存在,不同步", userId);
            return;
        }
        UserEntryRecordDO attendanceUserEntryRecord = userEntryRecordDao.getById(userId);
        if (attendanceUserEntryRecord != null) {
            // 考勤系统中已存在该用户的入职记录，使用MapStruct更新对象
            UserInfoMapstruct.INSTANCE.updateAttendanceUserEntryRecord(attendanceUserEntryRecord,
                    hrmsUserEntryRecordDO);
            userEntryRecordDao.updateById(attendanceUserEntryRecord);
        } else {
            // 考勤系统中不存在该用户的入职记录，新增入职记录
            UserEntryRecordDO userEntryRecordDO = UserInfoMapstruct.INSTANCE
                    .mapToAttendanceUserEntryRecordDO(hrmsUserEntryRecordDO);
            userEntryRecordDao.save(userEntryRecordDO);
        }
    }

    /**
     * 同步HRMS系统中的用户离职记录到考勤系统
     * <p>
     * 该方法根据用户ID查询HRMS系统中的离职记录，然后在考勤系统中更新或新增对应的离职记录。
     * 如果HRMS系统中没有该用户的离职记录，则记录错误日志并中断同步。
     * <p>
     * 同步流程：
     * 1. 查询HRMS系统中的离职记录
     * 2. 查询考勤系统中是否存在该用户的离职记录
     * 3. 如果存在则更新，不存在则新增
     *
     * @param userId 用户ID
     */
    @DSTransactional
    public void syncUserDimissionRecord(Long userId) {
        // 第一步：查询HRMS系统中的离职记录
        List<HrmsUserDimissionRecordDO> hrmsUserDimissionRecordDOList = hrmsUserDimissionRecordDao.listByUserId(userId);
        if (CollectionUtils.isEmpty(hrmsUserDimissionRecordDOList)) {
            log.error("hrms离职表数据为空，userId:{},不同步", userId);
            return;
        }

        // 第二步：查询考勤系统中的离职记录
        List<UserDimissionRecordDO> userDimissionRecordDOList = userDimissionRecordDao.listByUserId(userId);
        if (CollectionUtils.isNotEmpty(userDimissionRecordDOList)) {
            for (UserDimissionRecordDO userDimissionRecordDO : userDimissionRecordDOList) {
                // 考勤系统中已存在该用户的离职记录，更新离职数据
                HrmsUserDimissionRecordDO hrmsUserDimissionRecordDO = hrmsUserDimissionRecordDOList.stream()
                        .filter(item -> item.getDimissionStatus().equals(userDimissionRecordDO.getDimissionStatus()))
                        .findFirst().orElse(null);

                if (hrmsUserDimissionRecordDO != null) {
                    UserInfoMapstruct.INSTANCE.updateAttendanceUserDimissionRecord(userDimissionRecordDO,
                            hrmsUserDimissionRecordDO);
                    userDimissionRecordDao.updateById(userDimissionRecordDO);
                }
            }
        } else {
            // 考勤系统中不存在该用户的离职记录，新增离职数据
            userDimissionRecordDao.saveBatch(
                    UserInfoMapstruct.INSTANCE.mapToAttendanceUserDimissionRecordDO(userDimissionRecordDOList));
        }

        // 第三步：更新用户表
        UserInfoDO userInfoDO = userInfoDao.getByUserId(userId);
        if (null == userInfoDO) {
            //一般不会发生
            log.error("userId={}在考勤用户表不存在,不同步", userId);
            return;
        }
        // 同步用户的最新信息
        syncUserInfoRecordWithHrUser(userInfoDO);
    }
}
