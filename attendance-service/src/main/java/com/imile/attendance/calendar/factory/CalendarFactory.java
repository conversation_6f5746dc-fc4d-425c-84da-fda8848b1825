package com.imile.attendance.calendar.factory;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.imile.attendance.calendar.CalendarManage;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.imile.attendance.calendar.RangeBizTypeService;
import com.imile.attendance.calendar.command.CalendarAddCommand;
import com.imile.attendance.calendar.command.CalendarLegalLeaveConfigAddCommand;
import com.imile.attendance.calendar.command.CalendarUpdateCommand;
import com.imile.attendance.calendar.dto.AttendanceArchiveCalendarUpdateDTO;
import com.imile.attendance.calendar.dto.CalendarConfigRangeDTO;
import com.imile.attendance.calendar.dto.DaysConfigParam;
import com.imile.attendance.calendar.dto.LegalLeaveConfigInfoParam;
import com.imile.attendance.calendar.dto.LegalLeaveConfigParam;
import com.imile.attendance.calendar.dto.MonthDaysConfigParam;
import com.imile.attendance.calendar.dto.YearLegalLeaveConfigParam;
import com.imile.attendance.calendar.dto.YearMonthDaysConfigParam;
import com.imile.attendance.calendar.helper.CalendarConfigHelper;
import com.imile.attendance.calendar.notify.CalendarEventService;
import com.imile.attendance.calendar.notify.EmployeeSchedulingParam;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceDayTypeEnum;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.enums.rule.RuleRangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.hermes.dto.CountryDTO;
import com.imile.attendance.infrastructure.idwork.DefaultIdWorker;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarLegalLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.CountryService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.shift.UserShiftConfigManage;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.DateHelper;
import com.imile.common.constant.MsgCodeConstant;
import com.imile.common.enums.IsDeleteEnum;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.util.date.DateUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description 考勤日历工厂
 */
@Slf4j
@Component
public class CalendarFactory {

    @Resource
    private RangeBizTypeService rangeBizTypeService;
    @Resource
    private CalendarConfigHelper calendarConfigHelper;
    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private DefaultIdWorker defaultIdWorker;
    @Resource
    private CountryService countryService;
    @Resource
    private CalendarLegalLeaveConfigDao calendarLegalLeaveConfigDao;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;
    @Resource
    private CalendarEventService calendarEventService;
    @Resource
    private LogRecordService logRecordService;


    @Transactional
    public List<CalendarConfigRangeDTO> add(CalendarAddCommand addCommand) {
        // 参数校验
        AttendanceTypeEnum attendanceTypeEnum = AttendanceTypeEnum.valueOf(addCommand.getType());
        if (AttendanceTypeEnum.DEFAULT.equals(attendanceTypeEnum)) {
            return ((CalendarFactory) AopContext.currentProxy()).addDefault(addCommand);
        }
        // 每月对应的日期出勤设置不能为空
        //BusinessLogicException.checkTrue(CollectionUtils.isEmpty(addCommand.getMonthDaysConfigs()),
        //        MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "monthDaysConfigs");

        // 校验方案名称不能为空
        BusinessLogicException.checkTrue(StringUtils.isBlank(addCommand.getAttendanceConfigName()),
                MsgCodeConstant.PARAM_INVALID, MsgCodeConstant.PARAM_INVALID, "attendanceConfigName");

        return ((CalendarFactory) AopContext.currentProxy()).addCustom(addCommand);
    }

    @Transactional
    public List<CalendarConfigRangeDTO> addDefault(CalendarAddCommand addCommand) {
        log.info("default calendar config：{}", JSON.toJSONString(addCommand));
        // 1.校验是否已存在默认方案，如果存在则异常
        Integer result = calendarConfigDao.countDefaultCalendarConfig(addCommand.getCountry());
        if (result > 0) {
            throw BusinessException.get(ErrorCodeEnum.DATA_DUPLICATE.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.DATA_DUPLICATE.getDesc()));
        }

        // 2.新增日历配置表
        CalendarConfigDO model = buildCalendarConfig(
                calendarConfigHelper.getDefaultCalendarConfigName(),
                addCommand.getDeptIds(),
                AttendanceTypeEnum.DEFAULT,
                addCommand.getCountry()
        );
        calendarConfigDao.save(model);

        // 新增日历配置明细表
        saveCalendarConfigDetail(model.getId(), addCommand);

        // 处理法定假期
        handlerLegalLeaveConfig(addCommand, model.getId());

        logRecordService.recordOperation(model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.CALENDAR_ADD.getCode())
                        .country(model.getCountry())
                        .bizName(model.getAttendanceConfigName())
                        .build()
        );
        return Collections.emptyList();
    }

    @Transactional
    public List<CalendarConfigRangeDTO> addCustom(CalendarAddCommand addCommand) {
        log.info("addCustom param:{}", JSON.toJSONString(addCommand));
        // 1.校验是否已存在默认方案，如果不存在则异常
        Integer result = calendarConfigDao.countDefaultCalendarConfig(addCommand.getCountry());
        if (result <= 0) {
            // 默认日历方案不存在，请先创建默认方案
            throw BusinessLogicException.getException(ErrorCodeEnum.DEFAULT_CALENDAR_NOT_EXITS);
        }
        //获取该国家下的所有日历规则
        List<CalendarConfigDO> coutnryCalendarConfigList = calendarConfigDao.getCountryCalendarByType(null, addCommand.getCountry());
        rangeBizTypeService.checkCalendarDeptAndUserExistRecord(
                coutnryCalendarConfigList,
                null,
                addCommand.getAttendanceConfigName(),
                addCommand.getDeptIds(),
                addCommand.getUserIds(),
                addCommand.getIsCoverOld()
        );

        // 新增配置表
        CalendarConfigDO model = buildCalendarConfig(
                addCommand.getAttendanceConfigName(),
                addCommand.getDeptIds(),
                AttendanceTypeEnum.CUSTOM,
                addCommand.getCountry()
        );
        calendarConfigDao.save(model);

        // 处理法定假期
        handlerLegalLeaveConfig(addCommand, model.getId());

        // 新增配置明细表
        saveCalendarConfigDetail(model.getId(), addCommand);
        // 新增适用范围
        saveCalendarConfigRange(model, addCommand.getCountry(),
                addCommand.getDeptIds(), addCommand.getUserIds(),
                coutnryCalendarConfigList, true,
                addCommand.getReSchedule(), null);

        logRecordService.recordOperation(model,
                LogRecordOptions.builder()
                        .pageOperateType(PageOperateType.ADD)
                        .operationType(OperationTypeEnum.CALENDAR_ADD.getCode())
                        .country(model.getCountry())
                        .bizName(model.getAttendanceConfigName())
                        .build()
        );

        return Collections.emptyList();
    }


    @Transactional
    public List<CalendarConfigRangeDTO> update(CalendarUpdateCommand updateCommand) {
        if (CollectionUtils.isEmpty(updateCommand.getUserIds())) {
            updateCommand.setUserIds(new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(updateCommand.getDeptIds())) {
            updateCommand.setDeptIds(new ArrayList<>());
        }
        // 查找记录
        CalendarConfigDO calendarConfigDO = calendarConfigDao.getActiveById(updateCommand.getId());
        if (calendarConfigDO == null || !BusinessConstant.Y.equals(calendarConfigDO.getIsLatest())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        //获取该国家下的所有日历规则
        List<CalendarConfigDO> coutnryCalendarConfigList =
                calendarConfigDao.getCountryCalendarByType(null, updateCommand.getCountry());

        rangeBizTypeService.checkCalendarDeptAndUserExistRecord(
                coutnryCalendarConfigList,
                calendarConfigDO.getAttendanceConfigNo(),
                updateCommand.getAttendanceConfigName(),
                updateCommand.getDeptIds(),
                updateCommand.getUserIds(),
                updateCommand.getIsCoverOld()
        );

        handlerLegalLeaveConfig(updateCommand, updateCommand.getId());

        // 修改记录 将旧版本置为非最新
        CalendarConfigDO model = BeanUtil.copyProperties(calendarConfigDO, CalendarConfigDO.class);

        if (StringUtils.isNotBlank(updateCommand.getAttendanceConfigName())) {
            model.setAttendanceConfigName(updateCommand.getAttendanceConfigName());
        }
        model.setDeptIds(null);
        if (CollectionUtils.isNotEmpty(updateCommand.getDeptIds())) {
            String deptIds = StringUtils.join(updateCommand.getDeptIds(), ",");
            model.setDeptIds(deptIds);
        }
        BaseDOUtil.fillDOUpdate(model);
        LambdaUpdateWrapper<CalendarConfigDO> updateWrapper = Wrappers.lambdaUpdate(CalendarConfigDO.class);
        updateWrapper.set(CalendarConfigDO::getDeptIds, model.getDeptIds());
        updateWrapper.eq(CalendarConfigDO::getId, model.getId());
        calendarConfigDao.update(model, updateWrapper);

        // 新增配置明细表
        saveCalendarConfigDetail(calendarConfigDO.getId(), updateCommand);

        logRecordService.recordObjectChange(model, calendarConfigDO,
                LogRecordOptions.builder()
                        .operationType(OperationTypeEnum.CALENDAR_UPDATE.getCode())
                        .country(model.getCountry())
                        .bizName(model.getAttendanceConfigName())
                        .build()
        );

        // 更新周末信息也需要更新现有人员排班
        saveCalendarConfigRange(calendarConfigDO, updateCommand.getCountry(),
                updateCommand.getDeptIds(), updateCommand.getUserIds(),
                coutnryCalendarConfigList, false,
                updateCommand.getReSchedule(), updateCommand.getUpdateDayIds());
        return Collections.emptyList();
    }


    /**
     * 员工档案日历变更
     * 若旧日历范围为用户级别则移除
     * 若旧日历范围为非用户级别更新为历史版本
     * 添加新用户级别的日历范围
     */
    public void userCalendarRangeUpdate(AttendanceArchiveCalendarUpdateDTO calendarUpdateDTO) {
        if (Objects.isNull(calendarUpdateDTO.getNewCalendarId())
                || Objects.isNull(calendarUpdateDTO.getUserId())
                || Objects.isNull(calendarUpdateDTO.getStartDate())) {
            return;
        }

        List<CalendarConfigRangeDO> calendarConfigRangeList = calendarConfigRangeDao.selectConfigRange(Collections.singletonList(calendarUpdateDTO.getUserId()));
        if (CollectionUtils.isNotEmpty(calendarConfigRangeList)) {
            calendarConfigRangeList.forEach(range -> {
                range.setIsLatest(BusinessConstant.N);
                range.setEndDate(calendarUpdateDTO.getStartDate());
                BaseDOUtil.fillDOUpdateByUserOrSystem(range);
            });
        }

        // 查找新日历记录
        CalendarConfigDO calendarConfigDO = calendarConfigDao.getActiveById(calendarUpdateDTO.getNewCalendarId());
        if (Objects.isNull(calendarConfigDO)) {
            log.error("员工档案变更日历,新的日历配置不存在");
            return;
        }

        if (Objects.equals(AttendanceTypeEnum.DEFAULT.name(), calendarConfigDO.getType())) {
            log.error("员工档案变更日历,不允许变更到默认日历");
            return;
        }

        List<CalendarConfigRangeDO> addCalendarConfigRangeList = new ArrayList<>();

        buildCalendarPunchData(calendarUpdateDTO.getUserId(), calendarConfigDO.getId(), calendarConfigDO.getAttendanceConfigNo(),
                RangeTypeEnum.USER.getCode(), calendarUpdateDTO.getStartDate(), addCalendarConfigRangeList);

        calendarManage.userCalendarRangeUpdate(calendarConfigRangeList, addCalendarConfigRangeList);
    }


    public void addCalendarLegalLeaveConfig(CalendarLegalLeaveConfigAddCommand addCommand) {
        log.info("保存法定假期配置参数:{}", addCommand);
        CalendarLegalLeaveConfigQuery query = new CalendarLegalLeaveConfigQuery();
        query.setLocationCountry(addCommand.getLocationCountry());
        query.setLastYear(addCommand.getYear() - 1);
        // 这个条件不生效，注释掉
        //query.setYear(param.getYear());
        // 获取当前年份的下一年
        query.setNextYear(addCommand.getYear() + 1);

        // 根据国家+年份查询法定假期配置
        List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList = calendarLegalLeaveConfigDao.queryByCondition(query);
        checkLegalLeaveConfigSave(addCommand, calendarLegalLeaveConfigList);
        List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigInfoList = Lists.newArrayList();
        List<Integer> configDetailYear = Lists.newArrayList();
        List<LegalLeaveConfigInfoParam> legalLeaveConfigInfoList = addCommand.getLegalLeaveConfigList();
        for (LegalLeaveConfigInfoParam infoParam : legalLeaveConfigInfoList) {
            CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO =
                    buildCalendarLegalLeaveConfig(addCommand.getLocationCountry(), addCommand.getYear(), infoParam);
            BaseDOUtil.fillDOInsert(calendarLegalLeaveConfigDO);
            calendarLegalLeaveConfigInfoList.add(calendarLegalLeaveConfigDO);

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(DateUtils.str2Date(String.valueOf(infoParam.getLegalLeaveStartDayId()), DatePattern.PURE_DATE_PATTERN));
            configDetailYear.add(calendar.get(Calendar.YEAR));
            calendar.setTime(DateUtils.str2Date(String.valueOf(infoParam.getLegalLeaveEndDayId()), DatePattern.PURE_DATE_PATTERN));
            configDetailYear.add(calendar.get(Calendar.YEAR));
        }
        configDetailYear = configDetailYear.stream().distinct().collect(Collectors.toList());

        // 同步考勤日历法定假期

        // 1. 查询该国家下的考勤日历配置【激活和禁用的都有】
        List<CalendarConfigDO> calendarConfigList = calendarManage.getCalendarConfigsByCountries(
                Collections.singletonList(addCommand.getLocationCountry()));
        if (CollectionUtils.isEmpty(calendarConfigList)) {
            // 该国家下面没有考勤日历，所以无需同步法定节假日，只需要保存法定节假日配置即可
            calendarManage.saveLegalLeaveConfigAndAttendanceDetail(
                    calendarLegalLeaveConfigInfoList, Lists.newArrayList(), Lists.newArrayList());
            return;
        }
        // 获取attendanceConfigList的id集合
        List<Long> attendanceConfigIdList = calendarConfigList.stream().map(CalendarConfigDO::getId).collect(Collectors.toList());

        // 2. 查询这些日历id集合下 + 所选年份的日历配置详情表数据：【is_delete = 0 and is_latest = 1】
//        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailList = hrmsAttendanceConfigDetailManage.selectListByConfigIdsAndYear(attendanceConfigIdList, configDetailYear);
//        List<HrmsAttendanceConfigDetailDO> attendanceConfigDetailList = calendarManageAdapter.selectListByConfigIdsAndYear(attendanceConfigIdList, configDetailYear);
        List<CalendarConfigDetailDO> calendarConfigDetail =
                calendarManage.selectCalendarDetailsByIdsAndYear(attendanceConfigIdList, configDetailYear);
        log.info("该国家{},该年度{},所有考勤日历对应的考勤日历详情数据:{}",
                addCommand.getLocationCountry(), addCommand.getYear(), calendarConfigDetail);
        // 将attendanceConfigDetailList按照attendanceConfigId分组
        Map<Long, List<CalendarConfigDetailDO>> attendanceConfigIdToDetail =
                calendarConfigDetail.stream().collect(Collectors.groupingBy(CalendarConfigDetailDO::getAttendanceConfigId));
        // 将parma中该国家该年下的法定节假日日期都列出来
        List<Long> dayIdList = Lists.newArrayList();
        buildAllDayIdList(addCommand, dayIdList);
        log.info("该国家{},该年度{},法定节假日日期列表:{}", addCommand.getLocationCountry(), addCommand.getYear(), dayIdList);
        // 3. 遍历考勤日历，
        // 设置需要新增的考勤日历详情数据、需要设置为非最新的考勤日历详情数据
        List<CalendarConfigDetailDO> needSaveCalendarConfigDetailList = Lists.newArrayList();
        List<CalendarConfigDetailDO> needUpdateCalendarConfigDetailList = Lists.newArrayList();
        for (CalendarConfigDetailDO attendanceConfig : calendarConfigDetail) {
            // 获取考勤日历主键id
            Long attendanceConfigId = attendanceConfig.getId();
            List<CalendarConfigDetailDO> attendanceConfigDetailInfoList = attendanceConfigIdToDetail.get(attendanceConfigId);
            if (ObjectUtil.isNull(attendanceConfigDetailInfoList) || CollUtil.isEmpty(attendanceConfigDetailInfoList)) {
                // 如果考勤日历下没有配置详情表数据，直接新增
                buildSaveAttendanceConfigDetail(dayIdList, attendanceConfigId, needSaveCalendarConfigDetailList);
                continue;
            }
            // 过滤attendanceConfigDetailInfoList里面dayId为dayIdList的数据
            // 如果考勤日历下有配置详情表数据，先将指定天设置为非最新，在新增指定天为法定假期
            for (CalendarConfigDetailDO attendanceConfigDetail : attendanceConfigDetailInfoList) {
                // 构建需要更新的考勤日历详情数据，设置is_latest = 0，对于已经存在的天的考勤日历详情数据直接删除
                if (dayIdList.contains(attendanceConfigDetail.getDayId())) {
                    attendanceConfigDetail.setIsLatest(BusinessConstant.N);
                    BaseDOUtil.fillDOUpdate(attendanceConfigDetail);
                    needUpdateCalendarConfigDetailList.add(attendanceConfigDetail);
                }
            }
            // 构建需要新增的考勤日历详情数据
            buildSaveAttendanceConfigDetail(dayIdList, attendanceConfigId, needSaveCalendarConfigDetailList);
        }
        log.info("needSaveCalendarConfigDetailList:{}", needSaveCalendarConfigDetailList);
        log.info("needUpdateCalendarConfigDetailList:{}", needUpdateCalendarConfigDetailList);
        // 执行数据库操作
        //calendarManage.saveLegalLeaveConfigAndAttendanceDetail(calendarLegalLeaveConfigList, needUpdateCalendarConfigDetailList, needSaveCalendarConfigDetailList);
        calendarManage.saveLegalLeaveConfigAndAttendanceDetail(
                calendarLegalLeaveConfigList, needUpdateCalendarConfigDetailList, needSaveCalendarConfigDetailList);
        //4.更新员工排班(数据量大，采用异步方式)
        if (CollectionUtils.isEmpty(calendarLegalLeaveConfigList)) {
            log.info("legalLeaveConfigList is empty, 新增法假为空:{}", calendarLegalLeaveConfigList);
            return;
        }
        calendarEventService.sendCalendarLegalLeaveChange(calendarLegalLeaveConfigList);
    }


    private CalendarConfigDO buildCalendarConfig(String attendanceConfigName,
                                                 List<Long> deptIds,
                                                 AttendanceTypeEnum attendanceTypeEnum,
                                                 String country) {
        CalendarConfigDO attendanceConfigDO = new CalendarConfigDO();
        attendanceConfigDO.setId(defaultIdWorker.nextId());
        attendanceConfigDO.setCountry(country);
        attendanceConfigDO.setAttendanceConfigNo(defaultIdWorker.nextAttendanceConfigNo());
        attendanceConfigDO.setAttendanceConfigName(attendanceConfigName);
        attendanceConfigDO.setType(attendanceTypeEnum.name());
        attendanceConfigDO.setIsLatest(BusinessConstant.Y);
        attendanceConfigDO.setStatus(StatusEnum.ACTIVE.getCode());
        attendanceConfigDO.setDeptIds(StringUtils.join(deptIds, ","));

        // 填充基本属性
        BaseDOUtil.fillDOInsert(attendanceConfigDO);
        return attendanceConfigDO;
    }

    /**
     * 处理法定假期
     *
     * @param addCommand         入参
     * @param attendanceConfigId 日历id
     */
    private void handlerLegalLeaveConfig(CalendarAddCommand addCommand, Long attendanceConfigId) {
        List<YearLegalLeaveConfigParam> yearLegalLeaveConfigParamList = addCommand.getYearLegalLeaveConfigParamList();

        if (CollUtil.isNotEmpty(yearLegalLeaveConfigParamList)) {
            String country = addCommand.getCountry();

            // 查询法定假期数据
            CalendarLegalLeaveConfigQuery query = new CalendarLegalLeaveConfigQuery();
            query.setLocationCountry(country);
            query.setAttendanceConfigId(attendanceConfigId);
            List<CalendarLegalLeaveConfigDO> existsLegalLeaveList = calendarLegalLeaveConfigDao.queryByCondition(query);

            // 法定假期配置
            List<CalendarLegalLeaveConfigDO> targetLegalLeaveConfigList = Lists.newArrayList();

            // 遍历集合
            for (YearLegalLeaveConfigParam yearLegalLeaveConfigParam : yearLegalLeaveConfigParamList) {

                Integer year = yearLegalLeaveConfigParam.getYear();
                if (ObjectUtil.isNull(year)) {
                    log.info("getLegalLeaveConfigListNew year is null");
                    // 保存日历年份不能为null
                    throw BusinessLogicException.getException(ErrorCodeEnum.ADD_CALENDAR_CONFIG_YEAR_ERROR);
                }

                List<LegalLeaveConfigParam> legalLeaveConfigList = yearLegalLeaveConfigParam.getLegalLeaveConfigList();
                if (CollUtil.isEmpty(legalLeaveConfigList)) {
                    log.info("getLegalLeaveConfigListNew legalLeaveConfigList is empty");
                    // 保存日历-法定假期详情不能为空
                    throw BusinessLogicException.getException(ErrorCodeEnum.ADD_CALENDAR_CONFIG_LEGAL_LEAVE_CONFIG_ERROR);
                }
                //List<HrmsCompanyLegalLeaveConfigDO> currentYearExistsLegalLeaveList = existsLegalLeaveList.stream().filter(item -> ObjectUtil.equal(item.getYear(), year)).collect(Collectors.toList());
                checkLegalLeaveConfig(legalLeaveConfigList);

                for (LegalLeaveConfigParam infoParam : legalLeaveConfigList) {
                    CalendarLegalLeaveConfigDO legalLeaveConfig = buildLegalLeaveConfig(country, year, attendanceConfigId, infoParam);
                    BaseDOUtil.fillDOInsert(legalLeaveConfig);
                    targetLegalLeaveConfigList.add(legalLeaveConfig);
                }

            }


            if (CollUtil.isNotEmpty(existsLegalLeaveList)) {
                existsLegalLeaveList.forEach(item -> {
                    item.setIsDelete(IsDeleteEnum.YES.getCode());
                    BaseDOUtil.fillDOUpdate(item);
                });
                calendarLegalLeaveConfigDao.updateBatchById(existsLegalLeaveList);
            }

            // 落库
            if (CollUtil.isNotEmpty(targetLegalLeaveConfigList)) {
                calendarLegalLeaveConfigDao.saveBatch(targetLegalLeaveConfigList);
            }
        }
    }

    /**
     * 法定假期校验
     *
     * @param legalLeaveConfigList
     */
    private void checkLegalLeaveConfig(List<LegalLeaveConfigParam> legalLeaveConfigList) {

        // 校验法定假期参数：legalLeaveConfigList中，legalLeaveName字段是否存在与其他legalLeaveName重复的情况
        List<String> legalLeaveNameList = legalLeaveConfigList.stream()
                .map(LegalLeaveConfigParam::getLegalLeaveName)
                .collect(Collectors.toList());
        long legalLeaveCount = legalLeaveNameList.stream().distinct().count();
        if (legalLeaveNameList.size() != legalLeaveCount) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ADD_LEGAL_LEAVE_CONFIG_NAME_REPEAT_ERROR);
        }

        // 校验legalLeaveConfigList数据中每一个数据是否与其他数据的时间有交叉
        //for (LegalLeaveConfigParam infoParam : legalLeaveConfigList) {
        //    List<LegalLeaveConfigParam> legalLeaveConfigListBak = legalLeaveConfigList.stream().filter(legalLeaveConfig -> !legalLeaveConfig.equals(infoParam)).collect(Collectors.toList());
        //    for (LegalLeaveConfigParam configInfoParam : legalLeaveConfigListBak) {
        //        if (infoParam.getLegalLeaveStartDayId() <= configInfoParam.getLegalLeaveEndDayId() && infoParam.getLegalLeaveEndDayId() >= configInfoParam.getLegalLeaveStartDayId()) {
        //            throw BusinessLogicException.getException(HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_TIME_OVERLAP_ERROR);
        //        }
        //    }
        //}

        for (LegalLeaveConfigParam infoParam : legalLeaveConfigList) {

            if (ObjectUtil.isNull(infoParam.getLegalLeaveStartDayId())
                    || ObjectUtil.isNull(infoParam.getLegalLeaveEndDayId())
                    || ObjectUtil.isNull(infoParam.getLegalLeaveDuration())
                    || ObjectUtil.isEmpty(infoParam.getLegalLeaveName())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
            }
            if (infoParam.getLegalLeaveStartDayId() > infoParam.getLegalLeaveEndDayId()) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_LEGAL_LEAVE_CONFIG_TIME_ERROR);
            }
            // 校验法假名称是否存在
            //for (HrmsCompanyLegalLeaveConfigDO legalLeaveConfigDO : existsLegalLeaveList) {
            //    if (legalLeaveConfigDO.getLegalLeaveName().trim().equals(infoParam.getLegalLeaveName().trim())) {
            //        throw BusinessLogicException.getException(HrmsErrorCodeEnums.ADD_LEGAL_LEAVE_CONFIG_NAME_ERROR);
            //    }
            //}
            // 校验法定假期日期是否有交叉
            //for (HrmsCompanyLegalLeaveConfigDO legalLeave : existsLegalLeaveList) {
            //    if (infoParam.getLegalLeaveStartDayId() <= legalLeave.getLegalLeaveEndDayId() && infoParam.getLegalLeaveEndDayId() >= legalLeave.getLegalLeaveStartDayId()) {
            //        throw BusinessLogicException.getException(HrmsErrorCodeEnums.ADD_WELFARE_LEAVE_CONFIG_CROSS_ERROR);
            //    }
            //}
        }
    }

    /**
     * 构建法定假期配置
     *
     * @param country            国家
     * @param year               年份
     * @param attendanceConfigId 日历id
     * @param infoParam          法定假期配置
     * @return HrmsCompanyLegalLeaveConfigDO
     */
    private CalendarLegalLeaveConfigDO buildLegalLeaveConfig(String country, Integer year, Long attendanceConfigId,
                                                             LegalLeaveConfigParam infoParam) {
        CalendarLegalLeaveConfigDO legalLeaveConfig = new CalendarLegalLeaveConfigDO();
        legalLeaveConfig.setId(defaultIdWorker.nextId());
        legalLeaveConfig.setLocationCountry(country);
        legalLeaveConfig.setAttendanceConfigId(attendanceConfigId);
        legalLeaveConfig.setYear(year);
        legalLeaveConfig.setLegalLeaveName(infoParam.getLegalLeaveName());
        legalLeaveConfig.setLegalLeaveStartDayId(infoParam.getLegalLeaveStartDayId());
        legalLeaveConfig.setLegalLeaveEndDayId(infoParam.getLegalLeaveEndDayId());
        legalLeaveConfig.setLegalLeaveDuration(infoParam.getLegalLeaveDuration());
        return legalLeaveConfig;
    }


    /**
     * 保存日历配置的详细信息
     *
     * @param attendanceConfigId 考勤日历配置Id
     * @param addCommand         添加配置的命令对象
     */
    private void saveCalendarConfigDetail(Long attendanceConfigId, CalendarAddCommand addCommand) {
        //获取年月日配置列表
        List<YearMonthDaysConfigParam> yearMonthDaysConfigs = addCommand.getYearMonthDaysConfigs();

        List<CalendarConfigDetailDO> calendarConfigDetailDOs = new ArrayList<>();

        //处理每一年的配置
        for (YearMonthDaysConfigParam yearMonthDaysConfig : yearMonthDaysConfigs) {
            Integer year = yearMonthDaysConfig.getYear();
            if (ObjectUtil.isNull(year)) {
                log.warn("attendanceConfigId:{},yearMonthDaysConfig year is null", attendanceConfigId);
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_CALENDAR_CONFIG_DETAIL_YEAR_ERROR);
            }
            List<MonthDaysConfigParam> monthDaysConfigs = yearMonthDaysConfig.getMonthDaysConfigs();
            if (CollectionUtils.isEmpty(monthDaysConfigs)) {
                log.warn("attendanceConfigId:{},yearMonthDaysConfig monthDaysConfigs is empty", attendanceConfigId);
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_CALENDAR_CONFIG_DETAIL_ERROR);
            }
            //处理每个月的配置
            for (MonthDaysConfigParam monthDaysConfig : monthDaysConfigs) {
                List<DaysConfigParam> daysConfigs = monthDaysConfig.getDaysConfigs();
                if (CollectionUtils.isEmpty(daysConfigs)) {
                    log.warn("attendanceConfigId:{},year：{}，month:{} config is empty!",
                            attendanceConfigId, year, monthDaysConfig.getMonth());
                    continue;
                }
                //处理每一天的配置
                for (DaysConfigParam daysConfig : daysConfigs) {
                    if (daysConfig.getDate() == null || StringUtils.isEmpty(daysConfig.getDayType())) {
                        log.warn("attendanceConfigId:{},year：{}，month:{},date or dayType is empty！",
                                attendanceConfigId, year, monthDaysConfig.getMonth());
                        throw BusinessException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                                I18nUtils.getMessage(ErrorCodeEnum.PARAM_VALID_ERROR.getDesc()));
                    }
                    CalendarConfigDetailDO calendarConfigDetailDO = new CalendarConfigDetailDO();
                    calendarConfigDetailDO.setId(defaultIdWorker.nextId());
                    calendarConfigDetailDO.setAttendanceConfigId(attendanceConfigId);
                    calendarConfigDetailDO.setYear(DateHelper.year(daysConfig.getDate()));
                    calendarConfigDetailDO.setMonth(DateHelper.month(daysConfig.getDate()));
                    calendarConfigDetailDO.setDay(DateHelper.dayOfMonth(daysConfig.getDate()));
                    calendarConfigDetailDO.setDate(daysConfig.getDate());
                    calendarConfigDetailDO.setDayId(DateHelper.getDayId(daysConfig.getDate()));
                    calendarConfigDetailDO.setDayType(daysConfig.getDayType());
                    calendarConfigDetailDO.setIsLatest(BusinessConstant.Y);
                    if (!calendarConfigDetailDO.getYear().equals(year) || !calendarConfigDetailDO.getMonth().equals(monthDaysConfig.getMonth())) {
                        log.warn("attendanceConfigId:{},year：{}，month:{},date:{},年份、月份与date不匹配！",
                                attendanceConfigId, year, monthDaysConfig.getMonth(), daysConfig.getDate());
                        throw BusinessException.get(ErrorCodeEnum.PARAM_VALID_ERROR.getCode(),
                                I18nUtils.getMessage(ErrorCodeEnum.PARAM_VALID_ERROR.getDesc()));
                    }
                    BaseDOUtil.fillDOInsert(calendarConfigDetailDO);
                    calendarConfigDetailDOs.add(calendarConfigDetailDO);
                }
            }
        }
        // 获取所有配置的年份列表
        List<Integer> yearList = yearMonthDaysConfigs.stream().map(YearMonthDaysConfigParam::getYear).collect(Collectors.toList());

        // 将旧数据标记为非最新
        calendarConfigDetailDao.updateToOld(attendanceConfigId, yearList);
        // 批量插入
        if (CollectionUtils.isNotEmpty(calendarConfigDetailDOs)) {
            // 此处可能为空，：当每个月都未设置休息日及节假日时
            calendarConfigDetailDao.saveBatch(calendarConfigDetailDOs);
        }
    }

    private void saveCalendarConfigRange(CalendarConfigDO calendarConfigDO, String country,
                                         Collection<Long> deptIds, Collection<Long> userIds,
                                         List<CalendarConfigDO> configDOList, boolean isAttendanceAdd,
                                         Boolean reSchedule, List<Long> updateDayIds) {
        CountryDTO countryDTO = countryService.queryCountry(country);
        Date date = countryDTO.getCountryTimeZoneDate(new Date());

        //1.获取默认日历配置
        List<CalendarConfigDO> defaultCalendarConfigList = configDOList.stream()
                .filter(item -> item.getType().equals(AttendanceTypeEnum.DEFAULT.name()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(defaultCalendarConfigList)) {
            throw BusinessException.get(ErrorCodeEnum.COUNTRY_NOT_HAVE_DEFAULT_ATTENDANCE_CONFIG.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.COUNTRY_NOT_HAVE_DEFAULT_ATTENDANCE_CONFIG.getDesc()));
        }

        //2.计算新增和删除的用户/部门：

        //旧版本指定的人员
        List<Long> oldUserIdList = calendarManage.selectCalendarConfigRangeByConfigIds(Collections.singletonList(calendarConfigDO.getId()))
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRangeType(), RangeTypeEnum.USER.getCode()))
                .map(CalendarConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        //找出人员级别:新增/删除
        if (CollUtil.isEmpty(userIds)) {
            userIds = Lists.newArrayList();
        }
        List<Long> addUserIdList = userIds.stream()
                .filter(item -> !oldUserIdList.contains(item))
                .collect(Collectors.toList());

        Collection<Long> finalUserIds = userIds;
        List<Long> delUserIdList = oldUserIdList.stream()
                .filter(item -> !finalUserIds.contains(item))
                .collect(Collectors.toList());

        //看历史部门和本次部门，哪些新增，哪些删除
        if (CollUtil.isEmpty(deptIds)) {
            deptIds = Lists.newArrayList();
        }
        List<Long> oldDeptIdList = StringUtils.isBlank(calendarConfigDO.getDeptIds()) ?
                new ArrayList<>() :
                calendarConfigDO.listDeptIds();
        if (isAttendanceAdd) {
            oldDeptIdList = Lists.newArrayList();
        }
        List<Long> finalOldDeptIdList = oldDeptIdList;
        List<Long> addDeptIdList = deptIds.stream()
                .filter(item -> !finalOldDeptIdList.contains(item))
                .collect(Collectors.toList());

        Collection<Long> finalDeptIds = deptIds;
        List<Long> delDeptIdList = oldDeptIdList.stream()
                .filter(item -> !finalDeptIds.contains(item))
                .collect(Collectors.toList());

        //本次新增/删除的部门级别的用户
        List<AttendanceUser> userByDeptIds = userService.listOnJobUserByDeptIdList(new ArrayList<>(addDeptIdList));
        // userByDeptIds过滤掉常驻国不是country的用户
        userByDeptIds = userByDeptIds.stream()
                .filter(item -> ObjectUtil.equal(item.getLocationCountry(), country))
                .collect(Collectors.toList());

        List<Long> addDeptUserIdList = userByDeptIds.stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());
        List<Long> delDeptUserIdList = userService.listOnJobUserByDeptIdList(new ArrayList<>(delDeptIdList))
                .stream()
                .map(AttendanceUser::getId)
                .collect(Collectors.toList());


        //新增的用户，要针对部门和用户级别特殊处理，你新增的部门级的用户可能是别的规则的用户级的，那就不能新增了
        List<CalendarConfigRangeDO> addList = new ArrayList<>();
        List<Long> updateUserIdList = new ArrayList<>();

        //新增处理(用户级别)
        for (Long userId : addUserIdList) {
            updateUserIdList.add(userId);
            buildCalendarPunchData(userId, calendarConfigDO.getId(),
                    calendarConfigDO.getAttendanceConfigNo(),
                    RangeTypeEnum.USER.getCode(), date,
                    addList);
        }

        //新增处理(部门级别)
        List<Long> existUserIdList = calendarManage.selectCalendarConfigRange(addDeptUserIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRangeType(), RangeTypeEnum.USER.getCode()))
                .map(CalendarConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        addDeptUserIdList = addDeptUserIdList.stream()
                .filter(item -> !existUserIdList.contains(item))
                .collect(Collectors.toList());

        for (Long userId : addDeptUserIdList) {
            if (updateUserIdList.contains(userId)) {
                continue;
            }
            updateUserIdList.add(userId);
            buildCalendarPunchData(userId, calendarConfigDO.getId(),
                    calendarConfigDO.getAttendanceConfigNo(),
                    RangeTypeEnum.DEPT.getCode(), date,
                    addList);
        }

        //删除时，需要判断用户级别的，可能他的部门是别的规则指定的，那就从用户降级到部门级别，
        // 如果是部门删除，需要看部门的人是不是被别的规则用户级别指定了，如果是，不能删除，如果不是，那就只能是默认级别了

        //删除处理(用户级别)  看删除用户的部门是不是在别的日历中被指定
        List<AttendanceUser> delUserInfoDOList = userService.listUsersByIds(delUserIdList);
        for (AttendanceUser userInfo : delUserInfoDOList) {
            if (updateUserIdList.contains(userInfo.getId())) {
                continue;
            }
            updateUserIdList.add(userInfo.getId());
            boolean isDefaultType = true;
            //本次规则的部门和员工但去除，员工也在去除的部门中，那么直接就去默认打卡规则
            if (delDeptUserIdList.contains(userInfo.getId())) {
                buildCalendarPunchData(userInfo.getId(), defaultCalendarConfigList.get(0).getId(),
                        defaultCalendarConfigList.get(0).getAttendanceConfigNo(), RangeTypeEnum.DEFAULT.getCode(),
                        date, addList);
                continue;
            }
            //该用户的部门在别的日历中被指定
            for (CalendarConfigDO calendarConfig : configDOList) {
                if (StringUtils.isBlank(calendarConfig.getDeptIds())) {
                    continue;
                }
                List<Long> deptIdList = calendarConfig.listDeptIds();
                if (deptIdList.contains(userInfo.getDeptId())) {
                    buildCalendarPunchData(userInfo.getId(), calendarConfig.getId(),
                            calendarConfig.getAttendanceConfigNo(), RangeTypeEnum.DEPT.getCode(),
                            date, addList);
                    isDefaultType = false;
                    break;
                }
            }
            //该用户部门没有被别的日历使用，那就默认
            if (isDefaultType) {
                buildCalendarPunchData(userInfo.getId(), defaultCalendarConfigList.get(0).getId(),
                        defaultCalendarConfigList.get(0).getAttendanceConfigNo(), RangeTypeEnum.DEFAULT.getCode(),
                        date, addList);
            }
        }

        //删除处理(部门级别)  看部门中的用户是不是在别的日历被指定为用户级别，如果是，不做处理，不是，就降级为默认
        List<Long> existDeptUserIdList = calendarManage.selectCalendarConfigRange(delDeptUserIdList)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRangeType(), RangeTypeEnum.USER.getCode()))
                .map(CalendarConfigRangeDO::getBizId)
                .collect(Collectors.toList());

        delDeptUserIdList = delDeptUserIdList.stream()
                .filter(item -> !existDeptUserIdList.contains(item))
                .collect(Collectors.toList());

        for (Long userId : delDeptUserIdList) {
            if (updateUserIdList.contains(userId)) {
                continue;
            }
            updateUserIdList.add(userId);
            buildCalendarPunchData(userId, defaultCalendarConfigList.get(0).getId(),
                    defaultCalendarConfigList.get(0).getAttendanceConfigNo(), RangeTypeEnum.DEFAULT.getCode(),
                    date, addList);
        }

        List<CalendarConfigRangeDO> updateList = calendarManage.selectCalendarConfigRange(updateUserIdList);
        updateList.forEach(item -> {
            item.setIsLatest(BusinessConstant.N);
            item.setEndDate(date);
            BaseDOUtil.fillDOUpdate(item);
        });
        calendarManage.calendarConfigRangeUpdate(updateList, addList);
        log.info("新增考勤方案成功，考勤方案名称：{},发送mq排班数据数量：{},发送mq重新排班的数据：{}",
                calendarConfigDO.getAttendanceConfigName(), updateUserIdList.size(), JSON.toJSONString(updateUserIdList));
        // 更新日历对范围变动的人员排班(包含新增/删除人员)
        EmployeeSchedulingParam employeeSchedulingParam = calendarEventService.sendCalendarEvent(
                country, deptIds, userIds, reSchedule, updateDayIds, updateUserIdList, date);
        log.info("shouldNewModuleSendEvent is true,send event={}", JSONObject.toJSONString(employeeSchedulingParam));
    }

    private void buildCalendarPunchData(Long userId, Long attendanceConfigId,
                                        String attendanceConfigNo, String rangeType,
                                        Date date, List<CalendarConfigRangeDO> addList) {
        CalendarConfigRangeDO rangeDO = new CalendarConfigRangeDO();
        rangeDO.setId(defaultIdWorker.nextId());
        rangeDO.setBizId(userId);
        rangeDO.setAttendanceConfigId(attendanceConfigId);
        rangeDO.setAttendanceConfigNo(attendanceConfigNo);
        rangeDO.setRangeType(rangeType);
        rangeDO.setStartDate(date);
        rangeDO.setEndDate(BusinessConstant.DEFAULT_END_TIME);
        rangeDO.setIsLatest(BusinessConstant.Y);
        BaseDOUtil.fillDOInsert(rangeDO);
        addList.add(rangeDO);
    }

    /**
     * 校验法定假期配置
     *
     * @param addCommand                   法定假期配置参数
     * @param calendarLegalLeaveConfigList 法定假期配置列表
     */
    private void checkLegalLeaveConfigSave(CalendarLegalLeaveConfigAddCommand addCommand,
                                           List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList) {
        List<LegalLeaveConfigInfoParam> legalLeaveConfigList = addCommand.getLegalLeaveConfigList();
        if (CollUtil.isEmpty(legalLeaveConfigList)) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
        }

        // 校验legalLeaveConfigList数据中每一个数据是否与其他数据的时间有交叉
        for (LegalLeaveConfigInfoParam infoParam : legalLeaveConfigList) {
            List<LegalLeaveConfigInfoParam> legalLeaveConfigListBak = legalLeaveConfigList.stream()
                    .filter(legalLeaveConfig -> !legalLeaveConfig.equals(infoParam))
                    .collect(Collectors.toList());
            for (LegalLeaveConfigInfoParam configInfoParam : legalLeaveConfigListBak) {
                if (infoParam.getLegalLeaveStartDayId() <= configInfoParam.getLegalLeaveEndDayId() &&
                        infoParam.getLegalLeaveEndDayId() >= configInfoParam.getLegalLeaveStartDayId()) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.ADD_WELFARE_LEAVE_CONFIG_TIME_OVERLAP_ERROR);
                }
            }
        }

        for (LegalLeaveConfigInfoParam infoParam : legalLeaveConfigList) {
            checkLegalLeaveConfigInfo(infoParam, calendarLegalLeaveConfigList, addCommand.getYear());
        }
    }

    /**
     * 校验法定假期信息
     *
     * @param infoParam                    法定假期信息
     * @param calendarLegalLeaveConfigList 法定假期配置列表
     */
    private void checkLegalLeaveConfigInfo(LegalLeaveConfigInfoParam infoParam,
                                           List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigList,
                                           Integer year) {
        if (ObjectUtil.isNull(infoParam.getLegalLeaveStartDayId())
                || ObjectUtil.isNull(infoParam.getLegalLeaveEndDayId())
                || ObjectUtil.isNull(infoParam.getLegalLeaveDuration())
                || ObjectUtil.isEmpty(infoParam.getLegalLeaveName())) {
            throw BusinessLogicException.getException(ErrorCodeEnum.PARAM_VALID_ERROR);
        }
        if (infoParam.getLegalLeaveStartDayId() > infoParam.getLegalLeaveEndDayId()) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ADD_LEGAL_LEAVE_CONFIG_TIME_ERROR);
        }
        // 校验法假名称是否存在
        for (CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO : calendarLegalLeaveConfigList) {
            if (calendarLegalLeaveConfigDO.getLegalLeaveName().trim().equals(infoParam.getLegalLeaveName().trim())
                    && year.equals(calendarLegalLeaveConfigDO.getYear())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_LEGAL_LEAVE_CONFIG_NAME_ERROR);
            }
        }
        // 校验法定假期日期是否有交叉
        for (CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO : calendarLegalLeaveConfigList) {
            if (infoParam.getLegalLeaveStartDayId() <= calendarLegalLeaveConfigDO.getLegalLeaveEndDayId() &&
                    infoParam.getLegalLeaveEndDayId() >= calendarLegalLeaveConfigDO.getLegalLeaveStartDayId()) {
                throw BusinessLogicException.getException(ErrorCodeEnum.ADD_WELFARE_LEAVE_CONFIG_CROSS_ERROR);
            }
        }
    }

    /**
     * 构建CalendarLegalLeaveConfigDO
     *
     * @param locationCountry 国家
     * @param year            年份
     * @param infoParam       法定假期信息
     * @return CalendarLegalLeaveConfigDO
     */
    private CalendarLegalLeaveConfigDO buildCalendarLegalLeaveConfig(String locationCountry, Integer year, LegalLeaveConfigInfoParam infoParam) {
        CalendarLegalLeaveConfigDO calendarLegalLeaveConfigDO = new CalendarLegalLeaveConfigDO();
        calendarLegalLeaveConfigDO.setId(defaultIdWorker.nextId());
        calendarLegalLeaveConfigDO.setLocationCountry(locationCountry);
        calendarLegalLeaveConfigDO.setYear(year);
        calendarLegalLeaveConfigDO.setLegalLeaveName(infoParam.getLegalLeaveName());
        calendarLegalLeaveConfigDO.setLegalLeaveStartDayId(infoParam.getLegalLeaveStartDayId());
        calendarLegalLeaveConfigDO.setLegalLeaveEndDayId(infoParam.getLegalLeaveEndDayId());
        calendarLegalLeaveConfigDO.setLegalLeaveDuration(infoParam.getLegalLeaveDuration());
        return calendarLegalLeaveConfigDO;
    }

    /**
     * 创建该国家该年度下面所有法定假期的dayId集合
     *
     * @param addCommand 法定假期配置参数
     * @param dayIdList  目标集合
     */
    private void buildAllDayIdList(CalendarLegalLeaveConfigAddCommand addCommand, List<Long> dayIdList) {
        addCommand.getLegalLeaveConfigList().forEach(infoParam -> {
            Calendar startDate = Calendar.getInstance();
            startDate.setTime(DateUtils.str2Date(String.valueOf(infoParam.getLegalLeaveStartDayId()), DatePattern.PURE_DATE_PATTERN));
            Calendar endDate = Calendar.getInstance();
            endDate.setTime(DateUtils.str2Date(String.valueOf(infoParam.getLegalLeaveEndDayId()), DatePattern.PURE_DATE_PATTERN));
            while (startDate.before(endDate) || startDate.equals(endDate)) {
                dayIdList.add(Long.valueOf(DateUtils.date2Str(startDate.getTime(), DatePattern.PURE_DATE_PATTERN)));
                startDate.add(Calendar.DAY_OF_MONTH, 1);
            }
        });
    }

    /**
     * 构建需要新增的考勤日历详情数据
     *
     * @param dayIdList                        法定假期配置参数
     * @param attendanceConfigId               考勤日历id
     * @param needSaveCalendarConfigDetailList 目标集合
     */
    private void buildSaveAttendanceConfigDetail(List<Long> dayIdList,
                                                 Long attendanceConfigId,
                                                 List<CalendarConfigDetailDO> needSaveCalendarConfigDetailList) {
        for (Long dayId : dayIdList) {
            // 将20240401转换为2024-04-01 00:00:00
            Date date = DateUtil.parse(dayId.toString(), DatePattern.PURE_DATE_PATTERN);
            CalendarConfigDetailDO calendarConfigDetailDO = new CalendarConfigDetailDO();
            calendarConfigDetailDO.setId(defaultIdWorker.nextId());
            calendarConfigDetailDO.setAttendanceConfigId(attendanceConfigId);
            calendarConfigDetailDO.setYear(DateUtil.year(date));
            calendarConfigDetailDO.setMonth(DateUtil.month(date) + 1);
            calendarConfigDetailDO.setDay(DateUtil.dayOfMonth(date));
            calendarConfigDetailDO.setDate(date);
            calendarConfigDetailDO.setDayId(dayId);
            calendarConfigDetailDO.setDayType(AttendanceDayTypeEnum.HOLIDAY.name());
            calendarConfigDetailDO.setIsLatest(BusinessConstant.Y);
            BaseDOUtil.fillDOInsert(calendarConfigDetailDO);
            needSaveCalendarConfigDetailList.add(calendarConfigDetailDO);
        }
    }
}

