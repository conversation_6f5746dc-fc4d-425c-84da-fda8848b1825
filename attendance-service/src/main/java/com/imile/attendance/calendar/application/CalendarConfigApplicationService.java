package com.imile.attendance.calendar.application;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.imile.attendance.calendar.AttendanceCalendarApi;
import com.imile.attendance.calendar.CalendarConfigService;
import com.imile.attendance.calendar.CalendarManage;
import com.imile.attendance.calendar.command.CalendarAddCommand;
import com.imile.attendance.calendar.command.CalendarLegalLeaveConfigAddCommand;
import com.imile.attendance.calendar.command.CalendarStatusSwitchCommand;
import com.imile.attendance.calendar.command.CalendarUpdateCommand;
import com.imile.attendance.calendar.dto.AttendanceArchiveCalendarUpdateDTO;
import com.imile.attendance.calendar.dto.CalendarConfigDTO;
import com.imile.attendance.calendar.dto.CalendarConfigDetailDTO;
import com.imile.attendance.calendar.dto.CalendarConfigRangeDTO;
import com.imile.attendance.calendar.dto.CalendarConfigSelectDTO;
import com.imile.attendance.calendar.dto.CalendarDayConfigDTO;
import com.imile.attendance.calendar.dto.CalendarTypeDTO;
import com.imile.attendance.calendar.dto.DayConfigDTO;
import com.imile.attendance.calendar.dto.DaysConfigDTO;
import com.imile.attendance.calendar.dto.MonthDaysConfigDTO;
import com.imile.attendance.calendar.factory.CalendarFactory;
import com.imile.attendance.calendar.mapstruct.CalendarConfigServiceMapstruct;
import com.imile.attendance.calendar.param.CalendarApiParam;
import com.imile.attendance.calendar.query.CalendarLegalLeaveConfigDetailQuery;
import com.imile.attendance.calendar.query.CalendarLegalLeaveConfigListQuery;
import com.imile.attendance.calendar.vo.LegalLeaveConfigDetailInfoVO;
import com.imile.attendance.calendar.vo.LegalLeaveConfigDetailVO;
import com.imile.attendance.calendar.vo.LegalLeaveConfigVO;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.AttendanceTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.context.RequestInfoHolder;
import com.imile.attendance.infrastructure.logRecord.LogRecordService;
import com.imile.attendance.infrastructure.logRecord.dto.LogRecordOptions;
import com.imile.attendance.infrastructure.logRecord.enums.OperationTypeEnum;
import com.imile.attendance.infrastructure.logRecord.enums.PageOperateType;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarLegalLeaveConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarLegalLeaveConfigQuery;
import com.imile.attendance.infrastructure.repository.common.AttendanceDeptService;
import com.imile.attendance.infrastructure.repository.common.AttendanceUserService;
import com.imile.attendance.infrastructure.repository.common.UserResourceService;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceDept;
import com.imile.attendance.infrastructure.repository.common.dto.AttendanceUser;
import com.imile.attendance.migration.MigrationService;
import com.imile.attendance.permission.AttendancePermissionService;
import com.imile.attendance.util.BaseDOUtil;
import com.imile.attendance.util.PageUtil;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.common.page.PaginationResult;
import com.imile.genesis.api.enums.WorkStatusEnum;
import com.imile.rpc.common.RpcResult;
import com.imile.util.BeanUtils;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24
 * @Description
 */
@Slf4j
@Service
public class CalendarConfigApplicationService {

    @Resource
    private CalendarFactory calendarFactory;
    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;
    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarLegalLeaveConfigDao calendarLegalLeaveConfigDao;
    @Resource
    private UserResourceService userResourceService;
    @Resource
    private AttendancePermissionService attendancePermissionService;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private AttendanceUserService userService;
    @Resource
    private AttendanceDeptService deptService;
    @Resource
    private AttendanceCalendarApi attendanceCalendarApi;
    @Resource
    private CalendarConfigService calendarConfigService;
    @Resource
    private LogRecordService logRecordService;
    @Resource
    private MigrationService migrationService;


    //todo replace
//    private final HrmsCompanyLeaveConfigRangDao hrmsCompanyLeaveConfigRangDao;
//    private final HrmsAttendanceUserLeaveConfigHistoryDaoImpl userLeaveConfigHistoryDao;
//    private final HrmsCompanyLeaveConfigManage companyLeaveConfigManage;
//    private final ProducerBasicService producerBasicService;
//    private final AttendanceCalendarApi attendanceCalendarApi;

    /**
     * 根据国家获取日历类型
     *
     * @param country 国家
     * @return 日历类型
     */
    public CalendarTypeDTO getCalendarTypeByCountry(String country) {
        return calendarConfigService.getCalendarTypeByCountry(country);
    }

    /**
     * 添加日历配置方案
     */
    public List<CalendarConfigRangeDTO> add(CalendarAddCommand addCommand) {
        return calendarFactory.add(addCommand);
    }

    public List<CalendarConfigRangeDTO> addDefault(CalendarAddCommand addCommand) {
        return calendarFactory.addDefault(addCommand);
    }

    public List<CalendarConfigRangeDTO> addCustom(CalendarAddCommand addCommand) {
        return calendarFactory.addCustom(addCommand);
    }

    /**
     * 修改日历配置方案
     */
    public List<CalendarConfigRangeDTO> update(CalendarUpdateCommand updateCommand) {
        List<CalendarConfigRangeDTO> calendarConfigRangeDTOList = calendarFactory.update(updateCommand);
        try {
            // 睡眠300毫秒返回 避免事务未提交
            Thread.sleep(300L);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        return calendarConfigRangeDTOList;
    }

    /**
     * 员工档案日历变更
     */
    public void userCalendarRangeUpdate(AttendanceArchiveCalendarUpdateDTO calendarUpdateDTO) {
        calendarFactory.userCalendarRangeUpdate(calendarUpdateDTO);
    }

    /**
     * 状态切换
     */
    public List<CalendarConfigRangeDTO> statusSwitch(CalendarStatusSwitchCommand statusSwitchCommand) {
        // 查找记录
        CalendarConfigDO calendarConfigDO = calendarConfigDao.getById(statusSwitchCommand.getId());
        if (calendarConfigDO == null) {
            throw BusinessLogicException.getException(ErrorCodeEnum.DATA_NOT_EXITS);
        }
        Set<String> backupOperatorUserCodes = migrationService.getBackupOperatorUserCodes();
        if (CollectionUtils.isEmpty(backupOperatorUserCodes) ||
                !backupOperatorUserCodes.contains(RequestInfoHolder.getUserCode())) {
            List<String> allGrayScaleSwitchedCountries = migrationService.getAllGrayScaleSwitchedCountry();
            if (!allGrayScaleSwitchedCountries.contains(calendarConfigDO.getCountry())) {
                throw BusinessLogicException.getException(ErrorCodeEnum.SYSTEM_SWITCHING_CONTACT_ADMIN);
            }
        }
        if (StatusEnum.DISABLED.getCode().equals(statusSwitchCommand.getStatus())) {
            List<CalendarConfigRangeDO> configRangeDOList =
                    calendarConfigRangeDao.selectCalendarConfigByIds(Collections.singletonList(calendarConfigDO.getId()));
            List<AttendanceUser> userInfoList = userService.listUsersByIds(
                    configRangeDOList.stream().map(CalendarConfigRangeDO::getBizId)
                            .distinct().collect(Collectors.toList()))
                    .stream()
                    .filter(item -> StringUtils.equalsIgnoreCase(item.getStatus(), StatusEnum.ACTIVE.getCode()) &&
                            StringUtils.equalsIgnoreCase(item.getWorkStatus(), WorkStatusEnum.ON_JOB.getCode()) &&
                            Objects.equals(item.getIsDriver(), BusinessConstant.N))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                throw BusinessException.get(ErrorCodeEnum.CUSTOM_ATTENDANCE_NOT_CLOSE.getCode(),
                        I18nUtils.getMessage(ErrorCodeEnum.CUSTOM_ATTENDANCE_NOT_CLOSE.getDesc()));
            }
        }
        // 更改状态
        CalendarConfigDO model = BeanUtil.copyProperties(calendarConfigDO, CalendarConfigDO.class);
        model.setStatus(statusSwitchCommand.getStatus());
        BaseDOUtil.fillDOUpdate(model);
        calendarConfigDao.updateById(model);

        if (StatusEnum.DISABLED.getCode().equals(statusSwitchCommand.getStatus())) {
            logRecordService.recordOperation(model,
                    LogRecordOptions.builder()
                            .pageOperateType(PageOperateType.DISABLE)
                            .operationType(OperationTypeEnum.CALENDAR_DISABLE.getCode())
                            .country(model.getCountry())
                            .bizName(model.getAttendanceConfigName())
                            .build()
            );
        } else {
            logRecordService.recordOperation(model,
                    LogRecordOptions.builder()
                            .pageOperateType(PageOperateType.ACTIVE)
                            .operationType(OperationTypeEnum.CALENDAR_ENABLE.getCode())
                            .country(model.getCountry())
                            .bizName(model.getAttendanceConfigName())
                            .build()
            );
        }

        return Collections.emptyList();
    }

    /**
     * 考勤日历方案列表查询
     */
    public PaginationResult<CalendarConfigDTO> list(CalendarConfigQuery query) {
        // 更改为常驻国权限
        List<String> countryList = attendancePermissionService.filterUserCountryAuth(query.getCountry(), query.getCountryList());
        log.info("日历列表当前用户:{}，有权限的国家列表:{}", RequestInfoHolder.getUserCode(), countryList);
        if (CollectionUtils.isEmpty(countryList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        query.setCountryList(countryList);
        // 优化类型设置
        if (query.getIsDefault() != null) {
            query.setType(query.getIsDefault().equals(BusinessConstant.N) ?
                    AttendanceTypeEnum.CUSTOM.name() : AttendanceTypeEnum.DEFAULT.name());
        }

        // 添加人员查询对应日历
        if (CollectionUtils.isNotEmpty(query.getUserIdList())) {
            List<CalendarConfigRangeDO> userRangeList = calendarConfigRangeDao.selectConfigRange(query.getUserIdList());
            if (CollectionUtils.isNotEmpty(userRangeList)) {
                List<Long> attendanceConfigIdList = userRangeList.stream()
                        .map(CalendarConfigRangeDO::getAttendanceConfigId)
                        .collect(Collectors.toList());
                query.setIds(attendanceConfigIdList);
            }
        }

        PageInfo<CalendarConfigDO> pageInfo = PageHelper.startPage(
                        query.getCurrentPage(),
                        query.getShowCount(),
                        query.getCount() && query.getShowCount() > 0)
                .doSelectPageInfo(() -> calendarConfigDao.listByQuery(query));

        List<CalendarConfigDO> calendarConfigDOList = pageInfo.getList();
        if (CollectionUtils.isEmpty(calendarConfigDOList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }

        List<Long> configIdList = calendarConfigDOList.stream()
                .map(CalendarConfigDO::getId)
                .collect(Collectors.toList());

        // 批量查询所有关联数据
        List<CalendarConfigRangeDO> rangeDOS = calendarConfigRangeDao.selectCalendarConfigByIds(configIdList);

        Map<Long, List<CalendarConfigRangeDO>> rangeMap = rangeDOS.stream()
                .collect(Collectors.groupingBy(CalendarConfigRangeDO::getAttendanceConfigId));

        // 收集所有需要查询的用户ID和部门ID
        Set<Long> allUserIds = new HashSet<>();
        Set<Long> allDeptIds = new HashSet<>();

        for (CalendarConfigDO configDO : calendarConfigDOList) {
            // 收集用户ID
            List<CalendarConfigRangeDO> currentRanges = rangeMap.getOrDefault(configDO.getId(), Collections.emptyList());
            List<Long> userIds = currentRanges.stream()
                    .map(CalendarConfigRangeDO::getBizId)
                    .collect(Collectors.toList());
            allUserIds.addAll(userIds);

            // 收集部门ID
            if (StringUtils.isNotBlank(configDO.getDeptIds())) {
                allDeptIds.addAll(configDO.listDeptIds());
            }
        }

        // 批量查询所有用户和部门
        Map<Long, AttendanceUser> userMap = Collections.emptyMap();
        if (!allUserIds.isEmpty()) {
            List<AttendanceUser> allActiveUsers = userService.asyncListActiveAndOnJobUsers(new ArrayList<>(allUserIds));
            userMap = allActiveUsers.stream().collect(Collectors.toMap(AttendanceUser::getId, Function.identity()));
        }

        Map<Long, AttendanceDept> deptMap = Collections.emptyMap();
        if (!allDeptIds.isEmpty()) {
            List<AttendanceDept> allDepts = deptService.listByDeptIds(new ArrayList<>(allDeptIds));
            deptMap = allDepts.stream().collect(Collectors.toMap(AttendanceDept::getId, Function.identity()));
        }

        // 保存转换后的结果
        List<CalendarConfigDTO> attendanceConfigDTOS = new ArrayList<>();
        boolean isChinesEnv = RequestInfoHolder.isChinese();

        for (CalendarConfigDO calendarConfigDO : calendarConfigDOList) {
            CalendarConfigDTO calendarConfigDTO = CalendarConfigServiceMapstruct.INSTANCE.toCalendarConfigDTO(calendarConfigDO);
            attendanceConfigDTOS.add(calendarConfigDTO);
            //查询绑定的员工数
            List<CalendarConfigRangeDO> currentConfigRangeDOList = rangeMap.getOrDefault(calendarConfigDO.getId(), Collections.emptyList());
            List<Long> userIds = currentConfigRangeDOList.stream()
                    .map(CalendarConfigRangeDO::getBizId)
                    .collect(Collectors.toList());

            int employeeCount = 0;
            List<CalendarConfigRangeDTO> rangeRecords = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(userIds)) {
                // 过滤出活跃用户
                List<Long> activeUserIds = userIds.stream()
                        .filter(userMap::containsKey)
                        .collect(Collectors.toList());
                employeeCount = activeUserIds.size();
                if (calendarConfigDTO.areCustom()) {
                    Map<Long, CalendarConfigRangeDO> userRangeMap = currentConfigRangeDOList.stream()
                            .filter(CalendarConfigRangeDO::areUserRangeType)
                            .collect(Collectors.toMap(CalendarConfigRangeDO::getBizId, Function.identity(), (existing, replacement) -> existing));
                    for (Long userId : activeUserIds) {
                        AttendanceUser user = userMap.get(userId);
                        if (user != null && userRangeMap.containsKey(userId)) {
                            rangeRecords.add(CalendarConfigRangeDTO.buildUserRangeDTO(user, isChinesEnv));
                        }
                    }
                }
            }

            // 查出部门
            if (StringUtils.isNotBlank(calendarConfigDO.getDeptIds())) {
                List<Long> deptIdList = calendarConfigDO.listDeptIds();
                for (Long deptId : deptIdList) {
                    AttendanceDept dept = deptMap.get(deptId);
                    if (dept != null) {
                        rangeRecords.add(CalendarConfigRangeDTO.buildDeptRangeDTO(dept, isChinesEnv));
                    }
                }
            }
            calendarConfigDTO.setRangeRecords(rangeRecords);
            calendarConfigDTO.setEmployeeCount(employeeCount);
        }
        return PageUtil.getPageResult(attendanceConfigDTOS, query, (int) pageInfo.getTotal(), pageInfo.getPages());
    }

    /**
     * 获取详情
     */
    public CalendarConfigDetailDTO detail(CalendarConfigDetailQuery configDetailQuery) {
        //获取考勤日历
        CalendarConfigDetailDTO calendarConfigDetailDTO = getCalendarConfig(configDetailQuery);

        // 查询考勤方案对应的适用范围
        List<CalendarConfigRangeDO> rangeDOS = calendarManage.selectCalendarConfigRangeByConfigIds(
                Collections.singletonList(configDetailQuery.getCalendarConfigId()));

        // 已绑定的用户ID处理 设置  userRecords
        List<Long> userIds = rangeDOS.stream()
                .filter(CalendarConfigRangeDO::areUserRangeType)
                .map(CalendarConfigRangeDO::getBizId)
                .collect(Collectors.toList());
        boolean isChinesEnv = RequestInfoHolder.isChinese();

        if (CollectionUtils.isNotEmpty(userIds)) {
            //hrmsUserInfoDao.listByIds(userIds)改造
            calendarConfigDetailDTO.setUserRecords(
                    userService.asyncListOnJobUsers(userIds)
                            .stream()
                            .map(user -> CalendarConfigRangeDTO.buildUserRangeDTO(user, isChinesEnv))
                            .collect(Collectors.toList())
            );
        }

        // 查出部门下所有的用户数 // 设置 deptRecords
        if (StringUtils.isNotBlank(calendarConfigDetailDTO.getDeptIds())) {
            List<Long> deptIdList = calendarConfigDetailDTO.listDeptIds();
            //hrmsEntDeptDao.listByDeptIds(deptIds)改造
            calendarConfigDetailDTO.setDeptRecords(
                    deptService.listByDeptIds(deptIdList)
                            .stream()
                            .map(dept -> CalendarConfigRangeDTO.buildDeptRangeDTO(dept, isChinesEnv))
                            .collect(Collectors.toList())
            );
        }
        return calendarConfigDetailDTO;
    }


    private CalendarConfigDetailDTO getCalendarConfig(CalendarConfigDetailQuery configDetailQuery) {

        List<CalendarConfigDO> calendarConfigDOList = calendarManage.getByCalendarConfigIds(
                Collections.singletonList(configDetailQuery.getCalendarConfigId()));
        if (CollectionUtils.isEmpty(calendarConfigDOList)) {
            throw BusinessException.get(ErrorCodeEnum.DATA_NOT_EXITS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.DATA_NOT_EXITS.getDesc()));
        }
        CalendarConfigDO record = calendarConfigDOList.get(0);
        CalendarConfigDetailDTO calendarConfigDetailDTO = BeanUtil.copyProperties(record, CalendarConfigDetailDTO.class);
        //  日期属性处理
        // 以月分组，放入map中
        Map<Integer, List<CalendarConfigDetailDO>> listMap = calendarConfigDetailDao.listLatestRecords(configDetailQuery)
                .stream()
                .collect(Collectors.groupingBy(CalendarConfigDetailDO::getMonth));
        // 遍历循环，每月处理
        List<MonthDaysConfigDTO> monthDaysConfigs = listMap.entrySet()
                .stream()
                .map(integerListEntry ->
                        new MonthDaysConfigDTO(integerListEntry.getKey(), integerListEntry.getValue()
                                .stream()
                                .map(detailDO -> new DaysConfigDTO(detailDO.getDate(), detailDO.getDayType()))
                                .collect(Collectors.toList()))
                ).sorted(Comparator.comparingInt(MonthDaysConfigDTO::getMonth))
                .collect(Collectors.toList());

        // 日期属性设置 日期属性设置
        calendarConfigDetailDTO.setMonthDaysConfigs(monthDaysConfigs);
        return calendarConfigDetailDTO;
    }

    /**
     * 仓内外包部门变动需要重新匹配日历和打卡规则,todo
     */
    public void warehouseUserCalendarAndPunchHandler() {

    }

    /**
     * 获取考勤日历下拉
     */
    public List<CalendarConfigSelectDTO> selectList(String country) {
        List<String> countryList = userResourceService.getAuthorizedBizCountryList();
        if (CollectionUtils.isEmpty(countryList)) {
            return Collections.emptyList();
        }
        List<CalendarConfigDO> calendarConfigDOList = calendarManage.getCalendarConfigsByCountries(countryList);
        List<CalendarConfigSelectDTO> calendarConfigSelectDTOList =
                CalendarConfigServiceMapstruct.INSTANCE.mapToSelect(calendarConfigDOList);
        if (CollectionUtils.isEmpty(calendarConfigSelectDTOList) ||
                StringUtils.isBlank(country) || StringUtils.equalsIgnoreCase(country, "ALL")) {
            return calendarConfigSelectDTOList;
        }
        return calendarConfigSelectDTOList.stream()
                .filter(o -> StringUtils.equalsIgnoreCase(o.getCountry(), country))
                .collect(Collectors.toList());
    }

    /**
     * 法定假期新增逻辑
     *
     * @param addCommand 入参
     */
    public void addCalendarLegalLeaveConfig(CalendarLegalLeaveConfigAddCommand addCommand) {
        calendarFactory.addCalendarLegalLeaveConfig(addCommand);
    }

    /**
     * 法定假期详情
     */
    public LegalLeaveConfigDetailVO queryLegalLeaveDetail(CalendarLegalLeaveConfigDetailQuery detailQuery) {
        CalendarLegalLeaveConfigQuery query = CalendarConfigServiceMapstruct.INSTANCE.toLegalLeaveConfigQuery(detailQuery);
        List<CalendarLegalLeaveConfigDO> legalLeaveConfigList = calendarLegalLeaveConfigDao.queryByCondition(query);
        if (CollUtil.isEmpty(legalLeaveConfigList) || ObjectUtil.isNull(legalLeaveConfigList.get(0))) {
            return BeanUtils.convert(detailQuery, LegalLeaveConfigDetailVO.class);
        }
        log.info("查询法定假期详情结果:{}", legalLeaveConfigList);
        LegalLeaveConfigDetailVO legalLeaveConfigDetail = new LegalLeaveConfigDetailVO();
        List<LegalLeaveConfigDetailInfoVO> infoList = Lists.newArrayList();
        legalLeaveConfigDetail.setLocationCountry(legalLeaveConfigList.get(0).getLocationCountry());
        legalLeaveConfigDetail.setYear(legalLeaveConfigList.get(0).getYear());
        for (CalendarLegalLeaveConfigDO legalLeaveConfig : legalLeaveConfigList) {
            LegalLeaveConfigDetailInfoVO legalLeaveConfigDetailInfo = new LegalLeaveConfigDetailInfoVO();
            legalLeaveConfigDetailInfo.setId(legalLeaveConfig.getId());
            legalLeaveConfigDetailInfo.setLegalLeaveName(legalLeaveConfig.getLegalLeaveName());
            legalLeaveConfigDetailInfo.setLegalLeaveStartDayId(legalLeaveConfig.getLegalLeaveStartDayId());
            legalLeaveConfigDetailInfo.setLegalLeaveEndDayId(legalLeaveConfig.getLegalLeaveEndDayId());
            legalLeaveConfigDetailInfo.setLegalLeaveDuration(legalLeaveConfig.getLegalLeaveDuration());
            infoList.add(legalLeaveConfigDetailInfo);
        }
        legalLeaveConfigDetail.setLegalLeaveConfigDetailInfoList(infoList);

        return legalLeaveConfigDetail;
    }

    /**
     * 查询国家法定假期记录 分页
     *
     * @param queryList 查询参数
     * @return 分页结果
     */
    public PaginationResult<LegalLeaveConfigVO> queryLegalLeave(CalendarLegalLeaveConfigListQuery queryList) {
        log.info("查询法定假期配置参数:{}", queryList);
        CalendarLegalLeaveConfigQuery query = BeanUtils.convert(queryList, CalendarLegalLeaveConfigQuery.class);

        List<String> countryList = userResourceService.getAuthorizedBizCountryList(RequestInfoHolder.getUserId());
        //没有权限
        if (CollectionUtils.isEmpty(countryList)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        countryList = countryList.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
        query.setLocationCountryList(countryList);

        if (ObjectUtil.isNotEmpty(query.getLocationCountry())) {
            if (countryList.contains(query.getLocationCountry())) {
                query.setLocationCountryList(Collections.singletonList(query.getLocationCountry()));
            } else {
                return PaginationResult.get(Collections.emptyList(), query);
            }
        }

        List<CalendarLegalLeaveConfigDO> leaveConfigDOS = calendarLegalLeaveConfigDao.queryByConditionGroup(query);
        Map<String, List<CalendarLegalLeaveConfigDO>> countryMap = leaveConfigDOS.stream()
                .collect(Collectors.groupingBy(item -> item.getLocationCountry() + item.getYear()));
        List<Long> ids = new ArrayList<>();
        for (String key : countryMap.keySet()) {
            List<CalendarLegalLeaveConfigDO> leaveConfigDOS_country = countryMap.get(key);
            if (CollectionUtils.isNotEmpty(leaveConfigDOS_country)) {
                CalendarLegalLeaveConfigDO legalLeaveConfigDO = leaveConfigDOS_country.stream()
                        .sorted(Comparator.comparing(CalendarLegalLeaveConfigDO::getId).reversed())
                        .collect(Collectors.toList())
                        .get(0);
                ids.add(legalLeaveConfigDO.getId());
            }
        }
        Page<CalendarLegalLeaveConfigDO> page = PageHelper.startPage(
                query.getCurrentPage(), query.getShowCount(), query.getShowCount() > 0);

        PageInfo<CalendarLegalLeaveConfigDO> pageInfo = page.doSelectPageInfo(
                () -> calendarLegalLeaveConfigDao.queryByIds(ids));

        List<CalendarLegalLeaveConfigDO> legalLeaveConfig = pageInfo.getList();
        if (CollUtil.isEmpty(legalLeaveConfig)) {
            return PaginationResult.get(Collections.emptyList(), query);
        }
        return PageUtil.getPageResult(
                CalendarConfigServiceMapstruct.INSTANCE.toLegalLeaveConfigVO(legalLeaveConfig), query,
                (int) pageInfo.getTotal(), pageInfo.getPages());
    }


    public CalendarDayConfigDTO getCalendarByCondition(CalendarApiParam param) {
        CalendarDayConfigDTO result = new CalendarDayConfigDTO();

        CalendarConfigQuery query = new CalendarConfigQuery();
        query.setCountry(param.getCountry());
        query.setStatus(StatusEnum.ACTIVE.getCode());
        List<CalendarConfigDO> calendarConfigDOList = calendarConfigDao.listByQuery(query);
        if (CollectionUtils.isEmpty(calendarConfigDOList)) {
            return result;
        }
        List<Long> calendarConfigIds = calendarConfigDOList.stream()
                .map(CalendarConfigDO::getId)
                .collect(Collectors.toList());
        List<CalendarRangeCountDTO> calendarRangeCountList = calendarManage.countCalendarRange(calendarConfigIds);

        Long calendarConfigId;
        if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(calendarRangeCountList)) {
            List<CalendarRangeCountDTO> calendarRangeCountReverseSort = calendarRangeCountList.stream()
                    .sorted(Comparator.comparingInt(CalendarRangeCountDTO::getCount).reversed())
                    .collect(Collectors.toList());
            calendarConfigId = calendarRangeCountReverseSort.get(0).getCalendarConfigId();
        } else {
            calendarConfigId = calendarConfigDOList.get(0).getId();
        }
        result.setCalendarConfigId(calendarConfigId);

        RpcResult<List<DayConfigDTO>> rpcResult = attendanceCalendarApi.getCalendarByCondition(param);
        result.setDayConfigDTOList(rpcResult.getResult());
        return result;
    }
}
