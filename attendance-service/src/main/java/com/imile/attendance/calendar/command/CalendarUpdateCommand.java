package com.imile.attendance.calendar.command;

import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/7 
 * @Description
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarUpdateCommand extends CalendarAddCommand {

    /**
     * 考勤配置ID
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Long id;

    /**
     * 修改日历考勤日集合
     */
    private List<Long> updateDayIds;
}
