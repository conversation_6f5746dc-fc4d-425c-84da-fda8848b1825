package com.imile.attendance.calendar.helper;

import com.imile.attendance.apollo.AttendanceProperties;
import com.imile.ucenter.api.context.RequestInfoHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class CalendarConfigHelper {

    @Resource
    private AttendanceProperties attendanceProperties;


    public String getDefaultCalendarConfigName(){
        return RequestInfoHolder.isChinese() ?
                attendanceProperties.getAttendance().getDefaultAttendanceConfigNameCn() :
                attendanceProperties.getAttendance().getDefaultAttendanceConfigNameEn();
    }
}
