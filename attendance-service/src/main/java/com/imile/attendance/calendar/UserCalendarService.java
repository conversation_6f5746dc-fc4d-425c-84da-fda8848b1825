package com.imile.attendance.calendar;

import cn.hutool.core.date.DateUtil;
import com.imile.attendance.constants.BusinessConstant;
import com.imile.attendance.enums.CalendarDayTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigRangeDao;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarLegalLeaveConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigDetailQuery;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigRangeQuery;
import com.imile.attendance.util.DateHelper;
import com.imile.common.enums.StatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/4/18 
 * @Description
 */
@Slf4j
@Service
public class UserCalendarService {

    @Resource
    private CalendarConfigRangeDao calendarConfigRangeDao;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;

    /**
     * 检查用户是否关联了日历
     */
    public boolean hasCalendarConfig(Long userId) {
        CalendarConfigDO userCalendarConfig = getUserCalendarConfig(userId);
        return userCalendarConfig != null;
    }

    /**
     * 获取用户的日历配置
     */
    public CalendarConfigDO getUserCalendarConfig(Long userId) {
        Map<Long, CalendarConfigDO> configMap = getCalendarConfigs(Collections.singletonList(userId));
        if (MapUtils.isEmpty(configMap)) {
            return null;
        }
        return configMap.get(userId);
    }

    /**
     * 获取用户指定日期的日历类型
     */
    public CalendarConfigDetailDO getUserCalendarDayDetail(Long userId, Long dayId) {
        Map<Long, CalendarConfigDetailDO> detailMap = getUserCalendarDetail(userId, dayId);
        return detailMap.get(dayId);
    }

    /**
     * 判断指定日期是否为节假日
     */
    public boolean areHoliday(Long userId, Long dayId) {
        Map<Long, CalendarConfigDetailDO> detailMap = getUserCalendarDetail(userId, dayId);
        CalendarConfigDetailDO detail = detailMap.get(dayId);
        return detail != null && detail.areHOLIDAY();
    }

    /**
     * 获取指定日期的出勤类型,CalendarConfigDetailDO对象为空代表出勤日
     */
    public String getDayType(Long userId, Long dayId) {
        Map<Long, CalendarConfigDetailDO> detailMap = getUserCalendarDetail(userId, dayId);
        CalendarConfigDetailDO calendarConfigDetailDO = detailMap.get(dayId);
        if (null == calendarConfigDetailDO) {
            return CalendarDayTypeEnum.PRESENT.getCode();
        }
        return calendarConfigDetailDO.getDayType();
    }

    /**
     * 获取用户日历配置 <userId, CalendarConfigDO>
     */
    public Map<Long, CalendarConfigDO> getCalendarConfigs(List<Long> userIds) {
        CalendarConfigRangeQuery calendarConfigRangeQuery = CalendarConfigRangeQuery.builder()
                .bizIds(userIds)
                .startTime(DateUtil.date())
                .endTime(BusinessConstant.DEFAULT_END_TIME_DATE)
                .calendarConfigStatus(StatusEnum.ACTIVE.getCode())
                .build();
        List<CalendarConfigRangeDO> rangeDOList = calendarConfigRangeDao.listAllRecords(calendarConfigRangeQuery);
        if (CollectionUtils.isEmpty(rangeDOList)) {
            return Collections.emptyMap();
        }
        Map<Long, List<CalendarConfigRangeDO>> userCalendarRangeMap = rangeDOList.stream()
                .collect(Collectors.groupingBy(CalendarConfigRangeDO::getBizId));

        List<Long> configIds = rangeDOList.stream()
                .map(CalendarConfigRangeDO::getAttendanceConfigId)
                .distinct()
                .collect(Collectors.toList());

        Map<Long, CalendarConfigDO> calendarConfigDOMap = calendarManage.getByCalendarConfigIds(configIds).stream()
                .collect(Collectors.toMap(CalendarConfigDO::getId, o -> o, (v1, v2) -> v1));

        Map<Long, CalendarConfigDO> result = new HashMap<>();
        for (Long userId : userIds) {
            List<CalendarConfigRangeDO> calendarConfigRangeDOS = userCalendarRangeMap.get(userId);
            if (CollectionUtils.isEmpty(calendarConfigRangeDOS)) {
                continue;
            }
            Long calendarConfigId = calendarConfigRangeDOS.get(0).getAttendanceConfigId();
            CalendarConfigDO calendarConfigDO = calendarConfigDOMap.get(calendarConfigId);
            result.put(userId, calendarConfigDO);
        }
        return result;
    }

    public Map<Long, CalendarConfigDetailDO> getUserCalendarDetail(Long userId, Long dayId) {
        CalendarConfigDO config = getUserCalendarConfig(userId);
        if (config == null) {
            return Collections.emptyMap();
        }
        int year = DateHelper.year(DateHelper.transferDayIdToDate(dayId));
        List<CalendarConfigDetailDO> details = calendarConfigDetailDao.selectListByConfigIdsAndYear(
                Collections.singletonList(config.getId()),
                Collections.singletonList(year)
        );
        return details.stream()
                .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId, o -> o, (v1, v2) -> v1));
    }

    /**
     * 获取用户日历详情 <userId, <dayId, CalendarConfigDetailDO>>
     *
     * @param userCalendarMap 用户日历Map <userId, CalendarConfigDO>
     * @param years           年份列表
     * @return 用户日历详情Map <userId, <dayId, CalendarConfigDetailDO>>
     */
    public Map<Long, Map<Long, CalendarConfigDetailDO>> getUserCalendarDetailMap(Map<Long, CalendarConfigDO> userCalendarMap,
                                                                                 List<Integer> years) {
        if (MapUtils.isEmpty(userCalendarMap)) {
            return Collections.emptyMap();
        }
        // 获取用户日历配置ID列表
        List<Long> calendarConfigIds = userCalendarMap.values()
                .stream()
                .map(CalendarConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        // 获取用户日历详情(查询指定年份的日历详情，无需查询所有年份的日历详情)
        List<CalendarConfigDetailDO> calendarConfigDetailDOList = calendarConfigDetailDao.selectListByConfigIdsAndYear(
                calendarConfigIds,
                years
        );
        // 获取用户日历详情Map <attendanceConfigId, List<CalendarConfigDetailDO>>
        Map<Long, List<CalendarConfigDetailDO>> calenarDetailMap = calendarConfigDetailDOList.stream()
                .collect(Collectors.groupingBy(CalendarConfigDetailDO::getAttendanceConfigId));

        // 获取用户日历详情Map <userId, <dayId, CalendarConfigDetailDO>>
        Map<Long, Map<Long, CalendarConfigDetailDO>> result = new HashMap<>();
        userCalendarMap.forEach((userId, calendarConfig) -> {
            List<CalendarConfigDetailDO> userCalendarDetailList = calenarDetailMap.getOrDefault(calendarConfig.getId(), Collections.emptyList());
            result.put(userId, userCalendarDetailList.stream()
                    .collect(Collectors.toMap(CalendarConfigDetailDO::getDayId,
                            o -> o, (v1, v2) -> v1)));
        });
        return result;
    }

    /**
     * 获取用户法定假期详情 <userId, List<CalendarLegalLeaveConfigDO>>
     *
     * @param userCalendarMap 用户日历Map <userId, CalendarConfigDO>
     * @param years           年份列表
     * @return 用户法定假期详情Map <userId, List<CalendarLegalLeaveConfigDO>>
     */
    public Map<Long, List<CalendarLegalLeaveConfigDO>> getUserLegalLeaveMap(Map<Long, CalendarConfigDO> userCalendarMap,
                                                                            List<Integer> years) {
        if (MapUtils.isEmpty(userCalendarMap)) {
            return Collections.emptyMap();
        }
        List<Long> calendarConfigIds = userCalendarMap.values()
                .stream()
                .map(CalendarConfigDO::getId)
                .distinct()
                .collect(Collectors.toList());
        List<CalendarLegalLeaveConfigDO> calendarLegalLeaveConfigDOList = calendarManage.selectListByConfigIdsAndYear(
                calendarConfigIds,
                years
        );
        Map<Long, List<CalendarLegalLeaveConfigDO>> legalLeaveConfigMap = calendarLegalLeaveConfigDOList.stream()
                .collect(Collectors.groupingBy(CalendarLegalLeaveConfigDO::getAttendanceConfigId));

        // 获取用户法定假期详情Map <userId, <dayId, CalendarLegalLeaveConfigDO>>
        Map<Long, List<CalendarLegalLeaveConfigDO>> result = new HashMap<>();
        userCalendarMap.forEach((userId, calendarConfig) -> {
            List<CalendarLegalLeaveConfigDO> userLegalLeaveList =
                    legalLeaveConfigMap.getOrDefault(calendarConfig.getId(), Collections.emptyList());
            result.put(userId, userLegalLeaveList);
        });
        return result;
    }

    /**
     * 获取指定日期的法定假期配置
     */
    public CalendarLegalLeaveConfigDO getLegalLeaveConfigByDayId(List<CalendarLegalLeaveConfigDO> legalLeaveConfigList, Long dayId) {
        if (null == dayId) {
            return null;
        }
        return legalLeaveConfigList.stream()
                .filter(i -> dayId.compareTo(i.getLegalLeaveStartDayId()) >= 0 &&
                        dayId.compareTo(i.getLegalLeaveEndDayId()) <= 0)
                .findAny()
                .orElse(null);
    }

    /**
     * 获取指定日历配置和日期的日历详情
     */
    public CalendarConfigDetailDO getCalendarDetailByConfigIdAndDayId(Long configId, Long dayId) {
        CalendarConfigDetailQuery query = new CalendarConfigDetailQuery();
        query.setCalendarConfigId(configId);
        query.setDayId(dayId);
        List<CalendarConfigDetailDO> details = calendarConfigDetailDao.listRecords(query);
        return CollectionUtils.isEmpty(details) ? null : details.get(0);
    }


}
