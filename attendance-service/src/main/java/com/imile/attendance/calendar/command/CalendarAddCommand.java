package com.imile.attendance.calendar.command;

import com.imile.attendance.calendar.dto.YearLegalLeaveConfigParam;
import com.imile.attendance.calendar.dto.YearMonthDaysConfigParam;
import com.imile.common.constant.ValidCodeConstant;
import com.imile.common.validator.Groups;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/2/7 
 * @Description
 */
@Data
public class CalendarAddCommand {

    /**
     * 国家
     */
    private String country;

    /**
     * 考勤配置名称/日历名称，缺省方案时可为空
     */
    private String attendanceConfigName;

    /**
     * 方案类型 DEFAULT:缺省方案 CUSTOM:自定义方案,缺省方案只能有一种
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Add.class})
    private String type;

    /**
     * 日历年份组合
     */
    @NotEmpty(message = ValidCodeConstant.NOT_EMPTY, groups = {Groups.Add.class})
    private List<YearMonthDaysConfigParam> yearMonthDaysConfigs;

    /**
     * 适用部门，缺省方案时可为空
     */
    private List<Long> deptIds;

    /**
     * 适用用户id，缺省方案时可为空
     */
    private List<Long> userIds;

    /**
     * 适用用户编码，缺省方案时可为空（后续改成userCode）,暂时无用
     */
    private List<String> userCodes;

    /**
     * 当部门/用户已存在其他考勤日历方案时，是否强制覆盖 1：强制覆盖  0：不覆盖
     */
    private Integer isCoverOld;

    /**
     * 是否重新排班
     */
    @NotNull(message = ValidCodeConstant.NOT_NULL, groups = {Groups.Update.class})
    private Boolean reSchedule;

    /**
     * 年份法定假期信息
     */
    //@NotEmpty(message = ValidCodeConstant.NOT_EMPTY)
    private List<YearLegalLeaveConfigParam> yearLegalLeaveConfigParamList;
}
