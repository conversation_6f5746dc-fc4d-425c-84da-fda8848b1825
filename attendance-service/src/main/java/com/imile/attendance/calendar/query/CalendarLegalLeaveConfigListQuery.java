package com.imile.attendance.calendar.query;

import com.imile.common.query.BaseQuery;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 国家法定假期记录表查询入参
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CalendarLegalLeaveConfigListQuery extends BaseQuery {

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 法定假期名称
     */
    private String legalLeaveName;
}
