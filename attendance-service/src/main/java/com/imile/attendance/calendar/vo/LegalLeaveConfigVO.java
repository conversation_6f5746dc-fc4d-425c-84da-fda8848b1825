package com.imile.attendance.calendar.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Data
public class LegalLeaveConfigVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 常驻地国家
     */
    private String locationCountry;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 法定假期名称
     */
    private String legalLeaveName;

    /**
     * 法定假期开始时间：legal_leave_start_day_id 示例：20240124
     */
    private Long legalLeaveStartDayId;

    /**
     * 法定假期结束时间：legal_leave_end_day_id 示例：20240124
     */
    private Long legalLeaveEndDayId;

    /**
     * 法定假期时长
     */
    private Integer legalLeaveDuration;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    private String createUserCode;

    private String createUserName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdDate;

    private String lastUpdUserCode;

    private String lastUpdUserName;
}
