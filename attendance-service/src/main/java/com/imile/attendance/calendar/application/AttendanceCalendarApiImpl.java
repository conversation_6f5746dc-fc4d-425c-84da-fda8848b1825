package com.imile.attendance.calendar.application;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.imile.attendance.calendar.CalendarManage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.imile.attendance.calendar.AttendanceCalendarApi;
import com.imile.attendance.calendar.dto.DayConfigDTO;
import com.imile.attendance.calendar.param.CalendarApiParam;
import com.imile.attendance.enums.AttendanceDayTypeEnum;
import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDao;
import com.imile.attendance.infrastructure.repository.calendar.dao.CalendarConfigDetailDao;
import com.imile.attendance.infrastructure.repository.calendar.dto.CalendarRangeCountDTO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDetailDO;
import com.imile.attendance.infrastructure.repository.calendar.query.CalendarConfigQuery;
import com.imile.common.enums.StatusEnum;
import com.imile.common.exception.BusinessException;
import com.imile.rpc.common.RpcResult;
import com.imile.util.lang.I18nUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Service(version = "1.0.0")
@Slf4j
public class AttendanceCalendarApiImpl implements AttendanceCalendarApi {

    @Resource
    private CalendarConfigDao calendarConfigDao;
    @Resource
    private CalendarManage calendarManage;
    @Resource
    private CalendarConfigDetailDao calendarConfigDetailDao;

    @Override
    public RpcResult<List<DayConfigDTO>> getCalendarByCondition(CalendarApiParam param) {
        log.info("getCalendarByCondition param: {}", param);
        checkParams(param);

        CalendarConfigQuery query = new CalendarConfigQuery();
        query.setCountry(param.getCountry());
        query.setStatus(StatusEnum.ACTIVE.getCode());
        List<CalendarConfigDO> calendarConfigDOList = calendarConfigDao.listByQuery(query);
        if (CollectionUtils.isEmpty(calendarConfigDOList)) {
            return RpcResult.ok(Collections.emptyList());
        }
        List<Long> calendarConfigIds = calendarConfigDOList.stream()
                .map(CalendarConfigDO::getId)
                .collect(Collectors.toList());
        List<CalendarRangeCountDTO> calendarRangeCountList = calendarManage.countCalendarRange(calendarConfigIds);

        Long calendarConfigId;
        if (CollectionUtils.isNotEmpty(calendarRangeCountList)){
            List<CalendarRangeCountDTO> calendarRangeCountReverseSort = calendarRangeCountList.stream()
                    .sorted(Comparator.comparingInt(CalendarRangeCountDTO::getCount).reversed())
                    .collect(Collectors.toList());
            calendarConfigId = calendarRangeCountReverseSort.get(0).getCalendarConfigId();
        }else{
            calendarConfigId = calendarConfigDOList.get(0).getId();
        }

        List<Integer> years = getYearList(param);
        List<CalendarConfigDetailDO> calendarConfigDetailDOList =
                calendarConfigDetailDao.selectListByConfigIdsAndYear(Collections.singletonList(calendarConfigId), years);

        List<DayConfigDTO> result = new ArrayList<>();
        Date startDate = param.getStartTime();
        Long startDayId = Long.parseLong(DateUtil.format(startDate, DatePattern.PURE_DATE_PATTERN));
        Long endDayId = Long.parseLong(DateUtil.format(param.getEndTime(), DatePattern.PURE_DATE_PATTERN));

        while (startDayId.compareTo(endDayId) < 1) {
            DayConfigDTO dayConfig = new DayConfigDTO();
            int year = Integer.parseInt(startDayId.toString().substring(0, 4));
            Long finalStartDayId = startDayId;
            Optional<CalendarConfigDetailDO> configDetailOptional = calendarConfigDetailDOList.stream()
                    .filter(configDetail -> Objects.equals(finalStartDayId, configDetail.getDayId())
                            && year == configDetail.getYear())
                    .findFirst();
            if (configDetailOptional.isPresent()) {
                dayConfig.setDayType(configDetailOptional.get().getDayType());
            } else {
                dayConfig.setDayType(AttendanceDayTypeEnum.PRESENT.name());
            }
            dayConfig.setDate(DateUtil.parse(String.valueOf(startDayId), DatePattern.PURE_DATE_PATTERN));
            result.add(dayConfig);
            //后一天
            startDate = DateUtil.offsetDay(startDate, 1);
            startDayId = Long.parseLong(DateUtil.format(startDate, DatePattern.PURE_DATE_PATTERN));
        }

        return RpcResult.ok(result);
    }

    private void checkParams(CalendarApiParam param) {
        if (StringUtils.isEmpty(param.getCountry())) {
            throw BusinessException.get(ErrorCodeEnum.COUNTRY_NOT_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.COUNTRY_NOT_EMPTY.getDesc()));
        }

        if (Objects.isNull(param.getStartTime())) {
            throw BusinessException.get(ErrorCodeEnum.START_TIME_NOT_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.START_TIME_NOT_EMPTY.getDesc()));
        }

        if (Objects.isNull(param.getEndTime())) {
            throw BusinessException.get(ErrorCodeEnum.END_TIME_NOT_EMPTY.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.END_TIME_NOT_EMPTY.getDesc()));
        }

        if (param.getStartTime().getTime() > param.getEndTime().getTime()) {
            throw BusinessException.get(ErrorCodeEnum.START_TIME_CANNOT_BE_LATER_THAN_END_TIME.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.START_TIME_CANNOT_BE_LATER_THAN_END_TIME.getDesc()));
        }
        long timeInterval = DateUtil.between(param.getStartTime(), param.getEndTime(), DateUnit.DAY);
        if (timeInterval > 1000) {
            throw BusinessException.get(ErrorCodeEnum.TIME_RANGE_LIMITED_DAYS.getCode(),
                    I18nUtils.getMessage(ErrorCodeEnum.TIME_RANGE_LIMITED_DAYS.getDesc()));
        }
    }

    private List<Integer> getYearList(CalendarApiParam param) {
        List<Integer> years = new ArrayList<>();
        int startYear = DateUtil.year(param.getStartTime());
        years.add(startYear);
        int endYear = DateUtil.year(param.getEndTime());
        if (!years.contains(endYear)) {
            years.add(endYear);
        }
        return years;
    }
}
