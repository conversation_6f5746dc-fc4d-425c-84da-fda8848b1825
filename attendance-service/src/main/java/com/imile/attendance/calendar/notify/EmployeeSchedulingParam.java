package com.imile.attendance.calendar.notify;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description 排班事件参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EmployeeSchedulingParam {

    /**
     * 国家
     */
    private String country;

    /**
     * 用户ID
     */
    private List<Long> userIds;

    /**
     * 开始时间
     * 示例：20240124
     */
    private Long startDayId;

    /**
     * 结束时间
     * 示例：20240124
     */
    private Long endDayId;

    /**
     * 日期集合
     * 示例：20240124
     */
    private List<Long> dayIds;

    /**
     * 人工排班是否重新排班
     */
    private Boolean reSchedule;

    /**
     * 是否删除人员当前日期后所有排班(再根据reSchedule来判断是否删除手工得)
     */
    private Boolean delAllSchedule;

    /**
     * 是否为新考勤触发
     */
    private Boolean isNewAttendanceTrigger = false;
}
