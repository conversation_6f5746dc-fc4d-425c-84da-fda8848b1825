package com.imile.attendance.calendar;

import com.imile.attendance.infrastructure.repository.calendar.dao.BaseDayInfoDao;
import com.imile.attendance.infrastructure.repository.calendar.model.BaseDayInfoDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * <AUTHOR> chen
 * @Date 2025/3/25 
 * @Description
 */
@Service
public class BaseDayInfoService {

    @Resource
    private BaseDayInfoDao baseDayInfoDao;

    public void saveOrUpdateBatch(Collection<BaseDayInfoDO> entityList) {
        baseDayInfoDao.saveOrUpdateBatch(entityList);
    }
}
