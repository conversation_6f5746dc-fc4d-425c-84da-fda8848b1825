package com.imile.attendance.calendar;

import com.imile.attendance.enums.ErrorCodeEnum;
import com.imile.attendance.enums.RangeTypeEnum;
import com.imile.attendance.exception.BusinessLogicException;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigDO;
import com.imile.attendance.infrastructure.repository.calendar.model.CalendarConfigRangeDO;
import com.imile.common.exception.BusinessException;
import com.imile.util.lang.I18nUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> chen
 * @Date 2025/3/24 
 * @Description
 */
@Component
public class RangeBizTypeService {

    @Resource
    private CalendarManage calendarManage;

    public void checkCalendarDeptAndUserExistRecord(List<CalendarConfigDO> coutnryCalendarConfigList,
                                                    String calendarConfigNo,
                                                    String calendarConfigName,
                                                    Collection<Long> deptIds,
                                                    Collection<Long> userIds,
                                                    Integer isCoverOld){
        //检查日历配置名称是否重复
        boolean hasConflict = coutnryCalendarConfigList.stream()
                .anyMatch(config -> StringUtils.equalsIgnoreCase(config.getAttendanceConfigName(), calendarConfigName) &&
                        !StringUtils.equalsIgnoreCase(config.getAttendanceConfigNo(), calendarConfigNo));
        if (hasConflict) {
            throw BusinessLogicException.getException(ErrorCodeEnum.ATTENDANCE_CONFIG_NAME_EXIST);
        }
        //判断部门是否其他规则已经使用
        validateDepartmentUsage(coutnryCalendarConfigList, deptIds, calendarConfigNo);

        //查询这些人是否被别的规则指定，人员级别的
        validateUserUsage(coutnryCalendarConfigList, userIds, calendarConfigNo);
    }

    /**
     * 部门校验逻辑
     */
    private void validateDepartmentUsage(List<CalendarConfigDO> configDOList, Collection<Long> deptIds, String calendarConfigNo) {
        for (CalendarConfigDO configDO : configDOList) {
            List<Long> deptIdList = StringUtils.isBlank(configDO.getDeptIds()) ?
                    new ArrayList<>() :
                    configDO.listDeptIds();
            for (Long deptId : deptIdList) {
                if (deptIds.contains(deptId) && !StringUtils.equalsIgnoreCase(calendarConfigNo, configDO.getAttendanceConfigNo())) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.DEPT_EXIST_IN_OTHER_ATTENDANCE_CONFIG, configDO.getAttendanceConfigName());
                }
            }
        }
    }

    /**
     * 用户校验逻辑
     */
    private void validateUserUsage(List<CalendarConfigDO> configDOList, Collection<Long> userIds, String calendarConfigNo) {
        List<CalendarConfigRangeDO> existedConfigRecords = calendarManage.selectCalendarConfigRange((List<Long>) userIds)
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getRangeType(), RangeTypeEnum.USER.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existedConfigRecords)) {
            List<Long> attendanceConfigIdList = existedConfigRecords.stream()
                    .map(CalendarConfigRangeDO::getAttendanceConfigId)
                    .collect(Collectors.toList());
            List<CalendarConfigDO> configDOS = calendarManage.getByCalendarConfigIds(attendanceConfigIdList);
            for (CalendarConfigDO calendarConfigDO : configDOS) {
                if (!StringUtils.equalsIgnoreCase(calendarConfigDO.getAttendanceConfigNo(), calendarConfigNo)) {
                    throw BusinessLogicException.getException(ErrorCodeEnum.USER_EXIST_IN_OTHER_ATTENDANCE_CONFIG,
                                    calendarConfigDO.getAttendanceConfigName());
                }
            }
        }
    }
}
