package com.imile.attendance.sync.hrms;

import com.imile.attendance.bizMq.mapstruct.UserInfoMapstruct;
import com.imile.attendance.infrastructure.repository.employee.dao.UserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.employee.dao.UserInfoDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserDimissionRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserEntryRecordDao;
import com.imile.attendance.infrastructure.repository.hrms.dao.HrmsUserInfoDao;
import com.imile.attendance.infrastructure.sync.TableSyncHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> chen
 * @Date 2025/3/26 
 * @Description 人事系统用户信息表同步处理器
 */
@Component
@Slf4j
public class HrUserInfoTableSyncHandler {

    @Resource
    private HrmsUserInfoDao hrmsUserInfoDao;
    @Resource
    private HrmsUserEntryRecordDao hrmsUserEntryRecordDao;
    @Resource
    private HrmsUserDimissionRecordDao hrmsUserDimissionRecordDao;

    @Resource
    private UserInfoDao userInfoDao;
    @Resource
    private UserEntryRecordDao userEntryRecordDao;
    @Resource
    private UserDimissionRecordDao userDimissionRecordDao;


    @XxlJob("hrUserInfoTableSyncHandler")
    public ReturnT<String> syncHrUserInfo(String param) {
        if (StringUtils.isBlank(param)) {
            log.error("表名为空, 无法同步");
            return ReturnT.SUCCESS;
        }
        log.info("开始同步表：{}", param);
        XxlJobLogger.log("开始同步表：{}", param);
        switch (param) {
            case "hrms_user_info":
                XxlJobLogger.log("开始同步hrms_user_info");
                fullSyncHrmsUserInfo();
                break;
            case "hrms_user_entry_record":
                fullSyncHrmsUserEntryRecord();
                break;
            case "hrms_user_dimission_record":
                fullSyncHrmsUserDimissionRecord();
                break;
            case "all":
                fullSyncHrmsUserInfo();
                fullSyncHrmsUserEntryRecord();
                fullSyncHrmsUserDimissionRecord();
                break;
            default:
                log.error("未知的表名：{}", param);
                XxlJobLogger.log("未知的表名：{}", param);
                return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    public void fullSyncHrmsUserInfo() {
        TableSyncHelper.syncTable(
                "hrms_user_info",
                hrmsUserInfoDao::listByPage,
                UserInfoMapstruct.INSTANCE::mapToAttendanceUserInfoDO,
                userInfoDao::saveOrUpdateBatch
        );
    }

    public void fullSyncHrmsUserEntryRecord() {
        TableSyncHelper.syncTable(
                "hrms_user_entry_record",
                hrmsUserEntryRecordDao::listByPage,
                UserInfoMapstruct.INSTANCE::mapToAttendanceUserEntryRecordDO,
                userEntryRecordDao::saveOrUpdateBatch
        );
    }

    public void fullSyncHrmsUserDimissionRecord() {
        TableSyncHelper.syncTable(
                "hrms_user_dimission_record",
                hrmsUserDimissionRecordDao::listByPage,
                UserInfoMapstruct.INSTANCE::mapToAttendanceUserDimissionRecordDO,
                userDimissionRecordDao::saveOrUpdateBatch
        );
    }
}
