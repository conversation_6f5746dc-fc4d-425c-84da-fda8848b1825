package com.imile.attendance.report.month.calculator;

import com.imile.attendance.enums.shift.DayShiftRuleEnum;
import com.imile.attendance.infrastructure.repository.report.model.AttendanceDayReportDO;
import com.imile.attendance.infrastructure.repository.rule.model.PunchClassConfigDO;
import com.imile.attendance.report.month.dto.UserMonthReportMetricsDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 考勤指标计算器测试类
 *
 * <AUTHOR> chen
 * @date 2025/6/23
 */
@ExtendWith(MockitoExtension.class)
class AttendanceMetricsCalculatorTest {

    @InjectMocks
    private AttendanceMetricsCalculator attendanceMetricsCalculator;

    private Map<Long, PunchClassConfigDO> punchClassConfigMap;
    private List<AttendanceDayReportDO> userDayReportList;

    @BeforeEach
    void setUp() {
        // 准备班次配置数据
        punchClassConfigMap = new HashMap<>();
        PunchClassConfigDO punchClassConfig = new PunchClassConfigDO();
        punchClassConfig.setId(1L);
        punchClassConfig.setAttendanceHours(new BigDecimal("8")); // 8小时工作制
        punchClassConfigMap.put(1L, punchClassConfig);

        // 准备用户日报数据
        userDayReportList = Arrays.asList(
                createDayReport(1L, 20250601L, DayShiftRuleEnum.CLASS.getCode(), 1L, new BigDecimal("480")), // 工作日，8小时
                createDayReport(1L, 20250602L, DayShiftRuleEnum.CLASS.getCode(), 1L, new BigDecimal("240")), // 工作日，4小时
                createDayReport(1L, 20250603L, DayShiftRuleEnum.OFF.getCode(), 1L, new BigDecimal("0")),     // 休息日
                createDayReport(1L, 20250604L, DayShiftRuleEnum.CLASS.getCode(), 1L, new BigDecimal("480")), // 工作日，8小时
                createDayReport(1L, 20250605L, DayShiftRuleEnum.H.getCode(), 1L, new BigDecimal("0"))        // 节假日
        );
    }

//    @Test
//    void testCalculateMonthlyMetrics_正常情况() {
//        // 执行计算
//        UserMonthReportMetricsDTO result = attendanceMetricsCalculator.calculateMonthlyMetrics(userDayReportList, punchClassConfigMap);
//
//        // 验证结果
//        assertNotNull(result);
//        assertTrue(result.isValid());
//
//        // 验证应出勤天数：3天工作日（CLASS）
//        assertEquals(new BigDecimal("3"), result.getShouldAttendanceDays());
//
//        // 验证实际出勤天数：(480+240+480)/(8*60) = 1200/480 = 2.5天
//        assertEquals(new BigDecimal("2.5000"), result.getActualAttendanceDays());
//
//        // 验证实际出勤时长：1200分钟 = 20小时
//        assertEquals(new BigDecimal("20.00"), result.getActualAttendanceHours());
//
//        // 验证工作时长：应该等于实际出勤时长
//        assertEquals(new BigDecimal("20.00"), result.getWorkHours());
//
//        // 验证平均工时：1200分钟 / 2.5天 = 480分钟 = 8小时
//        assertEquals(new BigDecimal("8.00"), result.getActualAttendanceAverageHours());
//    }
//
//    @Test
//    void testCalculateMonthlyMetrics_空数据() {
//        // 测试空列表
//        UserMonthReportMetricsDTO result = attendanceMetricsCalculator.calculateMonthlyMetrics(null, punchClassConfigMap);
//
//        assertNotNull(result);
//        assertEquals(UserMonthReportMetricsDTO.empty(), result);
//    }
//
//    @Test
//    void testCalculateMonthlyMetrics_除零保护() {
//        // 创建实际出勤时长为0的数据
//        List<AttendanceDayReportDO> zeroAttendanceList = Arrays.asList(
//                createDayReport(1L, 20250601L, DayShiftRuleEnum.CLASS.getCode(), 1L, new BigDecimal("0"))
//        );
//
//        UserMonthReportMetricsDTO result = attendanceMetricsCalculator.calculateMonthlyMetrics(zeroAttendanceList, punchClassConfigMap);
//
//        assertNotNull(result);
//        // 应出勤天数应该是1
//        assertEquals(new BigDecimal("1"), result.getShouldAttendanceDays());
//        // 实际出勤天数应该是0
//        assertEquals(new BigDecimal("0.0000"), result.getActualAttendanceDays());
//        // 平均工时应该是0（除零保护）
//        assertEquals(new BigDecimal("0.00"), result.getActualAttendanceAverageHours());
//    }
//
//    @Test
//    void testCalculateMonthlyMetrics_班次配置缺失() {
//        // 使用不存在的班次ID
//        List<AttendanceDayReportDO> invalidConfigList = Arrays.asList(
//                createDayReport(1L, 20250601L, DayShiftRuleEnum.CLASS.getCode(), 999L, new BigDecimal("480"))
//        );
//
//        UserMonthReportMetricsDTO result = attendanceMetricsCalculator.calculateMonthlyMetrics(invalidConfigList, punchClassConfigMap);
//
//        assertNotNull(result);
//        // 应出勤天数应该是1（基于dayShiftRule判断）
//        assertEquals(new BigDecimal("1"), result.getShouldAttendanceDays());
//        // 但实际出勤相关指标应该是0（因为班次配置不存在）
//        assertEquals(new BigDecimal("0.0000"), result.getActualAttendanceDays());
//        assertEquals(new BigDecimal("0.00"), result.getActualAttendanceHours());
//    }

    /**
     * 创建测试用的日报数据
     */
    private AttendanceDayReportDO createDayReport(Long userId, Long dayId, String dayShiftRule, 
                                                 Long punchClassConfigId, BigDecimal actualAttendanceMinutes) {
        AttendanceDayReportDO dayReport = new AttendanceDayReportDO();
        dayReport.setUserId(userId);
        dayReport.setDayId(dayId);
        dayReport.setDayShiftRule(dayShiftRule);
        dayReport.setPunchClassConfigId(punchClassConfigId);
        dayReport.setActualAttendanceMinutes(actualAttendanceMinutes);
        return dayReport;
    }
}
