package com.imile.attendance.migration.processor;

import com.imile.attendance.infrastructure.repository.hrms.modle.HrmsAttendanceClassEmployeeConfigDO;
import com.imile.attendance.infrastructure.repository.migration.model.MappingPunchClassConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigDO;
import com.imile.attendance.infrastructure.repository.shift.model.UserShiftConfigHistoryDO;
import com.imile.attendance.migration.converter.UserShiftConfigDataConverter;
import com.imile.attendance.migration.service.MappingPunchClassConfigService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * UserShiftConfigMigrationProcessor性能优化测试
 * 验证批量转换方法是否正确工作，以及N+1查询问题是否得到解决
 * 
 * <AUTHOR> chen
 * @since 2025/6/18
 */
@ExtendWith(MockitoExtension.class)
class UserShiftConfigMigrationProcessorOptimizationTest {

    @Mock
    private MappingPunchClassConfigService mappingPunchClassConfigService;

    @InjectMocks
    private UserShiftConfigDataConverter dataConverter;

    private List<HrmsAttendanceClassEmployeeConfigDO> testHrmsDataList;
    private List<MappingPunchClassConfigDO> testMappingList;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testHrmsDataList = createTestHrmsDataList();
        testMappingList = createTestMappingList();
    }

    /**
     * 测试批量转换方法是否正确工作
     */
    @Test
    void testConvertFromHrmsBatch_ShouldWorkCorrectly() {
        // Given
        when(mappingPunchClassConfigService.listByHrPunchClassIds(anyList()))
                .thenReturn(testMappingList);

        // When
        List<UserShiftConfigDO> result = dataConverter.convertFromHrmsBatch(testHrmsDataList);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证只调用了一次批量查询方法，而不是N次单条查询
        verify(mappingPunchClassConfigService, times(1)).listByHrPunchClassIds(anyList());
        verify(mappingPunchClassConfigService, never()).getByHrPunchClassId(any());
    }

    /**
     * 测试批量转换历史数据方法是否正确工作
     */
    @Test
    void testConvertFromHrmsToHistoryBatch_ShouldWorkCorrectly() {
        // Given
        when(mappingPunchClassConfigService.listByHrPunchClassIds(anyList()))
                .thenReturn(testMappingList);

        // When
        List<UserShiftConfigHistoryDO> result = dataConverter.convertFromHrmsToHistoryBatch(testHrmsDataList);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证只调用了一次批量查询方法
        verify(mappingPunchClassConfigService, times(1)).listByHrPunchClassIds(anyList());
    }

    /**
     * 测试空数据列表的处理
     */
    @Test
    void testConvertFromHrmsBatch_WithEmptyList_ShouldReturnEmptyList() {
        // When
        List<UserShiftConfigDO> result = dataConverter.convertFromHrmsBatch(Collections.emptyList());

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证没有调用任何查询方法
        verify(mappingPunchClassConfigService, never()).listByHrPunchClassIds(anyList());
    }

    /**
     * 测试映射关系不存在的情况
     */
    @Test
    void testConvertFromHrmsBatch_WithNoMapping_ShouldHandleGracefully() {
        // Given
        when(mappingPunchClassConfigService.listByHrPunchClassIds(anyList()))
                .thenReturn(Collections.emptyList());

        // When
        List<UserShiftConfigDO> result = dataConverter.convertFromHrmsBatch(testHrmsDataList);

        // Then
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证所有转换后的数据的punchClassConfigId都为null
        for (UserShiftConfigDO data : result) {
            assertNull(data.getPunchClassConfigId());
        }
    }

    /**
     * 创建测试用的HRMS数据列表
     */
    private List<HrmsAttendanceClassEmployeeConfigDO> createTestHrmsDataList() {
        HrmsAttendanceClassEmployeeConfigDO data1 = new HrmsAttendanceClassEmployeeConfigDO();
        data1.setId(1L);
        data1.setUserId(100L);
        data1.setClassId(10L);
        data1.setDayId(20250101L);
        data1.setDayPunchType("W");

        HrmsAttendanceClassEmployeeConfigDO data2 = new HrmsAttendanceClassEmployeeConfigDO();
        data2.setId(2L);
        data2.setUserId(200L);
        data2.setClassId(20L);
        data2.setDayId(20250102L);
        data2.setDayPunchType("PH");

        HrmsAttendanceClassEmployeeConfigDO data3 = new HrmsAttendanceClassEmployeeConfigDO();
        data3.setId(3L);
        data3.setUserId(300L);
        data3.setClassId(30L);
        data3.setDayId(20250103L);
        data3.setDayPunchType("R");

        return Arrays.asList(data1, data2, data3);
    }

    /**
     * 创建测试用的映射关系列表
     */
    private List<MappingPunchClassConfigDO> createTestMappingList() {
        MappingPunchClassConfigDO mapping1 = new MappingPunchClassConfigDO();
        mapping1.setHrPunchClassId(10L);
        mapping1.setPunchClassConfigId(1001L);

        MappingPunchClassConfigDO mapping2 = new MappingPunchClassConfigDO();
        mapping2.setHrPunchClassId(20L);
        mapping2.setPunchClassConfigId(1002L);

        MappingPunchClassConfigDO mapping3 = new MappingPunchClassConfigDO();
        mapping3.setHrPunchClassId(30L);
        mapping3.setPunchClassConfigId(1003L);

        return Arrays.asList(mapping1, mapping2, mapping3);
    }
}
